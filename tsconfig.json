{"compilerOptions": {"module": "commonjs", "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@entities": ["src/modules/database/entities"], "@lib/*": ["src/modules/lib/*"], "@/*": ["src/*"]}}, "types": ["node"], "include": ["src/**/*"]}