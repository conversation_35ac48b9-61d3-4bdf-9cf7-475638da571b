APP_PATH='/Users/<USER>/Desktop/Document/projects/focusfeedback/backend'

# Postgres credentials
POSTGRES_HOST=localhost
POSTGRES_POST=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=qwerty1
POSTGRES_DATABASE=FocusGroup

# API version
API_VERSION=1.0.1

# Port
PORT=3000

# CORS
ALLOWED_ORIGINS='http://localhost:3000, localhost:3000'

# WEBHOOK
EMAIL_BUILDER_URL='http://127.0.0.1:3002'
API_WEBHOOK_SECRET=supersecret123

# TOGGLE MULTI-THREAD (0,1)
CLUSTER_MODE=0

# EMAIL CONFIGURATION
OVERRIDE_EMAIL_ADDRESS=''
EMAIL_FROM='<EMAIL>'
SMTP_HOST='smtp.gmail.com'
SMTP_PORT=465
# 0 = disabled, 1 = enabled
SMTP_SECURE=1
SMTP_NAME=''
SMTP_USERNAME='<EMAIL>'
SMTP_PASSWORD='12345'
# 0 = disabled, 1 = enabled. If set and smtp_secure is false, mailer tries to use STARTTLS
SMTP_REQUIRE_TLS=0

# RECAPTCHA CONFIGURATION
RECAPTCHA_URL='https://www.recaptcha.net/recaptcha/api/siteverify'
RECAPTCHA_SECRET='6LcQSrwUAAAAABHhm1n6mDO27cy2hqBFcnU5HJxC'

# SFTP
SFTP_PASSWORD_DECRYPT_KEY=2lrRKQfHzxACgOG0D1hDDAK3a3b98zfe
SFTP_TEMP_FOLDER=/Users/<USER>/Desktop/Document/projects/focusfeedback/backend/tmp

# OPENAI
OPENAI_API_KEY=
OPENAI_FORWARD='https://api-bsh.focusfeedback.nl/2/webhook/openai/create-chat-completion'
OPENAI_NEW_MODEL=gpt-4-turbo
OPENAI_OLD_MODEL=gpt-3.5-turbo-0125
OPENAI_CLASSIFICATION_MIN_LETTER=1

# Configurations  {0,1}
DISABLE_LOGGER=0
ENABLE_GLOBAL_COMPANY=1

# PRODUCT DATA FOR BSH
PRODUCT_DATA_URL=https://cons.productdata.api-pim.bsh-digital.com
PRODUCT_DATA_API_KEY=
