# Gympare API Testing Guide

This guide provides instructions for testing the Gympare external API, including the newly implemented link building functionality.

## 🎯 Overview

The Gympare API provides club information with reviews and generates StoryWidget links. The link building functionality creates URLs in the format:
```
https://feedback4sports.com/verhalen/?t=<base64_object>
```

Where the base64 object contains:
```json
{
  "token": "<retrieved_token>",
  "server": "https://servoy4.welcomeccs.nl/FF",
  "project": "",
  "companyType": 2
}
```

## 🧪 Testing Methods

### 1. Unit Tests (Recommended)

Run the comprehensive unit tests that cover all functionality:

```bash
# Run all Gympare service tests
npm run test -- --testPathPatterns=gympare.service.spec.ts

# Run with verbose output
npm run test -- --testPathPatterns=gympare.service.spec.ts --verbose

# Run all tests in watch mode
npm run test:watch
```

**Test Coverage:**
- ✅ Service instantiation
- ✅ Empty clubs array handling
- ✅ Clubs with reviews data
- ✅ StoryWidget token retrieval
- ✅ Link building with valid token
- ✅ Link building with no token (empty string)
- ✅ Query builder parameter validation

### 2. Node.js Test Script

Use the Node.js test script for integration testing:

```bash
# Install axios if not already installed
npm install axios

# Update the token in test-gympare-api.js
# Replace 'your-test-token-here' with a valid token

# Run the test
node test-gympare-api.js
```

**Features:**
- ✅ API endpoint testing
- ✅ Response structure validation
- ✅ Link format verification
- ✅ Base64 decoding and validation
- ✅ Error handling

### 3. cURL Test Script (Linux/Mac)

Use the bash script for command-line testing:

```bash
# Make the script executable
chmod +x test-gympare-curl.sh

# Update the token in test-gympare-curl.sh
# Replace 'YOUR_TOKEN_HERE' with a valid token

# Run the test
./test-gympare-curl.sh
```

**Features:**
- ✅ Basic API call
- ✅ Response structure validation (with jq)
- ✅ Link decoding and validation
- ✅ HTTP status and timing information

### 4. PowerShell Test Script (Windows)

Use the PowerShell script for Windows testing:

```powershell
# Update the token in test-gympare-powershell.ps1
# Replace 'YOUR_TOKEN_HERE' with a valid token

# Run the test
.\test-gympare-powershell.ps1
```

**Features:**
- ✅ API endpoint testing
- ✅ Response structure validation
- ✅ Link format verification
- ✅ Base64 decoding and validation
- ✅ Colored output for better readability

## 🔑 Token Requirements

To test the API, you need a valid token that:

1. **Has access to the Gympare API** - The token must be associated with a company that has Gympare API rights
2. **Is not expired** - The token must have a valid expiry date
3. **Has proper permissions** - The token should have the necessary privileges

### Getting a Test Token

1. **For StoryWidget functionality**: The API will automatically retrieve a token from the `sec_user_ws_token` table where:
   - `sec_company_id` matches the current company
   - `token_privilege` equals `TokenPrivilege.StoryWidget` (value 4)
   - `expiry_date` is greater than the current date

2. **For API access**: Use a token that has been validated by the `GympareAuthGuard`

## 📊 Expected Response Structure

```json
{
  "clubs": [
    {
      "id": "string",
      "name": "string",
      "score": "number",
      "based_on": "number",
      "link": "string",
      "reviews": [
        {
          "id": "number",
          "improvements": "string",
          "strengths": "string",
          "postedOn": "string",
          "score": "number"
        }
      ]
    }
  ]
}
```

## 🔗 Link Validation

The generated links should:

1. **Start with the correct URL**: `https://feedback4sports.com/verhalen/?t=`
2. **Contain valid base64 data**: The part after `?t=` should be valid base64
3. **Decode to valid JSON**: The decoded base64 should be valid JSON with the expected structure
4. **Contain required fields**: `token`, `server`, `project`, `companyType`

## 🚨 Common Issues

### 1. "No access to gympare API"
- **Cause**: The company associated with the token doesn't have Gympare API rights
- **Solution**: Ensure the company has the necessary API rights in the database

### 2. "Invalid token"
- **Cause**: The token is expired, invalid, or doesn't exist
- **Solution**: Generate a new valid token

### 3. Empty link field
- **Cause**: No valid StoryWidget token found for the company
- **Solution**: Create a StoryWidget token for the company in the `sec_user_ws_token` table

### 4. "Cannot destructure property 'company'"
- **Cause**: The CLS context doesn't contain user/company data
- **Solution**: Ensure the request is properly authenticated and the CLS context is set

## 🛠️ Development Testing

For development purposes, you can:

1. **Mock the database responses** in unit tests
2. **Use test tokens** with known values
3. **Test edge cases** like missing tokens, expired tokens, etc.
4. **Validate the base64 encoding/decoding** process

## 📝 Test Results

When all tests pass, you should see:

```
✅ API call successful!
✅ Response contains clubs array
✅ Link format is correct
✅ Base64 decoded successfully
✅ Link object structure validated
```

## 🔄 Continuous Integration

The unit tests are designed to run in CI/CD pipelines and provide:

- **Fast execution** (under 30 seconds)
- **Comprehensive coverage** of all functionality
- **Mocked dependencies** for reliable testing
- **Clear error messages** for debugging

## 📚 Additional Resources

- [Gympare API Documentation](./docs/gympare-api.md)
- [Database Schema](./ddl/gympare-setup.ddl)
- [Service Implementation](./src/modules/external/gympare/gympare.service.ts)
- [Controller Implementation](./src/modules/external/gympare/gympare.controller.ts)
