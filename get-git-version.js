const { join } = require('path');
const fs = require('fs').promises;

require('dotenv').config(join(__dirname, '.env'));

const getGitId = async () => {
  const gitId = await fs.readFile('.git/HEAD', 'utf8');
  if (gitId.indexOf(':') === -1) {
    return gitId;
  }
  const refPath = '.git/' + gitId.substring(5).trim();
  return (await fs.readFile(refPath, 'utf8')).trim();
};
getGitId().then((id) => {
  fs.writeFile(
    './dist/app-version.json',
    JSON.stringify({
      apiVersion: '4.27.3',//process.env.API_VERSION,
      hash: id.substring(0,7), // return short hash for the version number
    }),
  );
});
