import * as dotenv from 'dotenv';
import { join } from 'path';
import { z } from 'zod';

dotenv.config({
  path: join(__dirname, '../.env'),
});

const envSchema = z.object({
  PORT: z.coerce.number().default(3000),
  APP_PATH: z.string().nonempty(),
  API_VERSION: z.string().nonempty(),
  NODE_ENV: z.string().default('development'),

  // Database
  POSTGRES_HOST: z.string().nonempty(),
  POSTGRES_POST: z.coerce.number().min(1),
  POSTGRES_USER: z.string().nonempty(),
  POSTGRES_PASSWORD: z.string().nonempty(),
  POSTGRES_DATABASE: z.string().nonempty(),

  ALLOWED_ORIGINS: z.string().nonempty(),
  CLUSTER_MODE: z.enum(['0', '1']).optional(),

  // SMTP CONFIGURATION
  OVERRIDE_EMAIL_ADDRESS: z.string().nullable().optional(),
  OVERRIDE_CC_LIST: z.string().nullable().optional(),
  EMAIL_FROM: z.string().nullable().optional(),
  SMTP_HOST: z.string().nonempty(),
  SMTP_PORT: z.coerce.number().min(1),
  SMTP_NAME: z.string().nonempty(),
  SMTP_USERNAME: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
  SMTP_SECURE: z.enum(['0', '1']).optional(),

  // RECAPTCHA CONFIGURATION
  RECAPTCHA_URL: z.string().optional().nullable(),
  RECAPTCHA_SECRET: z.string().optional().nullable(),

  // SFTP CONFIGURATION
  SFTP_PASSWORD_DECRYPT_KEY: z.string().optional().nullable(),
  SFTP_TEMP_FOLDER: z.string().optional().nullable(),

  // OPENAI
  OPENAI_API_KEY: z.string().optional().nullable(),
  OPENAI_FORWARD: z.string().optional().nullable(),
  OPENAI_NEW_MODEL: z.string().optional().nullable().default('gpt-4o'),
  OPENAI_OLD_MODEL: z
    .string()
    .optional()
    .nullable()
    .default('gpt-3.5-turbo-0125'),
  OPENAI_CLASSIFICATION_MIN_LETTER: z.coerce.number().default(1),

  // Webhook
  EMAIL_BUILDER_URL: z.string().nonempty(),
  API_WEBHOOK_SECRET: z.string().nonempty(),

  // Configurations
  DISABLE_LOGGER: z.enum(['0', '1']).optional().nullable(),
  ENABLE_GLOBAL_COMPANY: z.enum(['0', '1']).optional().nullable(),

  // PRODUCT DATA FOR BSH
  PRODUCT_DATA_URL: z.string().optional(),
  PRODUCT_DATA_API_KEY: z.string().optional(),
});

// Define env
let env: z.infer<typeof envSchema>;
try {
  env = envSchema.parse(process.env);
} catch (error) {
  console.error(
    'ENVIRONMENT VARIABLE MISSING: ',
    error.errors.map((e) => e.path.join(', ')).join(', '),
  );
  process.exit(1);
}

export { env };
