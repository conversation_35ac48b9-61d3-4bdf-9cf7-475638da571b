<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style type="text/css" media="print">
        * {
            font-family: Poppins, "Poppins", Sans-serif;

        }

        .table_1 {
            width: 100%;
            border-collapse: collapse;
            font-style: normal;
            font-weight: normal;
        }

        table,
        th,
        td {
            font-size: 14px;
            color: rgba(0, 0, 0, .87);
        }

        th {
            font-weight: 600;
            color: #1c3858;
        }

        th,
        td {
            padding: 13px 12px;
        }

        .th_left {
            width: 33.33%;
            text-align: left;
            background: #1c3858;
            color: #fff;
        }

        .td_top {
            font-weight: 600;
            color: #1c3858;
            text-align: center;
        }


        tr:nth-child(odd) {
            background: #ecf7fb
        }

        tr:nth-child(even) {
            background: #FFF
        }


        .colorBlue {
            background: #ecf7fb !important;
        }

        .colorWhite {
            background: #FFF;
        }

        .roundedBack {
            border-radius: 50px;
            width: 60px;
            height: 60px;
            display: block;
            margin: 0 auto;
            color: #fff;
            line-height: 60px;
            font-size: 20px;
            background: #ff9222;
            font-weight: 600;
        }

        .green {
            background: #6cb549;

        }

        .orange {
            background: #ff9222;

        }

        .red {
            background: #e2231a;

        }

        @page {
            size: A4 portrait;
            margin: .4in .4in .4in .4in;
        }
    </style>

</head>

<body>
    <table cellpadding="5" cellspacing="0" class="table_1" id="headtable">
        <tdead>
            <tr>
                <td class="th_left" style="text-align:center;">Date</td>
                <td class="th_left" style="text-align:center;">Filled by</td>
                <td class="th_left" style="text-align:center;">Records</td>
            </tr>
            <tr class="colorBlue">
                <td scope="row" class="td_top">{{date}}</td>
                <td scope="row" class="td_top">{{user}}</td>
                <td scope="row" class="td_top">{{count}}</td>
            </tr>
        </tdead>
    </table>
    <br>
    {{#each consumers}}
    <table cellpadding="5" cellspacing="0" class="table_1">
        <tbody>
            <tr>
                <td class="th_left" colspan="3" style="text-align:left;">Customer Details</td>
            </tr>
            <tr>
                <td style="text-align:left;">Title</td>
                <td style="text-align:left;">{{this.salutation}}</td>
            </tr>
            <tr>
                <td style="text-align:left;">First Name</td>
                <td style="text-align:left;">{{this.firstname}}</td>
            </tr>
            <tr>
                <td style="text-align:left;">Last Name</td>
                <td style="text-align:left;">{{this.lastname}}</td>
            </tr>
            <tr>
                <td style="text-align:left;">Email Address</td>
                <td style="text-align:left;">{{this.email}}</td>
            </tr>
            <tr>
                <td style="text-align:left;">Phone 1</td>
                <td style="text-align:left;">{{this.phone1}}</td>
            </tr>
            <tr>
                <td style="text-align:left;">Phone 2</td>
                <td style="text-align:left;">{{this.phone2}}</td>
            </tr>
            {{#if this.project}}
            <tr>
                <td style="text-align:left;">Project</td>
                <td style="text-align:left;">{{this.project}}</td>
            </tr>
            {{/if}}

            {{#if this.risnumber}}
            <tr>
                <td style="text-align:left;">RIS Number</td>
                <td style="text-align:left;">{{this.risnumber}}</td>
            </tr>
            {{/if}}

            {{#if this.brand}}
            <tr>
                <td style="text-align:left;">Brand</td>
                <td style="text-align:left;">{{this.brand}}</td>
            </tr>
            {{/if}}

            {{#if this.club}}
            <tr>
                <td style="text-align:left;">Club</td>
                <td style="text-align:left;">{{this.club}}</td>
            </tr>
            {{/if}}


            <tr>
                <td style="text-align:left;">Creation Date</td>
                <td style="text-align:left;">{{this.creation_date}}</td>
            </tr>


            <tr>
                <td style="text-align:left;">Send Date</td>
                <td style="text-align:left;">{{this.send_date}}</td>
            </tr>


            <tr>
                <td style="text-align:left;">Response Received</td>
                <td style="text-align:left;">{{this.response_received}}</td>
            </tr>


            <tr>
                <td style="text-align:left;">Response Date</td>
                <td style="text-align:left;">{{this.response_date}}</td>
            </tr>


            <tr>
                <td style="text-align:left;">NPS</td>
                <td style="text-align:left;">{{this.nps}}</td>
            </tr>

            <tr>
                <td style="text-align:left;">Comment 1</td>
                <td style="text-align:left;">{{this.comment_1}}</td>
            </tr>

            <tr>
                <td style="text-align:left;">Comment 2</td>
                <td style="text-align:left;">{{this.comment_2}}</td>
            </tr>

        </tbody>
    </table>
    <br>
    {{/each}}
</body>

</html>