<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Share Feedback</title>

    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/material-icons@1.13.12/iconfont/material-icons.min.css"
    />
    <link
      href="
https://cdn.jsdelivr.net/npm/@fontsource/poppins@5.0.12/index.min.css
"
      rel="stylesheet"
    />
    <style>
      html,
      body,
      div,
      span,
      applet,
      object,
      iframe,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p,
      blockquote,
      pre,
      a,
      abbr,
      acronym,
      address,
      big,
      cite,
      code,
      del,
      dfn,
      em,
      img,
      ins,
      kbd,
      q,
      s,
      samp,
      small,
      strike,
      strong,
      sub,
      sup,
      tt,
      var,
      b,
      u,
      i,
      center,
      dl,
      dt,
      dd,
      ol,
      ul,
      li,
      fieldset,
      form,
      label,
      legend,
      table,
      caption,
      tbody,
      tfoot,
      thead,
      tr,
      th,
      td,
      article,
      aside,
      canvas,
      details,
      embed,
      figure,
      figcaption,
      footer,
      header,
      hgroup,
      menu,
      nav,
      output,
      ruby,
      section,
      summary,
      time,
      mark,
      audio,
      video {
        margin: 0;
        padding: 0;
        border: 0;
        font-size: 100%;
        font: inherit;
        vertical-align: baseline;
      }
      /* HTML5 display-role reset for older browsers */
      article,
      aside,
      details,
      figcaption,
      figure,
      footer,
      header,
      hgroup,
      menu,
      nav,
      section {
        display: block;
      }
      body {
        line-height: 1;
      }
      ol,
      ul {
        list-style: none;
      }

      body {
        font-family: 'poppins';
        padding: 0;
        margin: 0;
      }
      .main {
        display: block;
        width: 400px;
        height: fit-content;
        padding: 20px 15px;
        padding-bottom: 10px;
        background: url({{bgUrl}});
        background-repeat: no-repeat;
        background-size: cover;
      }

      .comment-placeholder {
        background-color: rgba(255, 255, 255, 0.92);
        padding: 18px;
        padding-bottom: 30px;
        /* margin-bottom: 80px; */
        min-height: 150px;
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        gap: 10px;
        position: relative;
      }

      .comment-placeholder::after {
        content: '';
        position: absolute;
        bottom: -33px;
        left: 40px;
        width: 0px;
        height: 0px;
        border-style: solid;
        border-width: 0 40px 26px 0px;
        border-color: transparent transparent rgba(255, 255, 255, 0.92)
          transparent;
        transform: rotate(90deg);
      }

      .head {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 10px;
        font-weight: bold;
        color: #374964;
      }

      .footer {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 25px;
      }

      .footer-logo img {
        width: 180px;
      }

      .feedbacks {
        display: flex;
        flex-direction: column;
        gap: 30px;
        padding: 18px 12px;
        padding-right: 0px;
      }

      .feedbacks .item {
        display: flex;
        justify-content: flex-start;
        align-items: start;
        gap: 10px;
        color: #374f6a;
        line-height: 1.5;
      }
      .feedbacks .feedback-content {
        margin-top: -4px;
        overflow: hidden;
        display: -webkit-box;
        /* -webkit-line-clamp: 12; */
        /* -webkit-box-orient: vertical; */
        word-break: break-word;
      }

      .circle {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        font-weight: bold;
        width: 60px;
        height: 60px;
        color: white;
        background-color: gray;
      }
      .head .circle {
        font-size: 25px;
        font-weight: bold;
      }

      .feedbacks .circle {
        font-size: 5px;
        width: 15px;
        height: 15px;
      }

      .feedbacks .circle .icon {
        font-size: 12px;
      }

      .circle.green {
        background-color: #6cb549;
      }
      .circle.red {
        background-color: #e22219;
      }
      .circle.yellow {
        background-color: #ff9222;
      }

      .icon {
        font-size: 24px; /* Adjust as needed */
        font-weight: normal;
        display: inline-block;
        line-height: 1;
      }
      /* .icon::before {
      font-style: normal;
      letter-spacing: normal;
      text-transform: none;
      white-space: nowrap;
      word-wrap: normal;
      direction: ltr;
    } */
    </style>
  </head>
  <body>
    <div class="main">
      <div class="comment-placeholder">
        <div class="head">
          <div
            class="circle {{#if (gt score 8)}} green {{else if (gt score 6)}} yellow {{else}} red {{/if}}"
          >
            {{score}}
          </div>
          {{title}}
        </div>

        <div class="feedbacks">
          {{#if strengths}}
          <div class="item">
            <div>
              <div class="circle green">
                <i class="material-icons icon plus">add</i>
              </div>
            </div>
            <div class="feedback-content">{{strengths}}</div>
          </div>
          {{/if}} {{#if improvements}}
          <div class="item">
            <div>
              <div class="circle red">
                <i class="material-icons icon minus">remove</i>
              </div>
            </div>
            <div class="feedback-content">{{improvements}}</div>
          </div>
          {{/if}}
        </div>
      </div>

      <div class="footer">
        <div class="footer-logo">
          <img src="{{logoUrl}}" alt="logo" />
        </div>
      </div>
    </div>
  </body>
</html>
