import { Injectable } from '@nestjs/common';
import { InjectDataSource, InjectEntityManager } from '@nestjs/typeorm';
import { DataSource, EntityManager } from 'typeorm';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { LogManagementEntity } from './modules/database/entities/log-management.entity';

@Injectable()
export class AppService {
  constructor(
    private logger: ModuleLogger,
    @InjectDataSource() private dataSource: DataSource,
    @InjectEntityManager() private entityManager: EntityManager,
  ) {}
  async getHello() {
    return { data: 'Hello World!' };
  }

  async syncLoggers() {
    if (ModuleLogger.modules) return;
    let logManagerRepo = this.entityManager.getRepository(LogManagementEntity);
    const modules = await logManagerRepo.find({
      where: { application_part: ModuleLogger.applicationPart },
    });
    ModuleLogger.modules = modules;
    for (let logger of ModuleLogger.loggerNames) {
      if (!modules.some((m) => m.logger_name === logger)) {
        this.logger.createLogManagement(logger);
      }
    }
  }

  async checkDatabase() {
    let list: { table_name: string; primary_column: string }[] = await this
      .dataSource.query(`
      SELECT
      t.table_name,
      c.column_name as primary_column
    FROM
      information_schema.tables t
      INNER JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
    WHERE
      t.table_schema = 'public' AND
      t.table_type = 'BASE TABLE' AND
      c.column_default LIKE 'nextval%' AND
      c.udt_name = 'int4'    
    `);
    let entities = this.dataSource.entityMetadatas;
    for (let entity of entities) {
      for (let column of entity.primaryColumns) {
        if (
          column.generationStrategy == 'increment' &&
          column.isGenerated == true
        ) {
          let table = list.find(
            (table) => table.table_name == entity.tableName,
          );
          if (!table) {
            throw new Error(
              `(⚠️) ${entity.tableName}.${column.databaseName} Has no any sequence or auto incremented key, \n Please add and restart application`,
            );
            // console.log(
            //   `(⚠️) ADDING PRIMARY GENERATED COLUMN FOR ${entity.tableName}.${column.databaseName}`,
            // );
            // let seqName = `nest_${entity.tableName}_${column.databaseName}`;
            // await this.dataSource
            //   .query(
            //     `CREATE SEQUENCE ${seqName} OWNED BY public.${entity.tableName}.${column.databaseName}`,
            //   )
            //   .catch(() => false);
            // await this.dataSource
            //   .query(
            //     `SELECT SETVAL('${seqName}', (select max(${column.databaseName}) from public.${entity.tableName}) + 1, false)`,
            //   )
            //   .catch(() => false);
            // await this.dataSource
            //   .query(
            //     `ALTER TABLE public.${entity.tableName} ALTER COLUMN ${column.databaseName} SET DEFAULT nextval('${seqName}')`,
            //   )
            //   .catch((e) => {
            //     console.log(e);
            //     return e;
            //   });
            // await this.dataSource
            //   .query(
            //     `SELECT SETVAL('${seqName}', (select max(${column.databaseName}) from public.${entity.tableName}), false)`,
            //   )
            //   .catch(() => false);
          }
        }
      }
    }
  }
}
