import { I18nContext } from 'nestjs-i18n';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nEntity } from '@entities';
import { In, IsNull, Repository } from 'typeorm';

@Injectable()
export class TranslateService {
  constructor(
    @InjectRepository(I18nEntity)
    private readonly i18nRepo: Repository<I18nEntity>,
  ) {}

  public async db(key: string): Promise<{ key: string; value: string }> {
    let list = await this.i18nRepo.find({
      where: [
        {
          message_key: key,
          message_language: this.lang,
        },
        {
          message_key: key,
          message_language: IsNull(),
        },
      ],
      select: ['message_value', 'message_key', 'message_language'],
    });

    let item = list.find((i) => i.message_language?.length) || list[0];
    return {
      key,
      value: item?.message_value || key,
    };
  }

  public async dbs(keys: string[]): Promise<{ key: string; value: string }[]> {
    let list = await this.i18nRepo.find({
      where: [
        {
          message_key: In(keys),
          message_language: this.lang,
        },
        {
          message_key: In(keys),
          message_language: IsNull(),
        },
      ],
      select: ['message_value', 'message_key', 'message_language'],
    });

    list = list.map((item) => ({
      ...item,
      message_language: !!item.message_language?.length,
    })) as any;

    list = list.filter((item) => {
      return (
        item.message_language ||
        (!item.message_language &&
          !list.some(
            (subitem) =>
              subitem.message_key === item.message_key &&
              subitem.message_language,
          ))
      );
    });

    return list.map((item) => ({
      key: item.message_key,
      value: item.message_value,
    }));
  }

  public get lang() {
    return I18nContext?.current()?.lang || 'en';
  }

  public async getLanguageList(): Promise<Record<string, string>> {
      const languages = await this.i18nRepo.query(`SELECT lang_369_2, lang_369_1 FROM languages`);

      // Convert array of objects to a single object with lang_369_2 as keys and lang_369_1 as values
      return languages.reduce((result, language) => {
        if (language.lang_369_2 && language.lang_369_1) {
          result[language.lang_369_2] = language.lang_369_1;
        }
        return result;
      }, {});
    }
}
