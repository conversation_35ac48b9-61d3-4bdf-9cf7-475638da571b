import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PresidioService } from './presidio.service';
import { FeedbackPropertiesModule } from '@/modules/common/feedback-properties/feedback-properties.module';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';

@Module({
  imports: [
    HttpModule,
    FeedbackPropertiesModule,
    ModuleLoggerModule.register('presidio'),
  ],
  providers: [PresidioService],
  exports: [PresidioService],
})
export class PresidioModule {}
