import { QueueService } from '@libs/queue';
import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { PresidioService } from './presidio.service';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';
import { AnswerItem } from '@/modules/database/entities/surv-surveyanswer.entity';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { RedactedAnswer } from '@/modules/database/entities/redacted-answers.entity';

@Injectable()
export class PresidioProcessor implements OnModuleInit, OnModuleDestroy {
  private redactedAnswerRepo: Repository<RedactedAnswer>;

  constructor(
    private readonly queue: QueueService,
    private readonly presidioService: PresidioService,
    private readonly logger: ModuleLogger,
    @InjectDataSource() private dataSource: DataSource,
  ) {
    this.redactedAnswerRepo = this.dataSource.getRepository(RedactedAnswer);
  }

  onModuleInit() {
    this.queue.registerQueue('presidio-answer-item', {
      process: this.process.bind(this),
    });
  }

  onModuleDestroy() {
    this.queue.clearQueue('presidio-answer-item');
  }

  async process(
    companyId: number,
    answerItem: AnswerItem,
    language: string,
    logReference?: any,
  ) {
    this.logger.debug('Presidio processs started', { logReference });

    const response = await this.presidioService.processAnswer(
      answerItem.value,
      language,
    );

    if (response?.text) {
      const redactedAnswer = this.redactedAnswerRepo.create({
        surv_surveyansweritem_id: answerItem.id,
        return_value: response.text,
        sec_company_id: companyId,
      });
      await redactedAnswer.save();
    }

    this.logger.debug('Presidio processs finished', { logReference, response });
    return response;
  }
}
