import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

export class SwaggerService {
  static apply(app: INestApplication) {
    this.setupMainDocs(app);
    this.setupPartnerDocs(app);
  }

  private static setupMainDocs(app: INestApplication) {
    const swaggerConfig = new DocumentBuilder()
      .setTitle('Focus Feedback backend')
      .setDescription('Focus Feedback backend API')
      .setVersion(`1.0.0`)
      .addGlobalParameters({ name: 'lang', in: 'query', required: false })
      .addBearerAuth()
      .addBasicAuth({ type: 'apiKey', in: 'header', name: 'x-webhook-key' })
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig, {
      deepScanRoutes: true,
      ignoreGlobalPrefix: false,
    });

    // Filter out external routes
    document.paths = Object.fromEntries(
      Object.entries(document.paths).filter(([path]) => !path.includes('/external/'))
    );

    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
        persistAuthorization: true,
      },
    });
  }

  private static setupPartnerDocs(app: INestApplication) {
    const partnerConfig = new DocumentBuilder()
      .setTitle('Focus Feedback Partner API')
      .setDescription('API documentation for Focus Feedback partners')
      .setVersion(`1.0.0`)
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, partnerConfig, {
      deepScanRoutes: true,
    });

    // Filter only external routes
    document.paths = Object.fromEntries(
      Object.entries(document.paths).filter(([path]) => path.includes('/external/'))
    );

    SwaggerModule.setup('partner-api', app, document, {
      swaggerOptions: {
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
        persistAuthorization: true,
      },
    });
  }
}