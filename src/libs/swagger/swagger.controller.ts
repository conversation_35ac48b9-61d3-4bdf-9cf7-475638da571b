import { <PERSON>, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { statSync } from 'fs';
import { join } from 'path';

@Controller()
export class SwaggerController {
  @Get('/api/docs')
  renderSwagger(@Res() res: Response) {
    const stats = statSync(join(__dirname, '../../views/swagger.hbs'));
    const version = stats.mtimeMs.toString().replace(/\D/g, '');
    return res.render('swagger', {
      version,
    });
  }

  @Get('/partner-api')
  renderPartnerSwagger(@Res() res: Response) {
    try {
      const stats = statSync(join(__dirname, '../../views/partner-swagger.hbs'));
      const version = stats.mtimeMs.toString().replace(/\D/g, '');
      return res.render('partner-swagger', {
        version,
      });
    } catch (error) {
      console.error('Partner Swagger render error:', error);
      return res.status(500).json({ error: error.message });
    }
  }
}
