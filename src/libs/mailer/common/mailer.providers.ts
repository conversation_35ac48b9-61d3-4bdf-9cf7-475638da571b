import { Provider } from '@nestjs/common';
import { createTransport, Transporter } from 'nodemailer';
import { MAILER_MODULE_OPTIONS, MAILER_TRANSPORTER } from './mailer.constants';
import { MailerModuleOptions } from './mailer.interface';

export const createMailerTransporter = (): Provider => ({
  provide: MAILER_TRANSPORTER,
  useFactory: (options: MailerModuleOptions): Transporter => {
    if (typeof options.transport === 'object' && 'sendMail' in options.transport) {
      return options.transport as Transporter;
    }
    
    return createTransport(options.transport);
  },
  inject: [MAILER_MODULE_OPTIONS],
});