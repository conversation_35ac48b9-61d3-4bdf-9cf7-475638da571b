import { Module, DynamicModule, Global } from '@nestjs/common';
import { MailerService } from './mailer.service';
import { createMailerTransporter } from './common/mailer.providers';
import { 
  MailerModuleOptions, 
  MailerAsyncModuleOptions 
} from './common/mailer.interface';
import { MAILER_MODULE_OPTIONS } from './common/mailer.constants';

@Global()
@Module({})
export class MailerModule {
  static forRoot(options: MailerModuleOptions): DynamicModule {
    return {
      module: MailerModule,
      providers: [
        {
          provide: MAILER_MODULE_OPTIONS,
          useValue: options,
        },
        createMailerTransporter(),
        MailerService,
      ],
      exports: [MailerService],
    };
  }

  static forRootAsync(options: MailerAsyncModuleOptions): DynamicModule {
    return {
      module: MailerModule,
      imports: options.imports || [],
      providers: [
        {
          provide: MAILER_MODULE_OPTIONS,
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        createMailerTransporter(),
        MailerService,
      ],
      exports: [MailerService],
    };
  }
}