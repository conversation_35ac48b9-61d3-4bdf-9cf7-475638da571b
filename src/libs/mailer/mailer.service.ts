import { Injectable, Inject, Logger } from '@nestjs/common';
import { Transporter } from 'nodemailer';
import { MAILER_TRANSPORTER, MAILER_MODULE_OPTIONS } from './common/mailer.constants';
import { 
  ISendMailOptions, 
  IMailerSendResult, 
  MailerModuleOptions 
} from './common/mailer.interface';

@Injectable()
export class MailerService {
  private readonly logger = new Logger(MailerService.name);

  constructor(
    @Inject(MAILER_TRANSPORTER)
    private readonly transporter: Transporter,
    @Inject(MAILER_MODULE_OPTIONS)
    private readonly options: MailerModuleOptions,
  ) {}

  async sendMail(mailOptions: ISendMailOptions): Promise<IMailerSendResult> {
    try {
      const mergedOptions = {
        ...this.options.defaults,
        ...mailOptions,
      };

      this.logger.log(`Sending email to: ${mergedOptions.to}`);
      
      const result = await this.transporter.sendMail(mergedOptions);
      
      this.logger.log(`Email sent successfully. MessageId: ${result.messageId}`);
      
      return {
        messageId: result.messageId,
        accepted: result.accepted || [],
        rejected: result.rejected || [],
        pending: result.pending || [],
        response: result.response,
      };
    } catch (error) {
      this.logger.error('Failed to send email', error);
      throw error;
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      this.logger.log('SMTP connection verified successfully');
      return true;
    } catch (error) {
      this.logger.error('SMTP connection verification failed', error);
      return false;
    }
  }

  getTransporter(): Transporter {
    return this.transporter;
  }
}