import { Injectable, OnModuleDestroy } from '@nestjs/common';
import * as Queue from 'better-queue';
import { QueueConfig } from './queue.interfaces';

@Injectable()
export class QueueService implements OnModuleDestroy {
  private queues: Map<string, Queue> = new Map();

  onModuleDestroy() {
    for (const queue of this.queues.values()) {
      queue.destroy(() => {});
    }
    this.queues.clear();
  }

  registerQueue<T = any, K = any>(
    queueName: string,
    queueConfig: QueueConfig<T, K>,
  ): void {
    if (this.queues.has(queueName)) {
      throw new Error(`Queue with name '${queueName}' already exists.`);
    }

    const queue = new Queue(queueConfig.process, queueConfig);

    if (queueConfig.events) {
      for (const [event, handler] of Object.entries(queueConfig.events)) {
        queue.on(event as Queue.QueueEvent, handler);
      }
    }

    this.queues.set(queueName, queue);
  }

  getQueue(queueName: string): Queue | undefined {
    return this.queues.get(queueName);
  }

  addToQueue<T = any>(queueName: string, data: T): Promise<any> {
    const queue = this.getQueue(queueName);

    if (!queue) {
      throw new Error(`Queue with name '${queueName}' does not exist.`);
    }

    return new Promise((resolve, reject) => {
      queue.push(data, (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  pauseQueue(queueName: string): void {
    const queue = this.getQueue(queueName);

    if (!queue) {
      throw new Error(`Queue with name '${queueName}' does not exist.`);
    }

    queue.pause();
  }

  resumeQueue(queueName: string): void {
    const queue = this.getQueue(queueName);

    if (!queue) {
      throw new Error(`Queue with name '${queueName}' does not exist.`);
    }

    queue.resume();
  }

  clearQueue(queueName: string): void {
    const queue = this.getQueue(queueName);

    if (!queue) {
      throw new Error(`Queue with name '${queueName}' does not exist.`);
    }

    queue.destroy(() => {});
    this.queues.delete(queueName);
  }
}
