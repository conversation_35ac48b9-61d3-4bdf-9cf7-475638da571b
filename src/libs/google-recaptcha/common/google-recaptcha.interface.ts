import { Request } from 'express';
import { GoogleRecaptchaNetwork } from './google-recaptcha.enum';

export interface GoogleRecaptchaModuleOptions {
  secretKey: string;
  response: string | ((req: Request) => string);
  skipIf?: boolean | ((req: Request) => boolean);
  network?: GoogleRecaptchaNetwork;
  threshold?: number;
  action?: string;
}

export interface GoogleRecaptchaAsyncModuleOptions {
  useFactory: (
    ...args: any[]
  ) => Promise<GoogleRecaptchaModuleOptions> | GoogleRecaptchaModuleOptions;
  inject?: any[];
}

export interface GoogleRecaptchaValidationOptions {
  response: string;
  remoteIp?: string;
}

export interface GoogleRecaptchaValidationResult {
  success: boolean;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
  score?: number;
  action?: string;
}
