import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Inject,
  BadRequestException,
} from '@nestjs/common';
import { Request } from 'express';
import { GoogleRecaptchaValidator } from '../google-recaptcha-validator.service';
import { GOOGLE_RECAPTCHA_MODULE_OPTIONS } from './google-recaptcha.constants';
import { GoogleRecaptchaModuleOptions } from './google-recaptcha.interface';

@Injectable()
export class GoogleRecaptchaGuard implements CanActivate {
  constructor(
    private readonly recaptchaValidator: GoogleRecaptchaValidator,
    @Inject(GOOGLE_RECAPTCHA_MODULE_OPTIONS)
    private readonly options: GoogleRecaptchaModuleOptions,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    if (this.shouldSkip(request)) {
      return true;
    }

    const response = this.extractResponse(request);
    if (!response) {
      throw new BadRequestException('reCAPTCHA response is required');
    }

    const result = await this.recaptchaValidator.validate({
      response,
      remoteIp: this.getClientIp(request),
    });

    if (!result.success) {
      throw new BadRequestException('reCAPTCHA validation failed');
    }

    return true;
  }

  private shouldSkip(request: Request): boolean {
    if (typeof this.options.skipIf === 'boolean') {
      return this.options.skipIf;
    }

    if (typeof this.options.skipIf === 'function') {
      return this.options.skipIf(request);
    }

    return false;
  }

  private extractResponse(request: Request): string | undefined {
    if (typeof this.options.response === 'string') {
      return this.options.response;
    }

    if (typeof this.options.response === 'function') {
      return this.options.response(request);
    }

    return undefined;
  }

  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      ''
    );
  }
}
