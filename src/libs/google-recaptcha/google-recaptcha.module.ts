import { Module, DynamicModule, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { GoogleRecaptchaValidator } from './google-recaptcha-validator.service';
import { GoogleRecaptchaGuard } from './common/google-recaptcha.guard';
import {
  GoogleRecaptchaModuleOptions,
  GoogleRecaptchaAsyncModuleOptions,
} from './common/google-recaptcha.interface';
import { GOOGLE_RECAPTCHA_MODULE_OPTIONS } from './common/google-recaptcha.constants';

@Global()
@Module({})
export class GoogleRecaptchaModule {
  static forRoot(options: GoogleRecaptchaModuleOptions): DynamicModule {
    return {
      module: GoogleRecaptchaModule,
      imports: [HttpModule],
      providers: [
        {
          provide: GOOGLE_RECAPTCHA_MODULE_OPTIONS,
          useValue: options,
        },
        GoogleRecaptchaValidator,
        GoogleRecaptchaGuard,
      ],
      exports: [GoogleRecaptchaValidator, GoogleRecaptchaGuard],
    };
  }

  static forRootAsync(
    options: GoogleRecaptchaAsyncModuleOptions,
  ): DynamicModule {
    return {
      module: GoogleRecaptchaModule,
      imports: [HttpModule],
      providers: [
        {
          provide: GOOGLE_RECAPTCHA_MODULE_OPTIONS,
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        GoogleRecaptchaValidator,
        GoogleRecaptchaGuard,
      ],
      exports: [GoogleRecaptchaValidator, GoogleRecaptchaGuard],
    };
  }
}
