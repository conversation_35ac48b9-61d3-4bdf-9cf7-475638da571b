import { Injectable, Inject, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { GOOGLE_RECAPTCHA_MODULE_OPTIONS } from './common/google-recaptcha.constants';
import {
  GoogleRecaptchaModuleOptions,
  GoogleRecaptchaValidationOptions,
  GoogleRecaptchaValidationResult,
} from './common/google-recaptcha.interface';
import { GoogleRecaptchaNetwork } from './common/google-recaptcha.enum';

@Injectable()
export class GoogleRecaptchaValidator {
  private readonly logger = new Logger(GoogleRecaptchaValidator.name);

  constructor(
    @Inject(GOOGLE_RECAPTCHA_MODULE_OPTIONS)
    private readonly options: GoogleRecaptchaModuleOptions,
    private readonly httpService: HttpService,
  ) {}

  async validate(
    validationOptions: GoogleRecaptchaValidationOptions,
  ): Promise<GoogleRecaptchaValidationResult> {
    try {
      const url = this.options.network || GoogleRecaptchaNetwork.Recaptcha;

      const params = new URLSearchParams();
      params.append('secret', this.options.secretKey);
      params.append('response', validationOptions.response);

      if (validationOptions.remoteIp) {
        params.append('remoteip', validationOptions.remoteIp);
      }

      const response = await firstValueFrom(
        this.httpService.post<GoogleRecaptchaValidationResult>(url, params, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );

      const result = response.data;

      if (this.options.threshold && result.score !== undefined) {
        result.success =
          result.success && result.score >= this.options.threshold;
      }

      if (this.options.action && result.action !== this.options.action) {
        result.success = false;
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to validate reCAPTCHA', error);
      return {
        success: false,
        'error-codes': ['request-failed'],
      };
    }
  }
}
