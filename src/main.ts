import { HttpException, HttpStatus, VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import { json, urlencoded } from 'express';
import { AppClusterService } from './app-cluster.service';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './shared/filters/global-exception.filter';
import { env } from './app.env';
import { join } from 'path';

import { EventsLogger } from '@lib/logger/handlers/events.db-logger';
import { AppLogger } from '@lib/logger/handlers/app.console-logger';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: new AppLogger(),
  });

  app.use(json({ limit: '25mb' }));
  app.use(urlencoded({ extended: true, limit: '25mb' }));

  if (
    process.env.DISABLE_LOGGER !== 'true' &&
    process.env.DISABLE_LOGGER !== '1'
  ) {
    const logger = await app.resolve(EventsLogger);
    app.useLogger(logger);
  }

  app.useGlobalFilters(new GlobalExceptionFilter());
  app.enableVersioning({
    type: VersioningType.URI,
    prefix: false,
  });

  const swaggerConfig = new DocumentBuilder()
    .setTitle('Focus Feedback backend')
    .setDescription('Focus Feedback backend API')
    .setVersion('1.0')
    .addBearerAuth()
    .addBasicAuth({ type: 'apiKey', in: 'header', name: 'x-webhook-key' })
    .build();

  const swaggerDocument = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('apiDocs', app, swaggerDocument, {
    swaggerOptions: {
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  let whitelist = (env.ALLOWED_ORIGINS || '')
    .replace(' ', ',')
    .split(',')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);

  app.enableCors({
    // origin: process.env.ALLOWED_ORIGINS,
    origin: function (origin, callback) {
      if (!origin || whitelist.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        callback(
          new HttpException('Not allowed by CORS', HttpStatus.BAD_REQUEST),
        );
      }
    },
    credentials: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
  });

  await app.listen(env.PORT);
}

if (process.env.CLUSTER_MODE == '1') {
  AppClusterService.clusterize(bootstrap);
} else {
  bootstrap();
}
