import { HttpException, HttpStatus, VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { json, urlencoded } from 'express';
import { AppClusterService } from './app-cluster.service';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './shared/filters/global-exception.filter';
import { env } from './app.env';
import { join } from 'path';

import { EventsLogger } from '@lib/logger/handlers/events.db-logger';
import { AppLogger } from '@lib/logger/handlers/app.console-logger';
import { SwaggerService } from './libs/swagger';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: new AppLogger(),
  });

  app.use(json({ limit: '25mb' }));
  app.use(urlencoded({ extended: true, limit: '25mb' }));

  if (
    process.env.DISABLE_LOGGER !== 'true' &&
    process.env.DISABLE_LOGGER !== '1'
  ) {
    const logger = await app.resolve(EventsLogger);
    app.useLogger(logger);
  }

  app.useGlobalFilters(new GlobalExceptionFilter());

  app.enableVersioning({
    type: VersioningType.URI,
    prefix: false,
  });

  app.setBaseViewsDir(join(__dirname, 'views'));
  app.setViewEngine('hbs');

  SwaggerService.apply(app);

  let whitelist = (env.ALLOWED_ORIGINS || '')
    .replace(' ', ',')
    .split(',')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);

  // Simplified CORS configuration - allow all origins if ALLOWED_ORIGINS is not properly configured
  if (whitelist.length === 0 || whitelist.includes('*')) {
    // Allow all origins
    app.enableCors({
      origin: true,
      credentials: true,
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
      preflightContinue: false,
      optionsSuccessStatus: 204,
    });
  } else {
    // Use whitelist-based CORS
    app.enableCors({
      origin: function (origin, callback) {
        // Always allow requests with no origin (Postman, mobile apps, curl, etc.)
        if (!origin) {
          callback(null, true);
          return;
        }

        // Allow if origin is in whitelist
        if (whitelist.indexOf(origin) !== -1) {
          callback(null, true);
          return;
        }

        // For production debugging: log rejected origins
        console.log(`CORS: Rejecting origin: ${origin}. Allowed origins: ${whitelist.join(', ')}`);

        // Reject all other origins
        callback(
          new HttpException('Not allowed by CORS', HttpStatus.BAD_REQUEST),
        );
      },
      credentials: true,
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
      preflightContinue: false,
      optionsSuccessStatus: 204,
    });
  }

  await app.listen(env.PORT);
}

if (process.env.CLUSTER_MODE == '1') {
  AppClusterService.clusterize(bootstrap);
} else {
  bootstrap();
}
