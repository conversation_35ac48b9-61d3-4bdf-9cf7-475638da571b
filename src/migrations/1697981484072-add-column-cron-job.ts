import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class addColumnCronJob1697981484072 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'cron_jobs',
      new TableColumn({
        name: 'lastCall',
        type: 'timestamp',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('cron_jobs', 'lastCall');
  }
}
