import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class createUserKeys1676898196325 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'sec_user_keys',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'sec_user_id',
            type: 'int',
          },
          {
            name: 'key_id',
            type: 'int',
          },
          {
            name: 'sec_company_id',
            type: 'int',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('sec_user_keys');
  }
}
