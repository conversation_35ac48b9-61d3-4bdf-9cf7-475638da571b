import { MigrationInterface, QueryRunner, Table, TableColumn } from 'typeorm';

export class addSurveyAnswerAi1683892846108 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'surv_surveyanswer_ai',
        columns: [
          {
            name: 'surv_surveyanswer_ai_id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'answer_id',
            type: 'int',
          },
          {
            name: 'response_confidence',
            type: 'double precision',
          },
          {
            name: 'response_justification',
            type: 'text',
          },
          {
            name: 'prompt_tokens',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'completion_tokens',
            type: 'int',
          },
          {
            name: 'total_tokens',
            type: 'int',
          },
          {
            name: 'response_review',
            type: 'int',
            default: 0,
          },
        ],
      }),
      true,
    );

    const hasColumn = await queryRunner.hasColumn(
      'surv_surveyanswer',
      'ai_last_try',
    );

    if (!hasColumn) {
      await queryRunner.addColumns('surv_surveyanswer', [
        new TableColumn({
          name: 'ai_last_try',
          type: 'timestamp',
          isNullable: true,
        }),
        new TableColumn({
          name: 'ai_processed_state',
          type: 'integer',
          isNullable: true,
        }),
        new TableColumn({
          name: 'ai_state',
          type: 'integer',
          isNullable: true,
        }),
        new TableColumn({
          name: 'ai_tries',
          type: 'integer',
          isNullable: true,
        }),
      ]);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('surv_surveyanswer_ai');

    const hasColumn = await queryRunner.hasColumn(
      'surv_surveyanswer',
      'ai_last_try',
    );
    if (hasColumn) {
      await queryRunner.dropColumns('surv_surveyanswer', [
        'ai_last_try',
        'ai_processed_state',
        'ai_state',
        'ai_tries',
      ]);
    }
  }
}
