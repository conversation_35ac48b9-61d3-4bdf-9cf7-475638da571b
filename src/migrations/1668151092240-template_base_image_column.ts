import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class templateBaseImageColumn1668151092240
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'template_base',
      new TableColumn({
        name: 'image',
        type: 'character varying',
        length: '50',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('template_base', 'image');
  }
}
