import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class addPhoneCodeSecUsers1692611657554 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'sec_users',
      new TableColumn({
        name: 'phone_code_country',
        type: 'character varying',
        length: '10',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('sec_users', 'phone_code_country');
  }
}
