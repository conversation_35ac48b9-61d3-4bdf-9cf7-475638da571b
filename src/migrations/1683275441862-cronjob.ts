import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class cronjob1683275441862 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'cron_jobs',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'character varying',
            length: '100',
          },
          {
            name: 'parameters',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'timer',
            type: 'character varying',
            length: '250',
          },
          {
            name: 'callOnStart',
            type: 'boolean',
            default: false,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('cron_jobs');
  }
}
