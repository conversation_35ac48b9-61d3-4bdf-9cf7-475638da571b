import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class addLoggingToCronjob1699866000094 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'cron_jobs',
      new TableColumn({
        name: 'logging',
        type: 'boolean',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('cron_jobs', 'logging');
  }
}
