import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class Queue1717248884294 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'queue',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'module',
            type: 'character varying',
            length: '150',
          },
          {
            name: 'value',
            type: 'character varying',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'integer',
            default: 0,
          },
          {
            name: 'tryCount',
            type: 'integer',
            default: 0,
          },
          {
            name: 'result',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'errors',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'creation_date',
            type: 'timestamp',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('cron_jobs');
  }
}
