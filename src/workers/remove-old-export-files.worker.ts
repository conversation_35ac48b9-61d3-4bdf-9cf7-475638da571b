import { env } from '@/app.env';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class RemoveOldExportFilesService implements OnModuleInit {
  onModuleInit() {
    this.removeOldExportFiles();
  }

  @Cron('0 0 * * *') // Runs every day at midnight
  removeOldExportFiles() {
    console.log('--- Removing old export files ---');
    const folderPath = path.join(env.APP_PATH, 'uploads', 'pdf');
    const files = fs.existsSync(folderPath) && fs.readdirSync(folderPath);

    const currentDate = new Date();
    const maxAgeInDays = 7; // Change this to the desired maximum age of files in days
    if (!files) return console.log('No files found in the folder');
    files.forEach((file) => {
      const filePath = path.join(folderPath, file);
      const fileStat = fs.statSync(filePath);
      const fileAgeInDays = Math.floor(
        (currentDate.getTime() - fileStat.mtime.getTime()) /
          (1000 * 60 * 60 * 24),
      );

      if (fileAgeInDays > maxAgeInDays) {
        fs.unlinkSync(filePath);
        console.log(`Removed old file: ${file}`);
      }
    });
  }
}
