import { OnModuleInit } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { promises } from 'fs';
import { join } from 'path';
import { env } from 'process';

export class ClearBrowserWorker implements OnModuleInit {
  onModuleInit() {
    this.clearBrowser();
  }
  @Cron('0 0 */1 * * *')
  async clearBrowser() {
    console.log('Cron JOB', '--- Clearing browser cache --- ');
    promises.rm(join(env.APP_PATH, 'tmp/browser/DeferredBroswerMetrics'), {
      recursive: true,
      force: true,
    });
  }
}
