import { LayoutType } from '../../../shared/enums/layout-type.enum';
import { ShareDashboardLayoutUserDto } from '../dto/share-dashboard-layout.dto';

export type GetListParams = {
  sec_user_id?: number;
  sec_company_id?: number;
  layout_type: LayoutType;
  flag?: string;
};
export type FindOneParams = {
  id: number;
  sec_user_id?: number;
  sec_company_id?: number;
  layout_type: LayoutType;
};

export type CopyLayoutParams = {
  name?: string;
  tags?: string[];
  layout_id: number;
};

export type ShareLayoutParams = {
  layout_id: number;
  viewers: ShareDashboardLayoutUserDto[];
};
