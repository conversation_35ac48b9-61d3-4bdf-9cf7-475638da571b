import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { LayoutType } from '@/shared/enums';

import { DashboardLayoutService } from './dashboard-layout.service';

import {
  CloneDashboardLayoutDto,
  ShareDashboardLayoutDto,
  UpsertLayoutDto,
} from './dto';

@ApiTags('Dashboard Layout')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'dashboard_layout',
})
export class DashboardLayoutConroller {
  constructor(private dashboardLayoutService: DashboardLayoutService) {}

  @Get()
  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Get Dashboard layout list',
  })
  @ApiQuery({
    name: 'flag',
    required: false,
    description:
      "putting 'new' will only return the list with new layouts for the user",
  })
  async list(@Query('flag') flag: string = null) {
    let result = await this.dashboardLayoutService.getList({
      layout_type: LayoutType.Dashboard,
      flag,
    });
    return {
      layouts: result,
    };
  }

  @Get(':id')
  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Get Requested dashboard layout',
  })
  async getLayout(@Param('id') id: string) {
    let result = await this.dashboardLayoutService.findOne({
      id: +id,
      layout_type: LayoutType.Dashboard,
    });
    return {
      layout: result,
    };
  }

  @Post()
  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Update or insert new record',
  })
  async upsertLayout(@Body() body: UpsertLayoutDto) {
    let response;
    if (body.viewed == 1) {
      response = await this.dashboardLayoutService.updateLastViewed(
        body.layout_id,
      );
    } else {
      body.layout_type = LayoutType.Dashboard;
      response = await this.dashboardLayoutService.upsertItem(body);
    }
    return {
      id: response.report_layout_id || body.layout_id,
      message: 'Data saved',
    };
  }

  @Delete(':id')
  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Delete dashboard layout',
  })
  async deleteLayout(@Param('id') id: string) {
    let data = await this.dashboardLayoutService.deleteLayout(id);
    if (!data) throw new HttpException('Dashboard is not found', 404);
    return {
      id: data.report_layout_id,
      message: `Dashboard ${data.layout_name} removed`,
    };
  }
}

@ApiTags('Dashboard Layout')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'copy_dashboard_layout',
})
export class CopyDashboardLayoutController {
  constructor(private dashboardLayoutService: DashboardLayoutService) {}

  @Post(':id')
  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Delete dashboard layout',
  })
  async copyLayout(
    @Param('id') layout_id: number,
    @Body() body: CloneDashboardLayoutDto,
  ) {
    const { name, tags } = body;
    let layout = await this.dashboardLayoutService.copyLayout({
      layout_id,
      name,
      tags,
    });
    return {
      layout,
    };
  }
}

@ApiTags('Dashboard Layout')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'share_dashboard_layout',
})
export class ShareDashboardLayout {
  constructor(private dashboardLayoutService: DashboardLayoutService) {}

  @Post(':id')
  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Share dashboard layout',
  })
  async shareLayout(
    @Param('id') layout_id: number,
    @Body() body: ShareDashboardLayoutDto,
  ) {
    const { viewers } = body;
    let result = await this.dashboardLayoutService.shareLayoutWith({
      layout_id,
      viewers,
    });
    return result;
  }
}
