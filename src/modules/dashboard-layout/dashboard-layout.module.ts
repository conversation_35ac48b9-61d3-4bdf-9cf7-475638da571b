import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatatableEntity } from '../database/entities/datatable.entity';
import { ReportLayoutSharedEntity } from '../database/entities/report-layout-shared.entity';
import { ReportLayoutEntity } from '../database/entities/report-layout.entity';
import { ReportLayoutTagsEntity } from '../database/entities/rl-tags.entity';
import { TagsEntity } from '../database/entities/tags.entity';
import { UserReportsEntity } from '../database/entities/user-reports.entity';
import { UsersModule } from '../users/users.module';
import {
  CopyDashboardLayoutController,
  DashboardLayoutConroller,
  ShareDashboardLayout,
} from './dashboard-layout.controller';
import { DashboardLayoutService } from './dashboard-layout.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ReportLayoutEntity,
      UserReportsEntity,
      ReportLayoutSharedEntity,
      TagsEntity,
      ReportLayoutTagsEntity,
    ]),
    ModuleLoggerModule.register('dashboard-layout'),
    UsersModule,
  ],
  controllers: [
    DashboardLayoutConroller,
    CopyDashboardLayoutController,
    ShareDashboardLayout,
  ],
  providers: [DashboardLayoutService],
  exports: [DashboardLayoutService],
})
export class DashboardLayoutModule {}
