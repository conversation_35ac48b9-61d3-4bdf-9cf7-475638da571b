import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON><PERSON>rray,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { LayoutType } from '../../../shared/enums/layout-type.enum';

export class UpsertLayoutDto {
  @Type(() => Number)
  @IsOptional()
  @ApiProperty({
    type: Number,
    description: 'layout_id ',
    required: false,
  })
  layout_id: number;

  @IsString()
  @Type()
  @IsOptional()
  @ApiProperty({
    type: String,
    description: 'name',
    required: false,
  })
  name: string;

  @ValidateNested()
  @IsOptional()
  @ApiProperty({
    type: Object,
    description: 'data',
    required: false,
  })
  data: Object;

  @IsArray()
  @Type(() => String)
  @IsOptional()
  @ApiProperty({
    description: 'tags',
    required: false,
  })
  tags: string[];

  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    type: Number,
    description: 'name',
    required: false,
  })
  viewed: number;

  @IsOptional()
  layout_type: LayoutType;
}
