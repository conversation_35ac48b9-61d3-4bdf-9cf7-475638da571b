import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, IsNumber, IsString } from 'class-validator';

export class ShareDashboardLayoutUserDto {
  @IsNumber()
  @Type()
  @ApiProperty({
    description: 'id of user',
    required: true,
  })
  id: number;

  @IsString()
  @Type()
  @ApiProperty({
    description: 'name of user',
    required: true,
  })
  name: string;
}

export class ShareDashboardLayoutDto {
  @IsArray()
  @Type(() => ShareDashboardLayoutUserDto)
  @ApiProperty({
    type: [ShareDashboardLayoutUserDto],
    description: 'list of viewers',
    required: true,
  })
  viewers: ShareDashboardLayoutUserDto[];
}
