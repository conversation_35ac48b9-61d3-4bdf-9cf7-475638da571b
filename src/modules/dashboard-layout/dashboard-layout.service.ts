import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import {
  CopyLayoutParams,
  FindOneParams,
  GetListParams,
  ShareLayoutParams,
} from './interfaces/layout-service-params';
import { UpsertLayoutDto } from './dto/upsert-layout.dto';
import * as moment from 'moment';

import { UsersService } from '../users/users.service';
import {
  ReportLayoutEntity,
  ReportLayoutSharedEntity,
  ReportLayoutTagsEntity,
  TagsEntity,
  UserReportsEntity,
} from '@entities';
import { parseJSON } from '@/shared/utils';
import { LayoutType } from '@/shared/enums';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class DashboardLayoutService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private userService: UsersService,
    @InjectRepository(ReportLayoutEntity)
    private readonly rlRepo: Repository<ReportLayoutEntity>,
    @InjectRepository(ReportLayoutSharedEntity)
    private readonly rlSharedRepo: Repository<ReportLayoutSharedEntity>,
    @InjectRepository(UserReportsEntity)
    private readonly userReportRepo: Repository<UserReportsEntity>,
    @InjectRepository(TagsEntity)
    private readonly tagsRepo: Repository<TagsEntity>,
    @InjectRepository(ReportLayoutTagsEntity)
    private readonly rlTagsRepo: Repository<ReportLayoutTagsEntity>,
  ) {}

  async getList(params: GetListParams): Promise<ReportLayoutEntity[]> {
    const { user, company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`getList for user ${user.id}, company ${company.id}`, {
      params,
    });
    let { sec_user_id, sec_company_id, layout_type, flag } = params;

    if (!sec_company_id) sec_company_id = company.id;
    if (!sec_user_id) sec_user_id = user.id;

    let sqlQuery = `SELECT
    rl.report_layout_id as   id,
    rl.layout_name as name,
    rl.layout_data,
    t.tags as tags,
    rl.is_home,
    rl.created_by as created_by_id,
    COALESCE(cb.firstname, '') || ' ' || COALESCE(cb.username, '') as created_by,
    rl.created_on,
    COALESCE(mb.firstname, '') || ' ' || COALESCE(mb.username, '') as modified_by,
    rl.modified_on,
    rls.last_viewed as last_viewed,
    viewers.users as viewers,
    cb.avatar_type, cb.avatar

    FROM report_layout rl
    CROSS JOIN LATERAL (
      SELECT ARRAY(
        SELECT tag_name FROM tags t LEFT JOIN report_layout_tags rlt ON rl.report_layout_id = rlt.rl_id WHERE t.tag_id = rlt.tag_id
          ) AS tags
      ) t
    LEFT JOIN report_layout_shared rls ON rl.report_layout_id = rls.report_layout_id AND rls.sec_user_id=${sec_user_id} 
    LEFT JOIN (SELECT  r.report_layout_id, JSON_AGG(json_build_object('username', u.username,'firstname', u.firstname, 'sec_user_id', u.sec_user_id)) as users from report_layout_shared r LEFT JOIN sec_users u on u.sec_user_id = r.sec_user_id GROUP BY 1) viewers ON viewers.report_layout_id = rl.report_layout_id
    LEFT JOIN sec_users cb ON rl.created_by = cb.sec_user_id
    LEFT JOIN sec_users mb ON rl.modified_by = mb.sec_user_id
    WHERE 
    ${
      flag != 'new'
        ? `
        (
          rl.sec_company_id = ${sec_company_id} AND
          rl.created_by = ${sec_user_id} AND 
          rl.layout_type = ${layout_type} AND
          (rl.is_home = 0 OR rl.is_home IS NULL)
        ) OR
    `
        : ''
    }
    
    (
      rl.sec_company_id = ${sec_company_id} AND
      rls.sec_user_id = ${sec_user_id} AND
      rl.layout_type = ${layout_type} AND 
      (rl.is_home = 0 OR rl.is_home IS NULL)
      ${flag == 'new' ? `AND rls.last_viewed IS NULL` : ''}
    )
    `;
    // WHERE (rl.created_by =${sec_user_id} OR rls.sec_user_id =${sec_user_id})
    //   AND rl.sec_company_id=${sec_company_id}
    //   AND rl.layout_type=${layout_type}
    //   AND (rl.is_home = 0 OR rl.is_home IS NULL)`;
    // if (flag == 'new')
    //   sqlQuery += `AND rls.last_viewed IS NULL AND rl.created_by != ${sec_user_id}`;

    sqlQuery += ` ORDER BY modified_on DESC`;
    let result = await this.rlRepo.query(sqlQuery);
    result = result.map((item) => {
      try {
        item.layout_data = JSON.parse(item.layout_data);
      } catch {}
      let isOwner = item.created_by_id == sec_user_id;
      return {
        ...item,
        viewers: item.viewers?.map((i) => ({
          name: this.userService.getFullName(i),
          id: i.sec_user_id,
        })),
        avatar: this.userService.getAvatar(item.avatar, item.avatar_type),
        isReadOnly: !isOwner,
        flag_new: !isOwner && !item.last_viewed,
        created_by_id: undefined,
        last_viewed: undefined,
        avatar_type: undefined,
      };
    });

    this.logger.debug(
      `getList query for user ${user.id}, company ${company.id}`,
      { sql: sqlQuery },
    );
    this.logger.debug(
      `getList result for user ${user.id}, company ${company.id}`,
      { result },
    );

    return result;
  }

  async getVanilla(
    sec_company_id: any,
    sec_user_id: any,
  ): Promise<ReportLayoutEntity[]> {
    return this.rlRepo.find({
      where: { sec_company_id, created_by: sec_user_id },
    });
  }

  async findOne(params: FindOneParams) {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    let { id, sec_company_id, sec_user_id, layout_type } = params;

    this.logger.debug(
      `findOne for user ${sec_user_id}, company ${sec_company_id}`,
      { params },
    );

    if (!sec_company_id) sec_company_id = company.id;
    if (!sec_user_id) sec_user_id = user.id;

    let sqlQuery = `SELECT
    rl.report_layout_id as id,
    rl.layout_name as name,
    rl.layout_data,
    rl.is_home,
    t.tags as tags,
    (rl.created_by = ${sec_user_id}) as is_not_readonly,
    COALESCE(cb.firstname, '') || ' ' || COALESCE(cb.username, '') as created_by,
    rl.created_on,
    COALESCE(mb.firstname, '') || ' ' || COALESCE(mb.username, '') as modified_by,
    rl.modified_on
    FROM report_layout rl
    CROSS JOIN LATERAL (
      SELECT ARRAY(
        SELECT tag_name FROM tags t LEFT JOIN report_layout_tags rlt ON rl.report_layout_id = rlt.rl_id WHERE t.tag_id = rlt.tag_id
          ) AS tags
    ) t
    LEFT JOIN sec_users cb ON rl.created_by = cb.sec_user_id
    LEFT JOIN sec_users mb ON rl.modified_by = mb.sec_user_id
      WHERE report_layout_id=${id}
        AND rl.sec_company_id=${sec_company_id}
        AND rl.layout_type=${layout_type}`;
    let result = await this.rlRepo.query(sqlQuery);
    this.logger.debug(`getList for user ${user.id}, company ${company.id}`, {
      params,
    });

    let item = result?.[0];
    if (!item) return null;

    this.logger.debug(`findOne query`, { sql: sqlQuery });
    this.logger.debug(`findOne result`, { item });
    try {
      item.layout_data = JSON.parse(item.layout_data);
      item.isReadOnly = !item.is_not_readonly;
      delete item.is_not_readonly;
    } catch {}
    return item;
  }

  async upsertItem(params: UpsertLayoutDto) {
    let { layout_id, name, data, tags, layout_type } = params;
    const { user, company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`upsertItem for user ${user.id}, company ${company.id}`, {
      params,
    });

    let item: Partial<ReportLayoutEntity> = {
      sec_company_id: company.id,
      layout_name: name,
      is_home: 0,
      layout_type,
      layout_data: JSON.stringify(data),
      created_by: user.id,
      created_on: new Date(),
      modified_by: user.id,
      modified_on: new Date(),
    };

    if (layout_id) item.report_layout_id = layout_id;

    let result = await this.rlRepo.save(item);

    layout_id = result.report_layout_id;

    await this.updateTags(layout_id, tags);

    this.logger.debug(
      `upsertResult for user ${user.id}, company ${company.id}`,
      { result },
    );

    return result;
  }

  async updateLastViewed(layout_id) {
    const { user } = this.cls.get<AuthorizedData>('user');

    return await this.rlRepo.query(`
      UPDATE report_layout_shared SET last_viewed=NOW() 
      WHERE report_layout_id=${layout_id} AND sec_user_id=${user.id}
      `);
  }

  async updateTags(layout_id: number, tags: string[]) {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    let existsTags =
      tags?.length > 0
        ? await this.tagsRepo
            .createQueryBuilder('t')
            .select(['t.tag_id', 't.tag_name'])
            .distinctOn(['t.tag_name'])
            .where('t.tag_type =:type', { type: 'report_layout' })
            .andWhere('t.sec_company_id =:cid', { cid: company.id })
            .andWhere('t.tag_name IN (:...tags)', { tags })
            .getMany()
        : [];
    let tagList;
    if (tags) {
      tagList = await this.tagsRepo.save(
        tags.map((name) => {
          let tag: DeepPartial<TagsEntity> = {
            tag_name: name,
            created_by: user.id,
            created_on: moment().format(),
            sec_company_id: company.id,
            tag_type: 'report_layout',
          };
          let id = existsTags.find((i) => i.tag_name == name)?.tag_id;
          if (id) tag.tag_id = id;
          return tag;
        }),
      );

      await this.rlTagsRepo
        .createQueryBuilder()
        .delete()
        .where('rl_id =:layout_id', { layout_id })
        .execute();

      await this.rlTagsRepo.save(
        tagList.map((i) => {
          let obj: DeepPartial<ReportLayoutTagsEntity> = {
            rl_id: layout_id,
            tag_id: i.tag_id,
            created_by: user.id,
            created_on: new Date(),
          };
          return obj;
        }),
      );
    }
  }

  async deleteLayout(layout_id): Promise<ReportLayoutEntity | null> {
    let layout = await this.rlRepo.findOne({
      where: { report_layout_id: layout_id },
    });
    if (!layout) return null;
    let layoutData = parseJSON(layout.layout_data) || [];
    if (Array.isArray(layoutData)) {
      await this.userReportRepo
        .createQueryBuilder()
        .delete()
        .whereInIds(layoutData.map((item) => item.chartId))
        .execute();
    }

    await this.rlRepo.delete({
      report_layout_id: layout.report_layout_id,
    });
    return layout;
  }

  async copyLayout(params: CopyLayoutParams) {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    const { layout_id, name, tags } = params;
    let mainLayout = await this.findOne({
      id: layout_id,
      layout_type: LayoutType.Dashboard,
    });
    if (!mainLayout)
      throw new HttpException(
        {
          msg: 'Couldnt find layout with provided id',
          error: 'layout_not_found',
        },
        HttpStatus.NOT_FOUND,
      );

    let layoutData: any = mainLayout.layout_data || [];
    let newLayoutData = [];
    for (let item of layoutData) {
      let reportItem = await this.userReportRepo.findOne({
        where: { user_report_id: item.id },
      });
      if (!reportItem) continue;
      let createdReport = await this.userReportRepo
        .createQueryBuilder()
        .insert()
        .values({
          report_name: reportItem.report_name,
          report_properties: reportItem.report_properties,
          short_description: reportItem.short_description,
          sec_user_id: user.id,
          report_template: reportItem.report_template,
          filters: reportItem.filters,
          created_by: user.id,
          created_on: new Date().toISOString(),
          modified_by: user.id,
          modified_on: new Date().toISOString(),
        })
        .execute();
      newLayoutData.push({
        ...item,
        shared_by: null,
        chartId: createdReport.generatedMaps[0].user_report_id,
        id: createdReport.generatedMaps[0].user_report_id,
        report_id: createdReport.generatedMaps[0].user_report_id,
        updatedId: createdReport.generatedMaps[0].user_report_id,
      });
    }
    let layout_type = mainLayout.layout_type || LayoutType.Dashboard;

    let result = await this.rlRepo
      .createQueryBuilder()
      .insert()
      .values({
        created_by: user.id,
        created_on: new Date(),
        modified_by: user.id,
        modified_on: new Date(),
        is_home: mainLayout.is_home,
        layout_data: JSON.stringify(newLayoutData),
        layout_name:
          name?.length > 0 ? name : mainLayout.layout_name + ' - Copy',
        layout_type,
        sec_company_id: company.id,
      })
      .execute();
    let id = result.generatedMaps[0].report_layout_id;

    await this.updateTags(id, tags);

    let layout = await this.findOne({
      id,
      layout_type,
    });

    return { ...layout, data: layout.layout_data, layout_data: undefined };
  }

  async shareLayoutWith({ viewers, layout_id }: ShareLayoutParams) {
    const { company } = this.cls.get<AuthorizedData>('user');

    await this.rlSharedRepo.delete({
      report_layout_id: layout_id,
      sec_company_id: company.id,
    });
    let list = viewers.map((user) => {
      return {
        report_layout_id: layout_id,
        sec_company_id: company.id,
        sec_user_id: user.id,
        last_viewed: null,
      };
    });
    let result = await this.rlSharedRepo.save(list);
    return result;
  }
}
