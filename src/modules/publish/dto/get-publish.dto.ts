import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';

export type PublishedFilterTypes = 0 | 1 | 2;

export class PublishFilterItem {
  @ApiProperty({
    type: Number,
    description: 'Item ID',
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    required: false,
    type: String,
    description: 'Item name',
  })
  @IsOptional()
  @IsString()
  name: string;
}

export class PublishGetListQueryDto {
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    type: Number,
    description: 'Offset ID',
    required: false,
    default: 0,
  })
  offsetId?: number;

  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  @IsOptional()
  @ApiProperty({
    type: Number,
    description: 'Number of items to return',
    required: false,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  limit?: number;

  constructor() {
    this.offsetId = 0;
    this.limit = 10;
  }
}

export class PublishGetListBodyDto {
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    type: Number,
    required: false,
    description: 'ID',
  })
  id?: number;

  total?: boolean;

  @IsString()
  @ApiProperty({
    type: String,
    description: 'Start date',
  })
  from_date?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    type: String,
    required: false,
    description: 'End date',
  })
  to_date?: string;

  @Type(() => PublishFilterItem)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: PublishFilterItem,
    isArray: true,
    required: false,
    description: 'Survey filters',
  })
  survey?: PublishFilterItem[];

  @Type(() => PublishFilterItem)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: PublishFilterItem,
    isArray: true,
    required: false,
    description: 'Project filters',
  })
  project?: PublishFilterItem[];

  @Type(() => PublishFilterItem)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: PublishFilterItem,
    isArray: true,
    required: false,
    description: 'Score filters',
  })
  score?: PublishFilterItem[];

  @Type(() => PublishFilterItem)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    type: PublishFilterItem,
    isArray: true,
    required: false,
    description: 'Publish filters',
  })
  publish?: PublishFilterItem[];
}
