import { Optional } from '@nestjs/common';
import { ApiBody, ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export type PublishUpdateState = 'published' | 'none' | 'excluded';

export class PublishUpdateDto {
  @Type()
  @Optional()
  @ApiProperty({ type: Number, isArray: true, required: false })
  ids?: number[];

  @Type()
  @ApiProperty({
    examples: ['published', 'none', 'excluded'],
    default: 'publish',
  })
  state: PublishUpdateState;
}
