import { Modu<PERSON> } from '@nestjs/common';
import { PublishController } from './publish.controller';
import { PublishService } from './publish.service';
import { ClassificationApiModule } from '../classification/classification-api/classification-api.module';

import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurvSurveyEntity } from '../database/entities/surv-survey.entity';
import { SurveyAnswerEntity } from '../database/entities/surv-surveyanswer.entity';
import { PublicationsEntity } from '../database/entities/publications.entity';
import { FeedbackDetailsModule } from '../feedback/feedback-details/feedback-details.module';
import { AuthModule } from '../auth/auth.module';
import { SurveyAnswerModule } from '../common/survey-answer/survey-answer.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SurvSurveyEntity,
      SurveyAnswerEntity,
      PublicationsEntity,
    ]),
    ModuleLoggerModule.register('publish'),
    AuthModule,
    ClassificationApiModule,
    ProgramSettingsModule,
    FeedbackDetailsModule,
    SurveyAnswerModule,
  ],
  controllers: [PublishController],
  providers: [PublishService],
  exports: [PublishService],
})
export class PublishModule {}
