import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import { ProgramSettingsService } from '../common/program-settings/program-settings.service';
import { FeedbackDetailsSerivce } from '../feedback/feedback-details/feedback-details.service';
import { ClassificationApiService } from '../classification/classification-api/classification-api.service';

import { v4 as uuid } from 'uuid';
import { ScoreType } from '@/shared/enums/score-type.enum';
import { PublicationState } from '@/shared/enums/publication.enum';

import { queryConvert } from '@/shared/utils';
import {
  PublicationsEntity,
  SurvSurveyEntity,
  SurveyAnswerEntity,
} from '@entities';
import {
  PublishGetListBodyDto,
  PublishGetListQueryDto,
  PublishUpdateDto,
} from './dto';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents';
import { QuestionType } from '@/shared/enums/question-type.enum';
import { PrivilegeService } from '../auth/privilege.service';
import { HttpException } from '@nestjs/common';
import { SruveyAnswerService } from '../common/survey-answer/survey-answer.service';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

import * as moment from 'moment';

export class PublishService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private saService: SruveyAnswerService,
    private privilegeService: PrivilegeService,
    private clfApiService: ClassificationApiService,
    private programSettingsService: ProgramSettingsService,
    private feedbackDetailsService: FeedbackDetailsSerivce,

    @InjectRepository(SurvSurveyEntity)
    private surveyRepo: Repository<SurvSurveyEntity>,

    @InjectRepository(SurveyAnswerEntity)
    private saRepo: Repository<SurveyAnswerEntity>,

    @InjectRepository(PublicationsEntity)
    private pubRepo: Repository<PublicationsEntity>,
  ) {}

  public async getData(
    body: PublishGetListBodyDto,
    query: PublishGetListQueryDto = {},
    { throwError = false } = {},
  ) {
    let limit = +query?.limit || 20;
    let offset = +query?.offsetId || 0;
    let sqlArgs: any = {};
    let filterSql = await this.clfApiService.getFilterData(
      {
        ...body,
        ignoreClassified: true,
        ignorePublish: body.id,
      },
      sqlArgs,
    );

    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug(`getData for company ${company.id}`);

    let response: any = {};

    let settings = await this.programSettingsService.get(company.id);
    let props = settings?.company_properties;
    let storyWidget = props?.storyWidget || {};

    const feedbackField = storyWidget.feedback_field;
    const rn = storyWidget.respondent_name;
    const scoreType = this.saService.getScoreType(storyWidget);

    let scoreSql = ' a.nps_value as score,';
    if (this.saService.isCesCSAT(scoreType)) {
      scoreSql = ' cescsat.value AS score, ';
    }

    let fields = `
    
    s.internal_name,
    ec.xml_initials,
    ec.xml_customername,
    project.project_name,
    a.surv_surveyanswer_id as id,
    a.creation_date,
    clf.clf_call_id, 
    clf.assigned_to,
    a.publication_state,
    ${scoreSql}
    ${
      body.id
        ? `
    ec.xml_brand,
    project.project_id,
    project.project_nr,
    a.surv_surveyanswer_id,
    a.answers,
    a.publication_id,
    a.creation_date,
    s.name as survey_name,
    s.surv_survey_id as surv_survey_id,
    `
        : ''
    }
    pos.return_value AS strengths,
    neg.return_value AS improvements
    `;
    let joins = `
    LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id 

    LEFT JOIN surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND s.clf_question2 = pos.surv_surveyquestion_id 
    LEFT JOIN surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND s.clf_question1 = neg.surv_surveyquestion_id 

    LEFT JOIN surv_surveypart p ON s.surv_survey_id = p.surv_survey_id
    LEFT JOIN surv_surveyquestion q ON p.surv_surveypart_id = q.surv_surveypart_id and q.questiontype = ${
      QuestionType.PublishQuestion
    }

    LEFT JOIN surv_surveyansweritem publish ON a.surv_surveyanswer_id = publish.surv_surveyanswer_id AND publish.surv_surveyquestion_id = q.surv_surveyquestion_id

    LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id 
    LEFT JOIN clf_call clf ON a.email_customer_id = clf.email_customer_id 
    LEFT JOIN projects project ON ec.xml_internal_external = project.project_id
    ${
      scoreType === 'CSAT' || scoreType === 'CES'
        ? `LEFT JOIN cescsat ON a.surv_surveyanswer_id = cescsat.surv_surveyanswer_id`
        : ''
    }

    `;

    if (!body.id) {
      filterSql += `
      ${feedbackField == 1 ? ` AND coalesce(neg.return_value, '') != '' ` : ''}
      ${feedbackField == 2 ? ` AND coalesce(pos.return_value, '') != '' ` : ''}
      ${
        feedbackField == 3
          ? ` AND (coalesce(neg.return_value, '') != '' OR coalesce(pos.return_value, '') != '')`
          : ''
      }
      `;
    }

    let sql = `
    ${
      scoreType === ScoreType.CES || scoreType === ScoreType.CSAT
        ? `
    WITH cescsat AS (
      select a.surv_surveyanswer_id, ans.question_id, ans.value::integer, ans.type
      from surv_surveyanswer a
      cross join jsonb_to_recordset(answers) ans(value text, question_id integer, type integer)
      where ans.type=${
        scoreType === ScoreType.CES
          ? QuestionType.CESQuestion
          : QuestionType.CSATQuestion
      } )
    `
        : ''
    }

    SELECT 
    ${
      body.total
        ? `
    COUNT(a.surv_surveyanswer_id) as total,
    COUNT(CASE WHEN a.publication_state = 1 THEN 1 ELSE NULL END) as published,
    COUNT(CASE WHEN a.publication_state = 2 THEN 1 ELSE NULL END) as excluded
    `
        : `DISTINCT ${fields} `
    }
    FROM surv_surveyanswer a
    ${joins}

    WHERE 
    ec.sec_company_id = ${company.id}
    ${
      body.id
        ? ` AND a.surv_surveyanswer_id =${+body.id}`
        : `AND publish.return_value = '1'`
    }

    
    ${filterSql}
    
    ${offset > 0 ? `AND a.surv_surveyanswer_id < '${offset}'` : ''}
${
  !body.total
    ? `
    ORDER BY a.surv_surveyanswer_id DESC
    
    LIMIT ${limit}
    `
    : ''
}
    `;

    let [sqlQuery, args] = queryConvert(sql, sqlArgs);

    this.logger.debug(`getData sql query`, { sql, sqlArgs });

    let data = await this.surveyRepo.query(sqlQuery, args);

    data = await this.privilegeService.restrictContent<any>(data, [
      'SHOW_CUSTOMER_DATA',
    ]);

    data = data.map((item) => {
      if (!item.creation_date) return item;
      return {
        ...item,
        creation_date: moment(item.creation_date).format('DD-MM-yyyy HH:mm'),
      };
    });

    if (body.id || body.total) {
      let item = data[0];
      if (!item) return null;
      item.name = this.saService.respondentName(item, rn);

      if (throwError) {
        let errorItem = null;
        if (feedbackField === 1 && !item.improvements?.length) {
          errorItem = 'improvements';
        } else if (feedbackField === 2 && !item.strengths?.length) {
          errorItem = 'strengths';
        } else if (
          feedbackField === 3 &&
          !item.strengths?.length &&
          !item.improvements?.length
        ) {
          errorItem = 'both';
        }
        if (errorItem) {
          throw new HttpException(
            { statusCode: 400, errorCode: 'empty_' + errorItem },
            400,
          );
        }
      }

      item = this.saService.filterFeedbackFields(item, feedbackField);

      return item;
    }

    response = data.map((item) => {
      item.name = this.saService.respondentName(item, rn);

      item = this.saService.filterFeedbackFields(item, feedbackField);
      return {
        ...item,
        state: ['none', 'published', 'excluded'][item.publication_state || 0],
        survey_name: item.internal_name,
        internal_name: undefined,
        xml_initials: undefined,
        xml_customername: undefined,
        project_name: undefined,
      };
    });

    this.logger.debug(`getData ${response?.length || 0} records found`);

    return response;
  }

  private async getSurveyAnswer(id: number) {
    if (!id) return;
    return this.getData({ id }, null);
  }

  public async getTotal(body: PublishGetListBodyDto) {
    let data = await this.getData({ ...body, total: true });
    return {
      total: +data?.total,
      published: +data?.published,
      excluded: +data?.excluded,
    };
  }

  public async updateData(body: PublishUpdateDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`updateData for ${body.ids.join(',')}`);

    for (let id of body.ids) {
      let surveyAnswer = await this.getSurveyAnswer(id);
      if (!surveyAnswer) continue;
      if (body.state === 'published') {
        let publication_id = uuid();

        let otherAnswers = this.feedbackDetailsService.getOtherQuestions(
          company.id,
          id,
        );

        await this.pubRepo.delete({ surv_surveyanswer_id: id });

        let response_date = surveyAnswer.creation_date;
        if (
          typeof response_date === 'string' &&
          response_date.split('-').length === 3
        ) {
          let splitted = response_date.split('-');
          response_date = new Date(
            `${splitted[1]}-${splitted[0]}-${splitted[2]}`,
          );
        }

        await this.pubRepo.save(
          this.pubRepo.create({
            publication_id,
            surv_surveyanswer_id: id,
            respondent_customername: surveyAnswer.xml_customername,
            respondent_initials: surveyAnswer.xml_initials,
            improvements: surveyAnswer.improvements,
            strengths: surveyAnswer.strengths,
            sec_company_id: company.id,
            project_id: surveyAnswer.project_id,
            project_nr: surveyAnswer.project_nr,
            brand: surveyAnswer.brand,
            score: surveyAnswer.score,
            survey_name: surveyAnswer.survey_name,
            surv_survey_id: surveyAnswer.surv_survey_id,
            other_answers: JSON.stringify(otherAnswers || {}),
            response_date,
          }),
        );

        await this.saRepo.update(
          { surv_surveyanswer_id: id },
          { publication_id, publication_state: PublicationState.Published },
        );
      } else if (body.state === 'none') {
        if (surveyAnswer.publication_id) {
          await this.pubRepo.delete({
            publication_id: surveyAnswer.publication_id,
          });
        }
        await this.saRepo.update(
          {
            surv_surveyanswer_id: id,
          },
          { publication_id: null, publication_state: null },
        );
      } else if (body.state === 'excluded') {
        await this.saRepo.update(
          { surv_surveyanswer_id: id },
          { publication_state: PublicationState.Excluded },
        );
      }
    }

    return {
      success: true,
    };
  }
}
