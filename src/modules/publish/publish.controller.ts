import { Body, Controller, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';

import { PublishService } from './publish.service';

import {
  PublishGetCountResponse,
  PublishGetListBodyDto,
  PublishGetListQueryDto,
  PublishUpdateDto,
} from './dto';
import { AuthGuard } from '@/shared/guards/auth.guard';

@Controller({
  path: 'publish',
  version: '2',
})
@ApiTags('Publish')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class PublishController {
  constructor(private publishService: PublishService) {}

  @Post('/list')
  @ApiOperation({
    description:
      'Possible publish filter values: 0 = all | 1 = yes | 2 = no  (default: all)',
  })
  getList(
    @Body() body: PublishGetListBodyDto,
    @Query() query: PublishGetListQueryDto,
  ) {
    return this.publishService.getData(body, query);
  }

  @Post('/count')
  @ApiOperation({
    description:
      'Possible publish filter values: 0 = all | 1 = yes | 2 = no  (default: all)',
  })
  getCount(
    @Body() body: PublishGetListBodyDto,
  ): Promise<PublishGetCountResponse> {
    return this.publishService.getTotal(body);
  }

  @Post('/update')
  @ApiOperation({
    description: 'Possible states: none | published | excluded |',
  })
  @ApiBody({
    type: PublishUpdateDto,
    examples: {
      none: {
        summary: 'Example De-Publish / De-Exclude',
        value: {
          ids: [4, 43, 12],
          state: 'none',
        } as PublishUpdateDto,
      },
      published: {
        summary: 'Example Publish',
        value: {
          ids: [1],
          state: 'published',
        } as PublishUpdateDto,
      },

      excluded: {
        summary: 'Example exclude',
        value: {
          ids: [4, 12],
          state: 'excluded',
        } as PublishUpdateDto,
      },
    },
  })
  updateData(@Body() body: PublishUpdateDto) {
    return this.publishService.updateData(body);
  }
}
