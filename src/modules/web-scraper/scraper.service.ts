import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import * as cheerio from 'cheerio';
const cloudscraper = require('cloudscraper');

@Injectable()
export class ScraperService {
  async scrape(url: string): Promise<any> {
    try {
      const html = await cloudscraper.get(url);

      return { data: this.extractParagraphs(html) };
    } catch (error) {
      // console.error(`Error while scraping URL: ${url}`, error);

      if (error.statusCode === 404) {
        throw new HttpException('Page not found', HttpStatus.NOT_FOUND);
      } else {
        throw new HttpException(
          'Error while scraping',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }

  extractParagraphs(html) {
    const options = {
      ignoreHref: true,
      ignoreImage: true,
      wordwrap: false,
    };

    const $ = cheerio.load(html);
    const plainText = $.text();

    // Split the text into paragraphs using the line breaks inserted by html-to-text
    const paragraphs = plainText
      .split(/\r?\n\r?\n/)
      .filter((paragraph) => paragraph.trim() !== '');

    return paragraphs;
  }

  async extractInternalLinks(url: string, domain: string): Promise<string[]> {
    const html = await cloudscraper.get(url);
    const $ = cheerio.load(html);
    const links = [];
    $('a').each((index, element) => {
      const link = $(element).attr('href');
      if (link && link.startsWith(domain)) {
        links.push(link);
      }
    });
    return links;
  }

  async scrapeEntireDomain(startUrl: string): Promise<any[]> {
    const domain = new URL(startUrl).origin; // Extract the domain from the start URL
    const visitedUrls = new Set<string>();
    const data = [];

    const crawl = async (url: string) => {
      if (visitedUrls.has(url)) return;
      visitedUrls.add(url);

      // Scrape the current URL
      const pageData = await this.scrape(url);
      data.push(pageData);

      // Find and crawl all internal links on the page
      const internalLinks = await this.extractInternalLinks(url, domain);
      for (const link of internalLinks) {
        await crawl(link);
      }
    };

    await crawl(startUrl);

    return data;
  }
}
