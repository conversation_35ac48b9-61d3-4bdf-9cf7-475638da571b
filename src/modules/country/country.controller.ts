import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CountryService } from './country.service';

import { AuthGuard } from '@/shared/guards/auth.guard';

@ApiTags('Country')
@ApiBearerAuth()
@Controller({
  path: 'country',
  version: '2',
})
export class CountryController {
  constructor(private countryService: CountryService) {}

  @Get()
  @UseGuards(AuthGuard)
  getCountries() {
    return this.countryService.getCountryList();
  }
}
