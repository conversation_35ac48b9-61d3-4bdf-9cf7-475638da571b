import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '../auth/auth.module';
import { CountryController } from './country.controller';
import { CountryService } from './country.service';
import { UsersModule } from '../users/users.module';
import { CompanyEntity } from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CompanyEntity]),
    AuthModule,
    ModuleLoggerModule.register('country'),
    UsersModule,
  ],
  providers: [CountryService],
  controllers: [CountryController],
  exports: [CountryService],
})
export class CountryModule {}
