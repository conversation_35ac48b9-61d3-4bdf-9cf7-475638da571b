import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { CompanyEntity } from '@entities';
import { IsAdmin, IsWelcome } from '@/shared/utils';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { CompanyType } from '@/shared/enums';

const countryList = require('country-list');

@Injectable()
export class CountryService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
  ) {}

  async getCountryList() {
    const { company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`getCountryList for company ${company.id}`);
    let query = this.companyRepo
      .createQueryBuilder('c')
      .select([
        'c.sec_company_id',
        'c.company_name',
        'c.country_code',
        'c.time_zone',
        'c.company_code',
        'c.company_type',
        'c.is_parent_company',
      ]);
    if (!IsAdmin(company.id)) {
      query.where('c.sec_company_id =:cid', { cid: company.id });
    }

    if (!IsWelcome(company.id)) {
      query.andWhere('c.sec_company_id != 1');
    }

    query.orderBy('c.sec_company_id');
    let result = await query.getMany();
    let response = result.map((item) => {
      let is_parent_company = item.is_parent_company === 1;
      if (
        IsWelcome(item.sec_company_id) &&
        (item.company_type === CompanyType.BOUW ||
          item.company_type === CompanyType.SPORT)
      ) {
        is_parent_company = false;
      }
      return {
        id: item.sec_company_id,
        company_id: item.sec_company_id,
        country_code: item.country_code,
        country_name:
          countryList.getName(item.country_code) || item.country_code,
        company_type: item.company_type,
        company_code: item.company_code,
        is_parent_company,
      };
    });

    this.logger.debug(`getCountryList result for company ${company.id}`, {
      response,
    });

    return response;
  }
}
