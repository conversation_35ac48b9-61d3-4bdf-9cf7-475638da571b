import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
} from '@nestjs/common';
import { MessageTranslationService } from './message-translation.service';
import { Auth } from '@/shared/decorators';
import { ApiTags } from '@nestjs/swagger';
import {
  CreateMessageTranslationDto,
  UpdateMessageTranslationDto,
} from './message-translation.dto';

@Controller({
  path: 'message_translation',
  version: '2',
})
@ApiTags('Message Translation')
export class MessageTranslationController {
  constructor(private messageTranslationService: MessageTranslationService) {}

  @Get()
  @Auth()
  list() {
    return this.messageTranslationService.companyList();
  }

  @Get('/:id')
  @Auth()
  async item(@Param('id') id: number) {
    let item = await this.messageTranslationService.companyItem(id);
    if (!item) throw new NotFoundException('Language not found');
    return item;
  }

  @Post()
  @Auth()
  create(@Body() body: CreateMessageTranslationDto) {
    return this.messageTranslationService.create(body);
  }

  @Post(':id')
  @Auth()
  update(@Param('id') id: number, @Body() body: UpdateMessageTranslationDto) {
    return this.messageTranslationService.update(id, body);
  }
}
