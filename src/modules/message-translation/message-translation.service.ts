import { Injectable } from '@nestjs/common';
import {
  CreateMessageTranslationDto,
  UpdateMessageTranslationDto,
} from './message-translation.dto';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { InjectRepository } from '@nestjs/typeorm';
import { SurvMessagesLocal } from '@entities';
import { FindOptionsWhere, Repository } from 'typeorm';

@Injectable()
export class MessageTranslationService {
  constructor(
    private cls: ClsService,
    @InjectRepository(SurvMessagesLocal)
    private messagesLocalRepository: Repository<SurvMessagesLocal>,
  ) {}

  async companyList(
    condition?: number | string | FindOptionsWhere<SurvMessagesLocal>,
    { pure = false } = {},
  ) {
    let { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let where: FindOptionsWhere<SurvMessagesLocal> = {
      sec_company_id: company.id,
    };
    if (typeof condition === 'number') {
      where.surv_messages_local_id = condition;
    } else if (typeof condition === 'string') {
      where.language = condition;
    } else {
      where = { ...where, ...condition };
    }
    let items = await this.messagesLocalRepository.find({
      where,
      select: pure ? undefined : { lang: { lang_desc: true } },
      relations: ['lang'],
    });
    if (pure) return items;
    return items.map((item) => {
      if (item.lang?.lang_desc) {
        (item as any).lang_desc = item.lang.lang_desc;
        delete item.lang;
      }
      return item;
    });
  }

  async companyItem(
    condition: number | string | FindOptionsWhere<SurvMessagesLocal>,
    { pure = false } = {},
  ) {
    let { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let where: FindOptionsWhere<SurvMessagesLocal> = {
      sec_company_id: company.id,
    };
    if (typeof condition === 'number') {
      where.surv_messages_local_id = condition;
    } else if (typeof condition === 'string') {
      where.language = condition;
    } else {
      where = { ...where, ...condition };
    }
    let list = await this.companyList(where, { pure });
    let [item] = list || [];
    return item;
  }

  async create(body: CreateMessageTranslationDto) {
    let { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let data: Partial<SurvMessagesLocal> = { ...body, language: undefined };
    if (body.language?.code) {
      data.language = body.language.code;
    }

    let exists = await this.companyItem(data.language);
    if (exists) return exists;

    let item = this.messagesLocalRepository.create({
      ...data,
      sec_company_id: company.id,
    });
    await item.save();
    return this.companyItem(item.surv_messages_local_id);
  }

  async update(id: number, body: UpdateMessageTranslationDto) {
    let item = await this.companyItem(id, { pure: true });

    let data: Partial<SurvMessagesLocal> = { ...body, language: undefined };
    if (body.language?.code) {
      data.language = body.language.code;
    }
    let merged = this.messagesLocalRepository.merge(item, data);
    if (body.language?.name && item.lang?.language_id) {
      merged.lang.lang_desc = body.language.name;
    }
    await merged.save();

    return this.companyItem(id);
  }
}
