import { Modu<PERSON> } from '@nestjs/common';
import { MessageTranslationController } from './message-translation.controller';
import { MessageTranslationService } from './message-translation.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurvMessagesLocal } from '@entities';
import { ApiLanguageModule } from '../language/language.module';

@Module({
  imports: [TypeOrmModule.forFeature([SurvMessagesLocal]), ApiLanguageModule],
  controllers: [MessageTranslationController],
  providers: [MessageTranslationService],
  exports: [MessageTranslationService],
})
export class MessageTranslationModule {}
