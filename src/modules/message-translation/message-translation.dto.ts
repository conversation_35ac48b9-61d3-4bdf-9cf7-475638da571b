import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  Length,
  MaxLength,
  MinLength,
  ValidateNested,
} from 'class-validator';

class MessageTranslationLanguage {
  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  @MinLength(2, { context: '2' })
  @MaxLength(2, { context: '2' })
  code: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  name: string;
}

export class CreateMessageTranslationDto {
  @Type(() => MessageTranslationLanguage)
  @IsOptional()
  @ApiProperty()
  @ValidateNested()
  language: MessageTranslationLanguage;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  btn_next: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  btn_back: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  btn_send: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  required_fields_msg: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  required_fields_label: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  header: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  trademark: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  default_placeholder: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  imprint: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  disclaimer: string;
}

export class UpdateMessageTranslationDto {
  @Type(() => MessageTranslationLanguage)
  @IsOptional()
  @ApiProperty()
  @ValidateNested()
  language: MessageTranslationLanguage;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  btn_next: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  btn_back: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  btn_send: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  required_fields_msg: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  required_fields_label: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  header: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  trademark: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  default_placeholder: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  imprint: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  disclaimer: string;
}
