import { ClsProperty } from '@/config/contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { SruveyAnswerService } from '@/modules/common/survey-answer/survey-answer.service';
import { PublishService } from '@/modules/publish/publish.service';
import { SurveyService } from '@/modules/survey/survey.service';
import { ClfContactCustomerType, CompanyType, ScoreType } from '@/shared/enums';
import { QuestionType } from '@/shared/enums/question-type.enum';
import { rawQuery, withItemAS } from '@/shared/utils';
import { ClfCallEntity } from '@entities';
import { InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { In, Repository } from 'typeorm';
import { GetContactCustomerQueryDto } from './dto/get-contact-customer.dto';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { ProjectService } from '../project/project.service';

export class ContactCustomerService {
  constructor(
    private logger: ModuleLogger,
    private surveySrevice: SurveyService,
    private projectService: ProjectService,

    private cls: ClsService,

    @InjectRepository(ClfCallEntity)
    private clfCallRepo: Repository<ClfCallEntity>,
  ) {}

  list(query: GetContactCustomerQueryDto) {
    this.logger.debug('list', { query });
    return this.data(query);
  }
  async total(params: { ignoreStatus: boolean }) {
    this.logger.debug('total');
    let total = await this.data({
      total: true,
      ignoreStatus: params.ignoreStatus,
    });

    this.logger.debug(`total result ${total}`);

    return {
      total,
    };
  }

  async state(ids: number[], state: ClfContactCustomerType) {
    this.logger.debug(`update state ${ids.join(',')}`, { state });
    let promises = [];
    for (let id of ids) {
      promises.push(
        this.clfCallRepo.update(
          { clf_call_id: id },
          { contact_customer: state === 0 ? null : state },
        ),
      );
    }

    let data = await Promise.all(promises);
    let updated = data.reduce((total, item) => (total += item.affected), 0);
    this.logger.debug(`update state  result for ${ids.join(',')}`, { updated });
    return {
      success: true,
      updated,
    };
  }

  async data(
    params: {
      id?: number;
      total?: boolean;
      limit?: number;
      page?: number;
      ignoreStatus?: boolean;
    } = {},
  ) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let limit = +params?.limit || 20;
    let page = (+params?.page || 1) - 1;
    // const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const surveyIds = (await this.surveySrevice.list()).map((i) => i.id);
    const projectIds = (await this.projectService.listByGroup()).map(
      (i) => i.id,
    );

    this.logger.debug(`data `, { params });

    // let settings = await this.programSettingsService.get(company.id);
    // let props = settings?.company_properties;
    // let storyWidget = props?.storyWidget || {};

    // const feedbackField = storyWidget.feedback_field;
    // const rn = storyWidget.respondent_name;
    // const scoreType = this.saService.getScoreType(storyWidget);

    let qb = this.clfCallRepo.createQueryBuilder('clf');
    if (!params.total) {
      qb.select('clf.clf_call_id', 'clf_call_id');
      qb.addSelect('a.surv_surveyanswer_id', 'surv_surveyanswer_id');
      qb.addSelect('a.creation_date', 'creation_date');
      qb.addSelect('clf.contact_customer_reason', 'contact_customer_reason');
      qb.addSelect('pos.return_value', 'strengths');
      qb.addSelect('neg.return_value', 'improvements');
      qb.addSelect('s.internal_name', 'survey_name');
      qb.addSelect('ec.xml_initials', 'xml_initials');
      qb.addSelect('ec.xml_customername', 'xml_customername');
      qb.addSelect('project.project_name', 'project_name');
    }
    qb.leftJoin('clf.survey_answer', 'a');
    qb.leftJoin('clf.email_customer', 'ec');
    qb.leftJoin('a.survey', 's');
    qb.leftJoin(
      'a.item',
      'pos',
      's.clf_question2 = pos.surv_surveyquestion_id',
    );
    qb.leftJoin(
      'a.item',
      'neg',
      's.clf_question1 = neg.surv_surveyquestion_id',
    );

    if (!params.total) {
      qb.leftJoin('ec.project', 'project');
    }

    if (params.ignoreStatus) {
      qb.where('clf.contact_customer IS NOT NULL');
    } else {
      qb.where('clf.contact_customer = :type', {
        type: ClfContactCustomerType.Requested,
      });
    }
    qb.andWhere('a.surv_survey_id IN(:...ids)', {
      ids: surveyIds?.length ? surveyIds : [0],
    });
    if (company.type != CompanyType.BSH) {
      qb.andWhere(
        '(ec.xml_internal_external IN(:...projectIds) OR ec.xml_internal_external IS NULL)',
        {
          projectIds: projectIds?.length ? projectIds : [0],
        },
      );
    }

    // this.saService.feedbackFieldsQueryBuilder(qb, feedbackField);

    if (!params.total) {
      // if (this.saService.isCesCSAT(scoreType)) {
      //   qb.innerJoin(
      //     'cescsat',
      //     'cescsat',
      //     'a.surv_surveyanswer_id = cescsat.surv_surveyanswer_id',
      //   );
      //   qb.addSelect('cescsat.value', 'score');

      //   let cescsatQuery = this.saService.cesCsatQuery(
      //     scoreType === ScoreType.CES
      //       ? QuestionType.CESQuestion
      //       : QuestionType.CSATQuestion,
      //   );

      //   withItemAS({ qb, alias: 'cescsat', query: cescsatQuery });
      // } else {
      qb.addSelect('a.nps_value', 'score');
      // }

      qb.limit(limit);
      qb.offset(limit * page);
    }

    this.logger.debug(`data sql`, { query: rawQuery(qb) });

    if (params.total) {
      return qb.getCount();
    } else if (params.id) {
      qb.take(1);
    } else {
      qb.orderBy('a.creation_date', 'DESC');
    }

    let result = await qb.getRawMany();

    let list = result.map((item) => {
      // item.name = this.saService.respondentName(item, rn);
      // item = this.saService.filterFeedbackFields(item, feedbackField);

      return {
        ...item,
        xml_initials: undefined,
        xml_customername: undefined,
        project_name: undefined,
      };
    });

    this.logger.debug(`data result`, { list });

    if (params.id) {
      return list[0];
    }
    return list;
  }
}
