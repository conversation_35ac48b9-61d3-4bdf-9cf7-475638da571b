import { ClfContactCustomerType } from '@/shared/enums';
import { Optional } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class ContactCustomerUpdateStateDto {
  @Type()
  @Optional()
  @ApiProperty({ type: Number, isArray: true, required: false })
  ids?: number[];

  @Type()
  @ApiProperty({
    examples: ['0', '1', '2'],
    default: '0',
  })
  state: ClfContactCustomerType;
}
