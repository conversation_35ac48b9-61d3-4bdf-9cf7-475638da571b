import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsN<PERSON>ber, IsOptional, Max, Min } from 'class-validator';

export class GetContactCustomerQueryDto {
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    type: Number,
    required: false,
    default: 1,
  })
  page: number = 1;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(100)
  @ApiProperty({
    type: Number,
    required: false,
    default: 20,
  })
  limit?: number = 20;

  @Transform(({ value }: any) => {
    return value === 'true' || value === true || value === 1 || value === '1';
  })
  @ApiProperty({ required: false })
  ignoreStatus: boolean;
}

export class GetContactCustomerTotalQueryDto {
  @Transform(({ value }: any) => {
    return value === 'true' || value === true || value === 1 || value === '1';
  })
  @ApiProperty({ required: false })
  ignoreStatus: boolean;
}
