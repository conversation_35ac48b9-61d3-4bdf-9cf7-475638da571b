import { PublishModule } from '../publish/publish.module';
import { Module } from '@nestjs/common';
import { ContactCustomerService } from './contact-customer.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClfCallEntity, SurveyAnswerEntity } from '@entities';
import { ContactCustomerController } from './contact-customer.controller';
import { SurveyModule } from '../survey/survey.module';
import { SurveyAnswerModule } from '../common/survey-answer/survey-answer.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { ProjectModule } from '../project/project.module';

@Module({
  imports: [
    ModuleLoggerModule.register('contact-customer'),
    TypeOrmModule.forFeature([SurveyAnswerEntity, ClfCallEntity]),
    PublishModule,
    SurveyModule,
    SurveyAnswerModule,
    ProjectModule,
  ],
  controllers: [ContactCustomerController],
  providers: [ContactCustomerService],
})
export class ContactCustomerModule {}
