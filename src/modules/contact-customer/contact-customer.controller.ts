import { AuthGuard } from '@/shared/guards/auth.guard';
import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ContactCustomerService } from './contact-customer.service';
import { ContactCustomerUpdateStateDto } from './dto/post-contact-customer.dto';
import { ClfContactCustomerType } from '@/shared/enums';
import {
  GetContactCustomerQueryDto,
  GetContactCustomerTotalQueryDto,
} from './dto/get-contact-customer.dto';

@Controller({
  path: 'contact_customer',
  version: '2',
})
@ApiTags('Contact Customer')
@ApiBearerAuth()
export class ContactCustomerController {
  constructor(private contactCustomerService: ContactCustomerService) {}

  @Get()
  @UseGuards(AuthGuard)
  list(@Query() query: GetContactCustomerQueryDto) {
    return this.contactCustomerService.list(query);
  }

  @Get('/total')
  @UseGuards(AuthGuard)
  total(@Query() query: GetContactCustomerTotalQueryDto) {
    return this.contactCustomerService.total(query);
  }

  @Post('/state')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: 'Possible states: 0 = null | 1 = Requested | 2 = Checked',
  })
  @ApiBody({
    type: ContactCustomerUpdateStateDto,
    examples: {
      checked: {
        summary: 'Example Request for Checked',
        value: {
          ids: [4, 43, 12],
          state: ClfContactCustomerType.Requested,
        } as ContactCustomerUpdateStateDto,
      },
    },
  })
  updateData(@Body() body: ContactCustomerUpdateStateDto) {
    return this.contactCustomerService.state(body.ids, body.state);
  }
}
