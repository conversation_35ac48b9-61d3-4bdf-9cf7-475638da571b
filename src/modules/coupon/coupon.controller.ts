import { Controller, Delete, Get, Param } from '@nestjs/common';
import { CouponService } from './coupon.service';
import { Auth } from '@/shared/decorators';
import { ApiTags } from '@nestjs/swagger';

@Controller({
  path: 'coupon',
  version: '2',
})
@ApiTags('Coupon')
export class CouponController {
  constructor(private couponService: CouponService) {}

  @Get()
  @Auth()
  list() {
    return this.couponService.list();
  }

  @Delete('/:brand_id')
  @Auth()
  delete(@Param('brand_id') brand_id: number) {
    return this.couponService.delete({ brand_id });
  }
}
