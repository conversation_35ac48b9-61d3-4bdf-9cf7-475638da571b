import { SurveyCouponEntity } from '@entities';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { Repository } from 'typeorm';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';

@Injectable()
export class CouponService {
  constructor(
    private cls: ClsService,
    @InjectRepository(SurveyCouponEntity)
    private surveyCouponRepo: Repository<SurveyCouponEntity>,
  ) {}

  async list() {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const result = await this.surveyCouponRepo
      .createQueryBuilder('sc')
      .leftJoinAndSelect('brands', 'b', 'sc.brand_id = b.brand_id')
      .select('b.brand_name', 'brand')
      .addSelect('sc.brand_id', 'brand_id')
      .addSelect(
        'SUM(CASE WHEN sc.email_date IS NULL THEN 1 ELSE 0 END)',
        'available_coupons',
      )
      .addSelect('COUNT(*)', 'total_coupons')
      .where('sc.sec_company_id = :id', { id: company.id })
      .andWhere("sc.end_date > 'today'")
      .groupBy('b.brand_name')
      .addGroupBy('sc.brand_id')
      .getRawMany();

    const formattedResult = result.map((item) => ({
      ...item,
      available_coupons: +item.available_coupons || 0,
      total_coupons: +item.total_coupons || 0,
    }));

    return formattedResult;
  }

  async delete(filter: { id?: number; brand_id?: number }) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    await this.surveyCouponRepo
      .createQueryBuilder()
      .delete()
      .where('sec_company_id = :id', { id: company.id })
      .andWhere('brand_id = :brand_id', { brand_id: filter.brand_id })
      .andWhere('email_date IS NULL')
      .execute();
    return { message: 'deleted' };
  }
}
