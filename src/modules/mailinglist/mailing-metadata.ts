const columns = [
  {
    columnName: 'sent_status',
    label: 'Status',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: false,
    mailinglist: true,
    mailinglist_visible: true,
    order: 1,
    id: 1,
  },
  {
    columnName: 'creation_date',
    label: 'Creation Date',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: false,
    mailinglist: true,
    mailinglist_visible: true,
    order: 2,
    id: 2,
  },
  {
    columnName: 'name',
    label: 'Survey',
    table: 's',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 3,
    id: 3,
  },
  {
    columnName: 'xml_rasnumber',
    label: 'Unique number',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 4,
    id: 4,
  },
  {
    columnName: 'xml_hybrisid',
    label: 'Hybris ID',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 5,
    id: 5,
  },
  {
    columnName: 'xml_salutation',
    label: 'Salutation',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 6,
    id: 6,
  },
  {
    columnName: 'xml_initials',
    label: 'Name',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 7,
    id: 7,
  },
  {
    columnName: 'xml_customername',
    label: 'Last Name',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 8,
    id: 8,
  },
  {
    columnName: 'xml_email',
    label: 'Email',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 9,
    id: 9,
  },
  {
    columnName: 'xml_phone1',
    label: 'Phone',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 10,
    id: 10,
  },
  {
    columnName: 'xml_phone2',
    label: 'Mobile Phone',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 11,
    id: 11,
  },
  {
    columnName: 'sms_id',
    label: 'Medium',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 12,
    id: 12,
  },
  {
    columnName: 'xml_brand',
    label: 'Brand',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 13,
    id: 13,
  },
  {
    columnName: 'send_date',
    label: 'Sent Date',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 14,
    id: 14,
  },
  {
    columnName: 'xml_language',
    label: 'Language',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 15,
    id: 15,
  },
  {
    columnName: 'xml_originservice',
    label: 'Origin of service',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 16,
    id: 16,
  },
  {
    columnName: 'xml_readydate',
    label: 'Visit Date',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 17,
    id: 17,
  },
  {
    columnName: 'xml_lastvisitdate',
    label: 'Last Visit Date',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 18,
    id: 18,
  },
  {
    columnName: 'xml_acceptancedate',
    label: 'Acceptance Date',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 19,
    id: 19,
  },
  {
    columnName: 'xml_purchasedate',
    label: 'Purchase Date',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 20,
    id: 20,
  },
  {
    columnName: 'xml_proddate',
    label: 'Production Date',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 21,
    id: 21,
  },
  {
    columnName: 'xml_operationtime',
    label: 'Operation Time',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 22,
    id: 22,
  },
  {
    columnName: 'xml_outagetime',
    label: 'Downtime',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 23,
    id: 23,
  },
  {
    columnName: 'xml_waitingtime',
    label: 'Waiting Time',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 24,
    id: 24,
  },
  {
    columnName: 'xml_nrvisits',
    label: 'Number of Visits',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 25,
    id: 25,
  },
  {
    columnName: 'xml_enumber',
    label: 'E Number',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 26,
    id: 26,
  },
  {
    columnName: 'xml_prodarea',
    label: 'Product Group (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 27,
    id: 27,
  },
  {
    columnName: 'xml_prodarea_mapped',
    label: 'Product Group (name)',
    table: 'xml_prodarea_mapped_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 28,
    id: 28,
  },
  {
    columnName: 'xml_proddivision',
    label: 'Product Division (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 29,
    id: 29,
  },
  {
    columnName: 'xml_proddivision_mapped',
    label: 'Product Division (name)',
    table: 'xml_proddivision_mapped_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 30,
    id: 30,
  },
  {
    columnName: 'xml_payment',
    label: 'Payment (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 31,
    id: 31,
  },
  {
    columnName: 'xml_payment_mapped',
    label: 'Payment (name)',
    table: 'xml_payment_mapped_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 32,
    id: 32,
  },
  {
    columnName: 'xml_grossinvoice',
    label: 'Gross Invoice',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 33,
    id: 33,
  },
  {
    columnName: 'xml_region',
    label: 'Region (code)',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 34,
    id: 34,
  },
  {
    columnName: 'xml_region_mapped',
    label: 'Region (name)',
    table: 'xml_region_mapped_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 35,
    id: 35,
  },
  {
    columnName: 'xml_techgrp',
    label: 'Engineer Group (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 36,
    id: 36,
  },
  {
    columnName: 'xml_techgrp_mapped',
    label: 'Engineer Group (name)',
    table: 'xml_techgrp_mapped_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 37,
    id: 37,
  },
  {
    columnName: 'xml_technician',
    label: 'Engineer (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 38,
    id: 38,
  },
  {
    columnName: 'xml_technician_mapped',
    label: 'Engineer (name)',
    table: 'xml_technician_mapped_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 39,
    id: 39,
  },
  {
    columnName: 'xml_acceptance_by',
    label: 'Front Line Agent (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 40,
    id: 40,
  },
  {
    columnName: 'xml_acceptance_by_mapped',
    label: 'Front Line Agent (name)',
    table: 'xml_acceptance_by_mapped_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 41,
    id: 41,
  },
  {
    columnName: 'xml_wvb',
    label: 'Preprepper (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 42,
    id: 42,
  },
  {
    columnName: 'xml_wvb_mapped',
    label: 'Preprepper (name)',
    table: 'xml_wvb_mapped_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 43,
    id: 43,
  },
  {
    columnName: 'xml_planning',
    label: 'Dispatch (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 44,
    id: 44,
  },
  {
    columnName: 'xml_planning_mapped',
    label: 'Dispatch (name)',
    table: 'xml_planning_mapped_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 45,
    id: 45,
  },
  {
    columnName: 'xml_custcreditnr',
    label: 'Service Partner (code)',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 46,
    id: 46,
  },
  {
    columnName: 'xml_custcreditnr_mapped',
    label: 'Service Partner (name)',
    table: 'xml_custcreditnr_mapped_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 47,
    id: 47,
  },
  {
    columnName: 'xml_employee',
    label: 'Counselor (code)',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 48,
    id: 48,
  },
  {
    columnName: 'xml_employee_mapped',
    label: 'Counselor (name)',
    table: 'xml_employee_mapped_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 49,
    id: 49,
  },
  {
    columnName: 'xml_dealer',
    label: 'Dealer (code)',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 50,
    id: 50,
  },
  {
    columnName: 'xml_dealer_mapped',
    label: 'Dealer (name)',
    table: 'xml_dealer_mapped_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 51,
    id: 51,
  },
  {
    columnName: 'xml_logisticpartner',
    label: 'Logistic Partner',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 52,
    id: 52,
  },
  {
    columnName: 'xml_la',
    label: 'LA',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 53,
    id: 53,
  },
  {
    columnName: 'xml_fkz',
    label: 'FKZ',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 54,
    id: 54,
  },
  {
    columnName: 'xml_cp',
    label: 'Branch',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 55,
    id: 55,
  },
  {
    columnName: 'xml_sv',
    label: 'Cluster',
    table: 'ec',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 56,
    id: 56,
  },
  {
    columnName: 'creation_date',
    label: 'Response date',
    table: 'a',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 57,
    id: 57,
  },
  {
    columnName: 'labels_strengths',
    label: 'Labels Strengths',
    table: 'lbl_pos',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'classification',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 58,
    id: 58,
  },
  {
    columnName: 'labels_improvements',
    label: 'Labels Improvements',
    table: 'lbl_neg',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'classification',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 59,
    id: 59,
  },
  {
    columnName: 'assigned_to',
    label: 'Assigned to (code)',
    table: 'call',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 60,
    id: 60,
  },
  {
    columnName: 'assigned_by',
    label: 'Assigned by (code)',
    table: 'call',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 61,
    id: 61,
  },
  {
    columnName: 'assigned_date',
    label: 'Assigned date',
    table: 'call',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 62,
    id: 62,
  },
  {
    columnName: 'closed_by_date',
    label: 'Closing date',
    table: 'call',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 63,
    id: 63,
  },
  {
    columnName: 'closed_by',
    label: 'Closed by (code)',
    table: 'call',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 64,
    id: 64,
  },
  {
    columnName: 'status',
    label: 'Status',
    table: 'call',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 65,
    id: 65,
  },
  {
    columnName: 'result',
    label: 'Result',
    table: 'call',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 66,
    id: 66,
  },
  {
    columnName: 'credit',
    label: 'Credit',
    table: 'call',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 67,
    id: 67,
  },
  {
    columnName: 'note',
    label: 'Call note',
    table: 'call',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 68,
    id: 68,
  },
  {
    columnName: 'assigned_by_mapped',
    label: 'Assigned by (name)',
    table: 'assigned_by_mapped_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 69,
    id: 69,
  },
  {
    columnName: 'assigned_to_mapped',
    label: 'Assigned to (name)',
    table: 'assigned_to_mapped_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 70,
    id: 70,
  },
  {
    columnName: 'closed_by_mapped',
    label: 'Closed by (name)',
    table: 'closed_by_mapped_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'CLF',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 71,
    id: 71,
  },
  {
    columnName: 'tags',
    label: 'Tags',
    table: 'tags',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 72,
    id: 72,
  },
  {
    columnName: 'labels_like',
    label: 'Labels Like',
    table: 'labels_like_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'classification',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 73,
    id: 73,
  },
  {
    columnName: 'labels_potential',
    label: 'Labels Potential',
    table: 'labels_potential_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'classification',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 74,
    id: 74,
  },
  {
    columnName: 'labels_excluded',
    label: 'Labels Excluded',
    table: 'labels_excluded_table',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'classification',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 75,
    id: 75,
  },
  {
    columnName: 'labels_neutral',
    label: 'Labels Neutral',
    table: 'labels_neutral_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'classification',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 76,
    id: 76,
  },
  {
    columnName: 'bsh_category_grandparent_name',
    label: 'BSH category grandparent (name)',
    table: 'bsh_category_grandparent_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 77,
    id: 77,
  },
  {
    columnName: 'bsh_category_grandparent_id',
    label: 'BSH category grandparent (ID)',
    table: 'bsh_category_grandparent_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 78,
    id: 78,
  },
  {
    columnName: 'bsh_category_parent_name',
    label: 'BSH category parent (name)',
    table: 'bsh_category_parent_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 79,
    id: 79,
  },
  {
    columnName: 'bsh_category_parent_id',
    label: 'BSH category parent (id)',
    table: 'bsh_category_parent_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 80,
    id: 80,
  },
  {
    columnName: 'bsh_category_child_name',
    label: 'BSH category child (name)',
    table: 'bsh_category_child_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 81,
    id: 81,
  },
  {
    columnName: 'bsh_category_child_id',
    label: 'BSH category child (id)',
    table: 'bsh_category_child_table',
    export_visible: false,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 82,
    id: 82,
  },
  {
    columnName: 'xml_address',
    label: 'Address',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 83,
    id: 83,
  },
  {
    columnName: 'xml_techgrp',
    label: 'Address nr',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 84,
    id: 84,
  },
  {
    columnName: 'object_buildnr',
    label: 'Build number',
    table: 'contacts',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 85,
    id: 85,
  },
  {
    columnName: 'xml_companyname',
    label: 'Company name',
    table: 'ec',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 86,
    id: 86,
  },
  {
    columnName: 'xml_city',
    label: 'City',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 87,
    id: 87,
  },
  {
    columnName: 'project_nr',
    label: 'Project nr',
    table: 'projects',
    export_visible: true,
    bouw: true,
    sport: false,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 88,
    id: 88,
  },
  {
    columnName: 'project_nr',
    label: 'Club nr',
    table: 'projects',
    export_visible: true,
    bouw: false,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 89,
    id: 89,
  },
  {
    columnName: 'project_name',
    label: 'Project',
    table: 'projects',
    export_visible: true,
    bouw: true,
    sport: false,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 90,
    id: 90,
  },
  {
    columnName: 'project_name',
    label: 'Club',
    table: 'projects',
    export_visible: true,
    bouw: false,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 91,
    id: 91,
  },
  {
    columnName: 'xml_fulladdress',
    label: 'Full address',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: true,
    order: 92,
    id: 92,
  },
  {
    columnName: 'xml_acceptance_by',
    label: 'Postalcode',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 93,
    id: 93,
  },
  {
    columnName: 'xml_refnr',
    label: 'Reference',
    table: 'ec',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: true,
    mailinglist_visible: false,
    order: 94,
    id: 94,
  },
  {
    columnName: 'project_category',
    label: 'Project category',
    table: 'pcategories',
    export_visible: false,
    bouw: true,
    sport: false,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 95,
    id: 95,
  },
  {
    columnName: 'project_category',
    label: 'Club category',
    table: 'pcategories',
    export_visible: false,
    bouw: false,
    sport: true,
    bsh: false,
    category: 'meta_data',
    classification: false,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 96,
    id: 96,
  },
  {
    columnName: 'surv_surveyanswer_id',
    label: 'Answer ID',
    table: 'a',
    export_visible: false,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'meta_data',
    classification: true,
    responses: true,
    mailinglist: false,
    mailinglist_visible: false,
    order: 97,
    id: 97,
  },
  {
    columnName: 'child_label',
    label: 'Label',
    table: 'mclass_label',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'classification',
    classification: true,
    responses: false,
    mailinglist: false,
    mailinglist_visible: false,
    order: 98,
    id: 98,
  },
  {
    columnName: 'parent_label',
    label: 'Main Label',
    table: 'mclass_main_label',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'classification',
    classification: true,
    responses: false,
    mailinglist: false,
    mailinglist_visible: false,
    order: 99,
    id: 99,
  },
  {
    columnName: 'record_status',
    label: 'Type',
    table: 'mr',
    export_visible: true,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'classification',
    classification: true,
    responses: false,
    mailinglist: false,
    mailinglist_visible: false,
    order: 100,
    id: 100,
  },
  {
    columnName: 'istype',
    label: 'Sentiment',
    table: 'mr',
    export_visible: true,
    bouw: true,
    sport: true,
    bsh: true,
    category: 'classification',
    classification: true,
    responses: false,
    mailinglist: false,
    mailinglist_visible: false,
    order: 101,
    id: 101,
  },
  {
    columnName: 'confidence',
    label: 'Confidence',
    table: 'mr',
    export_visible: true,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'classification',
    classification: true,
    responses: false,
    mailinglist: false,
    mailinglist_visible: false,
    order: 102,
    id: 102,
  },
  {
    columnName: 'created_on',
    label: 'Label Date',
    table: 'mr',
    export_visible: true,
    bouw: false,
    sport: false,
    bsh: true,
    category: 'classification',
    classification: true,
    responses: false,
    order: 103,
    id: 103,
  },
];
export default columns;
