import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MailinglistConroller } from './mailinglist.controller';
import { AuthModule } from '../auth/auth.module';

import { MailinglistService } from './mailinglist.service';
import { GdprService } from '../gdpr/gdpr.service';

import { EmailCustomerEntity, SurveyAnswerEntity, UserEntity } from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('mailinglist'),
    TypeOrmModule.forFeature([
      EmailCustomerEntity,
      SurveyAnswerEntity,
      UserEntity,
    ]),
    AuthModule,
  ],
  controllers: [MailinglistConroller],
  providers: [MailinglistService, GdprService],
})
export class MailinglistModule {}
