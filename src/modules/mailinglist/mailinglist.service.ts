import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { HttpException, HttpStatus } from '@nestjs/common';
import metadata from './mailing-metadata';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { AuthService } from '../auth/auth.service';
import {
  MailingListBaseDto,
  MailingListQueryDto,
} from './dto/mailing-query-params.dto';
import { EmailCustomerEntity } from '@entities';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents';
import { PrivilegeService } from '../auth/privilege.service';
import { CompanyType } from '@/shared/enums';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { rawQuery } from '@/shared/utils';

enum EmailCustomerStatus {
  Default = 0,
  UnknownError = 1,
  InvalidEmail = 2,
  Thershold = 3,
  Blacklisted = 4,
  InvalidPhoneNr = 5,
  InvalidTemplate = 6,
  NoMedium = 7,
  Sent = 8,
  SurveyCompleted = 9,
}

const statusField = `
	(CASE 
  WHEN blacklisted = 1 THEN  ${EmailCustomerStatus.Blacklisted}
  WHEN survey_done = 1 THEN   ${EmailCustomerStatus.SurveyCompleted}
	WHEN send_date IS NOT NULL THEN  ${EmailCustomerStatus.Sent}
	WHEN flag_sentthreshold = 1 THEN ${EmailCustomerStatus.Thershold}
	WHEN error_code IS NOT NULL THEN error_code
	ELSE ${EmailCustomerStatus.Default}
	END)
`;

export class MailinglistService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private privilegeService: PrivilegeService,
    private authService: AuthService,
    @InjectRepository(EmailCustomerEntity)
    private readonly emailCustomerRepository: Repository<EmailCustomerEntity>,
  ) {}

  getQuery(params: MailingListBaseDto) {
    const { company, userCompany } = this.cls.get<AuthorizedData>(
      ClsProperty.user,
    );
    let {
      search,
      search_field,
      survey_id,
      project_id,
      statuses,
      startDate,
      endDate,
    } = params;
    this.logger.debug(`getQuery for company ${company.id}`, { params });

    const queryBuilder = this.emailCustomerRepository
      .createQueryBuilder('ec')
      .where(`ec.sec_company_id = :cid`, { cid: company.id })
      .andWhere('ec.creation_date IS NOT NULL');

    if (survey_id?.length) {
      queryBuilder.leftJoin('ec.survey_department', 'sd');
      queryBuilder.andWhere('sd.surv_survey_id IN(:...survey_id)', {
        survey_id,
      });
    }

    if (company.type == CompanyType.BOUW || company.type == CompanyType.SPORT) {
      if (project_id?.length) {
        queryBuilder.andWhere('projects.project_id IN(:...project_id)', {
          project_id,
        });
      }

      queryBuilder.leftJoin('ec.project', 'projects');
      queryBuilder.andWhere(`(projects.project_id NOT IN (
        SELECT project_id
        FROM project_groups)
          OR projects.project_id IN (SELECT project_id
          FROM project_groups
          WHERE group_id = ${userCompany.sec_group}) OR ec.xml_internal_external IS NULL)`);
    }

    if (statuses?.length) {
      let isNull = statuses.findIndex((status) => status === 0) >= 0;
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where(`${statusField} IN(:...statuses)`, { statuses });
          isNull && qb.orWhere(`${statusField} IS NULL`);
          return qb;
        }),
      );
    }

    if (startDate && endDate) {
      queryBuilder.andWhere(
        'ec.creation_date between :startDate AND :endDate',
        { startDate, endDate },
      );
    }

    if (search?.length > 0) {
      if (!search_field?.length)
        search_field = ['xml_email', 'xml_rasnumber', 'xml_phone2'];
      queryBuilder.andWhere(
        new Brackets((qb) => {
          for (let item of search_field) {
            qb.orWhere(`ec.${item} ILIKE :search`, {
              search: `%${search}%`,
            });
          }
        }),
      );
    }

    this.logger.debug(`getQuery query for company ${company.id}`, {
      sql: rawQuery(queryBuilder),
    });

    return queryBuilder;
  }

  async getMailinglist(params: MailingListQueryDto) {
    let { page, search, search_field } = params;
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`getMailinglist for company ${company.id}`, { params });

    if (!search_field) search_field = [];

    const ruleChecks = ['SHOW_CUSTOMER_DATA', 'SHOW_EMPLOYEE_DATA'];

    let rules = await this.privilegeService.filteredUserKeys({
      names: ruleChecks,
    });

    const SHOW_CUSTOMER_DATA = rules.includes('SHOW_CUSTOMER_DATA');

    let allowedFields = [
      'xml_customername',
      'xml_email',
      'xml_rasnumber',
      'xml_phone2',
    ];

    if (!SHOW_CUSTOMER_DATA) {
      allowedFields = [];
    }
    if (search?.length > 0) {
      if (!SHOW_CUSTOMER_DATA)
        throw new HttpException('forbidden', HttpStatus.FORBIDDEN);

      if (!search_field?.length) search_field = ['xml_email'];
    }

    if (search_field.find((item) => !allowedFields.includes(item)))
      throw new HttpException(
        'Search fields must be ' + allowedFields.join(),
        400,
      );
    let fieldsForSelect = metadata
      .filter((v) => v[CompanyType[company.type].toLowerCase()])
      .filter((v) => v.mailinglist);

    fieldsForSelect = await this.privilegeService.restrictedContentFilter(
      fieldsForSelect,
      'columnName',
      ruleChecks,
      rules,
    );

    const queryBuilder = this.getQuery(params);

    const selectStatements = [
      'ec.email_customer_id as id',
      'ec.error_code as error_code',
    ];
    fieldsForSelect.forEach((f) => {
      switch (f.columnName) {
        case 'xml_technician_mapped':
          selectStatements.push(
            "COALESCE(NULLIF(COALESCE(engeneer.techgrp_firstname, '') || ' ' || COALESCE(engeneer.techgrp_name, ''),' '), ec.xml_technician) AS " +
              f.columnName,
          );
          queryBuilder.leftJoin(
            'ec.engineer',
            'engeneer',
            `engeneer.techgrp_type = 'xml_technician' AND ec.sec_company_id = engeneer.sec_company_id`,
          );
          break;
        case 'xml_planning_mapped':
          selectStatements.push(
            "COALESCE(NULLIF(COALESCE(planning.techgrp_firstname, '') || ' ' || COALESCE(planning.techgrp_name, ''),' '), ec.xml_planning) AS " +
              f.columnName,
          );
          queryBuilder.leftJoin(
            'ec.planning',
            'planning',
            `planning.techgrp_type = 'xml_planning' AND ec.sec_company_id = planning.sec_company_id`,
          );

          break;
        case 'xml_acceptance_by_mapped':
          selectStatements.push(
            "COALESCE(NULLIF(COALESCE(callcenter.techgrp_firstname, '') || ' ' || COALESCE(callcenter.techgrp_name, ''),' '), ec.xml_acceptance_by) AS " +
              f.columnName,
          );

          queryBuilder.leftJoin(
            'ec.callcenter',
            'callcenter',
            `callcenter.techgrp_type = 'xml_acceptance_by' AND ec.sec_company_id = callcenter.sec_company_id`,
          );
          break;
        case 'xml_employee_mapped':
          selectStatements.push(
            "COALESCE(NULLIF(COALESCE(employee.techgrp_firstname, '') || ' ' || COALESCE(employee.techgrp_name, ''),' '), ec.xml_employee) AS " +
              f.columnName,
          );
          queryBuilder.leftJoin(
            'ec.counselor',
            'employee',
            `employee.techgrp_type = 'xml_employee' AND ec.sec_company_id = employee.sec_company_id`,
          );
          break;
        case 'xml_dealer_mapped':
          selectStatements.push(
            "COALESCE(NULLIF(COALESCE(dealer.techgrp_firstname, '') || ' ' || COALESCE(dealer.techgrp_name, ''),' '), ec.xml_dealer) AS " +
              f.columnName,
          );
          queryBuilder.leftJoin(
            'ec.dealer',
            'dealer',
            `dealer.techgrp_type = 'xml_dealer' AND ec.sec_company_id = dealer.sec_company_id`,
          );

          break;
        case 'xml_wvb_mapped':
          selectStatements.push(
            "COALESCE(NULLIF(COALESCE(prepep.techgrp_firstname, '') || ' ' || COALESCE(prepep.techgrp_name, ''),' '), ec.xml_wvb) AS " +
              f.columnName,
          );

          queryBuilder.leftJoin(
            'ec.prepep',
            'prepep',
            `prepep.techgrp_type = 'xml_wvb' AND ec.sec_company_id = prepep.sec_company_id`,
          );

          break;
        case 'xml_custcreditnr_mapped':
          selectStatements.push(
            "COALESCE(NULLIF(COALESCE(sp.techgrp_firstname, '') || ' ' || COALESCE(sp.techgrp_name, ''),' '), ec.xml_custcreditnr) AS " +
              f.columnName,
          );

          queryBuilder.leftJoin(
            'ec.service_partner',
            'sp',
            `sp.techgrp_type = 'xml_custcreditnr' AND ec.sec_company_id = sp.sec_company_id`,
          );
          break;
        case 'xml_region_mapped':
          selectStatements.push(
            'COALESCE(map_region.map_description, ec.xml_region) AS ' +
              f.columnName,
          );

          queryBuilder.leftJoin(
            'ec.region',
            'map_region',
            `map_region.map_field = 'xml_region' AND ec.sec_company_id = map_region.sec_company_id`,
          );

          break;
        case 'xml_techgrp_mapped':
          selectStatements.push(
            "COALESCE(NULLIF(COALESCE(engroup.techgrp_firstname, '') || ' ' || COALESCE(engroup.techgrp_name, ''),' '), ec.xml_techgrp) AS " +
              f.columnName,
          );

          queryBuilder.leftJoin(
            'ec.techgrp',
            'engroup',
            `engroup.techgrp_type = 'xml_techgrp' AND ec.sec_company_id = engroup.sec_company_id`,
          );
          break;
        case 'xml_payment_mapped':
          selectStatements.push(
            'COALESCE(map_payment.map_description, ec.xml_payment)  AS ' +
              f.columnName,
          );

          queryBuilder.leftJoin(
            'ec.payment',
            'map_payment',
            `map_payment.map_field = 'xml_payment' AND ec.sec_company_id = map_payment.sec_company_id`,
          );
          break;
        case 'xml_proddivision_mapped':
          selectStatements.push(
            'COALESCE(map_div.map_description, ec.xml_proddivision)  AS ' +
              f.columnName,
          );

          queryBuilder.leftJoin(
            'ec.proddivision',
            'map_div',
            `map_div.map_field = 'xml_proddivision' AND ec.sec_company_id = map_div.sec_company_id`,
          );

          break;
        case 'xml_prodarea_mapped':
          selectStatements.push(
            'COALESCE(map_area.map_description, ec.xml_prodarea)  AS ' +
              f.columnName,
          );

          queryBuilder.leftJoin(
            'ec.prodarea',
            'map_area',
            `map_area.map_field = 'xml_prodarea' AND ec.sec_company_id = map_area.sec_company_id`,
          );

          break;
        case 'sent_status': {
          // leave empty
          break;
        }
        default:
          selectStatements.push(
            `${f.table}.${f.columnName} as ${f.columnName}`,
          );
          break;
      }
    });

    if (company.type == CompanyType.BOUW || company.type == CompanyType.SPORT) {
      queryBuilder.leftJoin('ec.project_stage', 'project_stages');
      queryBuilder.leftJoin('project_stages.contact', 'contacts');
    }

    queryBuilder.select(selectStatements);
    queryBuilder.addSelect(`${statusField} AS sent_status`);
    queryBuilder.leftJoin('ec.survey_answer', 'a');
    queryBuilder.leftJoin('ec.survey', 's');
    queryBuilder.orderBy('ec.creation_date', 'DESC');
    queryBuilder.limit(20);
    queryBuilder.offset((page - 1) * 20);

    let rows: EmailCustomerEntity[] = await queryBuilder.getRawMany();

    let result = {
      data: rows,
      metadata: fieldsForSelect.map((f) => {
        return {
          id: f.id,
          name: f.columnName,
          label: f.label,
          posistion: f.order,
          visible: f.mailinglist_visible,
        };
      }),
      order: 'desc',
    };

    this.logger.debug(`getMailinglist result for company ${company.id}`, {
      result,
    });

    return result;
  }

  async getMailinglistTotal(params: MailingListBaseDto) {
    const queryBuilder = this.getQuery(params);

    const emailCustomersTotal = await queryBuilder.getCount();
    this.logger.debug(`getMailinglistTotal result`, {
      total: emailCustomersTotal,
    });
    return {
      total: emailCustomersTotal,
    };
  }
}
