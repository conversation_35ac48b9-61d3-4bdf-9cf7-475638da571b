import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Min } from 'class-validator';

export class MailingListBaseDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  search: string;

  @ApiProperty({ required: false, type: String })
  @Transform(({ value }: any) =>
    typeof value === 'string'
      ? value?.split(',')?.filter((i) => i.length)
      : value || [],
  )
  @IsOptional()
  search_field: string[];

  @ApiProperty({ required: false, type: String })
  @Transform(({ value }: any) =>
    typeof value === 'string'
      ? value
          ?.split(',')
          ?.filter((i) => i.length)
          .map((i) => +i)
      : value || [],
  )
  @IsOptional()
  survey_id: number[];

  @ApiProperty({ required: false, type: String })
  @Transform(({ value }: any) =>
    typeof value === 'string'
      ? value?.split(',')?.filter((i) => i.length)
      : value || [],
  )
  @IsOptional()
  project_id: string[];

  @ApiProperty({ required: false, type: String })
  @Transform(({ value }: any) =>
    typeof value === 'string'
      ? value
          ?.split(',')
          ?.filter((i) => i.length)
          .map((i) => +i)
      : value || [],
  )
  @IsOptional()
  statuses: number[];

  @ApiProperty({ required: false, type: String })
  @IsOptional()
  startDate: Date;

  @ApiProperty({ required: false, type: String })
  @IsOptional()
  endDate: Date;
}

export class MailingListQueryDto extends MailingListBaseDto {
  @ApiProperty({ required: false })
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page: number;
}
