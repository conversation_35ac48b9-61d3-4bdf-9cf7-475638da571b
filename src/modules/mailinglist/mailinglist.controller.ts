import {
  Controller,
  Delete,
  Get,
  HttpStatus,
  Query,
  Req,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { GdprDeleteQueryDto } from '../gdpr/dto/gdpr-delete-query.dto';
import { GdprService } from '../gdpr/gdpr.service';
import {
  MailingListBaseDto,
  MailingListQueryDto,
} from './dto/mailing-query-params.dto';
import { MailinglistService } from './mailinglist.service';

@ApiTags('Mailinglist')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'mailinglist',
})
export class MailinglistConroller {
  constructor(
    private mailinglistService: MailinglistService,
    private gdprService: GdprService,
  ) {}

  @Get()
  @ApiOkResponse({
    description: 'Mailinglist',
  })
  @UseGuards(AuthGuard)
  getList(@Query() queryParams: MailingListQueryDto) {
    return this.mailinglistService.getMailinglist(queryParams);
  }

  @Get('/total')
  @UseGuards(AuthGuard)
  getListCount(@Query() queryParams: MailingListBaseDto) {
    return this.mailinglistService.getMailinglistTotal(queryParams);
  }

  @Delete()
  @UseGuards(AuthGuard)
  @ApiOkResponse({
    status: HttpStatus.OK,
  })
  @ApiQuery({ name: 'ids', required: true, type: [String] })
  deleteGdrp(
    @Query(new ValidationPipe({ transform: true }))
    { ids }: GdprDeleteQueryDto,
  ) {
    return this.gdprService.deleteGdpr(ids);
  }
}
