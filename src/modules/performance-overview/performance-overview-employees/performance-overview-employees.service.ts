import * as moment from 'moment';
import { VerbatimService } from '../../verbatim/verbatim.service';
import { UserReportsEntity } from '../../database/entities/user-reports.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { queryConvert } from '../../../shared/utils/query-convert';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';

export class PerformanceOverviewEmployeesService {
  constructor(
    private cls: ClsService,
    private readonly verbatimService: VerbatimService,

    @InjectRepository(UserReportsEntity)
    private readonly userReportsEntity: Repository<UserReportsEntity>,
  ) {}

  async getData(params: any = {}) {
    let response: any = {};
    params.deepdive = true;
    params.date_field = [{ id: 1 }];
    params.period = params.periods;

    await this.getContext(params, response);
    response.totalsSend = this.getSurveysSend(params);

    this.createLegend(response);

    let chart = this.getChart();

    chart.data.datasets[1].backgroundColor = '#4d89f9';
    chart.data.datasets[1].borderColor = 'rgba(77, 137, 249, 1)';
    chart.data.datasets[1].data = response.nps;

    chart.data.labels = response.labels;

    let max = Math.ceil(Math.max.apply(Math, response.nps) / 5) * 5;
    let min =
      Math.min.apply(Math, response.nps) > 0
        ? 0
        : Math.floor(Math.min.apply(Math, response.nps) / 5) * 5;

    max = max > 0 ? max : min;

    response.ticks_max = max;
    response.ticks_min = min;

    chart.data.datasets.shift();

    response.chart = chart;

    delete response.nps;
    delete response.labels;
    delete response.totals;
    delete response.totalsSend;

    return response;
  }

  async getSurveysSend(params: any) {
    let sql = `
        SELECT date_trunc($1, ec.send_date) date , count(ec.email_customer_id) count 
            FROM  email_customer ec 
            LEFT JOIN techgrp tg ON ec.xml_technician = tg.techgrp_techn AND tg.techgrp_type = 'xml_technician'
            WHERE ec.sec_company_id = 2  
            AND ec.send_date BETWEEN $2 AND $3 
        `;

    let args = [params.periods[0]?.id, params.from_date, params.to_date];
    if (params.team) {
      sql += ` AND tg.techgrp_dep  = $4`;
      args.push(params.team);
    }

    sql += ` GROUP BY  date ORDER BY date`;

    let data = await this.userReportsEntity.query(sql, args);

    return data.reduce((prev, item) => {
      return { ...prev, [item.date]: item.count };
    }, {});
  }

  createLegend(response) {
    let labels = response.dates;
    let state = response.totalsSend;
    let totals = response.totals;
    response.legends = labels.map((dt) => {
      return {
        NumberSent: (state[dt]?.[0] || 0).toFixed(0),
        NumberResp: (totals[dt] || 0).toFixed(0),
      };
    });
  }

  getChart() {
    return {
      type: 'bar',
      data: {
        labels: [],
        datasets: [
          {
            type: 'line',
            label: '',
            data: [],
            fill: false,
            borderColor: '#E66E0F',
            backgroundColor: '#E66E0F',
            pointBorderColor: '#E66E0F',
            pointBackgroundColor: '#E66E0F',
            pointHoverBackgroundColor: '#E66E0F',
            pointHoverBorderColor: '#000',
            yAxisID: 'y-axis-2',
          },
          {
            label: '',
            backgroundColor: 'rgba(0,0,0,1)',
            borderColor: 'rgba(0,0,0,1)',
            borderWidth: 1,
            hoverBorderColor: 'rgba(0,0,0,1)',
            data: [],
          },
        ],
      },
    };
  }

  async getContext(params: any, response: any) {
    let { chartNPS, chartPeriods, employees } = await this.getChartData(
      params,
      false,
      response,
    );
    response.nps = chartNPS;
    response.labels = chartPeriods;
    response.labelsEmployees = employees;
  }

  async getChartData(params: any, lastYear = false, response: any) {
    let data = await this.getQuery(params, lastYear);
    let nps = {
      nps0: 0,
      nps1: 0,
      nps2: 0,
      nps3: 0,
      nps4: 0,
      nps5: 0,
      nps6: 0,
      nps7: 0,
      nps8: 0,
      nps9: 0,
      nps10: 0,
    };
    let npsTotal = {
      nps0: 0,
      nps1: 0,
      nps2: 0,
      nps3: 0,
      nps4: 0,
      nps5: 0,
      nps6: 0,
      nps7: 0,
      nps8: 0,
      nps9: 0,
      nps10: 0,
    };

    let chartNPS = [];
    let chartPeriods = [];
    let employees = [];
    let label = data[0]?.techgrp_techn;
    let employee = data[0]?.employee;

    for (let [index, item] of Object.entries(data) as any) {
      if (index > 0 && label != item.techgrp_techn) {
        chartNPS.push(this.calcNPS(nps).nps);
        chartPeriods.push(label);
        employees.push(employee);
        for (let key in nps) {
          npsTotal[key] += nps[key];
        }
        nps = {
          nps0: 0,
          nps1: 0,
          nps2: 0,
          nps3: 0,
          nps4: 0,
          nps5: 0,
          nps6: 0,
          nps7: 0,
          nps8: 0,
          nps9: 0,
          nps10: 0,
        };
        label = item.techgrp_techn;
        employee = item.employee;
      }
      nps[`nps${item.return_value}`] += item.count;
      if (!lastYear) {
        //count all the responses for a period
        if (!response.totals[item.techgrp_techn])
          response.totals[item.techgrp_techn] = 0;
        response.totals[item.techgrp_techn] += item.count;
      }
    }

    chartNPS.push(this.calcNPS(nps).nps);
    chartPeriods.push(employee);
    for (let key in nps) {
      npsTotal[key] += nps[key];
    }
    if (!lastYear) {
      response.avg_nps_score = this.calcNPS(nps).nps;
    }

    // if (lastYear) {
    //   let index=0;
    //   let npsList=[]
    //   for(let date of response.dates)
    // }
    return { chartNPS, chartPeriods, employees };
  }

  async getQuery(params: any = {}, lastYear = false) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let sql = `SELECT a.nps_value return_value,tg.techgrp_techn, count(a.nps_value) as count, (tg.techgrp_firstname || ' ' || tg.techgrp_name) as employee`;

    sql += this.verbatimService.getBaseQuery(company.id);
    sql += ` WHERE a.nps_value is not null `;

    let [sqlQuery, sqlParams] = await this.verbatimService.getFilterData(
      params,
      {},
    );
    sql += sqlQuery;

    let selectedPeriods = this.getSelectedPeriods(params);

    //DATES
    //if in the first graph there are already dates selected, use those
    if (selectedPeriods.length > 0) {
      for (let [i, period] of Object.entries(selectedPeriods)) {
        if (+i == 0) {
          sql += ` AND (a.creation_date BETWEEN :from${i} AND :to${i} `;
        } else {
          sql += ` OR a.creation_date BETWEEN :from${i} AND :to${i}`;
        }
        sqlParams[`from${i}`] = period.from;
        sqlParams[`to${i}`] = period.to;
      }
      sql += ' ) ';
    } else {
      sql += ' AND a.creation_date BETWEEN :from1 AND :to1 ';
      sqlParams[`from1`] = params.from_date;
      sqlParams[`to1`] = params.to_date;
    }

    sql +=
      ' GROUP BY tg.techgrp_techn,tg.techgrp_firstname, tg.techgrp_name, a.nps_value order by tg.techgrp_techn';

    let [query, args] = queryConvert(sql, sqlParams);

    let data = this.userReportsEntity.query(query, args);
    return data;
  }

  calcNPS(item) {
    let nps = {
      detractors: 0,
      passives: 0,
      promoters: 0,
      perc_detractors: 0,
      perc_passives: 0,
      perc_promoters: 0,
      total: 0,
      nps: 0,
      nps0: 0,
      nps1: 0,
      nps2: 0,
      nps3: 0,
      nps4: 0,
      nps5: 0,
      nps6: 0,
      nps7: 0,
      nps8: 0,
      nps9: 0,
      nps10: 0,
    };

    if (item?.promoters >= 0) {
      nps.promoters = item.promoters;
      nps.detractors = item.detractors;
      nps.passives = item.passives;
      nps.total = item.total;
    } else {
      for (var i = 0; i <= 10; i++) {
        if (typeof item['nps' + i] !== 'undefined') {
          nps['nps' + i] = item['nps' + i];
          if (i <= 6) {
            nps.detractors += item['nps' + i];
          } else if (i >= 9) {
            nps.promoters += item['nps' + i];
          } else {
            nps.passives += item['nps' + i];
          }
        }
      }
    }

    nps.total = nps.detractors + nps.passives + nps.promoters;
    nps.perc_detractors = nps.total === 0 ? 0 : nps.detractors / nps.total;
    nps.perc_passives = nps.total === 0 ? 0 : nps.passives / nps.total;
    nps.perc_promoters = nps.total === 0 ? 0 : nps.promoters / nps.total;

    nps.nps = Math.round(((nps.promoters - nps.detractors) / nps.total) * 100);
    return nps;
  }

  getSelectedPeriods(params) {
    var vDateFrom, vDateTo;
    let period = params.periods?.[0]?.id;
    let selectedDates = [];
    for (let date of params.dates) {
      switch (period) {
        case 'week':
          vDateFrom = moment(date);
          vDateTo = moment(date).add('1', 'week');
          break;
        case 'day':
          vDateFrom = moment(date).startOf('day');
          vDateTo = moment(date).endOf('day');
          break;
        case 'month':
          vDateFrom = moment(date);
          vDateTo = moment(date).endOf('month');
          break;
        case 'quarter':
          vDateFrom = moment(date);
          vDateTo = moment(date).endOf('quarter');
          break;
        case 'year':
          vDateFrom = moment(date);
          vDateTo = moment(date).endOf('year');
          break;
        default:
          break;
      }

      // begin of end date can not be outside the filters, so check if the date is bigger then the filters
      if (params.from_date > vDateFrom) {
        vDateFrom = params.from_date;
      }
      if (params.to_date < vDateTo) {
        vDateTo = params.to_date;
      }
      selectedDates.push({ from: vDateFrom, to: vDateTo });
    }
    selectedDates.sort(function (a, b) {
      if (a.from > b.from) {
        return 1;
      } else if (a.from == b.from) {
        return 0;
      } else {
        return -1;
      }
    });
    return selectedDates;
  }
}
