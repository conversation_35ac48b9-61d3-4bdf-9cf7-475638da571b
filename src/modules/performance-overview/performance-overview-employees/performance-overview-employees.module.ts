import { Modu<PERSON> } from '@nestjs/common';
import { PerformanceOverviewEmployeesService } from './performance-overview-employees.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChartsModule } from '../../charts/charts.module';
import { VerbatimModule } from '../../verbatim/verbatim.module';
import { UserReportsEntity } from '../../database/entities/user-reports.entity';
import { PerformanceOverviewEmployeesController } from './performance-overview-employees.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserReportsEntity]),
    ChartsModule,
    VerbatimModule,
  ],
  controllers: [PerformanceOverviewEmployeesController],
  providers: [PerformanceOverviewEmployeesService],
})
export class PerformanceOverviewEmployeesModule {
  constructor() {}
}
