import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PerformanceOverviewEmployeesService } from './performance-overview-employees.service';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { AuthorizedRequest } from '../../auth/interfaces/authorized-request.interface';

import { GetPerformanceOverviewBodyDto } from '../dto/performance-overview.dto';

@Controller({
  path: 'performance_overview_employees',
  version: '2',
})
@ApiTags('Performance Overview')
@ApiBearerAuth()
export class PerformanceOverviewEmployeesController {
  constructor(private readonly pService: PerformanceOverviewEmployeesService) {}

  @Post()
  @UseGuards(AuthGuard)
  getPerformanceOverview(@Body() body: GetPerformanceOverviewBodyDto) {
    return this.pService.getData(body);
  }
}
