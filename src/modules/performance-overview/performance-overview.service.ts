import * as moment from 'moment';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { Repository } from 'typeorm';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { VerbatimService } from '../verbatim/verbatim.service';

import { ClsProperty } from '@/config/contents';
import { DateUtilsService, queryConvert } from '@/shared/utils';
import { UserReportsEntity } from '@entities';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class PerformanceOverviewService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private readonly verbatimService: VerbatimService,
    private readonly dateService: DateUtilsService,

    @InjectRepository(UserReportsEntity)
    private readonly userReportsEntity: Repository<UserReportsEntity>,
  ) {}

  async getData(params: any = {}) {
    this.logger.debug(`getData`, { params });
    let response: any = {};
    params.deepdive = true;
    params.date_field = [{ id: 1 }];
    params.period = params.periods;
    let showYear = params.show_previous_year === true ? 1 : 0;
    await this.getContext(params, false, response);
    response.totalsSend = this.getSurveysSend(params);

    // if(showYear){
    //   await this.getContext(params)
    // }
    this.createLegend(response);

    let chart = this.getChart();

    chart.data.datasets[1].backgroundColor = '#4d89f9';
    chart.data.datasets[1].borderColor = 'rgba(77, 137, 249, 1)';
    chart.data.datasets[1].data = response.nps;

    chart.data.labels = response.labels;

    let max = Math.ceil(Math.max.apply(Math, response.nps) / 5) * 5;
    let min =
      Math.min.apply(Math, response.nps) > 0
        ? 0
        : Math.floor(Math.min.apply(Math, response.nps) / 5) * 5;

    if (showYear) {
      let maxYear =
        Math.ceil(Math.max.apply(Math, response.npsLastyear) / 5) * 5;
      let minYear =
        Math.min.apply(Math, response.npsLastyear) > 0
          ? 0
          : Math.floor(Math.min.apply(Math, response.npsLastyear) / 5) * 5;
      max = maxYear > max ? maxYear : max;
      min = minYear < min ? minYear : min;
    }
    max = max > 0 ? max : min;

    response.ticks_max = max;
    response.ticks_min = min;

    if (showYear) {
      chart.data.datasets[0].backgroundColor = 'rgba(230, 110, 15, 1)';
      chart.data.datasets[0].data = response.npsLastyear;
    } else {
      chart.data.datasets.shift();
    }

    response.chart = chart;

    delete response.nps;
    delete response.labels;
    delete response.totals;
    delete response.totalsSend;

    this.logger.debug(`getData result`, { response });

    return response;
  }

  async getSurveysSend(params: any) {
    this.logger.debug(`getSurveysSend`, { params });

    let sql = `
    SELECT date_trunc($1, ec.send_date) date , count(ec.email_customer_id) count 
		FROM  email_customer ec 
		LEFT JOIN techgrp tg ON ec.xml_technician = tg.techgrp_techn AND tg.techgrp_type = 'xml_technician'
		WHERE ec.sec_company_id = 2  
		AND ec.send_date BETWEEN $2 AND $3 
    `;

    let args = [params.periods[0]?.id, params.from_date, params.to_date];
    if (params.team) {
      sql += ` AND tg.techgrp_dep  = $4`;
      args.push(params.team);
    }

    sql += ` GROUP BY  date ORDER BY date`;

    let data = await this.userReportsEntity.query(sql, args);

    let result = data.reduce((prev, item) => {
      return { ...prev, [item.date]: item.count };
    }, {});
    this.logger.debug(`getSurveysSend sql`, { sql, sqlArgs: args });
    this.logger.debug(`getSurveysSend result`, { result });

    return result;
  }

  createLegend(response) {
    let labels = response.dates;
    let state = response.totalsSend;
    let totals = response.totals;
    response.legends = labels.map((dt) => {
      return {
        NumberSent: (state[dt]?.[0] || 0).toFixed(0),
        NumberResp: (totals[dt] || 0).toFixed(0),
      };
    });
  }

  getChart() {
    return {
      type: 'bar',
      data: {
        labels: [],
        datasets: [
          {
            type: 'line',
            label: '',
            data: [],
            fill: false,
            borderColor: '#E66E0F',
            backgroundColor: '#E66E0F',
            pointBorderColor: '#E66E0F',
            pointBackgroundColor: '#E66E0F',
            pointHoverBackgroundColor: '#E66E0F',
            pointHoverBorderColor: '#000',
            yAxisID: 'y-axis-2',
          },
          {
            label: '',
            backgroundColor: 'rgba(0,0,0,1)',
            borderColor: 'rgba(0,0,0,1)',
            borderWidth: 1,
            hoverBorderColor: 'rgba(0,0,0,1)',
            data: [],
          },
        ],
      },
    };
  }

  async getContext(params: any, lastYear = false, response: any) {
    if (!lastYear) {
      response.totals = {};
    }

    let { chartNPS, chartPeriods } = await this.getChartData(
      params,
      lastYear,
      response,
    );
    const { fromDate, toDate } = this.dateService.formatDates({
      fromDate: params.from_date,
      toDate: params.to_date,
    });
    params.from_date = fromDate;
    params.to_date = toDate;
    let date = moment(params.from_date);
    let endDate = moment(params.to_date);
    if (lastYear) {
      response.npsLastYear = [];
      chartPeriods = response.dates;
    } else {
      response.labels = [];
      response.nps = [];
      response.dates = [];
    }

    let period = params.periods?.[0]?.id;

    switch (period) {
      case 'week':
        date = date.startOf('isoWeek');
        break;
      case 'month':
        date = date.startOf('month');
        break;
      case 'quarter':
        date = date.startOf('quarter');
        break;
      case 'year':
        date = date.startOf('year');
        break;
      default:
        break;
    }

    let index = 0;

    while (date <= endDate) {
      if (chartPeriods[index] != date.format()) {
        if (!lastYear) {
          response.nps.push(0);
          response.labels.push(this.dateService.getPeriodLabel(date, period));
          response.dates.push(date.format('YYYY-MM-DD'));
        } else {
          response.npsLastyear.push(0);
        }
      } else {
        if (!lastYear) {
          response.nps.push(chartNPS[index]);
          response.labels.push(
            this.dateService.getPeriodLabel(chartPeriods[index], period),
          );
          response.dates.push(date.format('YYYY-MM-DD'));
        } else {
          response.npsLastyear.push(chartNPS[index]);
        }
        index++;
      }

      switch (period) {
        case 'day':
          date.add(1, 'days');
          break;
        case 'week':
          date.add(7, 'days');
          break;
        case 'month':
          date.add(1, 'months');
          break;
        case 'quarter':
          date.add(3, 'months');
          break;
        case 'year':
          date.add(1, 'years');
          break;
      }
    }
  }

  async getChartData(params: any, lastYear = false, response: any) {
    this.logger.debug(`getChartData `, { params, lastYear });
    let data = await this.getQuery(params, lastYear);
    let nps = {
      nps0: 0,
      nps1: 0,
      nps2: 0,
      nps3: 0,
      nps4: 0,
      nps5: 0,
      nps6: 0,
      nps7: 0,
      nps8: 0,
      nps9: 0,
      nps10: 0,
    };
    let npsTotal = {
      nps0: 0,
      nps1: 0,
      nps2: 0,
      nps3: 0,
      nps4: 0,
      nps5: 0,
      nps6: 0,
      nps7: 0,
      nps8: 0,
      nps9: 0,
      nps10: 0,
    };

    let chartNPS = [];
    let chartPeriods = [];
    let date = data[0]?.dt;

    for (let [index, item] of Object.entries(data) as any) {
      if (index > 0 && date != item.dt) {
        chartNPS.push(this.calcNPS(nps).nps);
        chartPeriods.push(date);
        for (let key in nps) {
          npsTotal[key] += nps[key];
        }
        nps = {
          nps0: 0,
          nps1: 0,
          nps2: 0,
          nps3: 0,
          nps4: 0,
          nps5: 0,
          nps6: 0,
          nps7: 0,
          nps8: 0,
          nps9: 0,
          nps10: 0,
        };
        date = item.dt;
      }
      nps[`nps${item.return_value}`] += item.count;
      if (!lastYear) {
        //count all the responses for a period
        if (!response.totals[item.dt]) response.totals[item.dt] = 0;
        response.totals[item.dt] += item.count;
      }
    }

    chartNPS.push(this.calcNPS(nps).nps);
    chartPeriods.push(date);
    for (let key in nps) {
      npsTotal[key] += nps[key];
    }
    if (!lastYear) {
      response.avg_nps_score = this.calcNPS(nps).nps;
    }

    // if (lastYear) {
    //   let index=0;
    //   let npsList=[]
    //   for(let date of response.dates)
    // }
    this.logger.debug(`getChartData result`, { chartNPS, chartPeriods });

    return { chartNPS, chartPeriods };
  }

  async getQuery(params: any = {}, lastYear = false) {
    this.logger.debug(`getQuery`, { params, lastYear });
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let sql =
      'SELECT a.nps_value return_value, date_trunc(:dt, a.creation_date) dt , count(a.nps_value) count ';

    sql += this.verbatimService.getBaseQuery(company.id);
    sql += ` WHERE ec.survey_done = 1 `;

    if (lastYear && moment(params.from_date).isValid()) {
      params.from_date = moment(params.from_date)
        .subtract(1, 'years')
        .startOf('year')
        .format();
      params.to_date = moment(params.to_date)
        .subtract(1, 'years')
        .endOf('year')
        .format();
    }

    let [sqlQuery, sqlParams] = await this.verbatimService.getFilterData(
      params,
      {},
    );
    sqlParams = { ...sqlParams, dt: params.periods?.[0]?.id || 'week' };
    sql += sqlQuery;

    sql +=
      ' AND a.nps_value is not null  GROUP BY return_value, dt ORDER BY dt';

    let [query, args] = queryConvert(sql, sqlParams);

    let data = this.userReportsEntity.query(query, args);

    this.logger.debug(`getQuery sql`, { sql, sqlArgs: args });
    this.logger.debug(`getQuery result`, { result: data });

    return data;
  }

  calcNPS(item) {
    let nps = {
      detractors: 0,
      passives: 0,
      promoters: 0,
      perc_detractors: 0,
      perc_passives: 0,
      perc_promoters: 0,
      total: 0,
      nps: 0,
      nps0: 0,
      nps1: 0,
      nps2: 0,
      nps3: 0,
      nps4: 0,
      nps5: 0,
      nps6: 0,
      nps7: 0,
      nps8: 0,
      nps9: 0,
      nps10: 0,
    };

    if (item?.promoters >= 0) {
      nps.promoters = item.promoters;
      nps.detractors = item.detractors;
      nps.passives = item.passives;
      nps.total = item.total;
    } else {
      for (var i = 0; i <= 10; i++) {
        if (typeof item['nps' + i] !== 'undefined') {
          nps['nps' + i] = item['nps' + i];
          if (i <= 6) {
            nps.detractors += item['nps' + i];
          } else if (i >= 9) {
            nps.promoters += item['nps' + i];
          } else {
            nps.passives += item['nps' + i];
          }
        }
      }
    }

    nps.total = nps.detractors + nps.passives + nps.promoters;
    nps.perc_detractors = nps.total === 0 ? 0 : nps.detractors / nps.total;
    nps.perc_passives = nps.total === 0 ? 0 : nps.passives / nps.total;
    nps.perc_promoters = nps.total === 0 ? 0 : nps.promoters / nps.total;

    nps.nps = Math.round(((nps.promoters - nps.detractors) / nps.total) * 100);
    return nps;
  }
}
