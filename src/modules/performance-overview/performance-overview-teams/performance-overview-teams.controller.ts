import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PerformanceOverviewTeamsService } from './performance-overview-teams.service';
import { AuthGuard } from '@/shared/guards/auth.guard';

import { GetPerformanceOverviewTeamsBodyDto } from './dto/performance-overview-teams.dto';

@Controller({
  path: 'performance_overview_teams',
  version: '2',
})
@ApiTags('Performance Overview')
@ApiBearerAuth()
export class PerformanceOverviewTeamsController {
  constructor(private readonly ptService: PerformanceOverviewTeamsService) {}

  @Post()
  @UseGuards(AuthGuard)
  getPerformanceOverview(@Body() body: GetPerformanceOverviewTeamsBodyDto) {
    return this.ptService.getData(body);
  }
}
