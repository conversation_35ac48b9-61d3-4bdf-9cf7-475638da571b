import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PerformanceOverviewTeamsController } from './performance-overview-teams.controller';
import { PerformanceOverviewTeamsService } from './performance-overview-teams.service';
import { UserReportsEntity } from '../../database/entities/user-reports.entity';
import { ChartsModule } from '../../charts/charts.module';
import { VerbatimModule } from '../../verbatim/verbatim.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('performance-overview-teams'),
    TypeOrmModule.forFeature([UserReportsEntity]),
    ChartsModule,
    VerbatimModule,
  ],
  controllers: [PerformanceOverviewTeamsController],
  providers: [PerformanceOverviewTeamsService],
})
export class PerformanceOverviewTeamsModule {}
