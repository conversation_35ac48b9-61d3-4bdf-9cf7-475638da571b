import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PerformanceOverviewController } from './performance-overview.controller';
import { PerformanceOverviewService } from './performance-overview.service';
import { UserReportsEntity } from '../database/entities/user-reports.entity';
import { ChartsModule } from '../charts/charts.module';
import { VerbatimModule } from '../verbatim/verbatim.module';
import { DateUtilsService } from '../../shared/utils/date-utils.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('performance-overview'),
    TypeOrmModule.forFeature([UserReportsEntity]),
    ChartsModule,
    VerbatimModule,
  ],
  controllers: [PerformanceOverviewController],
  providers: [PerformanceOverviewService, DateUtilsService],
  exports: [PerformanceOverviewService],
})
export class PerformanceOverviewModule {}
