import {
  All,
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PerformanceOverviewService } from './performance-overview.service';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { GetPerformanceOverviewBodyDto } from './dto/performance-overview.dto';

@Controller({
  path: 'performance_overview',
  version: '2',
})
@ApiTags('Performance Overview')
@ApiBearerAuth()
export class PerformanceOverviewController {
  constructor(private readonly pService: PerformanceOverviewService) {}

  @Post()
  @UseGuards(AuthGuard)
  getPerformanceOverview(@Body() body: GetPerformanceOverviewBodyDto) {
    return this.pService.getData(body);
  }
}
