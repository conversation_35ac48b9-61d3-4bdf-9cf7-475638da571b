import { HttpException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { UpsertGroupBodyDto } from './dto/post-group.dto';

import { SecGroupEntity, SecUserRight } from '@entities';
import { ClsProperty } from '@/config/contents';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

export class GroupService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(SecGroupEntity)
    private readonly secGroupRepo: Repository<SecGroupEntity>,
    @InjectRepository(SecUserRight)
    private readonly secUserRightRepo: Repository<SecUserRight>,
  ) {}

  async getAllGroups(sec_company_id: number) {
    this.logger.debug(`getAllGroups for company ${sec_company_id}`);
    let list = await this.secGroupRepo.query(
      `SELECT g.group_id, g.name, array_agg(key_id) AS keys
    FROM sec_group g
    JOIN sec_user_right r USING (group_id)
    WHERE sec_company_id = $1
    GROUP BY group_id, name
    ORDER BY name`,
      [sec_company_id],
    );

    this.logger.debug(`getAllGroups result for company ${sec_company_id}`, {
      list,
    });

    return list;
  }

  async createGroup(params: UpsertGroupBodyDto) {
    const { name, keys } = params.group;
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`createGroup ${name}`, { name, keys, company });

    let group = await this.secGroupRepo
      .createQueryBuilder()
      .insert()
      .values({ name: name, sec_company_id: company.id })
      .returning('group_id')
      .execute();
    let group_id = group.generatedMaps[0].group_id;
    let savedKeys = await this.secUserRightRepo
      .createQueryBuilder()
      .insert()
      .values(keys.map((key_id) => ({ group_id, key_id })))
      .returning('user_right_id')
      .execute();

    this.logger.debug(`createGroup result ${name}`, { group, savedKeys });

    return {
      group_id,
      key_ids: savedKeys.generatedMaps.map((i) => i.user_right_id),
    };
  }

  async updateGroup(params: UpsertGroupBodyDto) {
    const { id, name, keys } = params.group;
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`updateGroup ${name}`, { params, company });

    let group = await this.secGroupRepo
      .createQueryBuilder()
      .select()
      .where('group_id =:id', { id })
      .andWhere('sec_company_id =:cid', { cid: company.id })
      .getOne();

    if (!group) throw new HttpException('group is not found', 404);

    await this.secGroupRepo
      .createQueryBuilder()
      .update({ name: name })
      .where('group_id =:id', { id })
      .execute();

    await this.secUserRightRepo
      .createQueryBuilder()
      .delete()
      .where('group_id =:id', { id })
      .execute();

    let savedKeys = await this.secUserRightRepo
      .createQueryBuilder()
      .insert()
      .values(keys.map((key_id) => ({ group_id: id, key_id })))
      .returning('user_right_id')
      .execute();

    this.logger.debug(`updateGroup result ${name}`, { group, savedKeys });

    return {
      group_id: id,
      key_ids: savedKeys.generatedMaps.map((i) => i.user_right_id),
    };
  }
}
