import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { UpsertGroupBodyDto } from './dto/post-group.dto';
import { GroupService } from './groups.service';

@ApiTags('Security Groups')
@ApiBearerAuth()
@Controller({
  path: 'groups',
  version: '2',
})
export class GroupsController {
  constructor(private groupService: GroupService) {}

  @Get()
  @UseGuards(AuthGuard)
  getGroups(@Req() { company }: AuthorizedRequest) {
    return this.groupService.getAllGroups(company.id);
  }

  @Post()
  @UseGuards(AuthGuard)
  createGroup(@Body() body: UpsertGroupBodyDto) {
    return this.groupService.createGroup(body);
  }

  @Post(':id')
  @UseGuards(AuthGuard)
  updateGroup(
    @Body() body: UpsertGroupBodyDto,
    @Req() { user }: AuthorizedRequest,
    @Param('id') id: number,
  ) {
    body.group.id = id;
    return this.groupService.updateGroup(body);
  }
}
