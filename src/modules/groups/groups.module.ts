import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GroupsController } from './groups.controller';
import { GroupService } from './groups.service';
import { SecGroupEntity, SecUserRight } from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SecGroupEntity, SecUserRight]),
    ModuleLoggerModule.register(`groups`),
  ],
  controllers: [GroupsController],
  providers: [GroupService],
})
export class GroupsModule {}
