import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>N<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class GroupBodyDto {
  @Type()
  @IsOptional()
  @IsNumber()
  id?: number = null;

  @Type()
  @IsString()
  @ApiProperty({ required: true })
  name: string;

  @Type()
  @IsArray()
  @ApiProperty({ required: true, type: Number, isArray: true })
  keys: number[];
}
export class UpsertGroupBodyDto {
  @Type()
  @ApiProperty({ required: true })
  group: GroupBodyDto;
}
