import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, DeepPartial, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import { GetClfArchiveBodyDto } from './dto/post-clf-archive.dto';
import { UpdateClfItem } from './dto/post-clf.dto';

import * as moment from 'moment';

import { UsersService } from '../users/users.service';
import { FeedbackClfService } from './feedback-clf.service';
import { AuthService } from '../auth/auth.service';
import { DBHelperService } from '../core/db-helper/db-helper.service';
import {
  ClfCallEntity,
  ClfCallNoteEntity,
  ClfFavoritesEntity,
  ClfTagsEntity,
  ContactEntity,
  DatatableEntity,
  EmailCustomerEntity,
  MClassResponse,
  ProjectsEntity,
  SurvSurveyEntity,
  SurveyAnswerEntity,
  SurveyAnswerItemEntity,
  TagsEntity,
  TechgrpEntity,
  UserEntity,
} from '@entities';
import { PrivilegeService } from '../auth/privilege.service';
import { RESTRICTED_CONTENT } from '@/config/restricted_content';
import { ClfNoteType, ClfStatus, CompanyType } from '@/shared/enums';
import { aliasPrefix, queryConvert, rawQuery } from '@/shared/utils';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class ClfService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private privilegeService: PrivilegeService,
    private feedbackClfService: FeedbackClfService,
    private userService: UsersService,
    private dbHelper: DBHelperService,
    @InjectRepository(ClfCallEntity)
    private readonly clfRepo: Repository<ClfCallEntity>,
    @InjectRepository(ClfCallNoteEntity)
    private readonly clfNoteRepo: Repository<ClfCallNoteEntity>,
    @InjectRepository(ClfFavoritesEntity)
    private readonly clfFavRepo: Repository<ClfFavoritesEntity>,
    @InjectRepository(ClfTagsEntity)
    private readonly clfTagsRepo: Repository<ClfTagsEntity>,
    @InjectRepository(TagsEntity)
    private readonly tagsRepo: Repository<TagsEntity>,
    @InjectRepository(ClfCallEntity)
    private readonly clfCallRepo: Repository<ClfCallEntity>,
    @InjectRepository(MClassResponse)
    private readonly mResponseRepo: Repository<MClassResponse>,
  ) {}

  async getClfDetails(id: number) {
    const { user, company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`getClfDetails cldId: ${id}, company: ${company}`);
    const removeFields = [
      'dispatch_firstname',
      'dispatch_name',
      'prepep_firstname',
      'prepep_name',
      'counselor_firstname',
      'counselor_name',
      'front_line_agent_firstname',
      'front_line_agent_name',
      'dealer_firstname',
      'dealer_name',
      'engineer_firstname',
      'engineer_name',
      'service_partner_firstname',
      'service_partner_name',
    ];

    const concatNames = {
      dispatch: ['dispatch_firstname', 'dispatch_name'],
      prepep: ['prepep_firstname', 'prepep_name'],
      counselor: ['counselor_firstname', 'counselor_name'],
      front_line_agent: ['front_line_agent_firstname', 'front_line_agent_name'],
      dealer: ['dealer_firstname', 'dealer_name'],
      engineer: ['engineer_firstname', 'engineer_name'],
      service_partner: ['service_partner_firstname', 'service_partner_name'],
    };

    let selectFields = [
      'cc.clf_call_id AS clf_call_id',
      'cc.status AS status',
      'ss.internal_name AS survey',
      'xml_brand AS brand',
      'a.creation_date AS response_date',
      'ec.xml_readydate AS visit_date',
      'xml_enumber AS e_no',
      'ec.send_date as send_date',
      'a.surv_surveyanswer_id AS answer_id',
      'ec.surv_survey_id AS survey_id',
      'cc.assigned_to AS assigned_to_id',
      'assigned_to_user.sec_user_id AS assigned_to',
      "CONCAT_WS(' ', assigned_to_user.firstname, assigned_to_user.username) AS assigned_to_username",
      "COALESCE(cc.clf_summary, 'No summary') AS summary",
      'ec.xml_initials AS customer_initials',
      'ec.xml_customername AS customer_name',
      'u_assigned_by.username AS assigned_by',
      'sent_medium AS medium',
      'a.nps_value AS nps_value',
      'pos.return_value AS pos',
      'neg.return_value AS neg',
      'COALESCE(map_region.map_description, xml_region) AS region',
      'COALESCE(map_product_group.map_description, xml_prodarea) AS product_group',
      'COALESCE(map_product_division.map_description, xml_proddivision) AS product_division',
      'xml_phone2 AS phone',
      'xml_email AS email',
      'xml_language AS language',
      "CASE WHEN xml_la = '01' OR xml_la = '83' OR xml_la = '84' THEN 'in warranty' ELSE 'out of warranty' END AS warranty",
      't_dispatch.techgrp_firstname AS dispatch_firstname',
      'COALESCE(t_dispatch.techgrp_name, xml_planning) AS dispatch_name',
      't_preprep.techgrp_firstname AS preprep_firstname',
      'COALESCE(t_preprep.techgrp_name, xml_wvb) AS preprep_name',
      't_counselor.techgrp_firstname AS counselor_firstname',
      'COALESCE(t_counselor.techgrp_name, xml_employee) AS counselor_name',
      't_front_line_agent.techgrp_firstname AS front_line_agent_firstname',
      'COALESCE(t_front_line_agent.techgrp_name, xml_acceptance_by) AS front_line_agent_name',
      't_engineer.techgrp_firstname AS engineer_firstname',
      'COALESCE(t_engineer.techgrp_name, xml_technician) AS engineer_name',
      't_dealer.techgrp_firstname AS dealer_firstname',
      'COALESCE(t_dealer.techgrp_name, xml_dealer) AS dealer_name',
      't_service_partner.techgrp_firstname AS service_partner_firstname',
      'COALESCE(t_service_partner.techgrp_name, xml_custcreditnr) AS service_partner_name',
      'COALESCE(c.object_city, xml_city) AS city',
      'COALESCE(c.object_postal_code, xml_enumber) AS postal_code',
      'COALESCE(c.object_address, xml_address) AS address',
      'a.device AS device',
      'a.os AS os',
    ];

    let privileges = await this.privilegeService.filteredKeys({
      userId: user.id,
      companyId: company.id,
      filter: { names: ['SHOW_CUSTOMER_DATA', 'SHOW_EMPLOYEE_DATA'] },
    });
    const SHOW_CUSTOMER_DATA = privileges.includes('SHOW_CUSTOMER_DATA');
    const SHOW_EMPLOYEE_DATA = privileges.includes('SHOW_EMPLOYEE_DATA');

    if (!SHOW_CUSTOMER_DATA) {
      selectFields = selectFields.filter((field) => {
        return !RESTRICTED_CONTENT.SHOW_CUSTOMER_DATA.find((val) =>
          field.includes(val),
        );
      });
    }
    if (!SHOW_EMPLOYEE_DATA) {
      selectFields = selectFields.filter((field) => {
        return !RESTRICTED_CONTENT.SHOW_EMPLOYEE_DATA.find((val) =>
          field.includes(val),
        );
      });
    }

    const queryBuilder = this.clfCallRepo
      .createQueryBuilder('cc')
      .select(selectFields)
      .leftJoin('cc.assigned_to_user', 'assigned_to_user')
      .leftJoin(
        EmailCustomerEntity,
        'ec',
        'cc.email_customer_id = ec.email_customer_id',
      )
      .leftJoin(SurvSurveyEntity, 'ss', 'ec.surv_survey_id = ss.surv_survey_id')
      .leftJoin(
        SurveyAnswerEntity,
        'a',
        'cc.email_customer_id = a.email_customer_id',
      )
      .leftJoin(
        SurveyAnswerItemEntity,
        'pos',
        'a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND ss.clf_question2 = pos.surv_surveyquestion_id',
      )
      .leftJoin(
        SurveyAnswerItemEntity,
        'neg',
        'a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND ss.clf_question1 = neg.surv_surveyquestion_id',
      )
      .leftJoin(
        UserEntity,
        'u_assigned_by',
        'cc.assigned_by = u_assigned_by.sec_user_id',
      )
      .leftJoin(
        DatatableEntity,
        'map_region',
        "ec.xml_region = map_region.map_key AND map_region.map_field = 'xml_region' AND ec.sec_company_id = map_region.sec_company_id",
      )
      .leftJoin(
        DatatableEntity,
        'map_product_group',
        "ec.xml_prodarea = map_region.map_key AND map_product_group.map_field = 'xml_prodarea' AND ec.sec_company_id = map_product_group.sec_company_id",
      )
      .leftJoin(
        DatatableEntity,
        'map_product_division',
        "ec.xml_proddivision = map_region.map_key AND map_product_division.map_field = 'xml_proddivision' AND ec.sec_company_id = map_product_division.sec_company_id",
      )
      .leftJoin(
        TechgrpEntity,
        't_dispatch',
        "ec.xml_planning = t_dispatch.techgrp_techn AND t_dispatch.techgrp_type = 'xml_planning' AND ec.sec_company_id = t_dispatch.sec_company_id",
      )
      .leftJoin(
        TechgrpEntity,
        't_preprep',
        "ec.xml_wvb = t_preprep.techgrp_techn AND t_preprep.techgrp_type = 'xml_wvb' AND ec.sec_company_id = t_preprep.sec_company_id",
      )
      .leftJoin(
        TechgrpEntity,
        't_counselor',
        "ec.xml_employee = t_counselor.techgrp_techn AND t_counselor.techgrp_type = 'xml_employee' AND ec.sec_company_id = t_counselor.sec_company_id",
      )
      .leftJoin(
        TechgrpEntity,
        't_front_line_agent',
        "ec.xml_acceptance_by = t_front_line_agent.techgrp_techn AND t_front_line_agent.techgrp_type = 'xml_acceptance_by' AND ec.sec_company_id = t_front_line_agent.sec_company_id",
      )
      .leftJoin(
        TechgrpEntity,
        't_engineer',
        "ec.xml_technician = t_engineer.techgrp_techn AND t_engineer.techgrp_type = 'xml_technician' AND ec.sec_company_id = t_engineer.sec_company_id",
      )
      .leftJoin(
        TechgrpEntity,
        't_dealer',
        "ec.xml_dealer = t_dealer.techgrp_techn AND t_dealer.techgrp_type = 'xml_dealer' AND ec.sec_company_id = t_dealer.sec_company_id",
      )
      .leftJoin(
        TechgrpEntity,
        't_service_partner',
        "ec.xml_custcreditnr = t_service_partner.techgrp_techn AND t_service_partner.techgrp_type = 'xml_custcreditnr' AND ec.sec_company_id = t_service_partner.sec_company_id",
      )
      .leftJoin(ProjectsEntity, 'p', 'ec.xml_internal_external = p.project_id')
      .leftJoin(
        'project_stages',
        'ps',
        'ec.email_customer_id = ps.email_customer_id',
      )
      .leftJoin(ContactEntity, 'c', 'ps.contact_id = c.contact_id')
      .where('cc.clf_call_id = :id', { id });

    if (company.type === CompanyType.BSH) {
      queryBuilder.addSelect(['ec.xml_rasnumber AS ris_number']);
    }

    if ([CompanyType.BOUW, CompanyType.SPORT].includes(company.type)) {
      queryBuilder.addSelect([
        'p.project_nr AS project_no',
        'p.project_name AS project_name',
        'c.object_buildnr AS build_no',
        'COALESCE(c.object_house_number, xml_techgrp::integer) AS house_no',
      ]);
    }

    const item = await queryBuilder.getRawOne();

    const [notes, tags, classification] = await Promise.all([
      this.getClfNotes(id),
      this.getClfTags(id),
      this.getClassificationResponse(item.answer_id),
    ]);

    for (let [key, val] of Object.entries(concatNames)) {
      item[key] = this.concatName(...val.map((v) => item[v]));
    }

    for (let key in item) {
      if (removeFields.includes(key)) delete item[key];
    }

    this.logger.debug(`getClfDetails sql`, { sql: rawQuery(queryBuilder) });
    this.logger.debug(`getClfDetails result`, {
      item,
      notes,
      tags,
      classification,
    });

    return {
      ...item,
      assigned_to: {
        id: item.assigned_to,
        name: item.assigned_to_username,
      },
      response_date: item.response_date
        ? moment(item.response_date).format('DD-MM-YYYY HH:mm')
        : null,
      send_date: item.send_date
        ? moment(item.send_date).format('DD-MM-YYYY HH:mm')
        : null,
      notes: notes.slice(0, 3),
      tag_arr: tags,
      classification,
      status: item.status
        ? { id: item.status, name: ClfStatus[item.status] }
        : null,
      assigned_to_id: undefined,
      assigned_to_username: undefined,
    };
  }

  async getClfNotes(id) {
    this.logger.debug(`getClfNotes ${id}`);
    let list = await this.clfNoteRepo
      .createQueryBuilder('n')
      .select([
        'n.note AS note',
        'n.note_type AS note_type',
        'n.creation_date AS creation_date',
        'u.avatar AS avatar',
        'u.avatar_type AS avatar_type',
        "CONCAT_WS(' ', u.firstname, u.username) AS username",
        "CONCAT_WS(' ', u2.firstname, u2.username) AS assigned_to",
      ])
      .leftJoin(UserEntity, 'u', 'u.sec_user_id = n.creator')
      .leftJoin(UserEntity, 'u2', 'u2.sec_user_id = n.assigned_to')
      .where('n.clf_call_id = :id', { id })
      .orderBy('n.clf_call_note_id', 'DESC')
      .getRawMany();
    this.logger.debug(`getClfNotes result`, { list });
    return list;
  }

  async getClfTags(id) {
    this.logger.debug(`getClfTags ${id}`);
    let list = await this.clfTagsRepo
      .createQueryBuilder('ct')
      .select(aliasPrefix(['t.tag_id', 't.tag_name']))
      .leftJoin('ct.tag', 't')
      .where('ct.clf_call_id = :id', { id })
      .getRawMany();

    this.logger.debug(`getClfTags result`, { list });
    return list;
  }

  async getClassificationResponse(id: number) {
    this.logger.debug(`getClassificationResponse ${id}`);
    if (!id) return [];

    let query = this.mResponseRepo
      .createQueryBuilder('mr')
      .select(
        aliasPrefix([
          'COALESCE(mil.item_label_locale, m.item_label) AS label',
          'istype',
          'is_liked',
          'is_potential',
          'is_excluded',
        ]),
      )
      .leftJoin('mr.item', 'm')
      .leftJoin('mr.item_local', 'mil', 'mil.sec_company_id = 2')
      .where('mr.answer_id = :id', { id })
      .andWhere('mr.record_status IN (:...status)', { status: [1, 2, 4] });
    let list = await query.getRawMany();
    this.logger.debug(`getClassificationResponse result ${id}`, { list });

    return list;
  }

  private concatName(firstname = null, name = null) {
    let resp = '';
    if (firstname?.length > 0) resp += firstname;
    if (name?.length > 0) resp += name;
    return resp.length > 0 ? resp : null;
  }

  async updateClf(items: UpdateClfItem[]) {
    this.logger.debug(`updateClf`, { items });
    const { user: modifier, company } = this.cls.get<AuthorizedData>('user');

    let userDetails = await this.userService.getUser(modifier.id);
    this.cls.set('user-details', userDetails);

    let resp = {
      newNotes: [],
      messages: [],
    };

    for (let item of items) {
      const { clf_call_id } = item;

      let record = await this.clfRepo
        .createQueryBuilder()
        .where('clf_call_id =:id', { id: clf_call_id })
        .getOne();
      if (!record) continue;
      if (item.summary?.length > 0) {
        record.clf_summary = item.summary;
        let note = `Summary is changed to: ${item.summary}`;
        resp.newNotes.push(
          await this.createClfNote(item, note, ClfNoteType.Summary),
        );
        resp.messages.push('Summary Added');
      }
      if (item.comment?.length > 0) {
        let note = item.comment;
        resp.newNotes.push(
          await this.createClfNote(item, note, ClfNoteType.Usernote),
        );
        resp.messages.push('Notes added');
      }

      if (item.assigned_to?.id) {
        record.assigned_to = item.assigned_to.id;
        record.assigned_date = new Date();
        record.assigned_by = modifier.id;
        record.modification_date = new Date();
        let note = `Assigned this call to ${item.assigned_to.name}`;
        resp.newNotes.push(
          await this.createClfNote(item, note, ClfNoteType.Assigned),
        );
        resp.messages.push(note);
      }

      if (item.tags?.length > 0) {
        await this.clfTagsRepo
          .createQueryBuilder()
          .delete()
          .where('clf_call_id =:id', { id: clf_call_id });
        await this.tagsRepo.save(
          item.tags.map((tagItem) => {
            let tag: DeepPartial<TagsEntity> = {
              tag_name: tagItem.name,
              created_by: modifier.id,
              created_on: moment().format(),
              sec_company_id: company.id,
              tag_type: 'clf',
            };
            if (tagItem.id) {
              tag.tag_id = tagItem.id;
            }
            return tag;
          }),
        );
        await this.clfTagsRepo.save(
          item.tags.map((i) => {
            let obj: DeepPartial<ClfTagsEntity> = {
              clf_call_id,
              tag_id: i.id,
              created_by: modifier.id,
              created_on: moment().format(),
            };
            return obj;
          }),
        );

        let note = `user updated following tags: ${item.tags.map(
          (i) => i.name,
        )}`;
        resp.newNotes.push(
          await this.createClfNote(item, note, ClfNoteType.Tag),
        );
        resp.messages.push('Tags updated');
      }

      if (item.remove_assigned_to) {
        record.assigned_to = null;
        record.assigned_date = null;
        resp.messages.push('Removed assigned to');
      }

      if (item.status?.id) {
        record.status = item.status.id;
        let stsName = ClfStatus[record.status];
        if (stsName == 'Closed') {
          record.result = item.result;
          record.credit = item.credit;
          record.closed_by = modifier.id;
          record.closed_by_date = new Date();

          resp.newNotes.push(
            await this.createClfNote(
              item,
              'Call is closed',
              ClfNoteType.Closed,
            ),
          );
        } else if (stsName == 'Declined') {
          record.declined_by = modifier.id;
          record.declined_by_date = new Date();
          record.assigned_to = null;
          record.assigned_date = null;
          let note = `Call is declined. Reason:<br>\r\n ${item.reason}`;
          resp.newNotes.push(
            await this.createClfNote(item, note, ClfNoteType.Closed),
          );
        }
        resp.messages.push('Status changed');
      }

      if (item.is_favorite === true) {
        let isExists = await this.clfFavRepo
          .createQueryBuilder()
          .where('sec_user_id =:uid', { uid: modifier.id })
          .andWhere('call_id =:id', { id: clf_call_id })
          .getOne();
        if (!isExists) {
          await this.clfFavRepo
            .createQueryBuilder()
            .insert()
            .values({
              call_id: clf_call_id,
              sec_user_id: modifier.id,
            })
            .execute();
        }
        let note = `Call marked as favorite`;
        resp.newNotes.push(
          await this.createClfNote(item, note, ClfNoteType.Favorite),
        );
        resp.messages.push(note);
      } else if (item.is_favorite === false) {
        await this.clfFavRepo
          .createQueryBuilder()
          .delete()
          .where('sec_user_id =:uid', { uid: modifier.id })
          .andWhere('call_id =:clf_call_id', { clf_call_id })
          .execute();

        let note = `Call unmarked as favorite`;
        resp.newNotes.push(
          await this.createClfNote(item, note, ClfNoteType.Favorite),
        );
        resp.messages.push(note);
      }
    }
    this.logger.debug(`updateClf result`, { resp });
    return resp;
  }
  async createClfNote(
    clfItem: UpdateClfItem,
    note: string,
    note_type: ClfNoteType,
  ) {
    this.logger.debug(`createClfNote`, { clfItem, note, note_type });
    const user = this.cls.get<UserEntity>('user-details');
    const { company } = this.cls.get<AuthorizedData>('user');

    const sec_company_id = company.id;

    const { clf_call_id } = clfItem;
    let nextId = await this.dbHelper.getNextId(
      ClfCallNoteEntity,
      'clf_call_note_id',
    );
    let result = await this.clfNoteRepo.createQueryBuilder().insert().values({
      clf_call_note_id: nextId,
      clf_call_id,
      note_type,
      sec_company_id,
      note,
      creation_date: new Date(),
    });
    let userFullName = this.userService.getFullName(user);
    let userAvatar = this.userService.getAvatar(user.avatar, user.avatar_type);

    let response = {
      posted_by: userFullName,
      assigned_to: clfItem.assigned_to?.name,
      note,
      posted_by_avatar: userAvatar,
      note_type,
      creation_date: moment().format('dd-MM-yyyy HH:mm:ss'),
    };

    this.logger.debug(`createClfNote result`, { response, result });
    return response;
  }

  async getClfArchive(body: GetClfArchiveBodyDto) {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`getClfArchive`, { body });

    let privileges = await this.privilegeService.filteredKeys({
      userId: user.id,
      companyId: company.id,
      filter: { names: ['SHOW_CUSTOMER_DATA'] },
    });
    const SHOW_CUSTOMER_DATA = privileges.includes('SHOW_CUSTOMER_DATA');

    if (!body.sort_field?.length)
      body.sort_field = [{ id: '3', name: 'Response Date' }];
    if (!body.sort_order?.length) body.sort_order = 'desc';
    let { quicksearch, page, count, sort_field, sort_order } = body;

    if (quicksearch) return this.clfQuickSearch(body, privileges);
    let sqlArgs: any = {};
    let filterSql = this.getCflArchiveFilter(body, sqlArgs, privileges);
    filterSql += await this.feedbackClfService.getFilter(body, sqlArgs);

    let sortSql = sort_field.map((i) => {
      let id = +i.id;
      if (id == 1)
        return `ec.xml_readydate ORDER BY ec.xml_readydate ${sort_order} NULLS LAST `;
      else if (id == 2)
        return `ec.send_date ORDER BY ec.send_date ${sort_order} NULLS LAST `;
      else if (id == 3)
        return `ORDER BY clf.assigned_date ${sort_order} NULLS LAST, response_date ${sort_order} `;
      else if (id == 4)
        return `lf.modification_date ORDER BY clf.modification_date ${sort_order} NULLS LAST `;
      else if (id == 5)
        return `clf.closed_by_date ORDER BY clf.closed_by_date ${sort_order} NULLS LAST `;
    });

    let sql = `
    SELECT clf.clf_call_id,
    clf.clf_summary as summary,
    a.nps_value,
    a.creation_date                                                     AS response_date,
    pos.return_value                                                    AS pos,
    neg.return_value                                                    AS neg,
    clf.status,
    CONCAT_WS(' ', sud.firstname, sud.username) AS declined_by_user,
    CONCAT_WS(' ', suc.firstname, suc.username) AS closed_by_user,
    CONCAT_WS(' ', sua.firstname, sua.username) AS assigned_to_user,
    sud.avatar AS declined_by_avatar,
    suc.avatar AS closed_by_avatar,
    sua.avatar AS assigned_to_avatar,
    sud.avatar_type AS declined_by_avatar_type,
    suc.avatar_type AS closed_by_avatar_type,
    sua.avatar_type AS assigned_to_avatar_type,
    declined_by as declined_by_id,
    closed_by as closed_by_id,
    assigned_to as assigned_to_id, 
    concat_ws(' ', ec.xml_initials, ec.xml_customername)                as customername,
    CASE WHEN clf_favorite_id IS NULL THEN FALSE ELSE TRUE END          AS is_favorite,
    (select json_agg(
            json_build_object(
                'id', t.tag_id,
                'name', t.tag_name
              ))
   from clf_tags ct
   left join tags t on ct.tag_id = t.tag_id
   where ct.clf_call_id = clf.clf_call_id)                            as tag_arr,
    s.internal_name                                                     AS touchpoint,
    a.surv_surveyanswer_id                                              AS answer_id
FROM clf_call clf
LEFT JOIN email_customer ec ON clf.email_customer_id = ec.email_customer_id
LEFT JOIN surv_surveyanswer a ON clf.email_customer_id = a.email_customer_id
LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id
LEFT JOIN sec_users sua ON sua.sec_user_id = assigned_to
LEFT JOIN sec_users suc ON suc.sec_user_id = closed_by
LEFT JOIN sec_users sud ON sud.sec_user_id = declined_by
LEFT JOIN clf_tags ct ON ct.clf_call_id = clf.clf_call_id
LEFT JOIN surv_surveyansweritem pos
       ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND s.clf_question2 = pos.surv_surveyquestion_id
LEFT JOIN surv_surveyansweritem neg
       ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND s.clf_question1 = neg.surv_surveyquestion_id
LEFT JOIN surv_surveyansweritem callback ON a.surv_surveyanswer_id = callback.surv_surveyanswer_id AND
                     s.callback_question = callback.surv_surveyquestion_id
LEFT JOIN clf_favorites fav ON clf.clf_call_id = fav.call_id AND fav.sec_user_id = ${
      user.id
    }
WHERE ec.survey_done = 1

${filterSql}

GROUP BY clf.clf_call_id, a.nps_value, a.creation_date, neg.return_value, pos.return_value, sud.firstname, sud.username,
    suc.firstname, suc.username,
    sua.firstname, sua.username, suc.username, sua.avatar, sud.avatar, suc.avatar, sua.avatar_type,
    sud.avatar_type, suc.avatar_type, clf_favorite_id, ec.xml_initials, ec.xml_customername, s.internal_name,
    a.surv_surveyanswer_id 
    

    ${sortSql.join(',')}         
  LIMIT :limit OFFSET :offset
    `;
    let queryParams = {
      ...(sqlArgs || {}),
      limit: count,
      offset: page * count,
    };

    queryParams.limit = count;
    queryParams.offset = page * count;

    const [query, queryArgs] = queryConvert(sql, queryParams);

    let list = await this.clfRepo.query(query, queryArgs);

    let totalValue = await this.getClfArchiveTotalValue(
      user.id,
      filterSql,
      sqlArgs,
    );
    let response = {
      sort_field,
      sort_list: this.getClfSortList(),
      archive: list.map((item) => {
        return {
          ...item,
          status: item.status
            ? { id: item.status, name: ClfStatus[item.status] }
            : null,
          customername: !SHOW_CUSTOMER_DATA ? undefined : item.customername,
          assigned_to: {
            id: item.assigned_to_id,
            name: item.assigned_to_user,
          },
          closed_by: {
            id: item.closed_by_id || undefined,
            name: item.closed_by_user,
          },
          declined_by: {
            id: item.declined_by_use || undefined,
            name: item.declined_by_user,
          },
          response_date: moment(item.response_date).format('DD-MM-YYYY HH:mm'),
          declined_by_user: undefined,
          closed_by_user: undefined,
          assigned_to_user: undefined,
          declined_by_avatar: undefined,
          closed_by_avatar: undefined,
          assigned_to_avatar: undefined,
          declined_by_avatar_type: undefined,
          closed_by_avatar_type: undefined,
          assigned_to_avatar_type: undefined,
          declined_by_id: undefined,
          closed_by_id: undefined,
          assigned_to_id: undefined,
        };
      }),
      totals: totalValue,
      sort_order,
    };

    this.logger.debug('getClfArchive sql', { sql: query, sqlArgs: queryArgs });
    this.logger.debug('getClfArchive result', { response });

    return response;
  }

  async getClfArchiveTotalValue(
    sec_user_id: number,
    filterSql?: string,
    sqlArgs?: any,
  ) {
    this.logger.debug('getClfArchiveTotalValue', {
      sec_user_id,
      filterSql,
      sqlArgs,
    });

    const sql = `
    SELECT count(DISTINCT clf.clf_call_id) as cnt
    FROM clf_call clf
    LEFT JOIN email_customer ec ON clf.email_customer_id = ec.email_customer_id
    LEFT JOIN surv_surveyanswer a ON clf.email_customer_id = a.email_customer_id
    LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id
    LEFT JOIN clf_tags ct ON ct.clf_call_id = clf.clf_call_id
    LEFT JOIN surv_surveyansweritem pos
            ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND s.clf_question2 = pos.surv_surveyquestion_id
    LEFT JOIN surv_surveyansweritem neg
            ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND s.clf_question1 = neg.surv_surveyquestion_id
    LEFT JOIN surv_surveyansweritem callback ON a.surv_surveyanswer_id = callback.surv_surveyanswer_id AND
                          s.callback_question = callback.surv_surveyquestion_id
    LEFT JOIN clf_favorites fav ON clf.clf_call_id = fav.call_id AND fav.sec_user_id = ${sec_user_id}
    WHERE ec.survey_done = 1 ${filterSql || ''}
    `;
    const [query, queryArgs] = queryConvert(sql, sqlArgs);
    let list = await this.clfRepo.query(query, queryArgs);
    let response = list[0]?.cnt || 0;

    this.logger.debug(`getClfArchiveTotalValue result `, { response });
    return response;
  }

  getCflArchiveFilter(
    body: GetClfArchiveBodyDto,
    queryParams: any = {},
    privileges: string[] = [],
  ) {
    const sqlFiltersArray = [];

    const SHOW_CUSTOMER_DATA = privileges.includes('SHOW_CUSTOMER_DATA');

    if (body.search?.length) {
      sqlFiltersArray.push(
        `AND (clf_summary ilike :search OR c_notes ilike :search ${
          SHOW_CUSTOMER_DATA ? `OR ec.xml_rasnumber ilike :search` : ''
        })`,
      );
      queryParams.search = body.search;
    }

    return sqlFiltersArray.length ? `AND ${sqlFiltersArray.join(' AND ')}` : '';
  }

  async clfQuickSearch(body: GetClfArchiveBodyDto, privileges: string[] = []) {
    this.logger.debug(`clfQuickSearch`, { body });
    const { search, quicksearch } = body;
    if (!quicksearch) return null;
    const SHOW_CUSTOMER_DATA = privileges.includes('SHOW_CUSTOMER_DATA');

    let query = this.clfCallRepo
      .createQueryBuilder('cc')
      .select(
        aliasPrefix([
          'cc.clf_summary',
          'cc.clf_call_id',
          "CASE WHEN declined_by IS NOT NULL THEN CONCAT_WS(' ', sud.firstname, sud.username) ELSE CASE WHEN closed_by IS NOT NULL THEN CONCAT_WS(' ', suc.firstname, suc.username) ELSE CONCAT_WS(' ', sua.firstname, sua.username) END END AS user",
          'CASE WHEN declined_by IS NOT NULL THEN sud.avatar ELSE CASE WHEN closed_by IS NOT NULL THEN suc.avatar ELSE sua.avatar END END AS avatar',
        ]),
      )
      .leftJoin('cc.email_customer', 'ec')
      .leftJoin(UserEntity, 'sua', 'sua.sec_user_id = cc.assigned_to')
      .leftJoin(UserEntity, 'suc', 'suc.sec_user_id = cc.closed_by')
      .leftJoin(UserEntity, 'sud', 'sud.sec_user_id = cc.declined_by')
      .leftJoin('cc.survey_answer', 'a')
      .where('ec.survey_done = :surveyDone', { surveyDone: 1 })
      .andWhere(
        new Brackets((qb) => {
          qb.where('clf_summary ilike :search', { search: `%${search}%` });
          if (SHOW_CUSTOMER_DATA)
            qb.orWhere('ec.xml_rasnumber ilike :search', { search });
        }),
      )
      .orderBy('cc.assigned_date', 'DESC', 'NULLS LAST')
      .limit(6);

    this.logger.debug(`clfQuickSearch query`, { query: rawQuery(query) });

    let list = await query.getRawMany();

    let moreRecords = false;
    if (list.length >= 6) moreRecords = true;
    return {
      moreRecords,
      sort_list: this.getClfSortList(),
      archive: list.slice(0, 5).map((item) => {
        return {
          summary: item.clf_summary,
          avatar: item.avatar,
          user: item.user,
          clf_call_id: item.clf_call_id,
        };
      }),
    };
  }

  getClfSortList() {
    return [
      {
        name: 'Visit Date',
        id: 1,
      },
      {
        name: 'Send Date',
        id: 2,
      },
      {
        name: 'Response Date',
        id: 3,
      },
      {
        name: 'Modification Date',
        id: 4,
      },
      {
        name: 'Close Date',
        id: 5,
      },
    ];
  }
}
