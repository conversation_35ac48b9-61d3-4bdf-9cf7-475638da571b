import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class UpdateClfItem {
  @ApiProperty()
  clf_call_id: number;

  @ApiProperty()
  summary: string;

  @ApiProperty()
  comment: string;

  @ApiProperty()
  reason: string;

  @ApiProperty()
  assigned_to: { id: number; name: string };

  @ApiProperty()
  lane: number;

  @ApiProperty()
  remove_assigned_to: boolean;

  @ApiProperty()
  tags: [{ id: number; name: string }];

  @ApiProperty()
  credit: number;

  @ApiProperty()
  result: string;

  @ApiProperty()
  status: { id: number; name: string };

  @ApiProperty()
  is_favorite: boolean;
}

export class UpdateClfBodyDto {
  @Type()
  @ApiProperty()
  items: UpdateClfItem;
}
