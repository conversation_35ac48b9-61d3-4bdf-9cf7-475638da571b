import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';

export class GetClfArchiveFilterItem {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  id: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  name: string;
}

export class GetClfArchiveBodyDto {
  @Type()
  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  page?: number = null;

  @Type()
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(200)
  @ApiProperty({ required: false })
  count?: number = 50;

  @Type()
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  from: string;

  @Type()
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  search: string;

  @Type()
  @IsOptional()
  @IsBoolean()
  @ApiProperty({ required: false })
  quicksearch?: boolean = false;

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  survey: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  language: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  brands: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  tags: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  regions: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  users: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  clf_status: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  clf_result: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  productgroup: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  project: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  project_bouw: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  club: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  score: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  bsh_survey_categories: GetClfArchiveFilterItem[];

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  callback_question: GetClfArchiveFilterItem[];

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  from_date: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  to_date: string;

  @Type()
  @IsBoolean()
  @IsOptional()
  @ApiProperty({ required: false })
  ignoreDates: boolean = false;

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  favourite: GetClfArchiveFilterItem[];

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  sort_order: 'asc' | 'desc' | null;

  @Type(() => GetClfArchiveFilterItem)
  @IsOptional()
  @ApiProperty({ required: false })
  sort_field: GetClfArchiveFilterItem[];
}
