import {
  Body,
  Controller,
  Get,
  ParseArrayPipe,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { ClfService } from './clf.service';
import { GetClfArchiveBodyDto } from './dto/post-clf-archive.dto';
import { UpdateClfBodyDto, UpdateClfItem } from './dto/post-clf.dto';

@Controller({
  version: '2',
  path: 'clf',
})
@ApiBearerAuth()
@ApiTags('CLF')
export class ClfController {
  constructor(private clfService: ClfService) {}

  @Get()
  @UseGuards(AuthGuard)
  async getClfDetails(@Query('id') id: number) {
    return this.clfService.getClfDetails(id);
  }

  @Post()
  @UseGuards(AuthGuard)
  async upsertClfRecord(
    @Body(new ParseArrayPipe({ items: UpdateClfBodyDto }))
    items: UpdateClfItem[],
  ) {
    return this.clfService.updateClf(items);
  }
}

@Controller({
  version: '2',
  path: 'clf_archive',
})
@ApiBearerAuth()
@ApiTags('CLF')
export class ClfArchiveController {
  constructor(private clfService: ClfService) {}

  @Post()
  @UseGuards(AuthGuard)
  async getClfArchive(@Body() body: GetClfArchiveBodyDto) {
    return this.clfService.getClfArchive(body);
  }
}
