import { Injectable } from '@nestjs/common';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { CompanyType } from '../../shared/enums/company-type.enum';
import { ProjectManagerService } from '../project/project-manager/project-manager.service';
import { SurveyService } from '../survey/survey.service';
import { DateUtilsService } from '../../shared/utils/date-utils.service';
import { GetClfArchiveBodyDto } from './dto/post-clf-archive.dto';
import { ClsService } from 'nestjs-cls';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class FeedbackClfService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private surveyService: SurveyService,
    private dateUtilService: DateUtilsService,
    private projectManagerService: ProjectManagerService,
  ) {}

  async getFilter(params: GetClfArchiveBodyDto, sqlArgs: any = {}) {
    const { company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`getFilter`, { params });
    let sql = '';

    if (params.survey?.length) {
      sql += ' AND a.surv_survey_id = ANY(:survey)';
      sqlArgs.survey = params.survey.map((i) => i.id);
    } else {
      const survey_list = await this.surveyService.list();
      sql += ' AND a.surv_survey_id = ANY(:survey)';
      sqlArgs.survey = survey_list.map((i) => i.id);
    }

    sql += ` AND a.sec_company_id =:sec_company_id`;
    sqlArgs.sec_company_id = company.id;

    if (params.language?.length) {
      sql += ' AND ec.xml_language = ANY(:language)';
      sqlArgs.language = params.language.map((i) => i.id);
    }

    if (params.brands?.length) {
      sql += ' AND LOWER(ec.xml_brand) = ANY(:brands)';
      sqlArgs.brands = params.brands.map((i) => i.id.toString().toLowerCase());
    }

    if (!params.ignoreDates) {
      if (params.from_date || params.to_date) {
        const { fromDate, toDate } = this.dateUtilService.formatDates({
          fromDate: params.from_date,
          toDate: params.to_date,
        });
        sql += ` AND ${'a.creation_date'} between '${fromDate}' AND '${toDate}'`;
      }
    }

    if (params.callback_question?.[0]?.id == '1') {
      sql += ` AND callback.return_value='1' `;
    } else if (params.callback_question?.[0]?.id == '2') {
      sql += ` AND callback.return_value='0' `;
    }

    if (params.favourite?.[0]?.id == '1') {
      sql += ` AND clf_favorite_id IS NOT NULL `;
    }

    if (params.tags?.length) {
      sql += ` AND ct.tag_id = ANY(:tags)`;
      sqlArgs.tags = params.tags.map((i) => i.id);
    }
    if (params.regions?.length) {
      sql += ` AND ${
        company.id == 2 ? 'ec.xml_techgrp' : 'ec.xml_region'
      } = ANY(:regions)`;
      sqlArgs.regions = params.regions.map((i) => i.id);
    }

    if (params.users?.length) {
      sql += ` AND clf.assigned_to = ANY(:users)`;
      sqlArgs.users = params.users.map((i) => i.id);
    }

    if (params.clf_status?.length) {
      let clf_status = params.clf_status.map((i) => i.id?.toString());
      if (clf_status.includes('1')) {
        sql += `  AND ( clf.status is null OR clf.status = ANY(:clf_status) )`;
      } else {
        sql += `  AND clf.status = ANY(:clf_status)`;
      }
      sqlArgs.clf_status = clf_status;
    }

    if (params.clf_result?.length) {
      sql += ` AND clf.result = ANY(:clf_result)`;
      sqlArgs.clf_result = params.clf_result.map((i) => i.name);
    }

    if (params.productgroup?.length) {
      sql += ` AND ec.xml_prodarea = ANY(:productgroup)`;
      sqlArgs.productgroup = params.productgroup.map((i) => i.id);
    }

    if (params.project?.length) {
      let pdiv = ['BO-MDA', 'BO-SDA', 'SI-MDA', 'SI-SDA'];
      sqlArgs.projectDiv = params.project.filter((item) =>
        pdiv.includes(item.id),
      ); // item.name
      sqlArgs.projectNr = params.project.filter(
        (item) => !pdiv.includes(item.id),
      ); // item.name

      if (sqlArgs.projectDiv?.length > 0) {
        sql += ` AND ec.xml_projectdiv = ANY(:projectDiv::text[])`;
      }
      if (sqlArgs.projectNr?.length > 0) {
        sql += ` AND ec.xml_projectnr = ANY(:projectNr::text[])`;
      }
    }

    if (company.type == CompanyType.BOUW) {
      if (params.project_bouw?.length) {
        sqlArgs.projectBouw = params.project_bouw.map((item) => item.id); // item.name
        sql += ` AND ec.xml_internal_external = ANY(:projectBouw::text[])`;
      } else {
        sqlArgs.projectBouw =
          await this.projectManagerService.getAllowedProjects();
        sql += ` AND (ec.xml_internal_external = ANY(:projectBouw::text[]) OR ec.xml_internal_external IS NULL)`;
      }
    }

    if (company.type == CompanyType.SPORT) {
      if (params.club?.length) {
        sqlArgs.club = params.club.map((item) => item.id); // item.name
        sql += ` AND ec.xml_internal_external = ANY(:club::text[])`;
      } else if (!sqlArgs.projectBouw) {
        sqlArgs.projectBouw =
          await this.projectManagerService.getAllowedProjects();
        sql += ` AND (ec.xml_internal_external = ANY(:projectBouw::text[]) OR ec.xml_internal_external IS NULL)`;
      }
    }

    if (params.score?.length) {
      sqlArgs.score = [
        ...new Set(
          [].concat(
            ...params.score.map((item) => {
              if (item.id == '11') return [9, 10];
              else if (item.id == '12') return [7, 8];
              else if (item.id == '13') return [0, 1, 2, 3, 4, 5, 6];
              else return [+item.id];
            }),
          ),
        ),
      ];
      sql += ` AND  a.nps_value = ANY(:score::int[])`;
    }

    if (params.bsh_survey_categories?.length) {
      sqlArgs.bshCid = params.bsh_survey_categories.map((item) => item.id);
      sql += ` AND s.bsh_category_id  = ANY(:bshCid::int[])`;
    }

    this.logger.debug(`getFilter sql`, { sql, sqlArgs });

    return sql;
  }
}
