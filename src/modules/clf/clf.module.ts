import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClfCallEntity } from '../database/entities/clf-call.entity';
import { ClfCallNoteEntity } from '../database/entities/clf-call-note.entity';
import { <PERSON>lf<PERSON><PERSON><PERSON><PERSON><PERSON>roller, ClfController } from './clf.controller';
import { ClfService } from './clf.service';
import { ClfTagsEntity } from '../database/entities/clf-tags.entity';
import { TagsEntity } from '../database/entities/tags.entity';
import { ClfFavoritesEntity } from '../database/entities/clf-favorites.entity';
import { UsersModule } from '../users/users.module';
import { DateUtilsService } from '../../shared/utils/date-utils.service';
import { SurveyModule } from '../survey/survey.module';
import { CompanyModule } from '../company/company.module';
import { ProjectManagerModule } from '../project/project-manager/project-manager.module';
import { FeedbackClfService } from './feedback-clf.service';
import { MClassResponse } from '../database/entities/mclass-response.entity';
import { AuthModule } from '../auth/auth.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    UsersModule,
    TypeOrmModule.forFeature([
      ClfCallEntity,
      ClfCallNoteEntity,
      ClfFavoritesEntity,
      ClfTagsEntity,
      TagsEntity,
      MClassResponse,
    ]),
    ModuleLoggerModule.register('clf'),
    SurveyModule,
    CompanyModule,
    ProjectManagerModule,
    AuthModule,
  ],
  controllers: [ClfController, ClfArchiveController],
  providers: [ClfService, DateUtilsService, FeedbackClfService],
})
export class ClfModule {}
