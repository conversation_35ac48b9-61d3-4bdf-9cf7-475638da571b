import { HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { v4 as uuidv4 } from 'uuid';

import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import {
  UpsertImportTemplateBodyDto,
  UpsertImportTemplateQueryDto,
} from './dto/post-import-template.dto';

import { ImportService } from './import.service';
import { TranslateService } from '@/core/modules/translate/translate.service';

import {
  ModWccsImportMapEntity,
  ModWccsImportTemplateEntity,
  ModWccsMetaFieldsEntity,
  ModWccsMetaTablesEntity,
} from '@entities';

import { stringCapitalize } from '@/shared/utils';
import { ClsProperty } from '@/config/contents';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

export class ImportTemplateService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private translate: TranslateService,
    @InjectRepository(ModWccsImportTemplateEntity)
    private readonly importTemplateRepo: Repository<ModWccsImportTemplateEntity>,
    @InjectRepository(ModWccsImportMapEntity)
    private readonly importMapsRepo: Repository<ModWccsImportMapEntity>,
    @InjectRepository(ModWccsMetaFieldsEntity)
    private readonly metaFieldsRepo: Repository<ModWccsMetaFieldsEntity>,
    @InjectRepository(ModWccsMetaTablesEntity)
    private readonly metaTableRepo: Repository<ModWccsMetaTablesEntity>,
    private importService: ImportService,
  ) {}

  async getTemplates(table: string) {
    let [tableName] = this.importService.translateTablename(table);
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`getTemplates for ${table}`, { company });

    let metatable = await this.metaTableRepo
      .createQueryBuilder('m')
      .select('m.metatable_id')
      .where('m.metatable_name =:tableName', { tableName })
      .andWhere('m.metatable_company_type =:type', { type: company.type })
      .getOne();
    if (!metatable) throw new HttpException('metatable not found', 404);

    let templates = await this.importTemplateRepo
      .createQueryBuilder('i')
      .leftJoin('i.metatable', 'm')
      .where('i.sec_company_id =:cid', { cid: company.id })
      .andWhere('m.metatable_name =:table', { table: tableName })
      .andWhere('m.metatable_company_type =:type', { type: company.type })
      .getMany();

    let query = this.metaFieldsRepo
      .createQueryBuilder('mf')
      .leftJoinAndSelect('mf.metatable', 'm')
      .where('m.metatable_name =:table', { table: tableName })
      .andWhere('mf.metatable_id =:mid', { mid: metatable.metatable_id });

    if (table !== 'blacklist') {
      query.andWhere('m.metatable_company_type =:type', { type: company.type });
    }

    query.andWhere('mf.metafield_isfield =1');

    let import_fields = await query.getMany();

    let translateKeys = import_fields.map((item) =>
      item.metafield_title.replace('i18n:', ''),
    );

    let translates = await this.translate.dbs(
      translateKeys?.length > 0 ? translateKeys : ['null'],
    );

    let result = {
      metatable_id: metatable.metatable_id,
      templates: templates.map((item) => {
        return {
          id: item.template_id,
          name: item.template_name,
          file_seperator: item.template_file_separator,
          file_type: item.template_file_type,
          date_format: item.template_date_format,
        };
      }),
      import_fields: import_fields.map((item) => {
        let title = item.metafield_title.replace('i18n:', '');
        let findTitle = translates.find((i) => i.key == title);
        title = findTitle?.value || title;
        return {
          id: item.metafield_id,
          name: item.metafield_name,
          title: stringCapitalize(title),
          type: item.metafield_type,
          limit: item.metafield_limit,
          unique: !!item.metafield_isunique,
          mandatory: !!item.metafield_ismandatory,
        };
      }),
    };

    this.logger.debug(`getTemplates result for ${table}`, { result });

    return result;
  }
  async getTemplate(id: string) {
    return await this.importTemplateRepo
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.metatable', 'm')
      .where('i.template_id =:id', { id })
      .getOne();
  }

  async getTemplateWithFields(id: string) {
    let template = await this.importTemplateRepo
      .createQueryBuilder()
      .where('template_id =:id', { id })
      .getOne();
    // let list = await this.importMapsRepo
    //   .createQueryBuilder('i')
    //   .where('i.template_id =:id', { id })
    //   .getMany();

    return {
      id: template.template_id,
      mapping_template_title: template.template_name,
      mapped_fields: template.mapped_fields,
    };
  }

  async upsertTemplate(
    body: UpsertImportTemplateBodyDto,
    query: UpsertImportTemplateQueryDto,
  ) {
    let { template: templateData, mapped_fields, data, metatable_id } = body;

    const { id, label, date_format } = templateData;

    this.logger.debug(`upsertTemplate id ${id || 'new'}`, { body, query });

    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let template_id = id || uuidv4().toUpperCase();

    if (templateData.label) {
      // upsert template
      let template: Partial<ModWccsImportTemplateEntity> = {
        template_id,
        sec_company_id: company.id,
        template_name: label,
        mapped_fields,
      };

      if (date_format?.length > 0) template.template_date_format = date_format;
      if (metatable_id?.length > 0) template.metatable_id = metatable_id;

      let result: any = await this.importTemplateRepo.save(template);
      if (id) {
        result = await this.importMapsRepo
          .createQueryBuilder()
          .delete()
          .where('template_id =:template_id', { template_id })
          .execute();
      }

      this.logger.debug(`upsertTemplate templateData result ${id}`, { result });
    }

    mapped_fields = mapped_fields.filter((item) => !item.ignoreMapping);

    let maps: DeepPartial<ModWccsImportMapEntity>[] = mapped_fields.map(
      (item) => {
        return {
          map_id: item.id || uuidv4().toUpperCase(),
          template_id,
          metafield_id: item.target.id,
          map_source_field: item.source.id,
          map_key: +item.isKeyField || 0,
          map_ismandatory: +item.target.mandatory,
        };
      },
    );
    await this.importMapsRepo.save(maps);

    let template = await this.getTemplate(template_id);
    let metatable = await this.metaTableRepo.findOne({
      where: { metatable_id },
    });

    let fields = await this.importMapsRepo
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.metafield', 'm')
      .where('i.template_id =:template_id', { template_id })
      .getMany();

    if (fields.find((i) => !i.metafield))
      throw new HttpException('metafield is not found', 404);
    try {
      let { insertData, updateData } = await this.importService.importDataToDb(
        fields,
        template,
        metatable,
        data,
        query.table,
      );
      let msg;
      if (id) msg = 'Template Updated';
      else if (label) msg = 'Template Created';
      else msg = 'Data Imported';

      let result = {
        msg,
        status: id ? HttpStatus.ACCEPTED : HttpStatus.CREATED,
        new: insertData?.length || 0,
        updated: updateData?.length || 0,
        template,
        fields,
      };
      this.logger.debug(`upsertTemplate result ${id}`, {
        result,
        fields,
        template,
      });
      return result;
    } catch (e) {
      this.logger.debug(`upsertTemplate error ${id}`, {
        error: e?.message || e,
      });

      throw new HttpException(e.message, 500);
    }
  }
}
