import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { GetImportTemplateQueryDto } from './dto/get-import-template.dto';
import {
  UpsertImportTemplateBodyDto,
  UpsertImportTemplateQueryDto,
} from './dto/post-import-template.dto';
import { ImportTemplateService } from './import-template.service';

import { AuthGuard } from '@/shared/guards/auth.guard';
@ApiTags('Import Template')
@ApiBearerAuth()
@Controller({
  path: 'import_template',
  version: '2',
})
export class ImportTemplateController {
  constructor(private importTemplateService: ImportTemplateService) {}

  @Get()
  @UseGuards(AuthGuard)
  getTemplates(@Query() { table, id }: GetImportTemplateQueryDto) {
    if (table?.length > 0)
      return this.importTemplateService.getTemplates(table);
    else return this.importTemplateService.getTemplateWithFields(id);
  }

  @Post()
  @UseGuards(AuthGuard)
  upsertTemplate(
    @Body() body: UpsertImportTemplateBodyDto,
    @Query() query: UpsertImportTemplateQueryDto,
  ) {
    if (query.id?.length > 0) {
      body.template.id = query.id;
    }
    return this.importTemplateService.upsertTemplate(body, query);
  }
}
