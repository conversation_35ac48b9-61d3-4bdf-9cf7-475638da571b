import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImportTemplateController } from './import-template.controller';
import { ImportTemplateService } from './import-template.service';
import { ImportService } from './import.service';
import {
  ModWccsImportMapEntity,
  ModWccsImportTemplateEntity,
  ModWccsMetaFieldsEntity,
  ModWccsMetaTablesEntity,
} from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ModWccsMetaFieldsEntity,
      ModWccsImportMapEntity,
      ModWccsImportTemplateEntity,
      ModWccsMetaTablesEntity,
    ]),
    ModuleLoggerModule.register('import-template'),
  ],
  controllers: [ImportTemplateController],
  providers: [ImportTemplateService, ImportService],
  exports: [ImportService],
})
export class ImportTemplateModule {}
