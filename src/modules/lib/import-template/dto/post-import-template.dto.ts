import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, Max, MaxLength } from 'class-validator';

export class UpsertImportTemplateQueryDto {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  id: string;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  table?: string = null;
}
export class UpsertImportTemplateData {}

export class UpsertImportTemplateItem {
  @Type()
  @IsOptional()
  id: string;

  @Type()
  @IsOptional()
  @MaxLength(200)
  @ApiProperty({ required: false, nullable: true })
  label: string;

  @Type()
  @IsOptional()
  @MaxLength(5)
  @ApiProperty({ required: false, nullable: true })
  date_format: string;
}

export class UpsertImportTemplateBodyDto {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  template: UpsertImportTemplateItem;

  @Type(() => MappedFieldDto)
  @IsOptional()
  @ApiProperty({ type: String, required: false, nullable: true })
  mapped_fields: MappedFieldDto[];

  data: any[];

  @Type()
  @ApiProperty({ required: true, nullable: true })
  metatable_id: string;
}

class MappedFieldSourceDto {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  id: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  name: string;
}

class MappedFieldTarget {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  id: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  name: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  title: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  type: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  limit: number;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  unique: boolean;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  mandatory: boolean;
}

class MappedFieldDto {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  id: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  source: MappedFieldSourceDto;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  target: MappedFieldTarget;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  isKeyField: boolean;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  autoMatched: boolean;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  hasValidLimit: boolean;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  confirmMapping: boolean;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  maxLengthFound: number;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  ignoreMapping: boolean;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  collapsed: boolean;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  mapped_value: string;
}
