import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { FindOptionsWhere, Repository } from 'typeorm';
import { LogEventEntity } from '../../database/entities/log-events.entity';
import { GetLogsQueryDto } from './dto/get-logs.dto';
import { CreateLogBodyDto } from './dto/post-logs.dto';
export class LoggerService {
  constructor(
    @InjectRepository(LogEventEntity)
    private readonly logEventRepo: Repository<LogEventEntity>,
  ) {}

  async getLogs(params: GetLogsQueryDto) {
    const { from, to, limit = 50, page, search, solution_name } = params;
    let query = this.logEventRepo.createQueryBuilder();
    if (from && to) {
      query.andWhere(`log_events BETWEEN :from AND :to`, {
        from: moment(from).toDate(),
        to: moment(to).toDate(),
      });
    }
    if (search?.length > 2) {
      query.andWhere(`log_message ILIKE :search`, { search: `%${search}%` });
    }
    if (solution_name?.length > 2) {
      query.andWhere(`solution_name = :solution_name`, {
        solution_name: `${solution_name}`,
      });
    }
    query.take(limit);
    query.skip(limit * page);
    let [log_messages, count] = await query.getManyAndCount();
    return {
      log_messages,
      count,
    };
  }
  async getLog(id: number = 0) {
    if (!id) return false;
    let where: FindOptionsWhere<LogEventEntity> = {
      log_event_id: id,
    };
    let query = this.logEventRepo.createQueryBuilder().where(where);

    let log = await query.getOne();
    return log;
  }

  async createLog(params: CreateLogBodyDto) {
    const {
      sec_user_id,
      sec_company_id,
      logger_name,
      log_message,
      log_level,
      event_time,
      solution_name,
      additional_info,
    } = params;
    let item = await this.logEventRepo
      .createQueryBuilder()
      .insert()
      .values({
        sec_user_id,
        sec_company_id,
        logger_name,
        log_message,
        log_level,
        event_time,
        solution_name,
        additional_info,
      })
      .returning(['log_event_id'])
      .execute();
    let id = item?.generatedMaps?.[0]?.log_event_id;
    let log_event = await this.getLog(id);

    return { log_event };
  }
}
