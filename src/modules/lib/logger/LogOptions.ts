export enum LogLevel {
  ALL,
  LOG,
  ERROR,
  WARN,
  DEBUG,
  VERBOSE,
}

export type LogLevels = 'all' | 'log' | 'error' | 'warn' | 'debug' | 'verbose';

export const logLevelMapper: Record<LogLevel, LogLevels> = {
  [LogLevel.ALL]: 'all',
  [LogLevel.LOG]: 'log',
  [LogLevel.ERROR]: 'error',
  [LogLevel.WARN]: 'warn',
  [LogLevel.DEBUG]: 'debug',
  [LogLevel.VERBOSE]: 'verbose',
};

export interface LogOptions {
  sec_user_id: number;
  logger_name: string;
  request_id: string;
  client_id: string;
}
