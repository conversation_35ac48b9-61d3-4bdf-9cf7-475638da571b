import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateLogBodyDto {
  @Type()
  @IsDate()
  @ApiProperty({
    required: true,
  })
  event_time?: Date = new Date();

  @Type()
  @IsString()
  @ApiProperty({
    required: true,
  })
  logger_name: string;

  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: true,
  })
  log_level: number;

  @Type()
  @IsString()
  @ApiProperty({
    required: true,
  })
  log_message: string;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  solution_name?: string = '';

  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  sec_user_id?: number = 0;

  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  sec_company_id?: number = 0;

  @IsOptional()
  @ApiProperty({
    required: false,
  })
  additional_info?: any = null;
}
