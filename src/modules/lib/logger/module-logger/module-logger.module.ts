import { Module, DynamicModule } from '@nestjs/common';
import { ModuleLogger } from './module-logger.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LogEventEntity, LogManagementEntity } from '@entities';

@Module({})
export class ModuleLoggerModule {
  static register(prefix: string): DynamicModule {
    return {
      module: ModuleLoggerModule,
      imports: [
        TypeOrmModule.forFeature([LogEventEntity, LogManagementEntity]),
      ],
      providers: [
        {
          provide: 'MODULE_NAME',
          useValue: prefix,
        },
        {
          provide: ModuleLogger,
          useClass: ModuleLogger,
        },
      ],
      exports: [ModuleLogger],
    };
  }
}
