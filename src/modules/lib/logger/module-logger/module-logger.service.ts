import { LogEventEntity, LogManagementEntity } from '@entities';
import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LogLevel, logLevelMapper, LogLevels } from '../LogOptions';
import { v4 as uuid } from 'uuid';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
const Queue = require('better-queue');

@Injectable()
export class ModuleLogger {
  static modules: LogManagementEntity[] = null;
  static loggerNames: string[] = [];
  static applicationPart: string = 'node-api';
  static queue: any;

  private readonly prefix: string;

  constructor(
    private cls: ClsService,
    @Inject('MODULE_NAME') prefix: string,
    @InjectRepository(LogManagementEntity)
    private logMgmtRepo: Repository<LogManagementEntity>,
    @InjectRepository(LogEventEntity)
    private logRepo: Repository<LogEventEntity>,
  ) {
    this.prefix = `${ModuleLogger.applicationPart}.${prefix}`;
    ModuleLogger.loggerNames.push(this.prefix);

    if (!ModuleLogger.queue) {
      console.log('Creating queue for logger module');
      ModuleLogger.queue = new Queue(this.queueHandler.bind(this), {
        concurrent: 1,
        maxRetries: 0,
        maxTimeout: 60 * 1000,
      });
    }
  }

  private async queueHandler(
    { level, message, context, prefix }: any,
    cb: any,
  ) {
    await this.createLog(level, prefix, message, context);
    cb(true);
  }

  log(message: string, context?: any) {
    ModuleLogger.queue.push({
      level: LogLevel.LOG,
      message,
      context,
      prefix: this.prefix,
    });
  }

  error(message: string, context?: any) {
    ModuleLogger.queue.push({
      level: LogLevel.ERROR,
      message,
      context,
      prefix: this.prefix,
    });
  }

  warn(message: string, context?: any) {
    ModuleLogger.queue.push({
      level: LogLevel.WARN,
      message,
      context,
      prefix: this.prefix,
    });
  }

  debug(message: string, context?: any) {
    ModuleLogger.queue.push({
      level: LogLevel.DEBUG,
      message,
      context,
      prefix: this.prefix,
    });
  }

  private async createLog(
    log_level: LogLevel,
    logger_name: string,
    message: string,
    context?: any,
  ) {
    if (!this.checkLogger(logger_name, logLevelMapper[log_level])) return;

    const caller = ModuleLogger.getCaller();
    const authData = await this.cls.get<AuthorizedData>(ClsProperty.user);
    const user = authData?.user;
    const company = authData?.company;
    let additional_info: any = {
      caller_file: caller.file,
      caller_function: caller.function,
    };
    if (context && typeof context === 'object' && !Array.isArray(context)) {
      additional_info = { ...additional_info, ...context };
    } else {
      additional_info = { ...additional_info, context };
    }
    await this.logRepo.save(
      this.logRepo.create({
        event_time: new Date(),
        log_message: message,
        sec_user_id: user?.id,
        sec_company_id: company?.id,
        request_id: this.cls.getId(),
        logger_name,
        additional_info,
        solution_name: ModuleLogger.applicationPart,
        log_level,
      }),
    );
    return true;
  }

  private checkLogger(logger_name, log_level: LogLevels) {
    if (!ModuleLogger.modules) {
      console.warn('WARNING! Logger modules are empty. Logs are not saving');
      return false;
    }
    let module = ModuleLogger.modules.find(
      (m) =>
        m.logger_name === logger_name &&
        m.application_part === ModuleLogger.applicationPart,
    );
    const isActive = module?.logger_active;
    const isSameLevel =
      !module?.log_level ||
      ModuleLogger.checkLogLevel(module.log_level, log_level);

    if (isActive && isSameLevel) {
      return true;
    } else if (!module) {
      console.error('WARNING! Log module is not found for', logger_name);
    }

    return false;
  }

  async createLogManagement(logger_name: string) {
    const item = await this.logMgmtRepo.save(
      this.logMgmtRepo.create({
        log_management_id: uuid(),
        logger_name,
        logger_active: 0,
        application_part: ModuleLogger.applicationPart,
      }),
    );

    ModuleLogger.modules.push(item);
  }

  static getCaller(): { file: string; function: string } {
    const stack = new Error().stack.split('\n');
    const caller = stack[4].trim().split(' ');
    const fn = caller[1].replace(/\(|@/g, '');
    const file = caller[2].replace(/\(|\/|\\|@/g, '/');
    return {
      file: file?.split('/')?.pop()?.split(':').shift(),
      function: fn,
    };
  }

  static checkLogLevel(selectedLevel: LogLevels, logLevel: LogLevels) {
    if (selectedLevel === 'all') return true;
    const levels = ['debug', 'info', 'warning', 'error'];

    const selectedLevelIndex = levels.indexOf(selectedLevel);
    const logLevelIndex = levels.indexOf(logLevel);

    return logLevelIndex >= selectedLevelIndex;
  }
}
