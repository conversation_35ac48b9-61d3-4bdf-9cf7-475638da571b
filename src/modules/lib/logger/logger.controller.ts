import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetLogsQueryDto } from './dto/get-logs.dto';
import { CreateLogBodyDto } from './dto/post-logs.dto';
import { LoggerService } from './logger.service';

@ApiTags('Logs')
@Controller({
  path: 'log_message',
  version: '2',
})
@ApiBearerAuth()
export class LoggerController {
  constructor(private loggerService: LoggerService) {}

  @Get()
  @UseGuards(AuthGuard)
  getLogs(@Query() query: GetLogsQueryDto) {
    return this.loggerService.getLogs(query);
  }

  @Post()
  @UseGuards(AuthGuard)
  createLog(@Body() body: CreateLogBodyDto) {
    return this.loggerService.createLog(body);
  }
}
