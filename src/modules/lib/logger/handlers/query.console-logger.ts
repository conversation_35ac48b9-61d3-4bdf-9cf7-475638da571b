import { AbstractLogger, LogLevel, LogMessage, QueryRunner } from 'typeorm';
import { nonParameterizedQuery } from '@/shared/utils/sql-utils';

export class QueryLogger extends AbstractLogger {
  /**
   * Write log to specific output.
   */
  protected writeLog(
    level: LogLevel,
    logMessage: LogMessage | LogMessage[],
    queryRunner?: QueryRunner,
  ) {
    const messages = this.prepareLogMessages(logMessage, {
      highlightSql: false,
    });

    for (let message of messages) {
      switch (message.type ?? level) {
        case 'log':
        case 'schema-build':
        case 'migration':
          console.log(message.message);
          break;

        case 'info':
        case 'query':
          if (message.prefix) {
            console.info(message.prefix, message.message);
          } else {
            console.info(message.message);
          }
          break;

        case 'warn':
        case 'query-slow':
          if (message.prefix) {
            console.warn(message.prefix, message.message);
          } else {
            console.warn(message.message);
          }
          break;

        case 'error':
        case 'query-error':
          if (message.prefix) {
            console.error(message.prefix, message.message);
          } else {
            console.error(message.message);
          }
          break;
      }
    }
  }

  logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
    if (query === 'START TRANSACTION' || query === 'COMMIT') return;
    if (query.includes('INSERT INTO "log_events"')) return;
    if (query.includes('information_schema')) return;
    if (query.includes('CREATE EXTENSION')) return;
    if (query.includes('current_schema()')) return;
    const requestUrl =
      queryRunner && queryRunner.data['request']
        ? '(' + queryRunner.data['request'].url + ') '
        : '';
    console.log(
      requestUrl + 'query: ' + nonParameterizedQuery(query, parameters),
    );
  }
}
