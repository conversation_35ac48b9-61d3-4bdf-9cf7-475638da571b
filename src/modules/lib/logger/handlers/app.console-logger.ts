import { ConsoleLogger } from '@nestjs/common';

export class AppLogger extends ConsoleLogger {
  log(message: string, context: string, ...args) {
    // Filter out the startup logs
    if (
      context !== 'NestFactory' &&
      context !== 'InstanceLoader' &&
      context != 'RoutesResolver' &&
      context != 'RouterExplorer'
    ) {
      super.log(message, context, ...args);
    }
  }

  error(message: any, ...args: any) {
    super.error(message, ...args);
  }

  warn(message: any, ...args: any) {
    super.warn(message, ...args);
  }
}
