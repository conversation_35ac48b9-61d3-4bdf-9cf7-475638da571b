import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LogEventEntity } from '../../database/entities/log-events.entity';
import { EventsLogger } from './handlers/events.db-logger';
import { LoggerController } from './logger.controller';
import { LoggerService } from './logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([LogEventEntity])],
  providers: [EventsLogger, LoggerService],
  controllers: [LoggerController],
  exports: [EventsLogger, LoggerService],
})
export class LoggerModule {}
