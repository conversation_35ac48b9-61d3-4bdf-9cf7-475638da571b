import { Injectable } from '@nestjs/common';
import { promises as fs } from 'fs';
import { join } from 'path';
import { v4 as uuid } from 'uuid';
import { generatePdf } from './utils/generate-pdf';
import { env } from '../../../app.env';
import { InjectBrowser } from 'nestjs-puppeteer';
import { Browser } from 'puppeteer';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class HtmlToPdfService {
  constructor(
    private logger: ModuleLogger,
    @InjectBrowser() private readonly browser: Browser,
  ) {}
  async convertToPDF(content: Buffer | string) {
    this.logger.debug('convertToPDF', { content: 'content is big' });
    const options = { preferCSSPageSize: true, printBackground: true };
    const file = { content };
    let buffer = await generatePdf(this.browser, file, options);
    return buffer;
  }

  async createRandomFile(content: Buffer | string) {
    const name = uuid() + '.pdf';
    this.logger.debug(`createRandomFile: ${name}`);

    const folderPath = join(env.APP_PATH, 'uploads/pdf');
    const filePath = join(folderPath, name);

    await fs.mkdir(folderPath, { recursive: true }).catch(() => false);

    await fs.writeFile(filePath, Buffer.from(content));

    return name;
  }

  removeUnusedTags(content: Buffer | string) {
    content = content.toString();
    content = content.replace('media="print"', '');
    content = content.replace(/rowspan="2"/g, '');
    content = content.replace(/<th/g, '<td');
    content = content.replace(/<\/th/g, '</td');
    content = content.replace(/<thead>/g, '');
    content = content.replace(/<\/thead>/g, '');
    content = content.replace(/<tbody>/g, '');
    content = content.replace(/<\/tbody>/g, '');
    content = content.replace(
      '<style',
      '<style> .roundedBack {  display: flex; justify-content: center;align-items: center;line-height: initial; } </style><style',
    );
    return content;
  }

  appendPerPageStyle(content: Buffer | string): string {
    content = content.toString();
    content = content.replace(
      '<style',
      '<style> table:not(#headtable) {     page-break-after: always; } </style><style',
    );
    return content;
  }
}
