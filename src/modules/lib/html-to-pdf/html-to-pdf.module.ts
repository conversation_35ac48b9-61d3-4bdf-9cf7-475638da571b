import { Module } from '@nestjs/common';
import { HtmlToPdfController } from './html-to-pdf.controller';
import { HtmlToPdfService } from './html-to-pdf.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [ModuleLoggerModule.register('html-to-pdf')],
  controllers: [HtmlToPdfController],
  providers: [HtmlToPdfService],
})
export class HtmlToPdfModule {}
