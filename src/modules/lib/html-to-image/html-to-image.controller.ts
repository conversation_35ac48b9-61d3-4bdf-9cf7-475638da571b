import { Body, Controller, Get, Post, Res, UseGuards } from '@nestjs/common';
import { ApiBasicAuth, ApiTags } from '@nestjs/swagger';

import { Response } from 'express';
import * as handlebars from 'handlebars';

import { HtmlToImageGenerateDto } from './dto/html-to-image.dto';
import { HtmlToImageService } from './html-to-image.service';
import WebhookGuard from '@/modules/webhook/webhook.guard';

// Register the custom helper
handlebars.registerHelper('gt', function (a, b, options) {
  return a > b;
});

@Controller({
  path: 'html-to-image',
  version: '2',
})
@ApiTags('Converter')
export class HtmlToImageController {
  constructor(private converterService: HtmlToImageService) {}

  @Post('render')
  @UseGuards(WebhookGuard)
  @ApiBasicAuth()
  async renderTemplate(@Body() body: HtmlToImageGenerateDto) {
    let html = await this.converterService.renderTemplate(body);
    return html;
  }
}
