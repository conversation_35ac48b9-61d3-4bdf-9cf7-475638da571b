import { Injectable } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as handlebars from 'handlebars';
import { env } from '../../../app.env';
import { HtmlToImageGenerateDto } from './dto/html-to-image.dto';
import { InjectBrowser } from 'nestjs-puppeteer';
import { Browser, Page } from 'puppeteer';
import { saveImage } from '../image-library/utils/file.utils';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class HtmlToImageService {
  constructor(
    @InjectBrowser() private readonly browser: Browser,
    private moduleLogger: ModuleLogger,
  ) {}
  async renderTemplate(params: HtmlToImageGenerateDto) {
    this.moduleLogger.log('renderTemplate', { params });
    const htmlFilePath = join(
      __dirname,
      '..',
      '..',
      '..',
      'templates',
      `${params.template}.html`,
    );
    const htmlFileContent = readFileSync(htmlFilePath, 'utf8');
    const template = handlebars.compile(htmlFileContent);
    const renderedHtml = template(params.parameters);

    this.moduleLogger.log('renderTemplate', { renderedHtml });

    return renderedHtml;
  }

  async generateImage(params: HtmlToImageGenerateDto, save: boolean = false) {
    let url = `http://localhost:${env.PORT}/2/html-to-image/render`;
    let folderName = 'html-to-image';
    if (params.folder) {
      folderName = join(folderName, params.folder);
    }

    this.moduleLogger.log('generateImage', { url, save });

    let page = await this.browser.newPage();
    try {
      await page.setCacheEnabled(false);
      await page.setRequestInterception(true);
      let checkFirstRequest = false;
      page.on('request', (request) => {
        if (!checkFirstRequest) {
          checkFirstRequest = true;
          request.continue({
            method: 'POST',
            postData: JSON.stringify(params),
            headers: {
              'Content-Type': 'application/json',
              'x-webhook-key': env.API_WEBHOOK_SECRET,
            },
          });
        } else {
          request.continue();
        }
      });

      await page.goto(url, { waitUntil: 'networkidle0' });
      const element = await page.$('.main');

      const imageBuffer = await element.screenshot({
        type: 'jpeg',
        quality: 100,
      });

      page.on('pageerror', (error) =>
        this.moduleLogger.error(`PUPPETEER: Page error ${error.message}`),
      );
      page.on('close', () => console.log('PUPPETEER: page closed'));

      this.closePage(page);

      if (save) {
        let name = await saveImage(
          {
            originalname: 'image.jpeg',
            buffer: imageBuffer,
          },
          folderName,
          params.fileName,
        );
        this.moduleLogger.log('generateImage result', { name });

        return {
          url: join('/uploads', folderName, name).replace(/\\/g, '/'),
        };
      } else {
        this.moduleLogger.log('generateImage result', { imageBuffer: true });
        return imageBuffer;
      }
    } catch (err) {
      console.error('Error in generateImage', err);
      this.moduleLogger.error('generateImage error', { err });
      await this.closePage(page);
      return null;
    }
  }

  private async closePage(page: Page) {
    try {
      const client = await page.target().createCDPSession();
      await client.send('Network.clearBrowserCache');
      page.on('close', () => console.log('PUPPETEER: page closed'));
      await page.close();
    } catch {
      await page.close();
    }
    page.off('request');
    page.off('close');
    page.off('pageerror');
  }
}
