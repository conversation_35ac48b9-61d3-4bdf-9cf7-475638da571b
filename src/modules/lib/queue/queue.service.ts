import { QueueStatus } from '@/shared/enums/queue.enum';
import { QueueEntity } from '@entities';
import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

@Injectable()
export class QueueService {
  private readonly prefix: string;
  private methodName?: string;

  constructor(
    @Inject('MODULE_NAME') prefix: string,
    @InjectRepository(QueueEntity)
    private queueRepo: Repository<QueueEntity>,
  ) {
    this.prefix = prefix;
  }

  private get module() {
    if (!this.methodName) return this.prefix;
    else return `${this.prefix}.${this.methodName}`;
  }
  method(name: string) {
    this.methodName = name;
    return this;
  }

  async save(value: string) {
    let item = await this.queueRepo.findOne({
      where: {
        module: this.module,
        value,
      },
    });
    if (item) {
      this.queueRepo.save({
        ...item,
        status: QueueStatus.INPROGRESS,
        tryCount: 0,
      });
    } else {
      await this.queueRepo.save(
        this.queueRepo.create({
          module: this.module,
          status: QueueStatus.INPROGRESS,
          value: value,
          creation_date: new Date(),
        }),
      );
    }
  }

  async remove(value: string) {
    await this.queueRepo.delete({
      module: this.module,
      value,
    });
  }
  async update(value: string, data: QueryDeepPartialEntity<QueueEntity>) {
    await this.queueRepo.update(
      {
        module: this.module,
        value,
      },
      data,
    );
  }
}
