import { Module, DynamicModule } from '@nestjs/common';
import { QueueService } from './queue.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueEntity } from '@entities';

@Module({})
export class QueueModule {
  static register(prefix: string): DynamicModule {
    return {
      module: QueueModule,
      imports: [TypeOrmModule.forFeature([QueueEntity])],
      providers: [
        {
          provide: 'MODULE_NAME',
          useValue: prefix,
        },
        {
          provide: QueueService,
          useClass: QueueService,
        },
      ],
      exports: [QueueService],
    };
  }
}
