import { BadRequestException } from '@nestjs/common';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { promises as fs } from 'fs';
import { env } from '../../../../app.env';

const validImageMimeType = ['image/png', 'image/jpg', 'image/jpeg'];

export const imageFileFilter = (req, file, cb) => {
  if (!file.originalname.toLowerCase().match(/\.(jpg|jpeg|png)$/)) {
    return cb(new BadRequestException('Only image files are allowed!'), false);
  }
  if (!validImageMimeType.includes(file.mimetype)) {
    return cb(new BadRequestException('Only image files are allowed!'), false);
  }
  cb(null, true);
};

export const saveImage = async (
  file: Express.Multer.File | { originalname: string; buffer: Buffer },
  folder = 'image-library',
  name?: string,
) => {
  let originalfilename = file.originalname.toLowerCase();
  let ext = originalfilename.split('.')[originalfilename.split('.').length - 1];
  name = name || `${uuidv4()}.${ext}`;

  const folderPath = join(env.APP_PATH, `uploads/${folder}`);
  const filePath = join(folderPath, name);

  await fs.mkdir(folderPath, { recursive: true }).catch(() => false);

  await fs.writeFile(filePath, Buffer.from(file.buffer));

  return name;
};

export const deleteImageByName = async (
  image: string,
  folder = 'image-library',
) => {
  const folderPath = join(env.APP_PATH, `uploads/${folder}`);
  const filePath = join(folderPath, image);

  await fs.unlink(filePath).catch(() => false);

  return true;
};
