import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
} from 'class-validator';

export class CreateImageLibraryBodyDto {
  @Type()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @ApiProperty({ required: true })
  name: string;

  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({ required: false })
  parent_id?: number | null;
}
export class RenameImageLibraryBodyDto {
  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  folder_id: number;

  @Type()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @ApiProperty({ required: true })
  name: string;
}

export class CreateImageBodyDto {
  @ApiProperty({ type: 'string', format: 'binary' })
  image: any;
}
