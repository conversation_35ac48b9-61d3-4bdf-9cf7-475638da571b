import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiConsumes, ApiTags } from '@nestjs/swagger';
import {
  CreateImageBodyDto,
  CreateImageLibraryBodyDto,
  RenameImageLibraryBodyDto,
} from './dto/post-image-library.dto';
import { ImageLibraryService } from './image-library.service';
import { imageFileFilter } from './utils/file.utils';

@Controller({
  path: '/image-library',
  version: '2',
})
@ApiTags('Image Library')
@ApiBearerAuth()
export class ImageLibraryController {
  constructor(private readonly imageLibraryService: ImageLibraryService) {}

  @Get()
  @UseGuards(AuthGuard)
  getList() {
    return this.imageLibraryService.getNestedContent();
  }

  @Post('/folder')
  @UseGuards(AuthGuard)
  async createFolder(@Body() body: CreateImageLibraryBodyDto) {
    let folder_id = await this.imageLibraryService.createFolder(body);
    return {
      message: 'Folder created',
      folder_id,
    };
  }

  @Post('/rename')
  @UseGuards(AuthGuard)
  async renameFolder(@Body() body: RenameImageLibraryBodyDto) {
    let folder_id = await this.imageLibraryService.renameFolder(body);
    return {
      message: 'Folder renamed',
      folder_id,
    };
  }

  @Delete('/folder/:id')
  @UseGuards(AuthGuard)
  async deleteFolder(@Param('id') id: number) {
    let folder_id = await this.imageLibraryService.deleteFolder(id);
    return {
      message: 'Folder deleted',
      folder_id,
    };
  }

  @Post('/image')
  @UseGuards(AuthGuard)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('image', {
      fileFilter: imageFileFilter,
      limits: { files: 1 },
    }),
  )
  uploadImage(
    @Query('folder_id') folder_id: number,
    @UploadedFile() image: Express.Multer.File,
    @Body() uploadDto: CreateImageBodyDto,
  ) {
    return this.imageLibraryService.createImage(folder_id, image);
  }

  @Delete('image/:id')
  @UseGuards(AuthGuard)
  deleteImage(@Param('id') id: number) {
    return this.imageLibraryService.deleteImage(id);
  }
}
