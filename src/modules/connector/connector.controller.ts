import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import connectorSchemas from './schemas/connector-schemas';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { ConnectorService } from './connector.service';
import {
  AddConnectorDto,
  AddConnectorSuccessResponseDto,
  CampaignDto,
  SurveyDto,
} from './dto/add-connector.dto';
import {
  DeleteConnectorResponseDto,
  DeleteConnectorErrorResponseDto,
} from './dto/delete-connector.dto';
import { GetConnectorListResponse } from './dto/get-connector.dto';

@ApiTags('connector')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'connector',
})
export class ConnectorController {
  constructor(private connectorService: ConnectorService) {}

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Connectors',
    type: GetConnectorListResponse,
  })
  @Get('/')
  getBlackList() {
    return this.connectorService.getConnectors();
  }

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Connector deleted',
    type: DeleteConnectorResponseDto,
  })
  @ApiResponse({
    description: 'Unable to delete connector',
    status: HttpStatus.BAD_REQUEST,
    type: DeleteConnectorErrorResponseDto,
  })
  @Delete('/:id')
  deleteConnector(@Param('id') id: number) {
    return this.connectorService.deleteConnector(id);
  }

  @ApiOkResponse({
    description: 'Connector object added',
    type: AddConnectorSuccessResponseDto,
  })
  @UseGuards(AuthGuard)
  @Post('/:id/data')
  @ApiBody({ schema: connectorSchemas.connector_array })
  pushToUsabilaOrNetigate(
    @Param('id') id: number,
    @Body() array: SurveyDto[] & CampaignDto[],
  ) {
    return this.connectorService.pushToUsabilaOrNetigate(id, array);
  }

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Connector setting added',
    type: AddConnectorSuccessResponseDto,
  })
  @ApiResponse({
    description: 'Connector setting updated',
    status: HttpStatus.OK,
    type: AddConnectorSuccessResponseDto,
  })
  @ApiQuery({ required: false, name: 'id' })
  @UseGuards(AuthGuard)
  @Post('/settings')
  createOrUpdateConnectorSettings(
    @Body() connector: AddConnectorDto,
    @Query('id') id?: number,
  ) {
    if (id) {
      return this.connectorService.updateConnectorSettings(connector, id);
    } else {
      return this.connectorService.addConnectorSettings(connector);
    }
  }
}
