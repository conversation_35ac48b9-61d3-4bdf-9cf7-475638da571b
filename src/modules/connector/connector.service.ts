import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConnectorEntity } from '../database/entities/connector.entity';
import { UserEntity } from '../database/entities/sec-user.entity';
import {
  AddConnectorDto,
  CampaignDto,
  SurveyDto,
} from './dto/add-connector.dto';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsService } from 'nestjs-cls';

enum ConnectorType {
  Usabilla = 1,
  Netigate,
}

@Injectable()
export class ConnectorService {
  constructor(
    private cls: ClsService,
    @InjectRepository(ConnectorEntity)
    private readonly connectorsRepository: Repository<ConnectorEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {}

  public async getConnectors() {
    const { company } = this.cls.get<AuthorizedData>('user');

    const connectors = await this.connectorsRepository.find({
      where: {
        sec_company_id: company.id,
      },
    });
    return {
      connectors: connectors.map((c) => ({
        connector_id: c.connector_id,
        connector_object: JSON.parse(c.connector_object),
        connector_type: c.connector_type,
        connector_type_name: ConnectorType[c.connector_type],
      })),
    };
  }

  public async deleteConnector(id: number) {
    const { company } = this.cls.get<AuthorizedData>('user');

    const result = await this.connectorsRepository.findOne({
      where: { sec_company_id: company.id, connector_id: id },
    });

    if (!result) {
      throw new HttpException(
        {
          msg: 'Unable to delete connector',
          error: 'Unable to delete connector',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.connectorsRepository.delete({
      sec_company_id: company.id,
      connector_id: id,
    });

    return {
      msg: 'Connector deleted',
    };
  }

  public async pushToUsabilaOrNetigate(
    id: number,
    array: SurveyDto[] & CampaignDto[],
  ) {
    const findedConnector = await this.connectorsRepository.findOne({
      where: {
        connector_id: id,
      },
    });
    findedConnector.connector_object = JSON.parse(
      findedConnector.connector_object,
    );
    if (!findedConnector) {
      throw new HttpException(
        {
          msg: "Connetor don't exist",
          error: "Connector don't exist",
        },
        HttpStatus.NOT_FOUND,
      );
    }
    switch (findedConnector.connector_type) {
      case ConnectorType.Netigate:
        findedConnector.connector_object.survey_array = array;
        break;
      case ConnectorType.Usabilla:
        findedConnector.connector_object.survey_array = array;
    }
    await this.connectorsRepository.update(
      findedConnector.connector_id,
      findedConnector,
    );

    return {
      msg: 'Connector object added',
      id: findedConnector.connector_id,
      status: HttpStatus.CREATED,
    };
  }

  public async addConnectorSettings(connector: AddConnectorDto) {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    let connector_object: string;
    if (connector.connector_type === ConnectorType.Netigate)
      connector_object = JSON.stringify({
        API_key: connector.connector_object.API_key,
        Touchpoint: connector.connector_object.Touchpoint,
        Testmode: connector.connector_object.Testmode,
        xml_department: connector.connector_object.xml_department,
      });
    else {
      connector_object = JSON.stringify({
        Username: connector.connector_object.Username,
        Password: connector.connector_object.Password,
        Touchpoint: connector.connector_object.Touchpoint,
        Testmode: connector.connector_object.Testmode,
        xmlDepartment: connector.connector_object.xmlDepartment,
      });
    }
    const newConnector = await this.connectorsRepository.save({
      connector_type: connector.connector_type,
      connector_object: connector_object,
      creator: user.email,
      modifier: user.email,
      creation_date: new Date(),
      modification_date: new Date(),
      sec_company_id: company.id,
    });

    return {
      msg: 'Connector settings added',
      id: newConnector.connector_id,
      status: HttpStatus.CREATED,
    };
  }

  public async updateConnectorSettings(connector: AddConnectorDto, id: number) {
    const { user } = this.cls.get<AuthorizedData>('user');

    const findedConnector = await this.connectorsRepository.findOne({
      where: {
        connector_id: id,
      },
    });
    if (!findedConnector) {
      throw new HttpException(
        {
          msg: "Connetor don't exist",
          error: "Connector don't exist",
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const connector_object = JSON.parse(
      findedConnector.connector_object,
    ) as any;
    if (connector.connector_type === ConnectorType.Netigate) {
      connector_object.API_key = connector.connector_object.API_key;
      connector_object.Touchpoint = connector.connector_object.Touchpoint;
      connector_object.Testmode = connector.connector_object.Testmode;
      connector_object.xml_department =
        connector.connector_object.xml_department;
    } else {
      connector_object.Username = connector.connector_object.Username;
      connector_object.Password = connector.connector_object.Password;
      connector_object.Touchpoint = connector.connector_object.Touchpoint;
      connector_object.Testmode = connector.connector_object.Testmode;
      connector_object.xmlDepartment = connector.connector_object.xmlDepartment;
    }

    await this.connectorsRepository.update(id, {
      connector_object: connector_object,
      modification_date: new Date(),
      modifier: user.email,
    });

    return {
      msg: 'Connector settings updated',
      status: HttpStatus.OK,
    };
  }
}
