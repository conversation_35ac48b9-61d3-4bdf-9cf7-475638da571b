const campaign_array = {
  type: 'array',
  items: {
    type: 'object',
    properties: {
      campaign_id: { type: 'string' },
      device: { type: 'number' },
      feedback: { type: 'number' },
      nps: { type: 'number' },
      sort_order: { type: 'string' },
      xml_brand: { type: 'string' },
      email_temaplate_id: { type: 'number' },
      xml_region: { type: 'string' },
      LastTimeStamp: { type: 'string' }
    }
  }
};

const survey_array = {
  type: 'array',
  items: {
    type: 'object',
    properties: {
      survey_id: { type: 'number' },
      xml_brand: { type: 'string' },
      email_template_id: { type: 'number' },
      LastTimeStamp: { type: 'string' },
      questions: { type: 'object' }
    }
  }
};

const connectorSchemas = {
  connector_object: {
    oneOf: [
      {
        type: 'object',
        properties: {
          API_key: { type: 'string' },
          Touchpoint: { type: 'number' },
          Testmode: { type: 'number' },
          xml_department: { type: 'number' }
        }
      },
      {
        type: 'object',
        properties: {
          Username: { type: 'string' },
          Password: { type: 'string' },
          Touchpoint: { type: 'number' },
          Testmode: { type: 'number' },
          xmlDepartment: { type: 'number' }
        }
      }
    ]
  },
  read_connector_object: {
    oneOf: [
      {
        type: 'object',
        properties: {
          API_key: { type: 'string' },
          Touchpoint: { type: 'number' },
          Testmode: { type: 'number' },
          xml_department: { type: 'number' },
          survey_array: survey_array
        }
      },
      {
        type: 'object',
        properties: {
          Username: { type: 'string' },
          Password: { type: 'string' },
          Touchpoint: { type: 'number' },
          Testmode: { type: 'number' },
          xmlDepartment: { type: 'number' },
          campaign_array: campaign_array
        }
      }
    ]
  },

  campaign_array: campaign_array,
  survey_array: survey_array,
  connector_array: {
    oneOf: [campaign_array, survey_array]
  }
};

export default connectorSchemas;
