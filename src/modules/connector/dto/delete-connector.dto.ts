import { ApiProperty } from '@nestjs/swagger';

export class DeleteConnectorSuccessDto {
  @ApiProperty()
  msg: string;
}

export class DeleteConnectorErrorDto {
  @ApiProperty()
  msg: string;

  @ApiProperty()
  error: string;
}

export class DeleteConnectorResponseDto extends DeleteConnectorSuccessDto {
  // @ApiProperty()
  // data: DeleteConnectorSuccessDto;
}

export class DeleteConnectorErrorResponseDto extends DeleteConnectorErrorDto {
  // @ApiProperty()
  // data: DeleteConnectorErrorDto;
}
