import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsString, Min } from 'class-validator';
import connectorSchemas from '../schemas/connector-schemas';
export class SurveyDto {
  @ApiProperty()
  survey_id: number;

  @ApiProperty()
  xml_brand: string;

  @ApiProperty()
  email_template_id: number;

  @ApiProperty()
  LastTimeStamp: Date;

  @ApiProperty()
  questions: string;
}

export class CampaignDto {
  @ApiProperty()
  campaign_id: string;

  @ApiProperty()
  device: number;

  @ApiProperty()
  feedback: number;

  @ApiProperty()
  nps: number;

  @ApiProperty()
  sort_order: string;

  @ApiProperty()
  xml_brand: string;

  @ApiProperty()
  email_template_id: number;

  @ApiProperty()
  xml_region: string;

  @ApiProperty()
  LastTimeStamp: Date;
}
export class NetigateConnector {
  @ApiProperty()
  API_key: string;

  @ApiProperty()
  Touchpoint: number;

  @ApiProperty()
  Testmode: number;

  @ApiProperty()
  xml_department: string;

  @ApiProperty()
  survey_array: SurveyDto[];
}

export class UsabilaConnector {
  @IsString()
  @ApiProperty()
  Username: string;

  @IsString()
  @ApiProperty()
  Password: string;

  @IsInt()
  @ApiProperty()
  Touchpoint: number;

  @IsInt()
  @ApiProperty()
  Testmode: number;

  @IsInt()
  @ApiProperty()
  xmlDepartment: number;

  @ApiProperty()
  campaign_array: CampaignDto[];
}

export class AddConnectorDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  @Min(1)
  connector_type: number;

  @ApiProperty({ ...connectorSchemas.connector_object })
  connector_object: NetigateConnector & UsabilaConnector;
}

export class AddConnectorSuccessDto {
  @ApiProperty()
  msg: string;

  @ApiProperty()
  id: number;
}

export class AddConnectorErrorDto {
  @ApiProperty()
  msg: string;

  @ApiProperty()
  error: string;
}

export class AddConnectorSuccessResponseDto extends AddConnectorSuccessDto {
  // @ApiProperty()
  // data: AddConnectorSuccessDto;
}

export class AddConnectorErrorResponseDto extends AddConnectorErrorDto {
  // @ApiProperty()
  // data: AddConnectorErrorDto;
}
