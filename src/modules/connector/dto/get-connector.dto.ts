import { NetigateConnector, UsabilaConnector } from './add-connector.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Min } from 'class-validator';
import connectorSchemas from '../schemas/connector-schemas';

export class GetConnectorDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  @Min(1)
  connector_type: number;

  @ApiProperty({ ...connectorSchemas.read_connector_object })
  connector_object: NetigateConnector & UsabilaConnector;

  @ApiProperty()
  connector_type_name: string;
}

export class GetConnectorListDto {
  @ApiProperty({
    type: [GetConnectorDto],
  })
  connectors: GetConnectorDto[];
}

export class GetConnectorListResponse extends GetConnectorListDto {
  // @ApiProperty()
  // data: GetConnectorListDto;
}
