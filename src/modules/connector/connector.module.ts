import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConnectorEntity } from '../database/entities/connector.entity';
import { EmailTemplateEntity } from '../database/entities/email-template.entity';
import { UserEntity } from '../database/entities/sec-user.entity';
import { ConnectorController } from './connector.controller';
import { ConnectorService } from './connector.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ConnectorEntity,
      UserEntity,
      EmailTemplateEntity,
    ]),
  ],
  controllers: [ConnectorController],
  providers: [ConnectorService],
})
export class ConnectorModule {}
