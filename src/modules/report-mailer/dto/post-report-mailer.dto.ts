import { EmployeeType } from '@/shared/enums/datatable.enum';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum } from 'class-validator';

export class SendNpsRecordBodyDto {
  @Type()
  @ApiProperty({
    enum: ['response_date', 'visit_date'],
  })
  date_field: 'response_date' | 'visit_date';

  @Type()
  @ApiProperty()
  dateFrom: Date;

  @Type()
  @ApiProperty()
  dateTo: Date;

  @Type()
  @IsEnum(EmployeeType)
  @ApiProperty()
  employeeType: EmployeeType;
}
