import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmailCustomerEntity } from '../database/entities/email-customer-ec.entity';
import { ReportMailerController } from './report-mailer.controller';
import { ReportMailerService } from './report-mailer.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('report-mailer'),
    TypeOrmModule.forFeature([EmailCustomerEntity]),
  ],
  controllers: [ReportMailerController],
  providers: [ReportMailerService],
})
export class ReportMailerModule {}
