import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { SendNpsRecordBodyDto } from './dto/post-report-mailer.dto';
import { ReportMailerService } from './report-mailer.service';

@ApiTags('Report Mailer')
@ApiBearerAuth()
@Controller({
  path: 'report_mailer',
  version: '2',
})
export class ReportMailerController {
  constructor(private reportMailerService: ReportMailerService) {}

  @Post()
  @UseGuards(AuthGuard)
  async sendReportMailer(@Body() body: SendNpsRecordBodyDto) {
    return await this.reportMailerService.sendNPSReport(body);
  }
}
