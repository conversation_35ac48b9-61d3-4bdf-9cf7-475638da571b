import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { SendNpsRecordBodyDto } from './dto/post-report-mailer.dto';
import { ClsProperty } from '@/config/contents';

import { EmailCustomerEntity } from '@entities';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

export class ReportMailerService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(EmailCustomerEntity)
    private emailCustomerRepo: Repository<EmailCustomerEntity>,
  ) {}
  async sendNPSReport(body: SendNpsRecordBodyDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const { dateFrom, dateTo, employeeType, date_field } = body;

    this.logger.debug('sendNPSReport', { body, company });

    let list = await this.emailCustomerRepo.query(
      `
    SELECT mail.${employeeType} AS id,
    COALESCE(tech.techgrp_firstname, '') || ' ' || COALESCE(tech.techgrp_name, '')  AS lastname,
 tech.techgrp_group AS region,
 tech.techgrp_email AS email,
 MAX(${
   date_field == 'response_date' ? 'sa.creation_date' : 'mail.creation_date'
 }) as date,
 round(T2.p_prom-T2.p_detr) AS nps,
 COALESCE((cast(t2.total AS decimal)/cast(count(mail.send_date) AS decimal)), 0) AS response_rate,
 count(mail.send_date) AS sent,
 COALESCE(t2.total, 0) AS completed
FROM email_customer mail
LEFT JOIN (
 SELECT t.${employeeType}, count(1) as Total, sum(T.detr) as t_detr,
        (cast(sum(t.detr) as decimal)/cast(count(1) as decimal) * 100) as p_detr,
        sum(T.prom) as t_prom, (cast(sum(t.prom) as decimal)/cast(count(1) as decimal) * 100) as p_prom
 FROM (  SELECT a.nps_value, ec.${employeeType},
         CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detr,
         CASE WHEN a.nps_value BETWEEN 9 AND 10 THEN 1 ELSE 0 END AS prom
         FROM surv_surveyanswer a
         LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id
         LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id
         AND ec.survey_done = 1
         AND a.sec_company_id = $1
         AND a.creation_date BETWEEN $2 AND $3) T
 GROUP BY T.${employeeType}) T2 ON mail.${employeeType}=T2.${employeeType}
LEFT JOIN surv_surveyanswer sa ON mail.email_customer_id = sa.email_customer_id
LEFT JOIN surv_surveytemplate st ON mail.email_template_id=st.email_template_id
LEFT JOIN techgrp tech ON tech.techgrp_techn = mail.${employeeType} AND tech.sec_company_id = $1
GROUP BY mail.${employeeType}, tech.techgrp_firstname, tech.techgrp_name, tech.techgrp_group, tech.techgrp_email, T2.p_prom-T2.p_detr, t2.total
ORDER BY mail.${employeeType}
    `,
      [company.id, dateFrom, dateTo],
    );

    this.logger.debug('sendNPSReport result', { list });
    return { employees: list };
  }
}
