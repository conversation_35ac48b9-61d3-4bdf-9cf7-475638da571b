import { Injectable, OnModuleInit } from "@nestjs/common";
import {WebSocketGateway, WebSocketServer} from '@nestjs/websockets';
import { Server, Socket } from "socket.io";

@WebSocketGateway({
  cors: {
    origin: '*',
  }
})
@Injectable()
export class GatewayService implements OnModuleInit{

  @WebSocketServer() server: Server
  private sockets: { [clientId: string]: Socket } = {};

  // onModuleInit(): any {
  //   this.server.on('connection', (socket) => {
  //     console.log('Client connected');
  //     socket.on('disconnect', () => console.log('Client disconnected'));
  //   });
  // }
  //
  // sendEvent(data: any) {
  //   this.server.emit('onToken', {data});
  // }

  onModuleInit(): any {
    this.server.on('connection', (socket) => {
      // Assume the client sends a 'register' event upon connecting
      // The 'register' event should include a unique ID for the client
      socket.on('register', (clientId) => {
        this.sockets[clientId] = socket;
        socket.on('disconnect', () => {
          delete this.sockets[clientId];
        });
      });
    });
  }

  sendEvent( data: any, clientId: string) {
    if(!this.sockets[clientId]) {
      this.sockets[clientId] = this.server.sockets[clientId];
    }
    const clientSocket = this.sockets[clientId];
    if (clientSocket) {
      clientSocket.emit('onToken', {data});
    } else {
      console.log(`No active connection for client ${clientId}`);
    }
  }

}
