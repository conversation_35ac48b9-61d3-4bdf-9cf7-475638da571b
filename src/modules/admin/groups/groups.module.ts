import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecGroupEntity } from '../../database/entities/sec-group.entity';
import { SecUserRight } from '../../database/entities/sec-user-right.entity';
import { AdminGroupsController } from './groups.controller';
import { AdminGroupService } from './groups.service';
import { SecKey } from '../../database/entities/sec-key.entity';
import { AuthModule } from '../../auth/auth.module';
import { FeedbackPropertiesModule } from '../../common/feedback-properties/feedback-properties.module';
import { AdminUsersModule } from '../users/users.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SecGroupEntity, SecUserRight, SecKey]),
    forwardRef(() => AuthModule),
    forwardRef(() => AdminUsersModule),
    FeedbackPropertiesModule,
  ],
  controllers: [AdminGroupsController],
  providers: [AdminGroupService],
  exports: [AdminGroupService],
})
export class AdminGroupsModule {}
