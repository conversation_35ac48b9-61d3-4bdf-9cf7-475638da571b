import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../../auth/interfaces/authorized-request.interface';
import { UpsertGroupBodyDto } from './dto/post-group.dto';
import { AdminGroupService } from './groups.service';
import { AdminUsersService } from '../users/users.service';
import { AdminUsersRegionsService } from '../users/users-regions.service';
import {
  AdminGetAuthorizationsFilterDto,
  AdminUpsertGroupAuthorizationDto,
} from './dto/group-authorizations.dto';
import { AuthGuard } from '@/shared/guards/auth.guard';

@ApiTags('(Admin) Groups')
@ApiBearerAuth()
@Controller({
  path: '/admin/groups',
  version: '2',
})
export class AdminGroupsController {
  constructor(
    private groupService: AdminGroupService,
    private adminUsersService: AdminUsersService,
    private adminUserRegionService: AdminUsersRegionsService,
  ) {}

  @Get()
  @UseGuards(AuthGuard)
  getGroups(@Req() { user }: AuthorizedRequest) {
    return this.groupService.getGroups();
  }

  @Post()
  @UseGuards(AuthGuard)
  createGroup(
    @Body() body: UpsertGroupBodyDto,
    @Req() { user }: AuthorizedRequest,
  ) {
    return this.groupService.createGroup(body);
  }

  @Post(':id')
  @UseGuards(AuthGuard)
  updateGroup(
    @Body() body: UpsertGroupBodyDto,
    @Req() { user }: AuthorizedRequest,
    @Param('id') id: number,
  ) {
    body.id = id;
    return this.groupService.updateGroup(body);
  }

  @Delete(':groupId')
  @UseGuards(AuthGuard)
  deleteGroup(
    @Param('groupId') groupId: number,
    @Req() { user }: AuthorizedRequest,
  ) {
    return this.groupService.deleteGroup(groupId);
  }

  @Get(':groupId/users')
  @UseGuards(AuthGuard)
  getUsers(
    @Param('groupId') group_id: string,
    @Req() { user }: AuthorizedRequest,
  ) {
    return this.adminUsersService.getUsers(
      { group_id },
      {
        exclude: [
          'additional_authorizations',
          'notification_settings',
          'regions',
        ],
      },
    );
  }

  @Get(':groupId/regions')
  @UseGuards(AuthGuard)
  getRegions(
    @Param('groupId') group_id: number,
    @Req() { user }: AuthorizedRequest,
  ) {
    return this.adminUserRegionService.getGroupCompanies(group_id);
  }

  @Get('/authorizations')
  @UseGuards(AuthGuard)
  getAuthorizations(@Query() query: AdminGetAuthorizationsFilterDto) {
    return this.groupService.getAuthorizations(query);
  }

  @Get(':groupId/authorizations')
  @UseGuards(AuthGuard)
  getGroupAuthorizations(
    @Param('groupId') groupId: number,
    @Query() query: AdminGetAuthorizationsFilterDto,
  ) {
    return this.groupService.getGroupAuthorizations(groupId, query);
  }

  @Post(':groupId/authorizations')
  @UseGuards(AuthGuard)
  upsertGroupAuthorizations(
    @Param('groupId') groupId: number,
    @Query() query: AdminGetAuthorizationsFilterDto,
    @Body() body: AdminUpsertGroupAuthorizationDto,
  ) {
    return this.groupService.updateGroupAuthorizations(
      groupId,
      query,
      body.items,
    );
  }
}
