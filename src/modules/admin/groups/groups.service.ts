import { HttpException } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import {
  Brackets,
  DataSource,
  In,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import {
  AuthorizedData,
  AuthorizedUser,
} from '../../auth/interfaces/authorized-request.interface';
import { UpsertGroupBodyDto } from './dto/post-group.dto';
import {
  AdminGetAuthorizationsFilterDto,
  AdminUpsertGroupAuthorizationItem,
} from './dto/group-authorizations.dto';
import { IUserGroup } from './dto/get-group.dto';

import { parentCompanyGroup, welcomeCompany } from '@/config/company.config';
import { SecGroupEntity, SecKey, SecUserRight } from '@entities';
import {
  IsAdmin,
  IsWelcome,
  compareListItems,
  convertJsonAgg,
} from '@/shared/utils';
import { CompanyType, SolutionType } from '@/shared/enums';
import { ClsProperty } from '@/config/contents';
import { TranslateService } from '@/core/modules/translate/translate.service';

export class AdminGroupService {
  constructor(
    private cls: ClsService,
    private translate: TranslateService,
    @InjectDataSource()
    private dataSource: DataSource,
    @InjectRepository(SecGroupEntity)
    private readonly secGroupRepo: Repository<SecGroupEntity>,
    @InjectRepository(SecUserRight)
    private readonly secUserRightRepo: Repository<SecUserRight>,
    @InjectRepository(SecKey)
    private readonly secKeyRepo: Repository<SecKey>,
  ) {}

  async getGroups() {
    const { company } = this.cls.get<AuthorizedData>('user');
    let noAdmin = !IsAdmin(company.id);
    let companyType: CompanyType = company.type;

    let companySql = '';
    if (noAdmin) {
      if (companyType != CompanyType.BSH) {
        companySql = ` AND g.sec_company_id = ${company.id}`;
      } else {
        companySql = ` AND (g.sec_company_id = ${company.id} OR g.sec_company_id = 25 OR g.sec_company_id = 1)`;
      }
    }

    if (!IsWelcome(company.id)) {
      companySql += ` AND (g.sec_company_id != ${welcomeCompany} OR g.group_id = ${parentCompanyGroup})`;
    }

    let list = await this.secGroupRepo.query(
      `SELECT g.group_id, g.name, array_agg(key_id) AS keys, g.sec_company_id, c.company_code
    FROM sec_group g
    LEFT JOIN sec_user_right r USING (group_id)
    LEFT JOIN sec_company c ON c.sec_company_id = g.sec_company_id
    WHERE
    group_id != 0
    ${companySql}
    GROUP BY group_id, name, c.company_code
    ORDER BY name`,
    );
    return list.map((i) => {
      return { ...i, keys: i.keys.filter((key) => key) };
    });
  }

  async createGroup(params: UpsertGroupBodyDto) {
    const { company } = this.cls.get<AuthorizedData>('user');

    const { name, keys } = params;
    let group = await this.secGroupRepo
      .createQueryBuilder()
      .insert()
      .values({ name: name, sec_company_id: company.id })
      .returning('group_id')
      .execute();
    let group_id = group.generatedMaps[0].group_id;
    let key_ids = [];
    if (keys?.length) {
      let savedKeys = await this.secUserRightRepo
        .createQueryBuilder()
        .insert()
        .values(keys.map((key_id) => ({ group_id, key_id })))
        .returning('user_right_id')
        .execute();
      key_ids = savedKeys.generatedMaps.map((i) => i.user_right_id);
    }
    return {
      group_id,
      key_ids,
    };
  }

  async updateGroup(params: UpsertGroupBodyDto) {
    const { company } = this.cls.get<AuthorizedData>('user');
    const { id, name, keys } = params;
    let group = await this.secGroupRepo
      .createQueryBuilder()
      .where('group_id =:id', { id })
      .getOne();

    let isUserAdmin = IsAdmin(company.id);

    if (!group) throw new HttpException('group is not found', 404);
    else if (group.sec_company_id != company.id && !isUserAdmin)
      throw new HttpException(
        `You don't have access to remove this group`,
        403,
      );

    await this.secGroupRepo
      .createQueryBuilder()
      .update({ name: name })
      .where('group_id =:id', { id })
      .execute();

    await this.secUserRightRepo
      .createQueryBuilder()
      .delete()
      .where('group_id =:id', { id })
      .execute();
    let key_ids = [];
    if (keys?.length) {
      let savedKeys = await this.secUserRightRepo
        .createQueryBuilder()
        .insert()
        .values(keys.map((key_id) => ({ group_id: id, key_id })))
        .returning('user_right_id')
        .execute();
      key_ids = savedKeys.generatedMaps.map((i) => i.user_right_id);
    }
    return {
      group_id: id,
      key_ids,
    };
  }

  async deleteGroup(group_id: number) {
    const { company } = this.cls.get<AuthorizedData>('user');

    let group = await this.secGroupRepo.findOne({
      where: { group_id },
    });
    let isUserAdmin = IsAdmin(company.id);

    if (!group) throw new HttpException('group is not found', 404);
    else if (group.sec_company_id != company.id && !isUserAdmin)
      throw new HttpException(
        `You don't have access to remove this group`,
        403,
      );

    await this.secUserRightRepo.delete({ group_id });

    await this.secGroupRepo.delete({ group_id });
    return {
      success: true,
    };
  }

  async getAuthorizations(filter: AdminGetAuthorizationsFilterDto) {
    const { company } = await this.cls.get<AuthorizedData>(ClsProperty.user);
    let query = this.secKeyRepo
      .createQueryBuilder('k')
      .leftJoinAndSelect(
        'k.description_translated',
        'dt',
        '(dt.message_language IS NULL OR dt.message_language = :lng)',
        {
          lng: 'en',
        },
      )
      .leftJoinAndSelect(
        'k.tooltip_translated',
        'tt',
        '(tt.message_language IS NULL OR tt.message_language = :lng)',
        {
          lng: 'en',
        },
      );
    query.where('k.key_id IS NOT NULL');
    if (filter.type) {
      query = query.andWhere('solution_type =:type', {
        type: filter.type || SolutionType.Normal,
      });
    }

    let items = await query.getMany();

    if (company.type === CompanyType.SPORT) {
      let item = items.find((i) => i.name === 'PROJECT_CATEGORIES');
      item.description = item.description.replace('project_', 'club_');
      item.tooltip = item.tooltip.replace('project_', 'club_');

      let translations = await this.translate.dbs([
        item.description,
        item.tooltip,
      ]);

      item.description_translated = {
        message_value: translations.find((t) => t.key === item.description)
          ?.value,
      } as any;

      item.tooltip_translated = {
        message_value: translations.find((t) => t.key === item.tooltip)?.value,
      } as any;
    }
    items = items.map((key) => {
      return {
        ...key,
        description:
          key.description_translated?.message_value || key.description,
        description_translated: undefined,
        tooltip: key.tooltip_translated?.message_value || key.tooltip,
        tooltip_translated: undefined,
      };
    });

    if (company.type === CompanyType.BSH) {
      items = items.filter((item) => item.name !== 'PROJECT_CATEGORIES');
    }
    items = items.sort((a, b) => a.description.localeCompare(b.description));
    return items;
  }

  async getGroupAuthorizations(
    group_id: number,
    filter: AdminGetAuthorizationsFilterDto,
    includeRights: boolean = false,
  ) {
    const { company } = await this.cls.get<AuthorizedData>(ClsProperty.user);

    let query = this.secUserRightRepo
      .createQueryBuilder('ur')
      .leftJoinAndSelect('ur.key', 'k')
      .leftJoinAndSelect(
        'k.description_translated',
        'dt',
        '(dt.message_language IS NULL OR dt.message_language = :lng)',
        {
          lng: 'en',
        },
      )
      .leftJoinAndSelect(
        'k.tooltip_translated',
        'tt',
        '(tt.message_language IS NULL OR tt.message_language = :lng)',
        {
          lng: 'en',
        },
      )
      .where('ur.group_id =:group_id', {
        group_id,
      })
      .andWhere('k.key_id IS NOT NULL');

    if (filter.type) {
      query = query.andWhere('k.solution_type =:type', {
        type: filter.type || SolutionType.Normal,
      });
    }

    let items = await query.getMany();

    if (company.type === CompanyType.SPORT) {
      let item = items.find((i) => i?.key?.name === 'PROJECT_CATEGORIES');
      if (item?.key) {
        item.key.description = item.key.description.replace(
          'project_',
          'club_',
        );
        item.key.tooltip = item.key.tooltip.replace('project_', 'club_');

        let translations = await this.translate.dbs([
          item.key.description,
          item.key.tooltip,
        ]);

        item.key.description_translated = {
          message_value: translations.find(
            (t) => t.key === item.key.description,
          )?.value,
        } as any;

        item.key.tooltip_translated = {
          message_value: translations.find((t) => t.key === item.key.tooltip)
            ?.value,
        } as any;
      }
    }

    items = items.map((item) => {
      return {
        ...item,
        key: {
          ...item.key,
          description:
            item.key.description_translated?.message_value ||
            item.key.description,
          description_translated: undefined,
          tooltip:
            item.key.tooltip_translated?.message_value || item.key.tooltip,
          tooltip_translated: undefined,
        },
      };
    });

    items = items.sort((a, b) =>
      a.key.description.localeCompare(b.key.description),
    );

    if (includeRights) return items;
    return items.map((item) => item.key);
  }
  async updateGroupAuthorizations(
    group_id: number,
    filter: AdminGetAuthorizationsFilterDto,
    items: AdminUpsertGroupAuthorizationItem[],
  ) {
    if (!filter.type)
      throw new HttpException('you should put type to query', 400);

    let allItemslist = (await this.getGroupAuthorizations(
      group_id,
      filter,
      true,
    )) as any;

    let allItems: SecKey[] = allItemslist.map((item) => item.key);

    const result = compareListItems<Partial<SecKey>>(allItems, items, [
      'key_id',
    ]);

    const { createdItems, updatedItems, deletedItems } = result;

    if (createdItems.length) {
      let createItems: SecUserRight[] = createdItems.map((item) =>
        this.secUserRightRepo.create({
          group_id,
          key_id: item.key_id,
        }),
      );
      this.secUserRightRepo.save(createItems);
    }

    if (deletedItems.length) {
      let deleteIds = deletedItems.map(
        (item) =>
          allItemslist.find((i) => i.key_id == item.key_id)?.user_right_id,
      );
      await this.secUserRightRepo.delete({ user_right_id: In(deleteIds) });
    }

    return { created: createdItems.length, deleted: deletedItems.length };
  }

  getUserGroupsQuery(
    userId = null,
    query: SelectQueryBuilder<any> = null,
    serverName = null,
  ) {
    const { company } = this.cls.get<AuthorizedData>('user');
    let select = {
      company_id: 'sc.sec_company_id',
      company_type: 'sc.company_type',
      company_code: 'sc.company_code',
      group_id: 'scg.group_id',
      group_name: 'scg.name',
      country_code: 'sc.country_code',
    };

    let isUserAdmin = IsAdmin(company.id);
    if (!query) {
      query = this.dataSource.createQueryBuilder();
    }

    query
      .select(convertJsonAgg(select), 'regions')
      .from('sec_user_company', 'uc')
      .leftJoin('sec_company', 'sc', 'sc.sec_company_id = uc.sec_company_id')
      .leftJoin('sec_group', 'scg', 'scg.group_id = uc.sec_group')
      .where(`uc.sec_user_id = ${userId}`);
    if (!isUserAdmin) {
      query.andWhere(`uc.sec_company_id = :company_id`, {
        company_id: company.id,
      });
    } else {
      // if (!isWelcome) {
      //   query.andWhere(`uc.sec_company_id != ${welcomeCompany}`);
      // }
      // TODO: commented below lines, if not necesarry remove in future
      // query.andWhere(
      //   new Brackets((qb) => {
      //     qb.where(`scg.sec_company_id = ${welcomeCompany}`);
      //     if (
      //       process.env.ENABLE_GLOBAL_COMPANY === 'true' ||
      //       process.env.ENABLE_GLOBAL_COMPANY === '1'
      //     ) {
      //       qb.orWhere('scg.sec_company_id = 25');
      //     }
      //   }),
      // );
    }
    return query as SelectQueryBuilder<any>;
  }

  async getUserGroups(
    userId: any,
    query = null,
    serverName = null,
  ): Promise<IUserGroup[]> {
    // if (!serverName) {
    //   serverName = await this.propertyService.getItem(
    //     FEEDBACK_PROPERTY_KEYS.SERVER_NAME,
    //   );
    // }

    let data = await this.getUserGroupsQuery(
      userId,
      query,
      serverName,
    ).getRawMany();

    let regions: IUserGroup[] = data[0]?.regions || [];
    regions = regions.sort((a, b) =>
      a.company_code.localeCompare(b.company_code),
    );
    return data[0]?.regions || [];
  }
}
