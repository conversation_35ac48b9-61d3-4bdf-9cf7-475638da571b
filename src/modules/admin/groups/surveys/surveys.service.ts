import { HttpException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { HttpStatusCode } from 'axios';

import { SurvSurveyEntity, SurveyGroupsEntity } from '@entities';
import { IsAdmin, IsWelcome } from '@/shared/utils';

import { AuthorizedData } from '../../../auth/interfaces/authorized-request.interface';
import { AddSurveyGroupBodyDto } from './dto/post-survey.dto';

export class AdminSurveyService {
  constructor(
    private cls: ClsService,
    @InjectRepository(SurvSurveyEntity)
    private surveyRepo: Repository<SurvSurveyEntity>,

    @InjectRepository(SurveyGroupsEntity)
    private surveyGroupRepo: Repository<SurveyGroupsEntity>,
  ) {}

  async getList() {
    const { company } = this.cls.get<AuthorizedData>('user');

    let notAdmin = !IsAdmin(company.id);
    let query = this.surveyRepo
      .createQueryBuilder('s')
      .select('s.surv_survey_id')
      .addSelect('s.sec_company_id')
      .addSelect('s.name')
      .addSelect('s.internal_name')
      .addSelect('s.creation_date')
      .addSelect('s.surv_survey_id')
      .addSelect('c.company_code')
      .addSelect('sg.surv_survey_group_id')
      .addSelect('g.name')
      .addSelect('g.group_id')
      .leftJoin('s.groups', 'sg')
      .leftJoin('sg.group', 'g')
      .leftJoin('s.company', 'c')
      .where('s.surv_survey_id IS NOT NULL');

    if (notAdmin) {
      query.andWhere(`s.sec_company_id =:cid`, { cid: company.id });
    }

    if (!IsWelcome(company.id)) {
      query.andWhere(`c.sec_company_id != 1`);
    }

    let list = await query.getMany();
    return list.map((item) => {
      const groups = item.groups
        .map((sg) => sg.group?.group_id)
        .filter((group) => group);
      return {
        ...item,
        groups,
        company_code: item.company?.company_code,
        survey_group: undefined,
        company: undefined,
      };
    });
  }
  async createItem(body: AddSurveyGroupBodyDto) {
    const { company } = this.cls.get<AuthorizedData>('user');

    let item = await this.surveyRepo.findOne({
      where: { surv_survey_id: body.surv_survey_id },
    });
    if (!item?.surv_survey_id)
      throw new NotFoundException(
        `Survey with ${body.surv_survey_id} ID  is not found`,
      );
    let notAdmin = !IsAdmin(company.id);
    if (notAdmin) {
      if (company.id != item.sec_company_id) {
        throw new HttpException(`Forbidden`, HttpStatusCode.Forbidden);
      }
    }
    return this.surveyGroupRepo.save(
      this.surveyGroupRepo.create({
        surv_survey_id: body.surv_survey_id,
        group_id: body.group_id,
        sec_company_id: item.sec_company_id,
      }),
    );
  }

  // async updateItem(user: AuthorizedUser, body: UpdateSurveyGroupBodyDto) {
  //   let item = await this.surveyGroupRepo.findOne({
  //     where: { surv_survey_group_id: body.survey_group_id },
  //   });
  //   if (!item?.surv_survey_group_id)
  //     throw new NotFoundException(
  //       `Survey group with ${body.surv_survey_id} ID  is not found`,
  //     );
  //   let notAdmin = !isAdmin(user.sec_company_id);
  //   if (notAdmin) {
  //     if (user.sec_company_id != item.sec_company_id) {
  //       throw new HttpException(`Forbidden`, HttpStatusCode.Forbidden);
  //     }
  //   }

  //   for (let key in body) {
  //     if (key == 'survey_group_id') continue;
  //     item[key] = body[key];
  //   }

  //   return this.surveyGroupRepo.save(item);
  // }

  async deleteItem(surveyId: number, groupId: number) {
    const { company } = this.cls.get<AuthorizedData>('user');

    let item = await this.surveyGroupRepo.findOne({
      where: { surv_survey_id: surveyId, group_id: groupId },
    });
    if (!item?.surv_survey_group_id)
      throw new NotFoundException(`Survey group  is not found`);
    let notAdmin = !IsAdmin(company.id);
    if (notAdmin) {
      if (company.id != item.sec_company_id) {
        throw new HttpException(`Forbidden`, HttpStatusCode.Forbidden);
      }
    }
    await this.surveyGroupRepo.delete({
      surv_survey_id: surveyId,
      group_id: groupId,
    });
    return {
      success: true,
    };
  }
}
