import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';

export class AddSurveyGroupBodyDto {
  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  surv_survey_id: number;

  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  group_id: number;
}

export class UpdateSurveyGroupBodyDto {
  @Type()
  @IsNumber()
  @IsOptional()
  survey_group_id: number;

  @Type()
  @IsNumber()
  sec_company_id: number;

  @Type()
  @IsNumber()
  surv_survey_id: number;

  @Type()
  @IsNumber()
  group_id: number;
}
