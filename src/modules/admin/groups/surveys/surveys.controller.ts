import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../../../auth/interfaces/authorized-request.interface';
import { AddSurveyGroupBodyDto } from './dto/post-survey.dto';
import { AdminSurveyService } from './surveys.service';
import { AuthGuard } from '@/shared/guards/auth.guard';

@ApiTags('(Admin) Groups')
@ApiBearerAuth()
@Controller({
  path: '/admin/survey-groups',
  version: '2',
})
export class AdminSurveysController {
  constructor(private surveyService: AdminSurveyService) {}

  @Get()
  @UseGuards(AuthGuard)
  getList() {
    return this.surveyService.getList();
  }

  @Post()
  @UseGuards(AuthGuard)
  createItem(@Body() body: AddSurveyGroupBodyDto) {
    return this.surveyService.createItem(body);
  }

  // @Post(':id')
  // @UseGuards(AuthGuard)
  // updateItem(
  //   @Param('id') id: number,
  //   @Body() body: UpdateSurveyGroupBodyDto,
  //   @Req() { user }: AuthorizedRequest,
  // ) {
  //   body.survey_group_id = id;
  //   return this.surveyService.updateItem(user, body);
  // }

  @Delete(':surveyId/:groupId')
  @UseGuards(AuthGuard)
  deleteItem(
    @Param('surveyId') surveyId: number,
    @Param('groupId') groupId: number,
  ) {
    return this.surveyService.deleteItem(surveyId, groupId);
  }
}
