import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecGroupEntity } from '../../../database/entities/sec-group.entity';
import { SecUserRight } from '../../../database/entities/sec-user-right.entity';
import { AdminUsersModule } from '../../users/users.module';
import { Sec<PERSON><PERSON> } from '../../../database/entities/sec-key.entity';
import { AdminSurveyService } from './surveys.service';
import { AdminSurveysController } from './surveys.controller';
import { SurvSurveyEntity } from '../../../database/entities/surv-survey.entity';
import { SurveyGroupsEntity } from '../../../database/entities/surv-survey-groups.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SurvSurveyEntity, SurveyGroupsEntity]),
    AdminUsersModule,
  ],
  controllers: [AdminSurveysController],
  providers: [AdminSurveyService],
})
export class AdminSurveysModule {}
