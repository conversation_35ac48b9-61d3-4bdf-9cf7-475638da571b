import { SolutionType } from '@/shared/enums';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional, Min, ValidateNested } from 'class-validator';

export class AdminGetAuthorizationsFilterDto {
  @Type()
  @IsOptional()
  @ApiProperty({
    type: 'string',
    enum: Object.values(SolutionType).filter((key) =>
      isNaN(parseInt(SolutionType[key], 10)),
    ),
    description: `Possible values for this property are: ${Object.entries(
      SolutionType,
    )
      .filter(([key]) => isNaN(parseInt(key, 10)))
      .map(([key, value]) => `${value} = ${key}`)
      .join(', ')}`,
    required: false,
  })
  type?: SolutionType;
}

export class AdminUpsertGroupAuthorizationItem {
  @Type()
  @IsNumber()
  @Min(0)
  @ApiProperty()
  key_id: number;
}

export class AdminUpsertGroupAuthorizationDto {
  @Type(() => AdminUpsertGroupAuthorizationItem)
  @ValidateNested({ each: true })
  @ApiProperty({
    required: true,
    type: AdminUpsertGroupAuthorizationItem,
    isArray: true,
  })
  items: AdminUpsertGroupAuthorizationItem[];
}
