import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON>Array, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class UpsertGroupBodyDto {
  @Type()
  @IsOptional()
  @IsNumber()
  id?: number = null;

  @Type()
  @IsString()
  @ApiProperty({ required: true })
  name: string;

  @Type()
  @IsArray()
  @IsOptional()
  @ApiProperty({ required: false, type: Number, isArray: true })
  keys: number[] = [];
}
