import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../../database/entities/sec-user.entity';
import { AdminUsersController } from './users.controller';
import { AdminUsersService } from './users.service';
import { UsersModule } from '../../users/users.module';
import { UserCompanyEntity } from '../../database/entities/sec-user-company.entity';
import { AdminUsersRegionsService } from './users-regions.service';
import { SecUserKey } from '../../database/entities/sec-user-keys.entity';
import { UserSettingsEntity } from '../../database/entities/user-settings.entity';
import { CompanyEntity } from '../../database/entities/sec-company.entity';
import { FeedbackPropertiesModule } from '../../common/feedback-properties/feedback-properties.module';
import { AuthModule } from '../../auth/auth.module';
import { AdminGroupsModule } from '../groups/groups.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      UserCompanyEntity,
      SecUserKey,
      UserSettingsEntity,
      CompanyEntity,
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => AuthModule),
    AdminGroupsModule,
    FeedbackPropertiesModule,
  ],
  controllers: [AdminUsersController],
  providers: [AdminUsersService, AdminUsersRegionsService],
  exports: [AdminUsersService, AdminUsersRegionsService],
})
export class AdminUsersModule {}
