import { ApiHideProperty, ApiProperty, PartialType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsEmail,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { UserDetailsBaseDto } from '../../../users/dto/post-user.dto';

export class AdminGetUsersOptions {
  exclude?: (
    | 'regions'
    | 'additional_authorizations'
    | 'notification_settings'
  )[];
}
export class AdminGetUsersQueryDto {
  @Type()
  @IsOptional()
  @IsEmail()
  @ApiProperty({ required: false })
  email?: string = null;

  @Type()
  @IsOptional()
  @ApiProperty({ type: String, required: false })
  @Transform(({ value }) => value.split(','))
  groups?: string[];

  @Type()
  @IsOptional()
  @ApiProperty({ type: String, required: false })
  @Transform(({ value }) => value.split(','))
  regions?: string[];

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  last_login?: Date;

  @ApiHideProperty()
  user_id?: number;

  @ApiHideProperty()
  group_id?: string;

  // @Type()
  // @IsOptional()
  // @IsNumber()
  // @Min(0)
  // @ApiProperty({ required: false, default: 0 })
  // page: number = 0;

  // @Type()
  // @IsOptional()
  // @IsNumber()
  // @Max(100)
  // @Min(1)
  // @ApiProperty({ required: false, default: 50 })
  // limit: number = 50;
}

export class AdminUsersRegionDto {
  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  company_id: number;

  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  group_id: number;
}

export class AdminUserAdditionalAuthDto {
  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  company_id: number;

  @Type()
  @IsNumber({}, { each: true })
  @ApiProperty({ required: true, isArray: true })
  keys: number[];
}

export class AdminCreateUsers extends UserDetailsBaseDto {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false, type: AdminUsersRegionDto, isArray: true })
  regions: AdminUsersRegionDto[];

  @Type(() => AdminUserAdditionalAuthDto)
  @ValidateNested({ each: true })
  @IsOptional()
  @ApiProperty({
    required: false,
    type: AdminUserAdditionalAuthDto,
    isArray: true,
  })
  additional_authorizations?: AdminUserAdditionalAuthDto[];

  @Type()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Object,
  })
  notification_settings?: Object;
}
export class AdminCreateUsersBodyDto {
  @Type(() => AdminCreateUsers)
  @ValidateNested({ each: true })
  @ApiProperty({ required: true, type: AdminCreateUsers, isArray: true })
  users: AdminCreateUsers[];
}

export class AdminUpdateUsers extends PartialType<UserDetailsBaseDto>(
  UserDetailsBaseDto,
) {
  @Type()
  @ApiProperty({ required: true })
  @IsNumber()
  sec_user_id: number;

  @Type(() => AdminUsersRegionDto)
  @IsOptional()
  @ApiProperty({ required: false, type: AdminUsersRegionDto, isArray: true })
  regions: AdminUsersRegionDto[];

  @Type(() => AdminUserAdditionalAuthDto)
  @ValidateNested({ each: true })
  @IsOptional()
  @ApiProperty({
    required: false,
    type: AdminUserAdditionalAuthDto,
    isArray: true,
  })
  additional_authorizations?: AdminUserAdditionalAuthDto[];

  @Type()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Object,
  })
  notification_settings?: any;
}

export class AdminUpdateUsersBodyDto {
  @Type(() => AdminUpdateUsers)
  @ValidateNested({ each: true })
  @ApiProperty({ required: true, type: AdminUpdateUsers, isArray: true })
  users: AdminUpdateUsers[];
}

export class AdminCheckUserEmailsDto {
  @Type()
  @IsArray()
  @IsString({ each: true })
  @ApiProperty({ required: true })
  emails: string[];
}
