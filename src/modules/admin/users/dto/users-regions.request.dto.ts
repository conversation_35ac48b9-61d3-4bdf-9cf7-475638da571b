import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber } from 'class-validator';

export class AdminAddUserRegionBodyDto {
  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  user_id?: number;

  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  company_id?: number;

  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  group_id?: number;
}
export class AdminRemoveUserRegionBodyDto extends OmitType(
  AdminAddUserRegionBodyDto,
  ['group_id'] as const,
) {}

export class AdminGetUserRegionListDto {
  @Type()
  @IsNumber()
  @ApiProperty({ required: true })
  group_id: number;
}
