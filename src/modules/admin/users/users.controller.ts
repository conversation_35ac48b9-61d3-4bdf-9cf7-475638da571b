import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AdminUsersService } from './users.service';
import {
  AdminCheckUserEmailsDto,
  AdminCreateUsersBodyDto,
  AdminGetUsersQueryDto,
  AdminUpdateUsersBodyDto,
} from './dto/users.request.dto';
import { AuthorizedRequest } from '../../auth/interfaces/authorized-request.interface';
import {
  AdminAddUserRegionBodyDto,
  AdminRemoveUserRegionBodyDto,
} from './dto/users-regions.request.dto';
import { AdminUsersRegionsService } from './users-regions.service';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { Keys } from '@/shared/decorators';

@Controller({
  path: '/admin/users',
  version: '2',
})
@ApiTags('(Admin) Users')
@ApiBearerAuth()
export class AdminUsersController {
  constructor(
    private adminUserService: AdminUsersService,
    private adminUserRegionService: AdminUsersRegionsService,
  ) {}

  @Get()
  @Keys('USERS_VIEWER')
  @UseGuards(AuthGuard)
  getUsers(@Query() query: AdminGetUsersQueryDto) {
    return this.adminUserService.getUsers(query);
  }

  @Get(':id')
  @Keys('USERS_VIEWER')
  @UseGuards(AuthGuard)
  getUser(@Param('id') id: number) {
    return this.adminUserService.getUser(id);
  }

  @Post('/check-emails')
  @UseGuards(AuthGuard)
  checkEmails(@Body() body: AdminCheckUserEmailsDto) {
    return this.adminUserService.checkEmails(body.emails);
  }

  @Post('/update')
  @Keys('USERS_EDITOR')
  @UseGuards(AuthGuard)
  updateUsers(@Body() body: AdminUpdateUsersBodyDto) {
    return this.adminUserService.updateUsers(body);
  }

  @Post('/create')
  @Keys('USERS_EDITOR')
  @UseGuards(AuthGuard)
  createUsers(@Body() body: AdminCreateUsersBodyDto) {
    return this.adminUserService.createUsers(body);
  }

  @Post('/region')
  @UseGuards(AuthGuard)
  async addRegion(@Body() body: AdminAddUserRegionBodyDto) {
    return await this.adminUserRegionService.addRegion(body);
  }

  @Post('/region/check')
  @UseGuards(AuthGuard)
  async checkRegion(@Body() body: AdminAddUserRegionBodyDto) {
    return await this.adminUserRegionService.checkRegion(body);
  }

  @Delete('/region')
  @UseGuards(AuthGuard)
  async deleteRegion(@Body() body: AdminRemoveUserRegionBodyDto) {
    return await this.adminUserRegionService.deleteRegion(body);
  }
}
