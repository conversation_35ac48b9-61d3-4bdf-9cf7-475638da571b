import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { parentCompanyGroup, welcomeCompany } from '@/config/company.config';
import { CompanyEntity, UserCompanyEntity, SecUserKey } from '@entities';
import { IsAdmin, IsWelcome, aliasPrefix } from '@/shared/utils';

import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import {
  AdminAddUserRegionBodyDto,
  AdminRemoveUserRegionBodyDto,
} from './dto/users-regions.request.dto';

import { AdminUsersService } from './users.service';

@Injectable()
export class AdminUsersRegionsService {
  constructor(
    private cls: ClsService,
    private adminUserService: AdminUsersService,
    @InjectRepository(UserCompanyEntity)
    private ucRepo: Repository<UserCompanyEntity>,
    @InjectRepository(SecUserKey)
    private userKeyRepo: Repository<SecUserKey>,
    @InjectRepository(CompanyEntity)
    private companyRepo: Repository<CompanyEntity>,
  ) {}

  async getGroupCompanies(group_id: number) {
    const { company } = this.cls.get<AuthorizedData>('user');
    let isUserAdmin = IsAdmin(company.id);
    let query = this.ucRepo
      .createQueryBuilder('uc')
      .select(
        aliasPrefix([
          'c.sec_company_id',
          'c.company_code',
          'c.is_parent_company',
        ]),
      )
      .leftJoin('uc.sec_company', 'c')
      .leftJoin('uc.group', 'g')
      .where('uc.sec_group  =:group_id', { group_id });

    if (!isUserAdmin) {
      query.andWhere('uc.sec_company_id =:id', { id: company.id });
    }

    if (!IsWelcome(company.id)) {
      query.andWhere(`uc.sec_company_id != ${welcomeCompany}`);
    }

    query.groupBy('c.sec_company_id');

    let list = await query.getRawMany();

    if (group_id != parentCompanyGroup) {
      list = list.filter((item) => !item.is_parent_company);
    }
    return list.map((item) => ({ ...item, is_parent_company: undefined }));
  }

  async addRegion(params: AdminAddUserRegionBodyDto) {
    let company = await this.companyRepo.findOne({
      where: { sec_company_id: params.company_id },
    });

    if (company.is_parent_company && !IsWelcome(company.sec_company_id)) {
      params.group_id = parentCompanyGroup;
    }

    const company_id = params.company_id;

    let userCompany = await this.ucRepo
      .createQueryBuilder()
      .where('sec_user_id =:sec_user_id', { sec_user_id: params.user_id })
      .andWhere('sec_company_id =:company_id', {
        company_id,
      })
      .getOne();
    let result = {
      sec_user_id: params.user_id,
      sec_company_id: company_id,
      sec_group: params.group_id,
      creation_date: new Date(),
      // creator: `${user.sec_user_id}`,
    };
    if (userCompany) {
      userCompany.sec_group = +params.group_id;
      await this.ucRepo
        .createQueryBuilder()
        .update(result)
        .where('sec_user_id =:sec_user_id', { sec_user_id: params.user_id })
        .andWhere('sec_company_id =:company_id', {
          company_id,
        })
        .execute();
    } else {
      await this.ucRepo
        .createQueryBuilder()
        .insert()
        .values({
          sec_user_id: params.user_id,
          sec_company_id: company_id,
          sec_group: params.group_id,
          creation_date: new Date(),
          // creator: `${user.sec_user_id}`,
        })
        .execute();
    }
    return result;
  }

  async checkRegion(params: AdminAddUserRegionBodyDto) {
    let company = await this.companyRepo.findOne({
      where: { sec_company_id: params.company_id },
      select: {
        is_parent_company: true,
      },
    });

    if (company.is_parent_company && params.group_id != parentCompanyGroup) {
      throw new HttpException(
        'Please use Parent Company group for this company',
        HttpStatus.BAD_REQUEST,
      );
    }
    return true;
  }

  async deleteRegion(params: AdminRemoveUserRegionBodyDto) {
    let userCompany = await this.ucRepo
      .createQueryBuilder()
      .where('sec_user_id =:sec_user_id', { sec_user_id: params.user_id })
      .andWhere('sec_company_id =:company_id', {
        company_id: params.company_id,
      })
      .getOne();
    if (userCompany) {
      await this.ucRepo.delete({
        sec_user_id: params.user_id,
        sec_company_id: params.company_id,
      });
      await this.userKeyRepo.delete({
        sec_company_id: params.company_id,
        sec_user_id: params.user_id,
      });
    }

    await this.adminUserService.checkAndDeleteUser(params.user_id);

    return { deleted: !!userCompany };
  }
}
