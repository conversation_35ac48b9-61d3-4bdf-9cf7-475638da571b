import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, DeepPartial, In, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { UsersService } from '../../users/users.service';

import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import {
  AdminCreateUsersBodyDto,
  AdminGetUsersOptions,
  AdminGetUsersQueryDto,
  AdminUpdateUsers,
  AdminUpdateUsersBodyDto,
} from './dto/users.request.dto';

import { UserSettingsType } from '../../../shared/enums/user-settings-type.enum';
import { FeedbackPropertiesService } from '../../common/feedback-properties/feedback-properties.service';

import { AdminGroupService } from '../groups/groups.service';
import { CompanyEntity } from '../../database/entities/sec-company.entity';
import { welcomeCompany } from '../../../config/company.config';
import { PrivilegeService } from '@/modules/auth/privilege.service';
import {
  SecUserKey,
  UserCompanyEntity,
  UserEntity,
  UserSettingsEntity,
} from '@entities';
import {
  IsAdmin,
  IsWelcome,
  aliasPrefix,
  convertJsonAgg,
  findDuplicates,
  parseJSON,
} from '@/shared/utils';
import { FEEDBACK_PROPERTY_KEYS } from '@/config/contents/feedback-property.contents';

const countryList = require('country-list');

@Injectable()
export class AdminUsersService {
  constructor(
    private cls: ClsService,
    private privilegeService: PrivilegeService,
    private userService: UsersService,
    private groupService: AdminGroupService,
    private propertyService: FeedbackPropertiesService,
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
    @InjectRepository(UserCompanyEntity)
    private ucRepo: Repository<UserCompanyEntity>,
    @InjectRepository(SecUserKey)
    private userKeyRepo: Repository<SecUserKey>,
    @InjectRepository(UserSettingsEntity)
    private userSettingsRepo: Repository<UserSettingsEntity>,
    @InjectRepository(CompanyEntity)
    private companyRepo: Repository<CompanyEntity>,
  ) {}

  async getUsers(
    params: AdminGetUsersQueryDto,
    { exclude = [] }: AdminGetUsersOptions = {},
  ) {
    const { company } = this.cls.get<AuthorizedData>('user');

    let notAdmin = !IsAdmin(company.id);
    let isWelcome = IsWelcome(company.id);

    let addJoin = ``;
    let addSql = `1 = 1`;
    addJoin += `LEFT JOIN sec_user_company uc ON u.sec_user_id = uc.sec_user_id`;

    let serverName = await this.propertyService.getItem(
      FEEDBACK_PROPERTY_KEYS.SERVER_NAME,
    );

    const query = this.userRepo
      .createQueryBuilder('u')
      .select(
        aliasPrefix([
          'u.sec_user_id',
          'u.firstname',
          'u.username AS lastname',
          'u.email',
          'u.islocked',
          'u.salutation',
          'u.phone',
          'u.phone_code_country',
          'u.job_title',
          'u.date_of_birth',
          'u.work_anniversary',
          'u.last_api_contact AS last_login',
          'u.cration_date AS created_at',
        ]),
      );

    if (!exclude.includes('regions')) {
      query.addSelect((subQuery) => {
        return this.groupService.getUserGroupsQuery(
          'u.sec_user_id',
          subQuery,
          serverName,
        );
      }, 'regions');
    }
    if (!exclude.includes('additional_authorizations')) {
      query.addSelect((subQuery) => {
        const keyParams = {
          key_id: 'k.key_id',
          name: 'seckey.name',
        };
        const params = {
          company_id: 'c.sec_company_id',
          keys: `(SELECT ${convertJsonAgg(
            keyParams,
          )} FROM sec_user_keys k LEFT JOIN sec_key seckey ON seckey.key_id = k.key_id WHERE k.sec_company_id = c.sec_company_id AND k.sec_user_id = u.sec_user_id)`,
        };
        subQuery
          .select(convertJsonAgg(params), 'additional_authorizations')
          .from('sec_user_company', 'uc')
          .leftJoin('sec_company', 'c', 'c.sec_company_id = uc.sec_company_id')
          .where('uc.sec_user_id = u.sec_user_id')
          .andWhere((subQuery) => {
            return (
              subQuery
                .subQuery()
                .select('COUNT(id)')
                .from('sec_user_keys', 'k')
                .where('k.sec_company_id = c.sec_company_id')
                .andWhere('k.sec_user_id = u.sec_user_id')
                .getQuery() + ' > 0'
            );
          });

        return subQuery;
      }, 'additional_authorizations');
    }

    if (!exclude.includes('notification_settings')) {
      query.addSelect((subQuery) => {
        let params = {
          setting_object: 'usettings.setting_object',
          sec_company_id: 'usettings.company_id',
        };
        subQuery
          .select(convertJsonAgg(params), 'notification_settings')
          .from('user_settings', 'usettings')
          .where(
            `usettings.user_setting_type = ${UserSettingsType.NOTIFICATIONS}`,
          )
          .andWhere('u.sec_user_id = usettings.sec_user_id');

        if (notAdmin) {
          subQuery.andWhere('usettings.company_id =:cid', {
            cid: company.id,
          });
          subQuery.limit(1);
        } else {
          subQuery.andWhere('usettings.company_id IS NOT NULL');
        }

        return subQuery;
      }, 'notification_settings');
    }
    query
      .leftJoin('sec_user_company', 'uc', 'u.sec_user_id = uc.sec_user_id')
      .leftJoin(
        'sec_user_company',
        'ucWelcome',
        `u.sec_user_id = ucWelcome.sec_user_id AND ucWelcome.sec_company_id = ${welcomeCompany}`,
      )
      .leftJoin('sec_company', 'c', 'c.sec_company_id = uc.sec_company_id')
      .where('u.sec_user_id != 0')
      .groupBy('u.sec_user_id');

    if (params.email) {
      query.andWhere(`u.email = :email`, { email: params.email });
    }
    if (params.groups?.length) {
      query.andWhere(`u.sec_group IN(:...groups)`, {
        groups: params.groups,
      });
    }
    if (params.regions?.length) {
      query.andWhere(`uc.sec_company_id IN(:...regions)`, {
        regions: params.regions,
      });
    }

    if (params.group_id) {
      query.andWhere(`uc.sec_group = :group_id`, {
        group_id: params.group_id,
      });
    }

    if (params.last_login) {
      query.andWhere(`u.last_api_contact > :last_login`, {
        last_login: params.last_login,
      });
    }

    if (params.user_id) {
      query.andWhere(`u.sec_user_id = :user_id`, {
        user_id: params.user_id,
      });
    }

    if (notAdmin) {
      query.andWhere(`uc.sec_company_id =:cid`, {
        cid: company.id,
      });
    }
    if (!isWelcome) {
      query.andWhere(`ucWelcome.sec_user_id IS NULL`);
    }
    query.andWhere(addSql);

    let list = await query.getRawMany();
    // list = list.slice(0, 20);

    list = list.map((item) => {
      let data = {
        ...item,
        status: item.islocked ? 'passive' : 'active',
        company_count: item.regions?.length || 0,
        islocked: undefined,
      };
      if (!exclude.includes('regions')) {
        data.regions = (item.regions || [])
          .map((region) => {
            return {
              ...region,
              country_name: countryList.getName(region.country_code),
            };
          })
          .sort((a, b) => a.company_code.localeCompare(b.company_code));
      }
      if (!exclude.includes('notification_settings')) {
        data.notification_settings = item.notification_settings?.map((s) => {
          return {
            sec_company_id: s.sec_company_id,
            settings: parseJSON(s.setting_object),
          };
        });
      }
      return data;
    });

    return list;
  }

  async getUser(user_id: number) {
    let u = (await this.getUsers({ user_id }))?.[0];
    if (!u) throw new HttpException('user with this id is not found', 404);
    return u;
  }

  async updateUsers({ users }: AdminUpdateUsersBodyDto) {
    let results = [];
    let existsEmails = await this.userRepo
      .createQueryBuilder('u')
      .select(['u.email', 'u.sec_user_id'])
      .where(`email ILIKE ANY(array[:...emails])`, {
        emails: users.map((i) => i.email),
      })
      .getMany();

    let companies = await this.companyRepo
      .createQueryBuilder('c')
      .select(['c.sec_company_id', 'c.is_parent_company'])
      .getMany();
    for (let item of users) {
      let user = await this.userService.getUser(item.sec_user_id);
      let result;
      if (!user) {
        results.push({ userId: item.sec_user_id, error: 'user not found' });
        continue;
      }
      let isEmailExists = existsEmails.find(
        (i) =>
          i.email.toLowerCase() === item.email.toLowerCase() &&
          i.sec_user_id !== item.sec_user_id,
      );
      if (isEmailExists) {
        results.push({
          userId: null,
          error: `The provided email address already exists in our system. Please use a different email address.`,
        });
        continue;
      }
      result = await this.userService.updateUser(item as any);
      if (item.regions) {
        item.regions = item.regions.filter(
          (region) => region.group_id && region.company_id,
        );
      }
      if (item.additional_authorizations) {
        item.additional_authorizations = item.additional_authorizations.filter(
          (data) => data.company_id,
        );
      }

      let regionsResult = await this.updateRegions(item, result.id, companies);
      if (regionsResult?.error) {
        results = [...results, ...regionsResult.error];
        continue;
      }
      let { addedRegions, updatedRegions, deletedRegions } = regionsResult;

      results = [...results, ...regionsResult.results];

      let additionalAuthResult = await this.updateAdditionalAuthorizations(
        item,
        result.id,
        companies,
      );
      let { addedItems, updatedItems, deletedItems } = additionalAuthResult;

      results = [...results, ...regionsResult.results];

      let notificationSettingsResult = await this.updateNotificationSettings(
        item,
      );

      results.push({
        userId: item.sec_user_id,
        regions: {
          added: addedRegions.length,
          updated: updatedRegions.length,
          deleted: deletedRegions.length,
        },
        additionalAuthorizations: {
          added: addedItems.length,
          updated: updatedItems.length,
          deleted: deletedItems.length,
        },
        notificationSettings: notificationSettingsResult,
        metadata: { ...result, id: undefined },
      });
    }
    return results;
  }

  async updateRegions(
    item: AdminUpdateUsers,
    userId: any,
    companies?: CompanyEntity[],
  ) {
    if (!companies)
      companies = await this.companyRepo
        .createQueryBuilder('c')
        .select(['c.sec_company_id', 'c.is_parent_company'])
        .getMany();

    let results = [];
    let addedRegions: any = [];
    let updatedRegions: any = [];
    let deletedRegions: any = [];

    let userRegions = await this.groupService.getUserGroups(userId, null);

    let currentRegionIds = userRegions.map((r) => +r.company_id);
    let incomingRegionIds = item.regions?.map((r) => +r.company_id) || [];

    if (Array.isArray(item.regions)) {
      if (!item.regions.length) {
        results.push({
          userId: userId,
          error: 'you must need to specify at least one region',
        });
        return { error: results };
      }
      deletedRegions = currentRegionIds.filter(
        (id) => !incomingRegionIds.includes(id),
      );
      if (deletedRegions.length) {
        await this.ucRepo.delete({
          sec_company_id: In(deletedRegions),
          sec_user_id: item.sec_user_id,
        });
        await this.userKeyRepo.delete({
          sec_company_id: In(deletedRegions),
          sec_user_id: item.sec_user_id,
        });
      }
      if (item.regions?.length) {
        // Create new regions
        addedRegions = item.regions
          .filter((r) => !currentRegionIds.includes(+r.company_id))
          .map((r) => ({
            sec_user_id: userId,
            sec_company_id: r.company_id,
            creation_date: new Date(),
            sec_group: r.group_id,
          }));

        await Promise.all(
          addedRegions.map((r) => {
            let company = companies.find(
              (c) => c.sec_company_id === r.sec_company_id,
            );
            if (company?.is_parent_company) {
              r.sec_group = 8;
            }

            return this.ucRepo
              .createQueryBuilder()
              .insert()
              .values(r)
              .execute();
          }),
        );

        // Update existing regions
        updatedRegions = item.regions
          .filter((r) => currentRegionIds.includes(+r.company_id))
          .map((r) => ({
            sec_user_id: userId,
            sec_company_id: r.company_id,
            creation_date: new Date(),
            sec_group: r.group_id,
          }));
        await Promise.all(
          updatedRegions.map((r) => {
            let company = companies.find(
              (c) => c.sec_company_id === r.sec_company_id,
            );
            if (company?.is_parent_company) {
              r.sec_group = 8;
            }

            return this.ucRepo
              .createQueryBuilder()
              .update()
              .set(r)
              .where(
                `sec_user_id =:sec_user_id AND sec_company_id =:sec_company_id`,
                r,
              )
              .execute();
          }),
        );
      }
    }

    return { results, addedRegions, updatedRegions, deletedRegions };
  }

  async updateAdditionalAuthorizations(
    item: AdminUpdateUsers,
    userId: any,
    companies?: CompanyEntity[],
  ) {
    if (!companies)
      companies = await this.companyRepo
        .createQueryBuilder('c')
        .select(['c.sec_company_id', 'c.is_parent_company'])
        .getMany();

    let results = [];
    let addedItems: DeepPartial<SecUserKey>[] = [];
    let updatedItems: any = [];
    let deletedItems: any = [];

    let userItems = await this.userKeyRepo
      .createQueryBuilder('uk')
      .where('uk.sec_user_id =:userId', { userId })
      .getMany();

    let currentItemIds = userItems.map(
      (r) => `${r.sec_company_id}-${r.key_id}`,
    );
    let incomingItemIds =
      item.additional_authorizations?.reduce((prev, i) => {
        return [...prev, ...i.keys.map((key) => `${i.company_id}-${key}`)];
      }, []) || [];

    if (Array.isArray(item.additional_authorizations)) {
      deletedItems = currentItemIds.filter(
        (id) => !incomingItemIds.includes(id),
      );

      for (let i of userItems) {
        let company = companies.find(
          (c) => c.sec_company_id === i.sec_company_id,
        );

        if (company?.is_parent_company) {
          deletedItems.push(`${i.sec_company_id}-${i.key_id}`);
        }
      }

      if (deletedItems.length) {
        await this.userKeyRepo
          .createQueryBuilder()
          .delete()
          .where(`sec_user_id =:sec_user_id`, { sec_user_id: item.sec_user_id })
          .andWhere(
            new Brackets((qb) => {
              let index = 0;
              for (let i of deletedItems) {
                let [sec_company_id, key_id] = i.split('-');
                qb.orWhere(
                  `(sec_company_id =:sec_company_id${index} AND key_id =:key_id${index})`,
                  {
                    [`sec_company_id${index}`]: sec_company_id,
                    [`key_id${index}`]: key_id,
                  },
                );
                index++;
              }
            }),
          )
          .execute();
      }
      if (item.additional_authorizations?.length) {
        // Create new regions
        addedItems = incomingItemIds
          .filter((r) => !currentItemIds.includes(r))
          .map((r) => ({
            sec_user_id: userId,
            sec_company_id: r.split('-')[0],
            key_id: r.split('-')[1],
          }));

        addedItems = addedItems.filter((r) => {
          let company = companies.find(
            (c) => c.sec_company_id === r.sec_company_id,
          );
          return !company?.is_parent_company;
        });

        await Promise.all(
          addedItems.map((r) => {
            return this.userKeyRepo
              .createQueryBuilder()
              .insert()
              .values(r)
              .execute();
          }),
        );

        // Update existing regions
        updatedItems = incomingItemIds.filter((r) =>
          currentItemIds.includes(r),
        );
      }
    }

    return { results, addedItems, updatedItems, deletedItems };
  }

  async updateNotificationSettings(
    item: AdminUpdateUsers,
  ): Promise<{ created: number; updated: number }> {
    let created: number = 0;
    let updated: number = 0;
    if (!item.notification_settings) return { updated, created };
    let notification_settings = Array.isArray(item.notification_settings)
      ? item.notification_settings
      : [item.notification_settings];
    for (let ns of notification_settings) {
      if (ns.settings && Object.keys(ns.settings).length) {
        let notificationSettings = await this.userSettingsRepo.findOne({
          where: {
            user_setting_type: UserSettingsType.NOTIFICATIONS,
            sec_user_id: item.sec_user_id,
            company_id: ns.sec_company_id,
          },
        });

        if (notificationSettings) {
          updated++;
          await this.userSettingsRepo.update(
            { user_setting_id: notificationSettings.user_setting_id },
            { setting_object: JSON.stringify(ns.settings) },
          );
        } else {
          created++;
          await this.userSettingsRepo.save(
            this.userSettingsRepo.create({
              name: 'email notification',
              sec_user_id: item.sec_user_id,
              setting_object: JSON.stringify(ns.settings),
              user_setting_type: UserSettingsType.NOTIFICATIONS,
              company_id: ns.sec_company_id,
              created_on: new Date(),
            }),
          );
        }
      }
    }
    return { created, updated };
  }

  async createUsers({ users }: AdminCreateUsersBodyDto) {
    let results = [];

    let existsEmails = await this.userRepo
      .createQueryBuilder('u')
      .select(['u.email'])
      .where(`email ILIKE ANY(array[:...emails])`, {
        emails: users.map((i) => i.email),
      })
      .getMany();

    let companies = await this.companyRepo
      .createQueryBuilder('c')
      .select(['c.sec_company_id', 'c.is_parent_company'])
      .getMany();
    for (let item of users) {
      if (!item.regions?.length) {
        results.push({
          userId: null,
          error: 'you must need to specify at least one region',
        });
        continue;
      }
      let checkSameEmail =
        item.email?.length &&
        users.filter(
          (user) => user.email?.toLowerCase() === item.email?.toLowerCase(),
        ).length >= 2;

      if (
        existsEmails.find(
          (i) => i.email?.toLowerCase() === item.email?.toLowerCase(),
        ) ||
        checkSameEmail
      ) {
        results.push({
          userId: null,
          error: `The provided email address already exists in our system. Please use a different email address.`,
        });
        continue;
      }
      let result = await this.userService.createUser(item as any);
      if (!result?.id) {
        results.push({
          userId: null,
          error: 'error while creating user',
          details: result,
        });
        continue;
      }

      if (item.regions) {
        item.regions = item.regions.filter(
          (region) => region.group_id && region.company_id,
        );
      }
      if (item.additional_authorizations) {
        item.additional_authorizations = item.additional_authorizations.filter(
          (data) => data.company_id,
        );
      }

      let addedRegions = [];
      if (item.regions?.length > 0) {
        addedRegions = item.regions.map((r) => ({
          sec_user_id: result.id,
          sec_company_id: r.company_id,
          creation_date: new Date(),
          sec_group: r.group_id,
        }));
        await Promise.all(
          addedRegions.map((r) => {
            let company = companies.find(
              (c) => c.sec_company_id === r.sec_company_id,
            );
            if (company?.is_parent_company) {
              r.sec_group = 8;
            }
            return this.ucRepo
              .createQueryBuilder()
              .insert()
              .values(r)
              .execute();
          }),
        );
      }

      let addedKeys = [];
      if (item.additional_authorizations?.length > 0) {
        addedKeys = item.additional_authorizations
          ?.reduce((prev, i) => {
            return [
              ...prev,
              ...i.keys.map((key) => ({ key, company_id: i.company_id })),
            ];
          }, [])
          .map((r) => ({
            sec_user_id: result.id,
            sec_company_id: r.company_id,
            key_id: r.key,
          }));

        addedKeys = addedKeys.filter(
          (k) =>
            !companies.find((c) => c.sec_company_id === k.sec_company_id)
              ?.is_parent_company,
        );
        await Promise.all(
          addedKeys.map((r) => {
            return this.userKeyRepo
              .createQueryBuilder()
              .insert()
              .values(r)
              .execute();
          }),
        );
      }

      let notificationSettingsResult = await this.updateNotificationSettings({
        ...item,
        sec_user_id: result.id,
      });

      results.push({
        userId: result.id,
        regions: {
          added: addedRegions.length,
          updated: 0,
          deleted: 0,
        },
        additionalAuthorizations: {
          added: addedKeys.length,
        },
        notificationSettings: notificationSettingsResult,
        metadata: { ...result, id: undefined },
      });
    }
    return results;
  }

  async checkAndDeleteUser(userId: number) {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    let notAdmin = !IsAdmin(company.id);

    let privileges = await this.privilegeService.filteredKeys({
      userId: user.id,
      companyId: company.id,
      filter: { names: ['USERS_EDITOR'] },
    });

    const USERS_EDITOR = privileges.includes('USERS_EDITOR');

    if (notAdmin && !USERS_EDITOR)
      throw new HttpException('forbidden', HttpStatus.FORBIDDEN);

    let regionCount = await this.ucRepo
      .createQueryBuilder()
      .where('sec_user_id =:sec_user_id', { sec_user_id: userId })
      .getCount();
    if (regionCount == 0) {
      await this.userRepo.delete({
        sec_user_id: userId,
      });
    }
    return regionCount === 0;
  }

  async checkEmails(emails: string[]) {
    if (emails.length === 0) return { exists: [], notExists: [] };
    let data = await this.userRepo
      .createQueryBuilder('u')
      .select('u.email')
      .where(`email ILIKE ANY(array[:...emails])`, { emails })
      .getMany();

    let list = data.map((i) => i.email);
    let notExists = emails.filter(
      (email) => !list.find((e) => e.toLowerCase() === email.toLowerCase()),
    );
    let dublicates = findDuplicates(notExists);
    if (dublicates.length) {
      list = [...list, ...dublicates];
      notExists = notExists.filter(
        (email) =>
          !dublicates.find((e) => e.toLowerCase() === email.toLowerCase()),
      );
    }
    return {
      exists: list,
      notExists,
    };
  }
}
