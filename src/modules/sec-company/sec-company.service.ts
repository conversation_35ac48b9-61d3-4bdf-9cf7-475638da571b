import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserCompanyEntity } from '../database/entities/sec-user-company.entity';
import { ChildCompany } from './interfaces/child-company.interface';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents/cls.contents';

@Injectable()
export class SecCompanyService {
  constructor(
    private cls: ClsService,
    @InjectRepository(UserCompanyEntity)
    private readonly secUserCompanyEntity: Repository<UserCompanyEntity>,
  ) {}

  async userChildCompanies() {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const companies = this.cls.get<ChildCompany[]>(ClsProperty.childCompanies);
    if (companies) return companies;

    let list: ChildCompany[] = await this.secUserCompanyEntity.query(
      `select spcl.sec_company_id_child as id, sc.company_code as code  from sec_parent_company_link spcl left join sec_company sc on spcl.sec_company_id_child = sc.sec_company_id where spcl.sec_company_id_parent = $1`,
      [company.id],
    );

    this.cls.set(ClsProperty.childCompanies, list);

    return list;
  }

  async childCompanies(sec_company_id: number) {
    let list: ChildCompany[] = await this.secUserCompanyEntity.query(
      `select spcl.sec_company_id_child as id, sc.company_code as code  from sec_parent_company_link spcl left join sec_company sc on spcl.sec_company_id_child = sc.sec_company_id where spcl.sec_company_id_parent = $1`,
      [sec_company_id],
    );
    return list;
  }
}
