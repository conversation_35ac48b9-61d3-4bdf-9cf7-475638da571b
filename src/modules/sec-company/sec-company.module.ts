import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserCompanyEntity } from '../database/entities/sec-user-company.entity';
import { SecCompanyService } from './sec-company.service';

@Module({
  imports: [TypeOrmModule.forFeature([UserCompanyEntity])],
  providers: [SecCompanyService],
  exports: [SecCompanyService],
})
export class SecCompanyModule {}
