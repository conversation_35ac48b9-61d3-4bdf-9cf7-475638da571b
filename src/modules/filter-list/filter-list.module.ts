import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AllowedFilterController } from './allowed-filter/allowed-filter.controller';
import { FilterListController } from './filter-list.controller';

import { SecCompanyModule } from '../sec-company/sec-company.module';
import { UserSettingsModule } from '../users/user-settings/user-settings.module';
import { AnswertableModule } from '../answertable/answertable.module';
import { AuthModule } from '../auth/auth.module';

import { AllowedFilterService } from './allowed-filter/allowed-filter.service';
import { FilterListService } from './filter-list.service';
import {
  AllowedFilterEntity,
  AnswertableConfigEntity,
  AnswertableEntity,
  DatatableEntity,
  EmailCustomerEntity,
  ProjectsEntity,
  SurvSurveyEntity,
  SurveyGroupsEntity,
} from '@entities';
import { FilterListUtils } from './filter-list.utils';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { SurveyModule } from '../survey/survey.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AllowedFilterEntity,
      EmailCustomerEntity,
      ProjectsEntity,
      DatatableEntity,
      AnswertableConfigEntity,
      AnswertableEntity,
      SurvSurveyEntity,
      SurveyGroupsEntity,
    ]),
    AuthModule,
    ModuleLoggerModule.register('filter-list'),
    AnswertableModule,
    SecCompanyModule,
    UserSettingsModule,
    SurveyModule,
    AnswertableModule,
  ],
  controllers: [FilterListController, AllowedFilterController],
  providers: [FilterListService, AllowedFilterService, FilterListUtils],
})
export class FilterListModule {}
