import { ClsProperty } from '@/config/contents/cls.contents';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ChildCompany } from '../sec-company/interfaces/child-company.interface';
import { Injectable } from '@nestjs/common';

@Injectable()
export class FilterListUtils {
  constructor(private cls: ClsService) {}

  public optionName(name: string, cid: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const childCompanies = this.cls.get<ChildCompany[]>(
      ClsProperty.childCompanies,
    );
    const childCompany = childCompanies.find(({ id }) => id == cid);
    const code = childCompany?.code;

    if (company.isParent && code) name += `(${code})`;

    return name;
  }

  mapDatatableItem(item) {
    return {
      id: item.map_key,
      name: this.optionName(item.map_description, item.sec_company_id),
    };
  }

  filterAllowedTemplates(list: any, template_id: number) {
    return list
      .filter((item) => {
        let allowedTemplates = item.allowed_templates || {};
        return (
          (!allowedTemplates.only_allowed?.length ||
            allowedTemplates.only_allowed?.indexOf(+template_id) >= 0) &&
          (!allowedTemplates.not_allowed?.length ||
            allowedTemplates.not_allowed.indexOf(+template_id) == -1)
        );
      })
      .map((item) => {
        delete item.allowed_templates;
        return item;
      });
  }
}
