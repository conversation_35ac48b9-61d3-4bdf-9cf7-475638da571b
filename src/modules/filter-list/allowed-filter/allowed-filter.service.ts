import { Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import { GetAllowedFiltersDto } from './dto/get-allowed-filters.dto';

import { AnswertableConfigService } from '../../answertable/answertable-config.service';
import { FilterListService } from '../filter-list.service';
import { PrivilegeService } from '../../auth/privilege.service';
23;

import { FilterType } from '@/shared/enums';
import { RESTRICTED_CONTENT } from '@/config/restricted_content';
import { InjectRepository } from '@nestjs/typeorm';
import { AllowedFilterEntity } from '@entities';
import { FindOptionsWhere, IsNull, Raw, Repository } from 'typeorm';
import { AllowedFilterParams } from './allowed-filter.interface';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class AllowedFilterService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private privilegeService: PrivilegeService,
    private filterListService: FilterListService,
    private answertableConfigService: AnswertableConfigService,
    @InjectRepository(AllowedFilterEntity)
    private allowedFilterRepo: Repository<AllowedFilterEntity>,
  ) {}

  async getAllowedFilters(query: GetAllowedFiltersDto) {
    const { user, company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`getAllowedFilters`, { query, user, company });
    try {
      let privileges = await this.privilegeService.filteredKeys({
        userId: user.id,
        companyId: company.id,
        filter: { names: ['SHOW_CUSTOMER_DATA', 'SHOW_EMPLOYEE_DATA'] },
      });

      const SHOW_EMPLOYEE_DATA = privileges.includes('SHOW_EMPLOYEE_DATA');

      const { filter_type } = query;
      let allowed_filters: any = await this.filterListService.filterList({
        filter_type: FilterType[filter_type],
        is_default: 0,
      });

      // Answertable Extension
      if (!['clf', 'classification', 'publish'].includes(query.filter_type)) {
        let answertableConfig =
          await this.answertableConfigService.getConfigList({ join: false });
        let answertable = answertableConfig.map((item) => {
          return {
            id: item.answertable_config_uuid,
            key: `answertable_${item.answertable_config_uuid}`,
            name: item.answertable_name,
            short_description: item.answertable_description,
            is_required: false,
            image: 'mdi mdi-fire-truck',
          };
        });

        allowed_filters.push(...answertable);
      }

      allowed_filters = allowed_filters.map((item) => {
        return { ...item, options: undefined };
      });

      if (!SHOW_EMPLOYEE_DATA) {
        allowed_filters = allowed_filters.filter((item) => {
          return !RESTRICTED_CONTENT.SHOW_EMPLOYEE_DATA.find((field) => {
            return field == item.key || field.includes(item.key);
          });
        });
      }

      this.logger.debug(`getAllowedFilters result`, { allowed_filters });
      return {
        allowed_filters,
      };
    } catch (e) {
      this.logger.error(`getAllowedFilters error`, { error: e?.message || e });
    }
  }
  private createWhere({
    company_type,
    company_type_nullable,
    filter_type,
    filter_key,
    is_default,
  }: AllowedFilterParams) {
    const where: FindOptionsWhere<AllowedFilterEntity> = {};

    if (typeof company_type !== 'undefined') {
      if (company_type_nullable) {
        where.company_type = Raw(
          (alias) => `(${alias} =:company_type OR ${alias} IS NULL)`,
          {
            company_type,
          },
        );
      } else {
        where.company_type = company_type === null ? IsNull() : company_type;
      }
    }
    if (typeof filter_type !== 'undefined') {
      where.filter_type = filter_type;
    }
    if (typeof filter_key !== 'undefined') {
      where.filter_key = filter_key;
    }
    if (typeof is_default !== 'undefined') {
      where.is_default = is_default;
    }
    return where;
  }

  async filters(params: AllowedFilterParams): Promise<AllowedFilterEntity[]> {
    this.logger.debug(`allowed-filter filters`, { params });
    let list = await this.allowedFilterRepo.find({
      where: this.createWhere(params),
      order: {
        sort_order: 'ASC',
      },
    });

    if (
      typeof params.company_type !== 'undefined' &&
      !list.length &&
      params.remove_company_type
    ) {
      return await this.filters({ ...params, company_type: null });
    }

    this.logger.debug(`allowed-filter filters result`, { list });

    return list;
  }

  async item(params: AllowedFilterParams): Promise<AllowedFilterEntity> {
    this.logger.debug(`allowed-filter item`, { params });
    let item = await this.allowedFilterRepo.findOne({
      where: this.createWhere(params),
    });
    if (
      typeof params.company_type !== 'undefined' &&
      !item &&
      params.remove_company_type
    ) {
      return await this.item({ ...params, company_type: null });
    }

    this.logger.debug(`allowed-filter item result`, { item });
    return item;
  }
}
