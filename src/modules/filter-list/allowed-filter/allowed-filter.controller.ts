import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { GetAllowedFiltersDto } from './dto/get-allowed-filters.dto';
import { AllowedFilterService } from './allowed-filter.service';

@ApiTags('Filter Lists / Allowed Filters')
@ApiBearerAuth()
@Controller({
  path: 'allowed_filters',
  version: '2',
})
export class AllowedFilterController {
  constructor(private allowedFilterService: AllowedFilterService) {}
  @Get()
  @ApiOkResponse({
    description: 'Allowed Filters',
  })
  @UseGuards(AuthGuard)
  async getFilterOptions(@Query() query: GetAllowedFiltersDto) {
    return this.allowedFilterService.getAllowedFilters(query);
  }
}
