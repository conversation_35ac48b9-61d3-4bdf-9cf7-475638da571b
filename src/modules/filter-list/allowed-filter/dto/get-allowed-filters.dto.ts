import { FILTER_TYPE_KEYS, FilterType } from '@/shared/enums';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsIn, IsNumber, IsOptional } from 'class-validator';

export class GetAllowedFiltersDto {
  @Type()
  @ApiProperty({
    required: false,
    enum: FILTER_TYPE_KEYS,
  })
  @IsIn(FILTER_TYPE_KEYS)
  @IsOptional()
  filter_type?: keyof typeof FilterType = 'report';

  @Type()
  @IsOptional()
  @IsNumber()
  @ApiProperty({
    required: false,
  })
  template_id: number;
}
