import { HttpException, Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, In, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { lastValueFrom, zip } from 'rxjs';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { GetFilterOptionsParams } from './interface/filter-list-service-params';

import { AnswertableConfigService } from '../answertable/answertable-config.service';
import { SecCompanyService } from '../sec-company/sec-company.service';
import {
  AllowedFilterEntity,
  DatatableEntity,
  EmailCustomerEntity,
  ProjectsEntity,
  SurvSurveyEntity,
} from '@entities';
import { GetFilterOptionsDto } from './dto/get-filter-options.dto';
import { parseJSON } from '@/shared/utils';
import { ClsProperty } from '@/config/contents/cls.contents';
import { BSHMapping, EmployeeType } from '@/shared/enums/datatable.enum';
import { PrivilegeService } from '../auth/privilege.service';
import { TranslateService } from '@/core/modules/translate/translate.service';
import { staticOptions } from './filter-list.config';
import { AllowedFilterService } from './allowed-filter/allowed-filter.service';
import { FilterListUtils } from './filter-list.utils';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { SurveyService } from '../survey/survey.service';

@Injectable()
export class FilterListService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @Inject(forwardRef(() => AllowedFilterService))
    private allowedFilterService: AllowedFilterService,
    private answertableConfigService: AnswertableConfigService,
    private secCompanyService: SecCompanyService,
    private surveyService: SurveyService,
    private privilegeService: PrivilegeService,
    private translate: TranslateService,
    private utils: FilterListUtils,
    @InjectRepository(AllowedFilterEntity)
    private readonly allowedFilterRepo: Repository<AllowedFilterEntity>,
    @InjectRepository(EmailCustomerEntity)
    private readonly ecRepo: Repository<EmailCustomerEntity>,
    @InjectRepository(ProjectsEntity)
    private readonly projectRepo: Repository<ProjectsEntity>,
    @InjectRepository(DatatableEntity)
    private readonly datatableRepo: Repository<DatatableEntity>,
  ) {}

  async filterList(params: GetFilterOptionsParams) {
    const {
      filter_type,
      is_default,
      list: name,
      template_id,
      addList: includeOptions,
      query,
    } = params;

    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug('filterList', { params, company });

    if (name?.startsWith('answertable_')) {
      return this.answertableFilterList(name);
    } else if (name?.length > 0) {
      return this.item(name, { includeOptions });
    }
    let filters = await this.allowedFilterService.filters({
      filter_type,
      is_default,
      company_type: company.type,
      company_type_nullable: true,
    });

    filters =
      await this.privilegeService.restrictedContentFilter<AllowedFilterEntity>(
        filters,
        'filter_key',
        ['SHOW_CUSTOMER_DATA', 'SHOW_EMPLOYEE_DATA'],
      );

    let result = await Promise.all(
      filters.map(async (item) => {
        let resp: any = {
          id: item.allowed_filter_id,
          key: item.filter_key,
          name: item.filter_name,
          short_description: item.short_description,
          image: item.filter_image,
          is_required: !!item.is_required,
          options: parseJSON(item.filter_options),
          allowed_templates: item.allowed_templates,
        };
        if (resp.key == 'date_from_to') {
          delete resp.image;
        } else if (includeOptions) {
          resp.data = await this.getList(item.filter_key, query);
        }
        return resp;
      }),
    );

    result = this.utils.filterAllowedTemplates(result, template_id);

    this.logger.debug('filterList result', { result, filters });

    return result;
  }
  async answertableFilterList(key: string) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug(`answertableFilterList for key ${key}`, { company });

    let id = key.replace('answertable_', '');
    let answertableConfig = await this.answertableConfigService.getConfig(
      id,
      company.id,
    );
    let result = [
      {
        id: answertableConfig.answertable_config_uuid,
        key,
        name: answertableConfig.answertable_name,
        is_required: false,
        options: {
          placeholder: 'Select',
          singleSelection: false,
          enableSearchFilter: true,
          primaryKey: 'id',
          labelKey: 'name',
          classes: 'search-multiselect',
          badgeShowLimit: 2,
        },
        data: await this.answertableConfigService.getQuestionRow(id),
      },
    ];

    this.logger.debug(`answertableFilterList result`, {
      id,
      result,
      answertableConfig,
    });

    return result;
  }

  async item(key: string, { includeOptions = false }) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`item for key ${key}`, {
      company,
    });
    let filter = await this.allowedFilterService.item({
      company_type: company.type,
      filter_key: key,
      remove_company_type: true,
    });

    if (!filter) throw new HttpException('List not found', 404);

    let options = [];
    if (includeOptions) {
      options = await this.getList(key);
    }
    let result = [
      {
        key,
        name: filter.filter_name,
        id: filter.allowed_filter_id,
        data: options,
        is_required: !!filter.is_required,
        options: parseJSON(filter.filter_options),
      },
    ];

    this.logger.debug(`item result`, { result });
    return result;
  }

  async getList(name: string, query?: GetFilterOptionsDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const companyId = +(query?.sec_company_id || company.id);

    this.logger.debug(`getList name ${name} company ${companyId}`, {
      name,
      query,
    });

    let response;
    switch (name) {
      case 'language':
        response = await this.getLanguageList();
        break;
      case 'project':
        response = await this.getProjectList(companyId);
        break;
      case 'project_bouw':
        response = await this.getProjectBouwList(companyId);
        break;
      case 'techgrp_type':
        response = await this.getTechgrpTypeList();
        break;
      case 'project_category':
        response = await this.getProjectCategoriesList(companyId);
        break;
      case 'club':
        response = await this.getClubList(companyId);
        break;
      case 'medium':
        response = await staticOptions.mediumList;
        break;
      case 'classified':
        response = await staticOptions.classifiedList;
        break;
      case 'confidence':
        response = await staticOptions.confidence;
        break;
      case 'published':
        response = await staticOptions.publishedList;
        break;
      case 'publish':
        response = await staticOptions.publishList;
        break;
      case 'publishable':
        response = await staticOptions.publishableList;
        break;
      case 'callback_question':
        response = await staticOptions.callbackQuestionList;
        break;
      case 'productgroup':
        response = await this.getProductGroupList(companyId);
        break;
      case 'product_division':
        response = await this.getProductDivisionList(companyId);
        break;
      case 'classification_labels':
        response = await this.getClassificationLabelList(companyId);
        break;
      case 'survey':
        response = await this.getSurveyList(companyId);
        break;
      case 'respondent_device':
        response = await this.getDeviceList();
        break;
      case 'respondent_os':
        response = await this.getOSList();
        break;
      case 'regions':
        response = await this.getRegionList(companyId);
        break;
      case 'saleschannel':
        response = await this.getSalesChannel(companyId);
        break;
      case 'orderchannel':
        response = await this.getOrderChannel(companyId);
        break;
      case 'label_sentiment':
        response = await staticOptions.labelSentiment;
        break;
      case 'labels':
        response = await this.getLabelList(companyId);
        break;
      case 'brands':
        response = await this.getBrandsList(companyId);
        break;
      case 'clf_result':
        response = await this.getClfResultsList(companyId);
        break;
      case 'date_fields':
        response = await staticOptions.dateFields;
        break;
      case 'score':
        response = await staticOptions.scoreFields;
        break;
      case 'periods':
        response = await staticOptions.periodsList;
        break;
      case 'engineer':
        response = await this.getEngineerList(
          EmployeeType.TECHNICIAN,
          companyId,
        );
        break;
      case 'cp':
        response = await this.getMappingList(BSHMapping.CP, companyId);
        break;
      case 'dealer':
        response = await this.getEngineerList(EmployeeType.DEALER, companyId);
        break;
      case 'bsh_survey_categories':
        response = await this.getBSHSurveyCategories();
        break;
      case 'sv':
        response = await this.getMappingList(BSHMapping.SV, companyId);
        break;
      case 'tags':
        response = await this.getCLFTags(companyId);
        break;
      case 'clf_status':
        response = await staticOptions.cLFStatus;
        break;
      case 'users':
        response = await this.getUsers(companyId);
        break;
      case 'more_questions':
        response = await this.getMoreQuestions(companyId);
        break;
      case 'group_by_fields':
        response = await staticOptions.groupByFields;
        break;
      case 'favourite':
        response = await staticOptions.favourite;
        break;
      case 'warranty':
        response = await staticOptions.warranty;
        break;
      case 'label_type':
        response = await staticOptions.labelType;
        break;
      case 'export_type':
        response = await staticOptions.exportType;
        break;
      case 'contact_center':
        response = await this.getEngineerList(EmployeeType.DOB, companyId);
        break;
      case 'dispatcher':
        response = await this.getEngineerList(
          EmployeeType.DISPATCHER,
          companyId,
        );
        break;
      case 'preprepper':
        response = await this.getEngineerList(
          EmployeeType.PREPEPPER,
          companyId,
        );
        break;
      case 'service_partner':
        response = await this.getEngineerList(
          EmployeeType.SERVICE_PARTNER,
          companyId,
        );
        break;
      case 'counselor':
        response = await this.getEngineerList(EmployeeType.EMPLOYEE, companyId);
        break;
      case 'dealer':
        response = await this.getEngineerList(EmployeeType.DEALER, companyId);
        break;
      case 'mailing_status':
        response = staticOptions.mailingListStatus;
        break;
      case 'survey_department':
        response = await this.getSurveyList(companyId);
        break;
      default:
        return null;
        break;
    }
    if (!response) return null;

    this.logger.debug(`getList result for ${name}, company ${companyId}`, {
      response,
    });
    return response;
  }

  async getLanguageList() {
    let list = await this.allowedFilterRepo.query(
      `SELECT lang_369_1, lang_desc FROM languages WHERE isselectable=1 ORDER BY lang_desc`,
    );
    return list.map((item) => {
      return {
        id: item.lang_369_1,
        name: item.lang_desc,
      };
    });
  }
  async getProjectList(companyId) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const childCompanies = await this.secCompanyService.userChildCompanies();

    let list = await this.ecRepo
      .createQueryBuilder('ec')
      .select(
        "DISTINCT ec.xml_projectnr || ' (' || ec.xml_projectdiv || ')' as name, ec.xml_projectnr, ec.sec_company_id",
      )
      .where(
        'ec.sec_company_id =:sec_company_id AND ec.xml_projectnr IS NOT NULL',
        { sec_company_id: companyId },
      )
      .orderBy('ec.xml_projectnr', 'DESC')
      .getRawMany();

    let result = list.map((item: any) => {
      const code = childCompanies.find(
        (i) => i.id == item.sec_company_id,
      )?.code;
      const name = `${item.name}${
        company.isParent && code ? ` (${code})` : ''
      }`;
      return {
        id: item.xml_projectnr,
        name,
      };
    });

    result = [
      { id: 'BO-MDA', name: 'BO-MDA' },
      { id: 'BO-SDA', name: 'BO-SDA' },
      { id: 'SI-MDA', name: 'SI-MDA' },
      { id: 'SI-SDA', name: 'SI-SDA' },
      ...result,
    ];

    return result;
  }
  async getProjectBouwList(companyId: number) {
    const { userCompany } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let list = await this.projectRepo.query(
      `SELECT project_nr, project_name, project_id FROM projects WHERE sec_company_id = $1
      AND project_id NOT IN (SELECT project_id FROM project_groups)
      OR project_id IN (SELECT project_id FROM project_groups WHERE group_id = $2)
    ORDER BY project_id`,
      [companyId, userCompany.sec_group],
    );
    return list.map((item) => {
      return {
        id: item.project_id,
        name: `${item.project_nr}-${item.project_name}`,
      };
    });
  }
  async getTechgrpTypeList() {
    return staticOptions.techgrpTypeList.map((item) => ({
      id: item.id,
      name: this.translate.t(item.name),
    }));
  }
  async getProjectCategoriesList(companyId: number) {
    const childCompanies = await this.secCompanyService.userChildCompanies();
    const ids = [companyId, ...childCompanies.map((i) => i.id)];

    let list = await this.projectRepo.query(
      `SELECT category, pcategory_id, sec_company_id FROM pcategories where sec_company_id = ANY($1::int[]) ORDER BY category`,
      [ids],
    );

    await this.secCompanyService.userChildCompanies();
    return list.map((item) => {
      return {
        id: item.pcategory_id,
        name: this.utils.optionName(item.category, item.sec_company_id),
      };
    });
  }
  public getClubList = this.getProjectBouwList;

  async getMappingList(map_field: BSHMapping, companyId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const childCompanies = await this.secCompanyService.userChildCompanies();

    let where: FindOptionsWhere<DatatableEntity> = {
      map_field,
      isactive: 1,
    };
    if (companyId) {
      where.sec_company_id = companyId;
    } else if (company.isParent) {
      where.sec_company_id = In(childCompanies.map((i) => i.id));
    } else {
      where.sec_company_id = companyId;
    }
    let list = await this.datatableRepo.find({
      where,
      order: {
        map_description: 'ASC',
      },
    });

    await this.secCompanyService.userChildCompanies();
    return list.map((item) => {
      const desc = item.map_description;
      const key = item.map_key;
      const cid = item.sec_company_id;
      return {
        id: `${key} ${cid}`,
        name: this.utils.optionName(desc, cid),
      };
    });
  }
  getProductGroupList(companyId: number) {
    return this.getMappingList(BSHMapping.PRODAREA, companyId);
  }
  getProductDivisionList(companyId: number) {
    return this.getMappingList(BSHMapping.PRODDIVISION, companyId);
  }
  async getClassificationLabelList(companyId: number) {
    let list = await this.datatableRepo.query(`
    SELECT 
		CASE WHEN COALESCE(mil.item_label_locale,'') = '' 
		THEN COALESCE(s.item_label,'') 
		ELSE mil.item_label_locale 
		END || ' (' || CASE WHEN COALESCE(milp.item_label_locale,'') = '' 
						THEN COALESCE(s.item_label,'') 
						ELSE milp.item_label_locale 
						END || ')' AS display_val 
		, s.mclass_item_id AS return_val, s.sec_company_id 
		FROM mclass_item s 
		LEFT JOIN mclass_item_local mil ON mil.mclass_item_id = s.mclass_item_id AND mil.sec_company_id = ${companyId} 
		LEFT JOIN mclass_item_local milp ON milp.mclass_item_id = s.parent_id AND milp.sec_company_id = ${companyId} 
		WHERE  s.parent_id IS NOT NULL 
			AND s.deleted_by IS NULL 
      AND s.sec_company_id = ${companyId}
      ORDER BY milp.item_label_locale, mil.item_label_locale
      `);

    await this.secCompanyService.userChildCompanies();
    return list.map((item) => {
      return {
        id: item.return_val,
        name: this.utils.optionName(item.display_val, item.sec_company_id),
      };
    });
  }
  async getSurveyList(companyId: number) {
    let list = await this.surveyService.list<SurvSurveyEntity>({
      selects: ['surv_survey_id', 'internal_name', 'sec_company_id'],
      order: 'internal_name',
      companyId,
      checkParent: true,
    });
    await this.secCompanyService.userChildCompanies();
    return list.map((item) => {
      return {
        id: item.surv_survey_id,
        name: this.utils.optionName(item.internal_name, item.sec_company_id),
      };
    });
  }

  async getDeviceList() {
    let list = await this.datatableRepo.find({
      where: {
        map_field: 'device',
        isactive: 1,
      },
    });
    return list.map((item) => this.utils.mapDatatableItem(item));
  }

  async getOSList() {
    let list = await this.datatableRepo.find({
      where: {
        map_field: 'os',
        isactive: 1,
      },
    });
    return list.map((item) => this.utils.mapDatatableItem(item));
  }

  async getRegionList(companyId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const childCompanies = await this.secCompanyService.userChildCompanies();

    let list = await this.datatableRepo.find({
      where: {
        map_field: 'xml_region',
        isactive: 1,
        sec_company_id: company.isParent
          ? In(childCompanies.map((i) => i.id))
          : companyId,
      },
    });
    return list.map((item) => this.utils.mapDatatableItem(item));
  }
  async getSalesChannel(companyId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const childCompanies = await this.secCompanyService.userChildCompanies();

    let list = await this.datatableRepo.find({
      where: {
        map_field: 'xml_saleschannel',
        isactive: 1,
        sec_company_id: company.isParent
          ? In(childCompanies.map((i) => i.id))
          : companyId,
      },
    });
    return list.map((item) => this.utils.mapDatatableItem(item));
  }
  async getOrderChannel(companyId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const childCompanies = await this.secCompanyService.userChildCompanies();

    let list = await this.datatableRepo.find({
      where: {
        map_field: 'xml_orderchannel',
        isactive: 1,
        sec_company_id: company.isParent
          ? In(childCompanies.map((i) => i.id))
          : companyId,
      },
    });
    return list.map((item) => this.utils.mapDatatableItem(item));
  }

  async getLabelList(companyId: number) {
    let list = await this.datatableRepo.query(`
    SELECT s.mclass_item_id AS return_val , 
    CASE WHEN COALESCE(mil.item_label_locale,'') = '' 
    THEN COALESCE(s.item_label,'') 
    ELSE mil.item_label_locale 
    END || ' (' || CASE WHEN COALESCE(milp.item_label_locale,'') = '' 
            THEN COALESCE(p.item_label,'') 
            ELSE milp.item_label_locale 
            END || ')' AS display_val 
    , s.sec_company_id 
    FROM mclass_item s 
    JOIN mclass_item p ON s.parent_id = p.mclass_item_id 
    LEFT JOIN mclass_item_local mil ON mil.mclass_item_id = s.mclass_item_id AND mil.sec_company_id = ? 
    LEFT JOIN mclass_item_local milp ON milp.mclass_item_id = s.parent_id AND milp.sec_company_id = ? 
    WHERE
    s.parent_id IS NOT NULL 
    AND s.deleted_by IS NULL
    AND sec_company_id =${companyId}
    `);
    return list.map((item) => {
      return {
        id: item.return_val,
        name: item.display_val,
      };
    });
  }
  async getBrandsList(sec_company_id: number) {
    let list = await this.datatableRepo.query(
      `SELECT brand_name, sec_company_id FROM brands WHERE sec_company_id =${sec_company_id} ORDER BY brand_name`,
    );
    return list.map((item) => {
      return {
        id: item.brand_name,
        name: item.brand_name,
      };
    });
  }

  async getClfResultsList(sec_company_id: number) {
    let list = await this.datatableRepo.query(
      `SELECT  description, item_id, sec_company_id from clf_category_lists WHERE list = 'clf_result' AND sec_company_id = ${sec_company_id} ORDER BY ordernumber`,
    );
    return list.map((item) => {
      return {
        id: item.item_id,
        name: item.description,
      };
    });
  }

  async getEngineerList(type: EmployeeType, companyId: number) {
    let list = await this.datatableRepo.query(
      `SELECT techgrp_id, techgrp_name FROM techgrp WHERE sec_company_id =${companyId} AND techgrp_type ='${type}' ORDER BY techgrp_name`,
    );
    return list.map((item) => {
      return {
        id: item.techgrp_id,
        name: item.techgrp_name,
      };
    });
  }
  async getBSHSurveyCategories() {
    let list = await this.datatableRepo.query(`
      SELECT bc.bsh_category_name || ' ('||bc2.bsh_category_name ||') ' as name,  bc.bsh_category_id FROM bsh_categories bc
      LEFT JOIN bsh_categories bc2 ON bc2.bsh_category_id = bc.parent_bsh_category_id
      WHERE bc2.parent_bsh_category_id IS NOT NULL ORDER BY bc.bsh_category_name
    `);
    return list.map((item) => {
      return {
        id: item.bsh_category_id,
        name: item.name,
      };
    });
  }
  async getCLFTags(sec_company_id: number) {
    let list = await this.datatableRepo.query(`
      SELECT t.tag_name, t.tag_id, t.sec_company_id
      FROM tags t LEFT JOIN clf_tags ct ON ct.tag_id = t.tag_id
      WHERE sec_company_id =${sec_company_id}
      GROUP BY t.tag_id  ORDER BY t.tag_name
    `);
    return list
      .map((item) => {
        return {
          id: item.tag_id,
          name: item.tag_name,
        };
      })
      .filter((item) => item.name);
  }

  async getUsers(sec_company_id: number) {
    let list = await this.datatableRepo.query(`
      SELECT CONCAT(u.firstname, ' ', u.username) username, u.sec_user_id, suc.sec_company_id
      FROM sec_users u
      LEFT JOIN sec_user_company suc  ON u.sec_user_id = suc.sec_user_id
      WHERE suc.sec_company_id = ${sec_company_id}
    `);
    return list
      .map((item) => {
        return {
          id: item.sec_user_id,
          name: item.username,
        };
      })
      .filter((item) => item.name);
  }
  async getMoreQuestions(sec_company_id: number) {
    let list = await this.datatableRepo.query(`
      SELECT sp.surv_survey_id, s.internal_name, sp.title, sq.surv_surveyquestion_id, sq.surv_survquest_temp_id, s.sec_company_id FROM surv_surveyquestion sq 
      LEFT JOIN surv_surveypart sp ON sp.surv_surveypart_id = sq.surv_surveypart_id 
      LEFT JOIN surv_survey s ON s.surv_survey_id = sp.surv_survey_id 
      WHERE sq.questiontype IN (1,3,6,7,10) 
      AND s.sec_company_id =${sec_company_id}
      ORDER BY sp.title
    `);

    let result = list.reduce(
      (prev, item) => {
        let name = item.title;
        let temp_id = item.surv_survquest_temp_id;
        if (temp_id) {
          if (!prev.template_questions[temp_id]) {
            prev.template_questions[temp_id] = {
              name,
              survey: [],
              id: temp_id,
              type: 'template_question',
            };
          }
          prev.template_questions[temp_id].survey.push(item.surv_survey_id);
        } else {
          prev.questions.push({
            id: item.surv_surveyquestion_id,
            name,
            survey: item.surv_survey_id,
            type: 'question',
          });
        }
        prev.surveys[item.surv_survey_id] = item.internal_name;

        return prev;
      },
      { surveys: {}, questions: [], template_questions: {} },
    );
    return {
      surveys: result.surveys,
      questions: result.questions,
      template_questions: Object.values(result.template_questions),
    };
  }
}
