import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { GetFilterOptionsDto } from './dto/get-filter-options.dto';

import { UserSettingsService } from '../users/user-settings/user-settings.service';
import { FilterListService } from './filter-list.service';
import { FilterType, UserSettingsType } from '@/shared/enums';

@ApiTags('Filter Lists / Allowed Filters')
@ApiBearerAuth()
@Controller({
  path: 'filter_lists',
  version: '2',
})
export class FilterListController {
  constructor(
    private filterListService: FilterListService,
    private userSettingsService: UserSettingsService,
  ) {}

  @Get()
  @ApiOkResponse({
    description: 'Filterlist',
  })
  @UseGuards(AuthGuard)
  async getFilterOptions(
    @Query() query: GetFilterOptionsDto,
    @Req() { user }: AuthorizedRequest,
  ) {
    const { filter_type, list, template_id } = query;
    let results = await this.filterListService.filterList({
      filter_type: FilterType[filter_type],
      is_default: 1,
      list,
      template_id,
      addList: true,
      query,
    });
    let resp: any = {
      lists: results,
    };
    if (filter_type == 'notification') {
      let cid = query.sec_company_id;
      resp.user_properties =
        await this.userSettingsService.getUserSettingsByUserId(
          user.id,
          cid,
          UserSettingsType.NOTIFICATIONS,
        );
    }

    return resp;
  }
}
