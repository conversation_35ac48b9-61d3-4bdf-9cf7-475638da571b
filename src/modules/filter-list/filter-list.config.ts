import { EmployeeType } from '@/shared/enums/datatable.enum';

export const staticOptions = {
  confidence: [
    { id: 0, name: 'All' },
    { id: 1, name: 'Lower than 90' },
    { id: 2, name: 'Lower than 80' },
    { id: 3, name: 'Lower than 70' },
    { id: 4, name: 'Lower than 60' },
  ],
  classifiedList: [
    { id: 0, name: 'Not classified' },
    { id: 2, name: 'Classified' },
  ],
  publishList: [
    { id: 0, name: 'All' },
    { id: 1, name: 'Published' },
    { id: 2, name: 'Excluded' },
    { id: 3, name: 'Not Published' },
  ],
  publishedList: [
    { id: 0, name: 'All' },
    { id: 1, name: 'Not published' },
    { id: 2, name: 'Published' },
  ],
  publishableList: [
    { id: 0, name: 'All' },
    { id: 1, name: 'Not publishable' },
    { id: 2, name: 'Publishable' },
  ],
  callbackQuestionList: [
    { id: 0, name: 'All' },
    { id: 1, name: 'Callback: yes' },
    { id: 2, name: 'Callback: no' },
  ],
  dateFields: [
    { id: 1, name: 'Response date' },
    { id: 2, name: 'Visit date' },
  ],
  scoreFields: [
    ...[...Array(10).keys()].map((id) => ({ id, name: id })),
    { id: 11, name: 'Promoters' },
    { id: 12, name: 'Passives' },
    { id: 13, name: 'Detractors' },
  ],
  periodsList: [
    { id: 'day', name: 'Days' },
    { id: 'week', name: 'Weeks' },
    { id: 'month', name: 'Months' },
    { id: 'quarter', name: 'Quarters' },
    { id: 'year', name: 'Years' },
  ],
  groupByFields: [
    { id: 'xml_acceptance_by', name: 'Contact Center' },
    { id: 'xml_employee', name: 'Counselor' },
    { id: 'xml_dealer', name: 'Dealer' },
    { id: 'xml_wvb', name: 'Preprepper' },
    { id: 'xml_technician', name: 'Engineer' },
    { id: 'xml_planning', name: 'Dispatcher' },
    { id: 'xml_proddivision', name: 'Product Division' },
    { id: 'xml_prodarea', name: 'Product Groups' },
    { id: 'xml_custcreditnr', name: 'Service Partner' },
  ],
  favourite: [
    { id: 0, name: 'All' },
    { id: 1, name: 'Favourite' },
  ],
  warranty: [
    { id: 0, name: 'All' },
    { id: 1, name: 'In Guarantee' },
    { id: 2, name: 'Out of Guarantee' },
  ],
  labelType: [
    { id: 1, name: 'Manual' },
    { id: 2, name: 'AI Label' },
    { id: 4, name: 'AI training label' },
  ],
  exportType: [
    { id: 0, name: 'All' },
    { id: 1, name: 'Completed' },
  ],
  labelSentiment: [
    { id: 0, name: 'All' },
    { id: 1, name: 'Positive' },
    { id: 2, name: 'Negative' },
  ],
  cLFStatus: [],
  mediumList: [
    { id: 1, name: 'Email' },
    { id: 2, name: 'SMS' },
  ],
  mailingListStatus: [
    { id: 0, name: 'Pending' },
    { id: 1, name: 'Unknown Error' },
    { id: 2, name: 'Invalid Email' },
    { id: 3, name: 'Threshold' },
    { id: 4, name: 'Blacklıst' },
    { id: 5, name: 'Invalid Phone Number' },
    { id: 6, name: 'Invalid Template' },
    { id: 7, name: 'No Medium' },
    { id: 8, name: 'Sent' },
    { id: 9, name: 'Survey Completed' },
  ],
  techgrpTypeList: [
    {
      id: EmployeeType.TECHNICIAN,
      name: 'prop.ccs.lbl.technici',
    },
    {
      id: EmployeeType.DOB,
      name: 'prop.ccs.lbl.callcenteragent',
    },
    {
      id: EmployeeType.PREPEPPER,
      name: 'prop.ccs.lbl.preprepper',
    },
    {
      id: EmployeeType.DISPATCHER,
      name: 'prop.ccs.lbl.dispatch',
    },
    {
      id: EmployeeType.EMPLOYEE,
      name: 'prop.ccs.lbl.counselor',
    },
    {
      id: EmployeeType.SERVICE_PARTNER,
      name: 'prop.ccs.lbl.service_partner',
    },
    { id: EmployeeType.DEALER, name: 'prop.ccs.lbl.dealer' },
  ],
};
