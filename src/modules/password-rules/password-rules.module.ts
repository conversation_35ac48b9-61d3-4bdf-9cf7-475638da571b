import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import { PasswordRulesController } from './password-rules.controller';
import { PasswordRulesService } from './password-rules.service';
import { UserEntity } from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity]),
    ProgramSettingsModule,
    ModuleLoggerModule.register('password-rules'),
  ],
  controllers: [PasswordRulesController],
  providers: [PasswordRulesService],
  exports: [PasswordRulesService],
})
export class PasswordRulesModule {}
