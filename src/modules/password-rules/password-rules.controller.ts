import { <PERSON>, Get, Param, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { PasswordRulesService } from './password-rules.service';

@ApiTags('Auth PasswordRules')
@Controller({
  path: 'password_rules',
  version: '2',
})
export class PasswordRulesController {
  constructor(private passwordRulesService: PasswordRulesService) {}

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  getPasswordRules(@Req() { user }: AuthorizedRequest) {
    return this.passwordRulesService.getPasswordRules(user.id);
  }

  @Get(':key')
  getPasswordRulesByKey(@Param('key') key: string) {
    return this.passwordRulesService.getPasswordRulesBy<PERSON>ey(key);
  }
}
