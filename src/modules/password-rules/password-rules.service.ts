import { HttpException, Injectable } from '@nestjs/common';
import { ProgramSettingsService } from '../common/program-settings/program-settings.service';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity } from '../database/entities/sec-user.entity';
import { Repository } from 'typeorm';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class PasswordRulesService {
  constructor(
    private logger: ModuleLogger,
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
    private programSettingService: ProgramSettingsService,
  ) {}

  async getPasswordRules(sec_user_id: number) {
    this.logger.debug(`getPasswordRules for user ${sec_user_id}`);
    let { pwd_score, pwd_settings } =
      await this.programSettingService.getPasswordSettings(sec_user_id || 0);

    this.logger.debug(`getPasswordRules result for user ${sec_user_id}`, {
      pwd_score,
      pwd_settings,
    });

    return { pwd_score, pwd_settings };
  }

  async getPasswordRulesByKey(key: string) {
    let user = await this.userRepo.findOne({
      where: { activation_pwd: key },
    });
    this.logger.debug(`getPasswordRulesByKey for user ${user.sec_user_id}`, {
      activation_pwd: key,
    });

    if (!user?.sec_user_id) throw new HttpException('token is expired', 400);
    let rules = await this.getPasswordRules(user?.sec_user_id);
    this.logger.debug(
      `getPasswordRulesByKey result for user ${user.sec_user_id}`,
      { rules },
    );
    return { rules: rules?.pwd_settings };
  }
}
