import { AfterLoad, Column, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';

@Entity('image_library_folder')
export class ImageLibraryFolderEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  id: number;

  @Column({ type: 'character varying', length: 100 })
  name: string;

  @Column({ type: 'boolean', default: false })
  global: boolean;

  @Column({ type: 'integer', nullable: true })
  parent_id: number;

  @Column({ type: 'integer' })
  user_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  type?: string;

  @AfterLoad()
  setType() {
    this.type = 'folder';
  }
}
