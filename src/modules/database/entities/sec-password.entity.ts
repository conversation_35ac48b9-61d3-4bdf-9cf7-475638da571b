import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import type { UserEntity } from '@entities';

@Entity({ name: 'sec_passwords' })
export class UserPassword {
  @PrimaryGeneratedColumn({ type: 'integer' })
  sec_password_id: number;

  @Column({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'integer' })
  is_active: number;

  @Column({ type: 'character varying', length: '100' })
  pass: string;

  @Column({ type: 'timestamp', nullable: true })
  valid_till: Date;

  @OneToOne('UserEntity')
  @JoinColumn({
    name: 'sec_user_id',
    referencedColumnName: 'sec_user_id',
  })
  user: UserEntity;
}
