import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'salutation_items' })
export class SalutationItemEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  salutation_item_id: number;

  @Column({ type: 'integer' })
  salutation_id: number;

  @Column({ type: 'character varying', length: 50, nullable: true })
  salutation_item: string;

  @Column({ type: 'integer' })
  creator: number;

  @Column({ type: 'timestamp' })
  creation_date: Date;

  @Column({ type: 'integer' })
  sec_company_id: number;
}
