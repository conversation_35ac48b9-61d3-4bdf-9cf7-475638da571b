import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'sec_company_api_key' })
export class CompanyApiKeyEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  sec_company_api_key_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'varchar' })
  api_key: string;

  @Column({ type: 'varchar' })
  api_secret: string;

  @Column({ type: 'timestamptz' })
  valid_from: Date;

  @Column({ type: 'timestamptz' })
  valid_till: Date;

  @Column({ type: 'integer' })
  created_by: number;

  @Column({ type: 'timestamptz' })
  created_on: Date;

  @Column({ type: 'integer' })
  updated_by: number;

  @Column({ type: 'timestamptz' })
  updated_on: Date;

  @Column({ type: 'varchar' })
  description: string;
}
