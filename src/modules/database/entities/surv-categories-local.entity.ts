import type { SurvCategoriesEntity } from '@entities';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';

@Entity({ name: 'surv_categories_local' })
export class SurvCategoriesLocalEntity {
  @PrimaryColumn({ type: 'integer' })
  surv_category_local_id: number;

  @Column({ type: 'integer' })
  category_id: number;

  @Column({ type: 'varchar', length: '100' })
  category: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @ManyToOne(
    'SurvCategoriesEntity',
    (category: SurvCategoriesEntity) => category.categoriesLocal,
  )
  @JoinColumn({
    referencedColumnName: 'category_id',
    name: 'category_id',
  })
  survCategory: SurvCategoriesEntity;
}
