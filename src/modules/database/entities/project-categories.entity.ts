import type { ProjectsEntity, SurvSurveyEntity } from '@entities';
import {
  Column,
  Entity,
  <PERSON>inC<PERSON><PERSON>n,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'project_pcategories' })
export class ProjectCategoriesEntity {
  @PrimaryGeneratedColumn('uuid')
  project_pcategory_id: string;

  @Column()
  pcategory_id: string;

  @Column()
  project_id: string;

  @OneToOne('ProjectsEntity')
  @JoinColumn({
    referencedColumnName: 'project_id',
    name: 'project_id',
  })
  project: ProjectsEntity;
}
