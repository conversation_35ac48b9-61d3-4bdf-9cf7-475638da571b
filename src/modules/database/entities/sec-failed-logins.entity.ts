import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'sec_failed_logins' })
export class FailedLoginEntity {
  @PrimaryGeneratedColumn('uuid')
  sec_failed_login_id: string;

  @Column({ type: 'character varying', length: 50 })
  username: string;

  @Column({ type: 'timestamp' })
  expires_on: Date;

  @Column({ type: 'integer' })
  captcha_used: boolean | number;

  @Column({ type: 'integer' })
  captcha_failed: boolean | number;
}
