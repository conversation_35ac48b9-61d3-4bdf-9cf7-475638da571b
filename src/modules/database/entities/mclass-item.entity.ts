import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import type { MClassResponse } from '@entities';

@Entity('mclass_item')
export class MClassItem {
  @PrimaryGeneratedColumn({ type: 'integer' })
  mclass_item_id: number;

  @Column({ type: 'integer', nullable: true })
  sec_company_id: number;

  @Column({ type: 'integer', nullable: true })
  parent_id: number;

  @Column({ type: 'integer', nullable: true })
  order_number: number;

  @Column({ type: 'character varying', length: 300, nullable: true })
  item_label: string;

  @Column({ type: 'character varying', length: 300, nullable: true })
  item_label_locale: string;

  @Column({ type: 'character varying', length: 50, nullable: true })
  external_id: string;

  @Column({ type: 'character varying', length: 10, nullable: true })
  graph_color: string;

  @Column({ type: 'timestamp', nullable: true })
  deleted_on: Date;

  @Column({ type: 'integer', nullable: true })
  deleted_by: number;

  @Column({ type: 'timestamp', nullable: true })
  changed_on: Date;

  @Column({ type: 'integer', nullable: true })
  changed_by: number;

  @Column({ type: 'timestamp', nullable: true })
  created_on: Date;

  @Column({ type: 'integer', nullable: true })
  created_by: number;

  @OneToMany('MClassResponse', (mr: MClassResponse) => mr.item)
  responses: MClassResponse[];
}
