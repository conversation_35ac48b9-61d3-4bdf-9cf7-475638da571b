import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import type { Sec<PERSON>ey } from '@entities';
@Entity({ name: 'sec_user_keys' })
export class SecUserKey {
  @PrimaryGeneratedColumn({ type: 'integer' })
  id: number;

  @Column({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'integer' })
  key_id: number;

  @OneToOne('SecKey')
  @JoinColumn({
    name: 'key_id',
    referencedColumnName: 'key_id',
  })
  key: Sec<PERSON>ey;
}
