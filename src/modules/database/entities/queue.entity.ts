import { QueueStatus } from '@/shared/enums/queue.enum';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'queue' })
export class QueueEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  id: number;

  @Column({ type: 'character varying', length: 150 })
  module: string;

  @Column({ type: 'character varying', length: 150 })
  value: string;

  @Column({ type: 'integer' })
  status: QueueStatus;

  @Column({ type: 'json' })
  result: any;

  @Column({ type: 'json' })
  errors: any;

  @Column({ type: 'integer' })
  tryCount: number;

  @Column({ type: 'time without time zone' })
  creation_date: Date;
}
