import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('email_template')
export class EmailTemplateEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  email_template_id: number;

  @Column({ type: 'bytea' })
  logo: any;

  @Column({ type: 'text' })
  emailtext: string;

  @Column({ type: 'character varying', length: 200 })
  companyname: string;

  @Column({ type: 'character varying', length: 300 })
  email: string;

  @Column({ type: 'character varying', length: 50 })
  triggername: string;

  @Column({ type: 'character varying', length: 200 })
  email_subject: string;

  @Column({ type: 'text' })
  emailtext_remainder: string;

  @Column({ type: 'character varying', length: 200 })
  modifier: string;

  @Column({ type: 'character varying', length: 200 })
  creator: string;

  @Column({ type: 'timestamp without time zone' })
  modification_date: Date;

  @Column({ type: 'timestamp without time zone' })
  creation_date: Date;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'integer' })
  remainder_afterdays: number;

  @Column({ type: 'integer' })
  close_survey_days: number;

  @Column({ type: 'integer' })
  remainder_count: number;

  @Column({ type: 'character varying', length: 200 })
  remainder_subject: string;

  @Column({ type: 'integer' })
  send_thank_mail: number;

  @Column({ type: 'text' })
  emailtext_thankmail: string;

  @Column({ type: 'character varying', length: 200 })
  subject_thankmail: string;

  @Column({ type: 'character varying', length: 200 })
  template_name: string;

  @Column({ type: 'integer' })
  base_template: number;

  @Column({ type: 'character varying', length: 500 })
  template_info: string;

  @Column({ type: 'character varying', length: 300 })
  email_reply: string;

  @Column({ type: 'character varying', length: 20 })
  sender_sms: string;
}
