import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('publications')
export class PublicationsEntity {
  @PrimaryGeneratedColumn('uuid')
  publication_id: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'integer' })
  surv_survey_id: number;

  @Column({ type: 'text' })
  project_id: string;

  @Column({ type: 'character varying', length: 50 })
  project_nr: string;

  @Column({ type: 'character varying', length: 50 })
  survey_name: string;

  @Column({ type: 'integer' })
  nps: number;

  @Column({ type: 'time without time zone' })
  response_date: Date;

  @Column({ type: 'character varying', length: 2000 })
  strengths: string;

  @Column({ type: 'character varying', length: 2000 })
  improvements: string;

  @Column({ type: 'text' })
  other_answers: string;

  @Column({ type: 'double precision' })
  score: number;

  @Column({ type: 'integer' })
  surv_surveyanswer_id: number;

  @Column({ type: 'character varying', length: 50 })
  respondent_initials: string;

  @Column({ type: 'character varying', length: 50 })
  respondent_customername: string;

  @Column({ type: 'character varying', length: 50 })
  brand: string;
}
