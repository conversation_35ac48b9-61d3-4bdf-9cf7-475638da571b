import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('mod_wccs_meta_tables')
export class ModWccsMetaTablesEntity {
  @PrimaryColumn({ type: 'character varying', length: 50 })
  metatable_id: string;

  @Column({ type: 'character varying', length: 50 })
  metatable_name: string;

  @Column({ type: 'character varying', length: 50 })
  metatable_title: string;

  @Column({ type: 'integer' })
  metatable_company_type: number;
}
