import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('projects')
export class ProjectsEntity {
  @PrimaryGeneratedColumn('uuid')
  project_id: string;

  @Column({ type: 'character varying', length: 100 })
  project_nr: string;

  @Column({ type: 'character varying', length: 100 })
  project_name: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'time without time zone' })
  creation_date: Date;

  @Column({ type: 'integer' })
  creator: number;

  @Column({ type: 'time without time zone' })
  modification_date: Date;

  @Column({ type: 'integer' })
  modifier: number;

  @Column({ type: 'character varying', length: 50 })
  project_city: string;

  @Column({ type: 'character varying', length: 100 })
  project_name_extern: string;
}
