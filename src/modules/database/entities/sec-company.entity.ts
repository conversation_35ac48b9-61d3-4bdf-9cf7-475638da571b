import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'sec_company' })
export class CompanyEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'varchar' })
  company_name: string;

  @Column({ type: 'timestamp without time zone' })
  creation_date: string;

  @Column({ type: 'timestamp without time zone' })
  modification_date: string;

  @Column({ type: 'varchar' })
  modifier: string;

  @Column({ type: 'varchar' })
  creator: string;

  @Column({ type: 'integer' })
  company_type: number;

  @Column({ type: 'varchar' })
  default_language_key: string;

  @Column({ type: 'text' })
  jasper_settings: string;

  @Column({ type: 'varchar' })
  country_code: string;

  @Column({ type: 'integer' })
  send_hour: number;

  @Column({ type: 'varchar' })
  time_zone: string;

  @Column({ type: 'varchar' })
  api_key: string;

  @Column({ type: 'varchar' })
  api_secret: string;

  @Column({ type: 'timestamp without time zone' })
  api_date: string;

  @Column({ type: 'varchar' })
  company_code: string;

  @Column({ type: 'integer' })
  gdpr_months: number;

  @Column({ type: 'integer' })
  last_run_by: number;

  @Column({ type: 'timestamp without time zone' })
  last_run_on: string;

  @Column({ type: 'integer' })
  is_parent_company: number;

  @Column({ type: 'bytea' })
  sec_company_uid: string;

  @Column({ type: 'timestamp without time zone' })
  gdpr_oldest_dt: string;
}
