import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { SolutionType } from '@/shared/enums';
import type { I18nEntity, SecUserRight } from '@entities';

@Entity({ name: 'sec_key' })
export class SecKey {
  @PrimaryGeneratedColumn({ type: 'integer' })
  key_id: number;

  @Column({ type: 'character varying', length: '50' })
  name: string;

  @Column({ type: 'character varying', length: '200' })
  description: string;

  @OneToOne('I18nEntity')
  @JoinColumn({
    name: 'description',
    referencedColumnName: 'message_key',
  })
  description_translated: I18nEntity;

  @Column({ type: 'character varying', length: '50' })
  tooltip: string;

  @OneToOne('I18nEntity')
  @JoinColumn({
    name: 'tooltip',
    referencedColumnName: 'message_key',
  })
  tooltip_translated: I18nEntity;

  @Column({ type: 'integer' })
  sort_order: number;

  @Column({ type: 'integer' })
  solution_type: SolutionType;

  @OneToOne('SecUserRight')
  @JoinColumn({
    name: 'key_id',
    referencedColumnName: 'key_id',
  })
  userRights: SecUserRight;
}
