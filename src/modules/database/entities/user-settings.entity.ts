import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_settings')
export class UserSettingsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  user_setting_id: number;

  @Column({ type: 'integer' })
  user_setting_type: number;

  @Column({ type: 'character varying', length: 100 })
  name: string;

  @Column({ type: 'text' })
  setting_object: string;

  @Column({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'integer' })
  company_id: number;

  @Column({ type: 'timestamp without time zone' })
  created_on: Date;
}
