import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'sec_group' })
export class SecGroupEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  group_id: number;

  @Column({ type: 'character varying', length: 500 })
  name: string;

  @Column({ type: 'character varying', length: 4000 })
  description: string;

  @Column({ type: 'integer' })
  sec_company_id: number;
}
