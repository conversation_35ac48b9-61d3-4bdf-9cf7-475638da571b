import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('log_events')
export class LogEventEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  log_event_id: number;

  @Column({ type: 'timestamp' })
  event_time: Date;

  @Column({ type: 'varchar', length: 100 })
  logger_name: string;

  @Column({ type: 'integer' })
  log_level: number;

  @Column({ type: 'varchar', length: 3000 })
  log_message: string;

  @Column({ type: 'varchar', length: 50 })
  solution_name: string;

  @Column({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'varchar', length: 50 })
  form_name: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'varchar', length: 36 })
  client_id: string;

  @Column({ type: 'varchar', length: 36 })
  request_id: string;

  @Column({ type: 'jsonb' })
  additional_info: any;
}
