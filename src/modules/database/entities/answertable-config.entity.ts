import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('answertable_config')
export class AnswertableConfigEntity {
  @PrimaryGeneratedColumn('uuid')
  answertable_config_uuid: string;

  @Column({ type: 'character varying', length: 50 })
  answertable_name: string;

  @Column({ type: 'character varying', length: 100 })
  answertable_description: string;

  @Column({ type: 'text' })
  answertable_config: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  is_locked: boolean;
}
