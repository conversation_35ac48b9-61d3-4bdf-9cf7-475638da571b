import {
  Column,
  <PERSON>ti<PERSON>,
  JoinTable,
  ManyToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { SurveyAnswerEntity } from './surv-surveyanswer.entity';

@Entity('keywords')
export class KeywordsEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'character varying', length: '255' })
  keyword: string;

  @Column({ type: 'character varying', length: '20' })
  sentiment: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'timestamp' })
  created_on: Date;

  @ManyToMany('SurveyAnswerEntity')
  @JoinTable({
    name: 'keywords_answer_link',
    joinColumn: {
      name: 'keyword_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'answer_id',
      referencedColumnName: 'surv_surveyanswer_id',
    },
  })
  answers: SurveyAnswerEntity[];
}
