import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'tags' })
export class TagsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  tag_id: number;

  @Column({ type: 'character varying', length: 100 })
  tag_name: string;

  @Column({ type: 'character varying', length: 25 })
  tag_type: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'integer' })
  created_by: number;

  @Column({ type: 'timestamp without time zone' })
  created_on: string;
}
