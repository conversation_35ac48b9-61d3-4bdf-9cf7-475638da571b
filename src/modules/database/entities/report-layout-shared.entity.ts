import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('report_layout_shared')
export class ReportLayoutSharedEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  report_layout_shared_id: number;

  @Column({ type: 'integer' })
  report_layout_id: number;

  @Column({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @CreateDateColumn({ type: 'timestamp without time zone' })
  last_viewed: Date;
}
