import { MclassRecordStatus } from '@/shared/enums';
import type { MClassItem, MClassItemLocal } from '@entities';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('mclass_response')
export class MClassResponse {
  @PrimaryGeneratedColumn({ type: 'integer' })
  mclass_response_id: number;

  @Column({ type: 'integer', nullable: true })
  item_id: number;

  @Column({ type: 'integer', nullable: true })
  answer_id: number;

  @Column({ type: 'integer', nullable: true })
  is_liked: number;

  @Column({ type: 'integer', nullable: true })
  is_potential: number;

  @Column({ type: 'integer', nullable: true })
  istype: number;

  @Column({ type: 'integer', nullable: true })
  record_status: MclassRecordStatus;

  @Column({ type: 'integer', nullable: true })
  is_excluded: number;

  @Column({ type: 'double precision', nullable: true })
  confidence: number;

  @Column({ type: 'timestamp', nullable: true })
  modification_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  created_on: Date;

  @Column({ type: 'integer', nullable: true })
  created_by: number;

  @ManyToOne('MClassItem', (item: MClassItem) => item.responses)
  @JoinColumn({ name: 'item_id', referencedColumnName: 'mclass_item_id' })
  item: MClassItem;

  @ManyToOne('MClassItemLocal', (item: MClassItemLocal) => item.responses)
  @JoinColumn({ name: 'item_id', referencedColumnName: 'mclass_item_id' })
  item_local: MClassItemLocal;
}
