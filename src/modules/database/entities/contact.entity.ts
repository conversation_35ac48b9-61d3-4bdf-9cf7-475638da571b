import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('contacts')
export class ContactEntity {
  @PrimaryColumn({ type: 'uuid' })
  contact_id: string;

  @Column({ type: 'character varying', length: 36, nullable: true })
  project_id: string;

  @Column({ type: 'character varying', length: 15, nullable: true })
  contact_initials: string;

  @Column({ type: 'character varying', length: 100, nullable: true })
  contact_name: string;

  @Column({ type: 'character varying', length: 20, nullable: true })
  contact_salutation: string;

  @Column({ type: 'character varying', length: 300, nullable: true })
  contact_email: string;

  @Column({ type: 'character varying', length: 200, nullable: true })
  contact_company: string;

  @Column({ type: 'character varying', length: 10, nullable: true })
  object_postal_code: string;

  @Column({ type: 'character varying', length: 100, nullable: true })
  object_address: string;

  @Column({ type: 'character varying', length: 10, nullable: true })
  object_nr_addition: string;

  @Column({ type: 'character varying', length: 120, nullable: true })
  object_fulladdress: string;

  @Column({ type: 'character varying', length: 20, nullable: true })
  object_buildnr: string;

  @Column({ type: 'character varying', length: 50, nullable: true })
  object_city: string;

  @Column({ type: 'character varying', length: 20, nullable: true })
  contact_phone1: string;

  @Column({ type: 'character varying', length: 2, nullable: true })
  contact_language: string;

  @Column({ type: 'integer', nullable: true })
  object_house_number: number;

  @Column({ type: 'integer', nullable: true })
  contact_type: number;

  @Column({ type: 'integer', nullable: true })
  contact_nr: number;

  @Column({ type: 'integer', nullable: true })
  sec_company_id: number;

  @Column({ type: 'timestamp', nullable: true })
  creation_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  modification_date: Date;

  @Column({ type: 'integer', nullable: true })
  creator: number;

  @Column({ type: 'integer', nullable: true })
  modifier: number;
}
