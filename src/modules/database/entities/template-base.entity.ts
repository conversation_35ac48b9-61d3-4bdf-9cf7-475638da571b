import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('template_base')
export class TemplateBaseEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  template_base_id: number;

  @Column({ type: 'character varying', length: 100 })
  template_name: string;

  @Column({ type: 'character varying', length: 50 })
  image: string;

  @Column({ type: 'text' })
  template_text: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'jsonb' })
  builder: any;
}
