import { <PERSON>umn, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'connector' })
export class ConnectorEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  connector_id: number;

  @Column({ type: 'text' })
  connector_object: string | any;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'varchar' })
  creator: string;

  @Column({ type: 'varchar' })
  modifier: string;

  @Column({ type: 'timestamp without time zone' })
  modification_date: Date;

  @CreateDateColumn({ type: 'timestamp without time zone' })
  creation_date: Date;

  @Column({ type: 'integer' })
  connector_type: number;
}
