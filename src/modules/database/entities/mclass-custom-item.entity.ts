import {
  Column,
  <PERSON>tity,
  JoinTable,
  ManyToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import type { SurveyAnswerEntity } from './surv-surveyanswer.entity';

@Entity('mclass_custom_item')
export class MClassCustomItem {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'character varying', length: '255' })
  item_label: string;

  @Column({ type: 'character varying', length: '255' })
  parent_label: string;

  @Column({ type: 'character varying', length: '20' })
  sentiment: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'timestamp' })
  created_on: Date;

  @ManyToMany('SurveyAnswerEntity')
  @JoinTable({
    name: 'mclass_custom_item_answer_link',
    joinColumn: {
      name: 'custom_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'answer_id',
      referencedColumnName: 'surv_surveyanswer_id',
    },
  })
  answers: SurveyAnswerEntity[];
}
