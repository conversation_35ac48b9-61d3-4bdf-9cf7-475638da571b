import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('cron_jobs')
export class CronJobsEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column()
  name: string;

  @Column('json')
  parameters: any[];

  @Column()
  timer: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  logging: boolean;

  @Column('boolean')
  callOnStart: boolean;

  @Column('timestamp')
  lastCall: Date;
}
