import type {
  ClfTagsEntity,
  EmailCustomerEntity,
  SurveyAnswerEntity,
  UserEntity,
} from '@entities';
import {
  Column,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('clf_call')
export class ClfCallEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  clf_call_id: number;

  @Column({ type: 'integer', nullable: true })
  email_customer_id: number;

  @Column({ type: 'integer', nullable: true })
  assigned_by: number;

  @Column({ type: 'integer', nullable: true })
  assigned_to: number;

  @Column({ type: 'timestamp', nullable: true })
  assigned_date: Date;

  @Column({ type: 'integer', nullable: true })
  closed_by: number;

  @Column({ type: 'timestamp', nullable: true })
  closed_by_date: Date;

  @Column({ type: 'integer', nullable: true })
  declined_by: number;

  @Column({ type: 'timestamp', nullable: true })
  declined_by_date: Date;

  @Column({ type: 'integer', nullable: true })
  status: number;

  @Column({ type: 'integer', nullable: true })
  sec_company_id: number;

  @Column({ type: 'character varying', length: 100, nullable: true })
  caused_by: string;

  @Column({ type: 'character varying', length: 100, nullable: true })
  cause: string;

  @Column({ type: 'character varying', length: 100, nullable: true })
  result: string;

  @Column({ type: 'double precision', nullable: true })
  credit: number;

  @Column({ type: 'integer', nullable: true })
  c_answer_id: number;

  @Column({ type: 'integer', nullable: true })
  c_clf1_q_id: number;

  @Column({ type: 'integer', nullable: true })
  c_clf2_q_id: number;

  @Column({ type: 'integer', nullable: true })
  c_nps_q_id: number;

  @Column({ type: 'text', nullable: true })
  c_notes: string;

  @Column({ type: 'integer', nullable: true })
  lane: number;

  @Column({ type: 'integer', nullable: true })
  onhold_by: number;

  @Column({ type: 'timestamp', nullable: true })
  onhold_date: Date;

  @Column({ type: 'character varying', length: 2000, nullable: true })
  clf_summary: string;

  @Column({ type: 'timestamp', nullable: true })
  modification_date: Date;

  @Column({ type: 'integer', nullable: true })
  c_callback_q_id: number;

  @Column({ type: 'integer', nullable: true })
  contact_customer: number;

  @Column({ type: 'character varying', length: '255', nullable: true })
  contact_customer_reason: string;

  @OneToOne('SurveyAnswerEntity')
  @JoinColumn({
    name: 'email_customer_id',
    referencedColumnName: 'email_customer_id',
  })
  survey_answer: SurveyAnswerEntity;

  @OneToOne('EmailCustomerEntity')
  @JoinColumn({
    name: 'email_customer_id',
    referencedColumnName: 'email_customer_id',
  })
  email_customer: EmailCustomerEntity;

  @OneToMany('ClfTagsEntity', (clf_tag: ClfTagsEntity) => clf_tag.clf_call)
  tags: ClfTagsEntity[];

  @OneToOne('UserEntity')
  @JoinColumn({
    foreignKeyConstraintName: 'sec_user_id',
    name: 'assigned_to',
  })
  assigned_to_user: UserEntity;
}
