import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'surv_surveyansweritem' })
export class SurveyAnswerItemEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  surv_surveyansweritem_id: number;

  @Column({ type: 'varchar' })
  creator: string;

  @Column({ type: 'varchar' })
  modifier: string;

  @Column({ type: 'timestamp' })
  creation_date: string;

  @Column({ type: 'timestamp' })
  modification_date: string;

  @Column({ type: 'integer' })
  surv_surveyanswer_id: number;

  @Column({ type: 'integer' })
  surv_surveyquestion_id: number;

  @Column({ type: 'integer' })
  surv_surveysubquestion_id: number;

  @Column({ type: 'text' })
  return_value: string;
}
