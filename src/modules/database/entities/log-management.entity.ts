import { LogLevels } from '@lib/logger/LogOptions';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('log_management')
export class LogManagementEntity {
  @PrimaryGeneratedColumn('uuid')
  log_management_id: string;

  @Column({ type: 'varchar', length: 100 })
  logger_name: string;

  @Column({ type: 'integer' })
  logger_active: number;

  @Column({ type: 'varchar', length: 50 })
  application_part: string;

  @Column({ type: 'varchar', length: 5 })
  log_level: LogLevels;

  @Column({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;
}
