import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import type { SecGroupEntity, SecKey } from '@entities';

@Entity({ name: 'sec_user_right' })
export class SecUserRight {
  @PrimaryGeneratedColumn({ type: 'integer' })
  user_right_id: number;

  @Column({ type: 'integer' })
  group_id: number;

  @Column({ type: 'integer' })
  key_id: number;

  @OneToOne('SecKey')
  @JoinColumn({
    name: 'key_id',
    referencedColumnName: 'key_id',
  })
  key: Sec<PERSON>ey;

  @OneToOne('SecGroupEntity')
  @JoinColumn({
    name: 'group_id',
    referencedColumnName: 'group_id',
  })
  group: SecGroupEntity;
}
