import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'surv_surveyanswer_ai' })
export class SurveyAnswerAIEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  surv_surveyanswer_ai_id: number;

  @Column({ type: 'integer' })
  answer_id: number;

  @Column({ type: 'double precision' })
  response_confidence: number;

  @Column({ type: 'text' })
  response_justification: string;

  @Column({ type: 'integer' })
  prompt_tokens: number;

  @Column({ type: 'integer' })
  completion_tokens: number;

  @Column({ type: 'integer' })
  total_tokens: number;

  @Column({ type: 'integer', default: 0 })
  response_review: number;

  @Column({ type: 'character varying', length: '50' })
  ai_model: string;
}
