import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import type { ModWccsMetaTablesEntity } from '@entities';

@Entity('mod_wccs_meta_fields')
export class ModWccsMetaFieldsEntity {
  @PrimaryColumn({ type: 'character varying', length: 50 })
  metafield_id: string;

  @Column({ type: 'character varying', length: 50 })
  metatable_id: string;

  @ManyToOne('ModWccsMetaTablesEntity')
  @JoinColumn({ name: 'metatable_id' })
  metatable: ModWccsMetaTablesEntity;

  @Column({ type: 'character varying', length: 200 })
  metafield_name: string;

  @Column({ type: 'integer' })
  metafield_isfield: number;

  @Column({ type: 'character varying', length: 50 })
  metafield_title: string;

  @Column({ type: 'character varying', length: 50 })
  metafield_valuelist: string;

  @Column({ type: 'character varying', length: 50 })
  metafield_order: string;

  @Column({ type: 'integer' })
  metafield_ismandatory: number;

  @Column({ type: 'integer' })
  metafield_isunique: number;

  @Column({ type: 'character varying', length: 20 })
  metafield_type: string;

  @Column({ type: 'integer' })
  metafield_limit: number;
}
