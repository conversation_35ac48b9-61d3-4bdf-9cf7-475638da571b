import { Column, <PERSON>tity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'sec_company_api_rights' })
export class SecCompanyApiRightsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  sec_company_api_rights_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'integer' })
  sec_company_api_id: number;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Column({ type: 'integer' })
  created_by: number;

  @CreateDateColumn({ type: 'timestamptz' })
  created_on: Date;

  @Column({ type: 'integer' })
  updated_by: number;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_on: Date;
}

