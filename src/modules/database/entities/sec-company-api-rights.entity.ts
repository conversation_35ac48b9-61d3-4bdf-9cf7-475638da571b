import { Column, Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, JoinColumn, ManyToOne } from 'typeorm';
import type { CompanyApiKeyEntity } from './sec_company_api_key.entity';

@Entity({ name: 'sec_company_api_rights' })
export class SecCompanyApiRightsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  sec_company_api_rights_id: number;

  @Column({ type: 'integer' })
  sec_company_api_key_id: number;

  @ManyToOne('CompanyApiKeyEntity')
  @JoinColumn({
    referencedColumnName: 'sec_company_api_key_id',
    name: 'sec_company_api_key_id',
  })
  apiKey: CompanyApiKeyEntity;

  @Column({ type: 'integer' })
  sec_company_api_id: number;
}

