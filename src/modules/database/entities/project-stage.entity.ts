import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  OneToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';

import type { ContactEntity } from '@entities';
@Entity('project_stages')
export class ProjectStageEntity {
  @PrimaryGeneratedColumn('uuid')
  project_stage_id: string;

  @Column({ type: 'character varying', length: 36 })
  stage_id: string;

  @Column('uuid')
  contact_id: string;

  @Column({ type: 'integer' })
  email_customer_id: number;

  @Column({ type: 'time without time zone' })
  creation_date: Date;

  @Column({ type: 'character varying', length: 200 })
  creator: string;

  @OneToOne('ContactEntity')
  @JoinColumn({
    name: 'contact_id',
    referencedColumnName: 'contact_id',
  })
  contact: ContactEntity;
}
