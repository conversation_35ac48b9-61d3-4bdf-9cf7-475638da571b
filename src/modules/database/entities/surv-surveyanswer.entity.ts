import type {
  EmailCustomerEntity,
  KeywordsEntity,
  MClassCustomItem,
  SurvSurveyEntity,
  SurveyAnswerItemEntity,
} from '@entities';
import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  ManyToMany,
  JoinTable,
  OneToOne,
} from 'typeorm';

@Entity({ name: 'surv_surveyanswer' })
export class SurveyAnswerEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  surv_surveyanswer_id: number;

  @Column({ type: 'varchar' })
  creator: string;

  @Column({ type: 'varchar' })
  modifier: string;

  @Column({ type: 'timestamp' })
  creation_date: Date;

  @Column({ type: 'timestamp' })
  modification_date: Date;

  @Column({ type: 'integer' })
  surv_survey_id: number;

  @ManyToOne('SurvSurveyEntity', { cascade: true })
  @JoinColumn({
    name: 'surv_survey_id',
    referencedColumnName: 'surv_survey_id',
  })
  survey: SurvSurveyEntity;

  @Column({ type: 'integer' })
  email_customer_id: number;

  @ManyToOne('EmailCustomerEntity', { cascade: true })
  @JoinColumn({
    name: 'email_customer_id',
    referencedColumnName: 'email_customer_id',
  })
  emailCustomer: EmailCustomerEntity;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'varchar' })
  browser_version: string;

  @Column({ type: 'varchar' })
  browser: string;

  @Column({ type: 'varchar' })
  device: string;

  @Column({ type: 'varchar' })
  os_version: string;

  @Column({ type: 'varchar' })
  os: string;

  @Column({ type: 'timestamp' })
  creation_date_system: string;

  @Column({ type: 'integer' })
  nps_value: number;

  @Column({ type: 'integer' })
  comments_neg_length: number;

  @Column({ type: 'integer' })
  comments_length: number;

  @Column({ type: 'integer' })
  comments_pos_length: number;

  @Column({ type: 'integer' })
  has_text: number;

  @Column({ type: 'timestamp' })
  lock_date: string;

  @Column({ type: 'integer' })
  lock_user: number;

  @Column({ type: 'varchar' })
  publication_id: string;

  @Column({ type: 'integer' })
  publication_state: number;

  @Column({ type: 'timestamp', default: null, nullable: true })
  ai_last_try: Date;

  @Column({ type: 'integer', nullable: true })
  ai_processed_state: number;

  @Column({ type: 'integer', nullable: true })
  ai_state: number;

  @Column({ type: 'integer', nullable: true })
  ai_tries: number;

  @OneToOne('SurveyAnswerItemEntity')
  @JoinColumn({
    name: 'surv_surveyanswer_id',
    referencedColumnName: 'surv_surveyanswer_id',
  })
  item: SurveyAnswerItemEntity;

  @ManyToMany('MClassCustomItem', { cascade: ['insert'] })
  @JoinTable({
    name: 'mclass_custom_item_answer_link',
    joinColumn: {
      name: 'answer_id',
      referencedColumnName: 'surv_surveyanswer_id',
    },
    inverseJoinColumn: {
      name: 'custom_id',
      referencedColumnName: 'id',
    },
  })
  customCategories: MClassCustomItem[];

  @ManyToMany('KeywordsEntity', { cascade: ['insert'] })
  @JoinTable({
    name: 'keywords_answer_link',
    joinColumn: {
      name: 'answer_id',
      referencedColumnName: 'surv_surveyanswer_id',
    },
    inverseJoinColumn: {
      name: 'keyword_id',
      referencedColumnName: 'id',
    },
  })
  keywords: KeywordsEntity[];
}
