import { AfterLoad, <PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';

@Entity('image_library')
export class ImageLibraryEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  id: number;

  @Column({ type: 'character varying', length: 500 })
  name: string;

  @Column({ type: 'character varying', length: 100 })
  key: string;

  @Column({ type: 'integer' })
  folder_id: number;

  @Column({ type: 'integer' })
  user_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  type?: string;

  @AfterLoad()
  setType() {
    this.type = 'image';
  }
}
