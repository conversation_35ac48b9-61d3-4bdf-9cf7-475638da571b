import type { SurvSurveyEntity } from '@entities';
import {
  Column,
  Entity,
  <PERSON>inColumn,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'surv_survey_categories' })
export class SurvSurveyCategoriesEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  surv_survey_category_id: number;

  @Column({ type: 'integer' })
  surv_survey_id: number;

  @Column({ type: 'integer' })
  category_id: number;

  @OneToOne('SurvSurveyEntity')
  @JoinColumn({
    referencedColumnName: 'surv_survey_id',
    name: 'surv_survey_id',
  })
  survey: SurvSurveyEntity;
}
