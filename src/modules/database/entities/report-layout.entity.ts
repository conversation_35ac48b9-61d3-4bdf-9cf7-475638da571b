import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('report_layout')
export class ReportLayoutEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  report_layout_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'character varying', length: 100 })
  layout_name: string;
  @Column({ type: 'integer' })
  layout_type: number;

  @Column({ type: 'text' })
  layout_data: string;

  @Column({ type: 'integer' })
  created_by: number;

  @Column()
  created_on: Date;

  @Column({ type: 'integer' })
  modified_by: number;

  @Column()
  modified_on: Date;

  @Column({ type: 'character varying', length: 500 })
  tags: string;

  @Column({ type: 'integer' })
  is_home: number;
}
