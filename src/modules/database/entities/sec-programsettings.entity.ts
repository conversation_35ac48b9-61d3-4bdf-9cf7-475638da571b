import { parse<PERSON><PERSON><PERSON> } from '@/shared/utils';
import { AfterLoad, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

export class Smtp {
  host: string;
  name?: string = null;
  username: string;
  password: string;
  secure: boolean;
  port: number;
}

@Entity('sec_programsettings')
export class SecProgramSettingsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  sec_programsetting_id: number;

  @Column({ type: 'character varying', length: 200 })
  ftp_path: string;

  @Column({ type: 'character varying', length: 50 })
  ftp_host: string;

  @Column({ type: 'integer' })
  ftp_port: number;

  @Column({ type: 'character varying', length: 50 })
  ftp_username: string;

  @Column({ type: 'character varying', length: 50 })
  ftp_password: string;

  @Column({ type: 'integer' })
  ftp_usesftp: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'text' })
  survey_link: string;

  @Column({ type: 'text' })
  survey_linkdisplay: string;

  @Column({ type: 'text' })
  survey_previewaddress: string;

  @Column({ type: 'text' })
  optout_link: string;

  @Column({ type: 'text' })
  optout_linkdisplay: string;

  @Column({ type: 'timestamp without time zone' })
  ftp_lastimport_time: Date;

  @Column({ type: 'character varying', length: 500 })
  sent_threshold: string;

  @Column({ type: 'character varying', length: 500 })
  import_filter: string;

  @Column({ type: 'character varying', length: 500 })
  export_mailer: string;

  @Column({ type: 'text' })
  company_properties: any;

  @Column({ type: 'character varying', length: 50 })
  file_encoding: string;

  @Column({ type: 'integer' })
  pwd_score: number;

  @Column({ type: 'text' })
  pwd_settings: string;

  @Column({ type: 'text' })
  event_code: string;

  @Column({ type: 'text' })
  disclaimer: string;

  @Column({ type: 'character varying', length: 200 })
  imprint_link: string;

  @Column({ type: 'character varying', length: 3 })
  export_prefix: string;

  @Column({ type: 'integer' })
  export_to_ftp: number;

  @Column({ type: 'integer' })
  benchmark_fase1_surv_survey_id: number;

  @Column({ type: 'integer' })
  benchmark_fase2_surv_survey_id: number;

  @Column({ type: 'integer' })
  benchmark_fase3_surv_survey_id: number;

  @Column({ type: 'integer' })
  benchmark_fase4_surv_survey_id: number;

  @Column({ type: 'integer' })
  flag_benchmark: number;

  @Column({ type: 'integer' })
  neutral_sentiment: number;

  @AfterLoad()
  updateToJSON() {
    this.company_properties = parseJSON(this.company_properties);
  }
}
