import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import type { ModWccsMetaFieldsEntity } from './mod-wccs-meta-fields.entity';

@Entity('mod_wccs_import_maps')
export class ModWccsImportMapEntity {
  @PrimaryColumn({ type: 'character varying', length: 50 })
  map_id: string;

  @Column({ type: 'character varying', length: 50 })
  template_id: string;

  @Column({ type: 'character varying', length: 50 })
  metafield_id: string;

  @ManyToOne('ModWccsMetaFieldsEntity')
  @JoinColumn({ name: 'metafield_id' })
  metafield: ModWccsMetaFieldsEntity;

  @Column({ type: 'character varying', length: 50 })
  map_destination_field: string;

  @Column({ type: 'character varying', length: 50 })
  map_destination_title: string;

  @Column({ type: 'character varying', length: 50 })
  map_source_field: string;

  @Column({ type: 'integer' })
  map_key: number;

  @Column({ type: 'integer' })
  map_order: number;

  @Column({ type: 'integer' })
  map_ismandatory: number;
}
