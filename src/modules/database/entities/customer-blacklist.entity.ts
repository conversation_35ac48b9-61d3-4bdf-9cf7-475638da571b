import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'customer_blacklist' })
export class CustomerBlacklistEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  customer_blacklist_id: number;

  @CreateDateColumn({ type: 'timestamp without time zone' })
  date_blacklisted: string;

  @Column({ type: 'varchar' })
  customer_email: string;

  @Column({ type: 'varchar' })
  customer_email_md5: string;

  @Column({ type: 'varchar' })
  phone: string;

  @Column({ type: 'varchar' })
  phone_md5: string;

  @Column({ type: 'varchar' })
  brand: string;

  @Column({ type: 'integer' })
  sec_company_id: number;
}
