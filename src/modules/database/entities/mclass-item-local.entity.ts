import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import type { MClassResponse } from '@entities';

@Entity('mclass_item_local')
export class MClassItemLocal {
  @PrimaryGeneratedColumn({ type: 'integer' })
  mclass_item_local_id: number;

  @Column({ type: 'integer', nullable: true })
  mclass_item_id: number;

  @Column({ type: 'integer', nullable: true })
  sec_company_id: number;

  @Column({ type: 'character varying', length: 300, nullable: true })
  item_label_locale: string;

  @OneToMany('MClassResponse', (mr: MClassResponse) => mr.item_local)
  responses: MClassResponse[];
}
