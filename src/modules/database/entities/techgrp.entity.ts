import { AfterLoad, Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('techgrp')
export class TechgrpEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  techgrp_id: number;

  @Column({ type: 'character varying', length: 50 })
  techgrp_group: string;

  @Column({ type: 'character varying', length: 50 })
  techgrp_techn: string;

  @Column({ type: 'character varying', length: 100 })
  techgrp_name: string;

  @Column({ type: 'character varying', length: 254 })
  techgrp_email: string;

  @Column({ type: 'character varying', length: 50 })
  techgrp_login: string;

  @Column({ type: 'character varying', length: 50 })
  techgrp_function: string;

  @Column({ type: 'character varying', length: 50 })
  techgrp_function2: string;

  @Column({ type: 'character varying', length: 20 })
  techgrp_dep: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'character varying', length: 100 })
  techgrp_firstname: string;

  @Column({ type: 'character varying', length: 100 })
  techgrp_type: string;

  fullname: string;

  @AfterLoad()
  setFullname() {
    let fullname;
    if (!this.techgrp_firstname?.length) fullname = this.techgrp_firstname;
    fullname = this.techgrp_firstname + ' ' + this.techgrp_name;

    this.fullname = fullname;
  }
}
