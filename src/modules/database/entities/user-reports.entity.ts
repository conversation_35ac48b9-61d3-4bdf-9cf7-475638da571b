import { ReportTemplate } from '@/shared/enums';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'user_reports' })
export class UserReportsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  user_report_id: number;

  @Column({ type: 'varchar' })
  report_name: string;

  @Column({ type: 'varchar' })
  short_description: string;

  @Column({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'integer' })
  report_template: ReportTemplate;

  @Column({ type: 'text' })
  filters: string;

  @Column({ type: 'integer' })
  created_by: number;

  @Column({ type: 'timestamp without time zone' })
  created_on: Date;

  @Column({ type: 'integer' })
  modified_by: number;

  @Column({ type: 'timestamp without time zone' })
  modified_on: Date;

  @Column({ type: 'text' })
  report_properties: string;
}
