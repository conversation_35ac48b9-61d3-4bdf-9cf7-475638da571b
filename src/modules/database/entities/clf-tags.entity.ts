import type { ClfCallEntity, TagsEntity } from '@entities';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('clf_tags')
export class ClfTagsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  clf_tag_id: number;

  @Column({ type: 'integer', nullable: true })
  clf_call_id: number;

  @Column({ type: 'integer', nullable: true })
  tag_id: number;

  @Column({ type: 'timestamp', nullable: true })
  created_on: Date;

  @Column({ type: 'integer', nullable: true })
  created_by: number;

  @OneToOne('TagsEntity')
  @JoinColumn({ name: 'tag_id', referencedColumnName: 'tag_id' })
  tag: TagsEntity;

  @ManyToOne('ClfCallEntity', (clf_call: ClfCallEntity) => clf_call.tags)
  @JoinColumn({
    name: 'clf_call_id',
    referencedColumnName: 'clf_call_id',
  })
  clf_call: ClfCallEntity;
}
