import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('report_layout_tags')
export class ReportLayoutTagsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  rl_tag_id: number;

  @Column({ type: 'integer', nullable: true })
  rl_id: number;

  @Column({ type: 'integer', nullable: true })
  tag_id: number;

  @Column({ type: 'timestamp', nullable: true })
  created_on: Date;

  @Column({ type: 'integer', nullable: true })
  created_by: number;
}
