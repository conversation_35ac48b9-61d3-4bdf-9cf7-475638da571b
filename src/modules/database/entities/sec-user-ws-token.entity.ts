import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import type { CompanyEntity, UserCompanyEntity, UserEntity } from '@entities';

@Entity({ name: 'sec_user_ws_token' })
export class UserTokenEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  sec_user_ws_token_id: number;

  @Column({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @OneToOne('UserEntity')
  @JoinColumn({
    referencedColumnName: 'sec_user_id',
    name: 'sec_user_id',
  })
  user: UserEntity;

  @OneToOne('CompanyEntity')
  @JoinColumn({
    referencedColumnName: 'sec_company_id',
    name: 'sec_company_id',
  })
  company: CompanyEntity;

  @OneToOne('UserCompanyEntity')
  @JoinColumn([
    { referencedColumnName: 'sec_user_id', name: 'sec_user_id' },
    { referencedColumnName: 'sec_company_id', name: 'sec_company_id' },
  ])
  user_company: UserCompanyEntity;

  @Column({ type: 'varchar' })
  token: string;

  @Column({ type: 'timestamp' })
  creation_date: Date;

  @Column({ type: 'timestamp' })
  expiry_date: Date;

  @Column({ type: 'integer' })
  token_privilege: number;

  @Column({ type: 'varchar' })
  hash: string;

  @Column({ type: 'varchar' })
  ip_range: string;
}
