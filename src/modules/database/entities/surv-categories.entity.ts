import {
  Column,
  Entity,
  OneToMany,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm';
import type { SurvCategoriesLocalEntity } from '@entities';

@Entity({ name: 'surv_categories' })
export class SurvCategoriesEntity {
  @PrimaryColumn({ type: 'integer' })
  category_id: number;

  @Column({ type: 'varchar', length: '100' })
  category: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @OneToMany(
    'SurvCategoriesLocalEntity',
    (category: SurvCategoriesLocalEntity) => category.survCategory,
  )
  categoriesLocal: SurvCategoriesLocalEntity;
}
