import type {
  ClfTagsEntity,
  EmailCustomerEntity,
  SurveyAnswerEntity,
  UserEntity,
} from '@entities';
import {
  Column,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('clf_category_lists')
export class ClfCategoryListEntity {
  @PrimaryColumn({ type: 'integer' })
  item_id: number;

  @Column({ type: 'character varying', length: '50', nullable: true })
  list: string;

  @Column({ type: 'character varying', length: '100', nullable: true })
  description: string;

  @Column({ type: 'character varying', length: '254', nullable: true })
  email: string;

  @Column({ type: 'integer', nullable: true })
  ordernumber: number;

  @Column({ type: 'integer', nullable: true })
  template_id: number;

  @Column({ type: 'integer', nullable: true })
  sec_company_id: number;
}
