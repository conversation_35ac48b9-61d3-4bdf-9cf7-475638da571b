import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'allowed_filters' })
export class AllowedFilterEntity {
  @PrimaryColumn({ type: 'integer' })
  allowed_filter_id: number;

  @Column({ type: 'character varying', length: 100 })
  filter_name: string;

  @Column({ type: 'character varying', length: 500 })
  short_description: string;

  @Column({ type: 'character varying', length: 100 })
  filter_image: string;

  @Column({ type: 'character varying', length: 1000 })
  filter_options: string;

  @Column({ type: 'character varying', length: 50 })
  filter_key: string;

  @Column({ type: 'integer' })
  company_type: number;

  @Column({ type: 'integer' })
  is_default: number;

  @Column({ type: 'integer' })
  sort_order: number;

  @Column({ type: 'integer' })
  filter_type: number;

  @Column({ type: 'json' })
  allowed_templates: any;

  @Column({ type: 'integer', default: 0 })
  is_required: number;
}
