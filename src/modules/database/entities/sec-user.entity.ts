import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import type { UserCompanyEntity } from '@entities';

@Entity({ name: 'sec_users' })
export class UserEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  sec_user_id: number;

  @Column({ type: 'varchar' })
  username: string;

  @Column({ type: 'varchar' })
  user_password: string;

  @Column({ type: 'varchar' })
  email: string;

  @Column({ type: 'varchar' })
  modifier: string;

  @Column({ type: 'varchar' })
  creator: string;

  @Column({ type: 'timestamp without time zone', nullable: true })
  modification_date: string;

  // Error in DB naming, should be creation_date. TODO migration
  @Column({ type: 'timestamp without time zone', nullable: true })
  cration_date: string;

  @Column({ type: 'integer' })
  sec_group: number;

  @Column({ type: 'varchar' })
  activation_pwd: string;

  @Column({ type: 'timestamp without time zone', nullable: true })
  activation_date: string;

  @Column({ type: 'integer' })
  failed_count: number;

  @Column({ type: 'timestamp without time zone', nullable: true })
  failed_last_date: string;

  @Column({ type: 'varchar' })
  firstname: string;

  @Column({ type: 'integer' })
  islocked: number;

  @Column({ type: 'timestamp without time zone', nullable: true })
  last_api_contact: string;

  @Column({ type: 'varchar' })
  phone: string;

  @Column({ type: 'character varying', length: '10' })
  phone_code_country: string;

  @Column({ type: 'varchar' })
  job_title: string;

  @Column({ type: 'timestamp without time zone', nullable: true })
  date_of_birth: string;

  @Column({ type: 'timestamp without time zone', nullable: true })
  work_anniversary: string;

  @Column({ type: 'bytea' })
  avatar: any;

  @Column({ type: 'varchar' })
  avatar_type: string;

  @Column({ type: 'varchar' })
  salutation: string;

  @Column({ type: 'integer' })
  new_news_articles: number;

  @Column({ type: 'integer' })
  has_opened_settings: number;

  @Column({ type: 'varchar', length: 5 })
  language: string;

  @OneToMany('UserCompanyEntity', (uc: UserCompanyEntity) => uc.user)
  @JoinColumn({
    referencedColumnName: 'sec_user_id',
    name: 'sec_user_id',
  })
  company: UserCompanyEntity[];

  get companyKeys(): string[] {
    return this.company?.reduce((prev, item) => {
      prev.push(...item.keys.map((key) => key.name));
      return prev;
    }, []);
  }

  get fullname(): string {
    return `${this.firstname || ''} ${this.username || ''}`.trim();
  }
}
