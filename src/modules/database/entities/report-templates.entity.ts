import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('report_templates')
export class ReportTemplatesEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  report_template_id: number;

  @Column({ type: 'character varying', length: 100 })
  report_name: string;

  @Column({ type: 'character varying', length: 300 })
  short_description: string;

  @Column({ type: 'character varying', length: 50 })
  report_image: string;

  @Column({ type: 'integer' })
  company_type: number;

  @Column({ type: 'character varying', length: 200 })
  allowed_charttypes: string;

  @Column({ type: 'character varying', length: 20 })
  preferred_charttype: string;

  @Column({ type: 'character varying', length: 200 })
  restricted_filters: string;

  @Column({ type: 'integer' })
  for_parent_company: number;
}
