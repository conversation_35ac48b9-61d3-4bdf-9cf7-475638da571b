import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('clf_call_note')
export class ClfCallNoteEntity {
  @PrimaryColumn({ type: 'integer' })
  clf_call_note_id: number;

  @Column({ type: 'integer', nullable: true })
  clf_call_id: number;

  @Column({ type: 'integer', nullable: true })
  sec_company_id: number;

  @Column({ type: 'integer', nullable: true })
  note_type: number;

  @Column({ type: 'text', nullable: true })
  note: string;

  @Column({ type: 'timestamp', nullable: true })
  creation_date: Date;

  @Column({ type: 'integer', nullable: true })
  creator: number;

  @Column({ type: 'integer', nullable: true })
  assigned_to: number;
}
