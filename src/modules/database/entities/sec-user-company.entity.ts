import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';

import type {
  CompanyEntity,
  SecGroupEntity,
  SecKey,
  UserEntity,
} from '@entities';
@Entity({ name: 'sec_user_company' })
export class UserCompanyEntity {
  @PrimaryColumn({ type: 'integer', primary: false })
  sec_company_id: number;

  @OneToOne('CompanyEntity')
  @JoinColumn({
    referencedColumnName: 'sec_company_id',
    name: 'sec_company_id',
  })
  sec_company: CompanyEntity;

  @OneToOne('SecGroupEntity')
  @JoinColumn({
    referencedColumnName: 'group_id',
    name: 'sec_group',
  })
  group: SecGroupEntity;

  @ManyToOne('UserEntity', (user: UserEntity) => user.company)
  @JoinColumn({
    referencedColumnName: 'sec_user_id',
    name: 'sec_user_id',
  })
  user: UserEntity;

  @ManyToMany('SecKey')
  @JoinTable({
    name: 'sec_user_right',
    joinColumn: {
      name: 'group_id',
      referencedColumnName: 'sec_group',
    },
    inverseJoinColumn: {
      name: 'key_id',
      referencedColumnName: 'key_id',
    },
  })
  keys: SecKey[];

  @Column({ type: 'integer' })
  sec_user_id: number;
  @Column({ type: 'integer' })
  sec_group: number;
  @Column({ type: 'varchar' })
  modifier: string;
  @Column({ type: 'varchar' })
  creator: string;
  @Column({ type: 'timestamp with time zone' })
  modification_date: Date;
  @Column({ type: 'timestamp with time zone' })
  creation_date: Date;
}
