import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('answertable')
export class AnswertableEntity {
  @PrimaryGeneratedColumn('uuid')
  answertable_uuid: string;

  @Column('uuid')
  answertable_tableid: string;

  @Column({ type: 'integer' })
  created_by: number;

  @Column({ type: 'timestamp without time zone' })
  created_on: Date;

  @Column({ type: 'integer' })
  modified_by: number;

  @Column({ type: 'timestamp without time zone' })
  modified_on: Date;

  @Column({ type: 'character varying', length: 100 })
  large_01: string;

  @Column({ type: 'character varying', length: 100 })
  large_02: string;

  @Column({ type: 'character varying', length: 100 })
  large_03: string;

  @Column({ type: 'character varying', length: 50 })
  medium_01: string;

  @Column({ type: 'character varying', length: 50 })
  medium_02: string;

  @Column({ type: 'character varying', length: 50 })
  medium_03: string;

  @Column({ type: 'character varying', length: 50 })
  medium_04: string;

  @Column({ type: 'character varying', length: 20 })
  small_01: string;

  @Column({ type: 'character varying', length: 20 })
  small_02: string;

  @Column({ type: 'character varying', length: 20 })
  small_03: string;

  @Column({ type: 'character varying', length: 50 })
  unique_id: string;

  @Column({ type: 'integer' })
  sec_company_id: number;
}
