import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import type { SecGroupEntity, SurvSurveyEntity } from '@entities';
@Entity({ name: 'surv_survey_groups' })
export class SurveyGroupsEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  surv_survey_group_id: number;

  @Column({ type: 'integer' })
  surv_survey_id: number;

  @Column({ type: 'integer' })
  group_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @ManyToOne('SurvSurveyEntity', (survey: SurvSurveyEntity) => survey.groups)
  @JoinColumn({
    referencedColumnName: 'surv_survey_id',
    name: 'surv_survey_id',
  })
  survey: SurvSurveyEntity;

  @OneToOne('SecGroupEntity')
  @JoinColumn({
    referencedColumnName: 'group_id',
    name: 'group_id',
  })
  group: SecGroupEntity;
}
