import type {
  ContactEntity,
  DatatableEntity,
  EmailTemplateEntity,
  ProjectStageEntity,
  ProjectsEntity,
  SurvSurveyEntity,
  SurveyAnswerEntity,
  TechgrpEntity,
} from '@entities';
import {
  Column,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('email_customer')
export class EmailCustomerEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  email_customer_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'character varying' })
  xml_email: string;

  @Column({ type: 'character varying' })
  xml_customername: string;

  @Column({ type: 'character varying' })
  xml_salutation: string;

  @Column({ type: 'character varying' })
  xml_initials: string;

  @Column({ type: 'character varying' })
  xml_brand: string;

  @Column({ type: 'character varying' })
  xml_rasnumber: string;

  @Column({ type: 'character varying' })
  xml_technician: string;

  @Column({ type: 'character varying' })
  xml_internal_external: string;

  @Column({ type: 'character varying' })
  creator: string;

  @Column({ type: 'character varying' })
  modifier: string;

  @Column({ type: 'timestamp with time zone' })
  creation_date: Date;

  @Column({ type: 'timestamp without time zone' })
  modification_date: Date;

  @Column({ type: 'timestamp without time zone' })
  send_date: Date;

  @Column({ type: 'timestamp without time zone' })
  import_date: Date;

  @Column({ type: 'integer' })
  survey_done: number;

  @Column({ type: 'timestamp without time zone' })
  remainder_send_date: Date;

  @Column({ type: 'integer' })
  send_count: Date;

  @Column({ type: 'timestamp without time zone' })
  xml_readydate: Date;

  @Column({ type: 'integer' })
  email_template_id: number;

  @Column({ type: 'character varying' })
  xml_techgrp: string;

  @Column({ type: 'character varying' })
  xml_acceptance_by: string;

  @Column({ type: 'integer' })
  thankmail_send: number;

  @Column({ type: 'timestamp without time zone' })
  xml_visittime_end: Date;

  @Column({ type: 'character varying' })
  xml_wvb: string;

  @Column({ type: 'character varying' })
  xml_planning: string;

  @Column({ type: 'integer' })
  xml_department: number;

  @Column({ type: 'integer' })
  surv_survey_id: number;

  @Column({ type: 'integer' })
  blacklisted: number;

  @Column({ type: 'timestamp without time zone' })
  thankmail_senddate: Date;

  @Column({ type: 'character varying' })
  xml_address: string;

  @Column({ type: 'character varying' })
  xml_fulladdress: string;

  @Column({ type: 'character varying' })
  xml_city: string;

  @Column({ type: 'character varying' })
  xml_refnr: string;

  @Column({ type: 'character varying' })
  xml_language: string;

  @Column({ type: 'character varying' })
  xml_region: string;

  @Column({ type: 'character varying' })
  xml_enumber: string;

  @Column({ type: 'character varying' })
  xml_prodarea: string;

  @Column({ type: 'character varying' })
  xml_proddivision: string;

  @Column({ type: 'character varying' })
  xml_proddate: string;

  @Column({ type: 'timestamp without time zone' })
  xml_purchasedate: string;

  @Column({ type: 'integer' })
  xml_operationtime: number;

  @Column({ type: 'integer' })
  xml_nrvisits: number;

  @Column({ type: 'character varying' })
  xml_originservice: string;

  @Column({ type: 'timestamp without time zone' })
  xml_acceptancedate: Date;

  @Column({ type: 'timestamp without time zone' })
  xml_lastvisitdate: Date;

  @Column({ type: 'integer' })
  xml_waitingtime: number;

  @Column({ type: 'integer' })
  xml_outagetime: number;

  @Column({ type: 'numeric' })
  xml_grossinvoice: number;

  @Column({ type: 'character varying' })
  xml_payment: string;

  @Column({ type: 'integer' })
  flag_sentthreshold: number;

  @Column({ type: 'character varying' })
  xml_causecomplaint: string;

  @Column({ type: 'character varying' })
  xml_solution: string;

  @Column({ type: 'character varying' })
  xml_complain_agent: string;

  @Column({ type: 'timestamp without time zone' })
  xml_closuredate: Date;

  @Column({ type: 'character varying' })
  xml_phone1: string;

  @Column({ type: 'character varying' })
  xml_phone2: string;

  @Column({ type: 'integer' })
  xml_projectid: number;

  @Column({ type: 'character varying' })
  xml_projectnm: string;

  @Column({ type: 'double precision' })
  xml_amount: string;

  @Column({ type: 'character varying' })
  xml_presentitem: string;

  @Column({ type: 'character varying' })
  xml_employee: string;

  @Column({ type: 'character varying' })
  xml_wa: string;

  @Column({ type: 'character varying' })
  xml_custcreditnr: string;

  @Column({ type: 'character varying' })
  xml_logisticpartner: string;

  @Column({ type: 'character varying' })
  xml_companyname: string;

  @Column({ type: 'character varying' })
  xml_projectnr: string;

  @Column({ type: 'character varying' })
  xml_projectdiv: string;

  @Column({ type: 'character varying' })
  xml_fkz: string;

  @Column({ type: 'character varying' })
  xml_la: string;

  @Column({ type: 'integer' })
  error_code: number;

  @Column({ type: 'character varying' })
  surv_key: string;

  @Column({ type: 'character varying' })
  xml_callcode: string;

  @Column({ type: 'character varying' })
  xml_functioncode: string;

  @Column({ type: 'character varying' })
  xml_rc: string;

  @Column({ type: 'character varying' })
  xml_fc: string;

  @Column({ type: 'character varying' })
  xml_accountindic: string;

  @Column({ type: 'character varying' })
  xml_fident: string;

  @Column({ type: 'double precision' })
  xml_consecnr: number;

  @Column({ type: 'character varying' })
  xml_timezone: string;

  @Column({ type: 'character varying' })
  xml_currency: string;

  @Column({ type: 'character varying' })
  xml_techgrouptext: string;

  @Column({ type: 'character varying' })
  xml_areatext: string;

  @Column({ type: 'character varying' })
  sms_id: string;

  @Column({ type: 'character varying' })
  xml_cp: string;

  @Column({ type: 'character varying' })
  xml_sv: string;

  @Column({ type: 'double precision' })
  flag_ext_source: number;

  @Column({ type: 'character varying' })
  ext_id: string;

  @Column({ type: 'character varying' })
  email_template_i18n_id: string;

  @Column({ type: 'integer' })
  sms_content_type: number;

  @Column({ type: 'integer' })
  sms_status: number;

  @Column({ type: 'character varying' })
  short_url: string;

  @Column({ type: 'character varying' })
  xml_dealer: string;

  @Column({ type: 'character varying' })
  xml_dealer_pcode: string;

  @Column({ type: 'integer' })
  survey_accessed: number;

  @Column({ type: 'character varying' })
  xml_hybrisid: string;

  @Column({ type: 'integer' })
  is_anonymous: number;

  sent_status: number;

  @OneToOne('SurvSurveyEntity')
  @JoinColumn({
    name: 'surv_survey_id',
    referencedColumnName: 'surv_survey_id',
  })
  survey: SurvSurveyEntity;

  @OneToOne('EmailTemplateEntity')
  @JoinColumn({
    name: 'email_template_id',
    referencedColumnName: 'email_template_id',
  })
  email_template: EmailTemplateEntity;

  @OneToOne('SurveyAnswerEntity')
  @JoinColumn({
    name: 'email_customer_id',
    referencedColumnName: 'email_customer_id',
  })
  survey_answer: SurveyAnswerEntity;

  @OneToOne('DatatableEntity')
  @JoinColumn({
    name: 'xml_region',
    referencedColumnName: 'map_key',
  })
  region: DatatableEntity;

  @OneToOne('DatatableEntity')
  @JoinColumn({
    name: 'xml_payment',
    referencedColumnName: 'map_key',
  })
  payment: DatatableEntity;

  @OneToOne('DatatableEntity')
  @JoinColumn({
    name: 'xml_prodarea',
    referencedColumnName: 'map_key',
  })
  prodarea: DatatableEntity;

  @OneToOne('DatatableEntity')
  @JoinColumn({
    name: 'xml_proddivision',
    referencedColumnName: 'map_key',
  })
  proddivision: DatatableEntity;

  @OneToOne('TechgrpEntity')
  @JoinColumn({
    name: 'xml_planning',
    referencedColumnName: 'techgrp_techn',
  })
  planning: TechgrpEntity;

  @OneToOne('TechgrpEntity')
  @JoinColumn({
    name: 'xml_wvb',
    referencedColumnName: 'techgrp_techn',
  })
  prepep: TechgrpEntity;

  @OneToOne('TechgrpEntity')
  @JoinColumn({
    name: 'xml_techgrp',
    referencedColumnName: 'techgrp_techn',
  })
  techgrp: TechgrpEntity;

  @OneToOne('TechgrpEntity')
  @JoinColumn({
    name: 'xml_acceptance_by',
    referencedColumnName: 'techgrp_techn',
  })
  callcenter: TechgrpEntity;

  @OneToOne('TechgrpEntity')
  @JoinColumn({
    name: 'xml_custcreditnr',
    referencedColumnName: 'techgrp_techn',
  })
  service_partner: TechgrpEntity;

  @OneToOne('TechgrpEntity')
  @JoinColumn({
    name: 'xml_technician',
    referencedColumnName: 'techgrp_techn',
  })
  engineer: TechgrpEntity;

  @OneToOne('TechgrpEntity')
  @JoinColumn({
    name: 'xml_dealer',
    referencedColumnName: 'techgrp_techn',
  })
  dealer: TechgrpEntity;

  @OneToOne('TechgrpEntity')
  @JoinColumn({
    name: 'xml_employee',
    referencedColumnName: 'techgrp_techn',
  })
  counselor: TechgrpEntity;

  @OneToOne('ContactEntity')
  @JoinColumn({
    name: 'xml_internal_external',
    referencedColumnName: 'project_id',
  })
  contact: ProjectsEntity;

  @OneToOne('ProjectsEntity')
  @JoinColumn({
    name: 'xml_internal_external',
    referencedColumnName: 'project_id',
  })
  project: ProjectsEntity;

  @OneToOne('ProjectStageEntity')
  @JoinColumn({
    name: 'email_customer_id',
    referencedColumnName: 'email_customer_id',
  })
  project_stage: ProjectStageEntity;

  @OneToOne('SurvSurveyEntity')
  @JoinColumn({
    name: 'xml_department',
    referencedColumnName: 'department',
  })
  survey_department: SurvSurveyEntity;
}
