import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import type { CompanyEntity, SurveyGroupsEntity } from '@entities';

@Entity({ name: 'surv_survey' })
export class SurvSurveyEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  surv_survey_id: number;

  @Column({ type: 'varchar' })
  creator: string;

  @Column({ type: 'varchar' })
  modifier: string;

  @Column({ type: 'timestamp without time zone' })
  modification_date: Date;

  @CreateDateColumn({ type: 'timestamp without time zone' })
  creation_date: Date;

  @Column({ type: 'varchar', length: 200 })
  name: string;

  @Column({ type: 'integer' })
  email_template_id: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'varchar', length: 1000 })
  description: string;

  @Column({ type: 'integer' })
  max_question_page: number;

  @Column({ type: 'varchar', length: 1000 })
  description_end: string;

  @Column({ type: 'varchar', length: 1000 })
  desc_surv_already_done: string;

  @Column({ type: 'integer' })
  department: number;

  @Column({ type: 'integer' })
  dashboard: number;

  @Column({ type: 'varchar', length: 5 })
  survey_language: string;

  @Column({ type: 'integer' })
  on_dashboard2: number;

  @Column({ type: 'integer' })
  is_clf: number;

  @Column({ type: 'integer' })
  nps_question: number;

  @Column({ type: 'integer' })
  clf_question1: number;

  @Column({ type: 'integer' })
  clf_question2: number;

  @Column({ type: 'integer' })
  callback_question: number;

  @Column({ type: 'varchar', length: 200 })
  internal_name: string;

  @Column({ type: 'integer' })
  category: number;

  @Column({ type: 'varchar', length: 200 })
  test_email: string;

  @Column({ type: 'integer' })
  test_mode: number;

  @Column({ type: 'varchar', length: 20 })
  test_phone_nr: string;

  @Column({ type: 'double precision' })
  surv_weighting_factor: number;

  @Column({ type: 'integer' })
  surv_target: number;

  @Column({ type: 'varchar', length: 100 })
  touchpoint_name: string;

  @Column({ type: 'bytea' })
  surv_uuid: any;

  @Column({ type: 'integer' })
  is_anonymous: number;

  @Column({ type: 'integer' })
  bsh_category_id: number;

  @OneToMany('SurveyGroupsEntity', (sg: SurveyGroupsEntity) => sg.survey)
  @JoinColumn({
    name: 'surv_survey_id',
    referencedColumnName: 'surv_survey_id',
  })
  groups: SurveyGroupsEntity[];

  @OneToOne('CompanyEntity')
  @JoinColumn({
    name: 'sec_company_id',
    referencedColumnName: 'sec_company_id',
  })
  company: CompanyEntity;
}
