import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('hot_news')
export class HotNewsEntity {
  @PrimaryGeneratedColumn('uuid')
  hot_news_id: string;

  @Column({ type: 'character varying', length: 50 })
  hot_news_title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'integer' })
  is_published: number;

  @Column({ type: 'character varying', length: 2000 })
  hot_news_link: string;

  @Column({ type: 'integer' })
  company_type: number;

  @Column({ type: 'integer' })
  creator: number;

  @Column({ type: 'time without time zone' })
  creation_date: Date;

  @Column({ type: 'time without time zone' })
  published_date: Date;

  @Column({ type: 'integer' })
  modified_by: number;

  @Column({ type: 'time without time zone' })
  modified_on: Date;
}
