import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('bsh_mapping')
export class DatatableEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  map_id: number;

  @Column({ type: 'character varying', length: 12 })
  map_key: string;

  @Column({ type: 'character varying', length: 100 })
  map_description: string;

  @Column({ type: 'integer' })
  isactive: number;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'character varying', length: 100 })
  map_field: string;
}
