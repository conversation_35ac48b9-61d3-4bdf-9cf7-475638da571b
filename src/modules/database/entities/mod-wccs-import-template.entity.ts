import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import type { ModWccsMetaTablesEntity } from '@entities';

@Entity('mod_wccs_import_templates')
export class ModWccsImportTemplateEntity {
  @PrimaryColumn({ type: 'character varying', length: 50 })
  template_id: string;

  @Column({ type: 'character varying', length: 50 })
  metatable_id: string;

  @ManyToOne('ModWccsMetaTablesEntity')
  @JoinColumn({ name: 'metatable_id' })
  metatable: ModWccsMetaTablesEntity;

  @Column({ type: 'character varying', length: 200 })
  template_name: string;

  @Column({ type: 'integer' })
  template_firstrow: number;

  @Column({ type: 'character varying', length: 5 })
  template_file_separator: string;

  @Column({ type: 'character varying', length: 5 })
  template_date_separator: string;

  @Column({ type: 'character varying', length: 5 })
  template_time_separator: string;

  @Column({ type: 'character varying', length: 5 })
  template_file_type: 'txt' | 'csv' | 'xlsx' | 'json';

  @Column({ type: 'character varying', length: 500 })
  template_last_imported: string;

  @Column({ type: 'integer' })
  sec_company_id: number;

  @Column({ type: 'character varying', length: 5 })
  template_date_format: string;

  @Column({ type: 'json', nullable: true })
  mapped_fields: MappedField[];
}

class MappedFieldSource {
  id: string;

  name: string;
}

class MappedFieldTarget {
  id: string;

  name: string;

  title: string;

  type: string;

  limit: number;

  unique: boolean;

  mandatory: boolean;
}

class MappedField {
  id: string;

  source: MappedFieldSource;

  target: MappedFieldTarget;

  isKeyField: boolean;

  autoMatched: boolean;

  hasValidLimit: boolean;

  confirmMapping: boolean;

  maxLengthFound: number;

  ignoreMapping: boolean;

  collapsed: boolean;

  mapped_value: string;
}
