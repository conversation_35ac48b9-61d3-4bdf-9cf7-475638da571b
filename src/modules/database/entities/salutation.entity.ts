import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'salutations' })
export class SalutationEntity {
  @PrimaryGeneratedColumn({ type: 'integer' })
  salutation_id: number;

  @Column({ type: 'character varying', length: 50, nullable: true })
  salutation_greet: string;
  @Column({ type: 'character varying', length: 5, nullable: true })
  salutation_language: string;

  @Column({ type: 'integer' })
  creator: number;

  @Column({ type: 'timestamp' })
  creation_date: Date;

  @Column({ type: 'integer' })
  modified_by: number;

  @Column({ type: 'timestamp' })
  modified_on: Date;

  @Column({ type: 'integer' })
  sec_company_id: number;
}
