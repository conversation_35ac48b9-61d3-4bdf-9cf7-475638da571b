import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import * as moment from 'moment';

import { ClassificationApiService } from '../classification/classification-api/classification-api.service';
import { FeedbackReportService } from '../feedback/feedbac-report/feedback-report.service';

import { SurveyAnswerEntity } from '@entities';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { CompanyType } from '@/shared/enums';
import { queryConvert } from '@/shared/utils';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class TestimonialsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private readonly classificationApiService: ClassificationApiService,
    private readonly feedbackReportService: FeedbackReportService,
    @InjectRepository(SurveyAnswerEntity)
    private readonly saRepo: Repository<SurveyAnswerEntity>,
  ) {}

  async getTestimonials(query: any = {}, params: any = null) {
    if (params) {
      params.deepdive = true;
    }

    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let onlyCLF = false;
    let limit = 20;

    if (params?.limit) limit = params.limit;

    if (query.ignoreDates == 'true') params.ignoreDates = true;
    if (query.onlyCLF == 'true') onlyCLF = true;
    params.ignorePublish = true;

    this.logger.debug(`getTestimonials`, { params, query, company });

    let sql = '';
    let sqlArgs: any = {};
    let filterSql = await this.classificationApiService.getFilterData(
      params,
      sqlArgs,
    );

    filterSql = filterSql.replace(' AND rs.mclass_response_id IS NULL', '');
    let fields = `a.creation_date, a.surv_surveyanswer_id, a.nps_value AS nps, pos.return_value AS strengths, neg.return_value AS improvements, s.internal_name AS survey, a.sec_company_id, clf.clf_call_id, clf.assigned_to `;
    let joins = `LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id 
    LEFT JOIN surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND s.clf_question2 = pos.surv_surveyquestion_id 
    LEFT JOIN surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND s.clf_question1 = neg.surv_surveyquestion_id 
    LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id 
    LEFT JOIN clf_call clf on a.email_customer_id = clf.email_customer_id 
    LEFT JOIN clf_tags ct ON clf.clf_call_id = ct.clf_call_id`;
    let where = ``;
    let sort = ``;

    // if(params.employees || params.dates){

    // }

    if (company.type == CompanyType.BOUW || company.type == CompanyType.SPORT) {
      fields += `, p.project_name`;
      joins += ` LEFT JOIN projects p ON ec.xml_internal_external = p.project_id`;
    }

    if (params?.mclass_items?.length || params?.label_sentiment?.[0]?.id > 0) {
      joins +=
        ' LEFT JOIN mclass_response mr ON a.surv_surveyanswer_id = mr.answer_id ';
    }
    if (params?.callback_question) {
      joins +=
        ' LEFT JOIN surv_surveyansweritem callback on a.surv_surveyanswer_id = callback.surv_surveyanswer_id and s.callback_question = callback.surv_surveyquestion_id ';
    }
    if (params?.contact_center?.length > 0) {
      joins += ` LEFT JOIN techgrp techDOB ON ec.xml_acceptance_by = techDOB.techgrp_techn AND techDOB.techgrp_type = 'xml_acceptance_by' `;
    }
    if (params?.dispatcher?.length > 0) {
      joins += ` LEFT JOIN techgrp techDispatch ON ec.xml_planning = techDispatch.techgrp_techn AND techDispatch.techgrp_type = 'xml_planning'`;
    }
    if (params?.preprepper?.length > 0) {
      joins += ` LEFT JOIN techgrp techPreppep ON ec.xml_wvb = techPreppep.techgrp_techn AND techPreppep.techgrp_type = 'xml_wvb' `;
    }
    if (params?.service_partner?.length > 0) {
      joins += `  LEFT JOIN techgrp techServicePartner ON ec.xml_custcreditnr = techServicePartner.techgrp_techn AND techServicePartner.techgrp_type = 'xml_custcreditnr' `;
    }
    if (params?.counselor?.length > 0) {
      joins += ` LEFT JOIN techgrp techCounselor ON ec.xml_employee = techCounselor.techgrp_techn AND techCounselor.techgrp_type = 'xml_employee' `;
    }
    if (params?.engineer?.length > 0) {
      joins += ` LEFT JOIN techgrp tech ON ec.xml_technician = tech.techgrp_techn AND tech.techgrp_type = 'xml_technician'`;
    }

    if (params?.dealer?.length > 0) {
      joins += ` LEFT JOIN techgrp techDealer ON ec.xml_dealer = techDealer.techgrp_techn AND techDealer.techgrp_type = 'xml_dealer' `;
    }

    if (params?.callback_selector) {
      switch (params.callback_selector) {
        case 'random':
          sort = 'RANDOM()';
          fields += `, RANDOM()`;
          where += ` AND (clf.status = 1 OR clf.status is null)`;
          break;
        case 'longest':
          sort = ' a.comments_length desc';
          fields += ', a.comments_length ';
          where +=
            ' AND a.comments_length is not null AND (clf.status = 1 OR clf.status is null) ';
          break;
        case 'no_feedback':
          sort = ' a.comments_length asc';
          fields += ', a.comments_length ';
          where +=
            ' AND ( a.comments_length is null or a.comments_length = 0 ) AND (clf.status = 1 OR clf.status is null) ';
          break;
        default:
          break;
      }
    }
    let helperSql = '';

    if (params?.callback_question?.[0]?.id == '1') {
      helperSql += ` AND callback.return_value='1' `;
    } else if (params?.callback_question?.[0]?.id == '2') {
      helperSql += ` AND callback.return_value='0' `;
    }

    if (params?.label_sentiment?.[0]?.id > 0) {
      sqlArgs.label_sentiment = params?.label_sentiment[0].id;
      helperSql += ` AND  mr.istype = :label_sentiment `;
    }

    sql = `
    SELECT DISTINCT ${fields} FROM surv_surveyanswer a
    ${joins}
    WHERE 1 = 1 
    ${where}
    ${
      query.from
        ? ` AND a.creation_date > '${moment(query.from).toISOString()}'`
        : ''
    }
    ${onlyCLF ? ` AND clf.clf_call_id is not null` : ''}
    ${helperSql}
     ${filterSql}
    `;

    if (params) {
      let aids = await this.feedbackReportService.hasAnswerTableFilters(params);
      for (let item of aids) {
        sqlArgs.atq = item.surv_surveyquestion_id.map((i) => +i);
        sqlArgs.ati = params[item.name].filter((i) => i?.id).map((i) => i.id);
        sql += ` AND EXISTS (SELECT FROM jsonb_array_elements(answers) WHERE value ->>'type' = '14' AND (value->>'question_id')::int = ANY(:atq::int[]) AND value->>'value' = ANY(:ati) ) `;
      }
    }

    if (!params) {
      sql += `AND a.surv_surveyanswer_id IN ( 
        SELECT a2.surv_surveyanswer_id 
        FROM surv_surveyanswer a2 
        ${
          company.type == CompanyType.BOUW || company.type == CompanyType.SPORT
            ? `LEFT JOIN email_customer ec2 ON a2.email_customer_id = ec2.email_customer_id `
            : ''
        }
        WHERE 1=1 AND sec_company_id =${company.id}
        ORDER BY a2.creation_date DESC 
        LIMIT ${+limit}
        ${query.page ? ` OFFSET ${query.page * limit || 0}` : ''}
        )
        `;
    }

    if (!sort?.length) {
      sort = ` a.creation_date desc`;
    }

    sql += `  ORDER BY ${sort}`;

    if (params) {
      sql += ` LIMIT ${+limit}`;
      if (query.page) {
        sql += ` OFFSET ${query.page * limit}`;
      }
    }

    let [q, args] = queryConvert(sql, sqlArgs);
    this.logger.debug('getTestimonials query', { query: q, sqlArgs: args });

    let result = await this.saRepo.query(q, args);
    let response = result?.map((item) => {
      return {
        ...item,
        ...(item.assigned_to !== null && {
          assigned_to: { id: item.assigned_to },
        }),
      };
    });

    this.logger.debug('getTestimonials result', { response });
    return response;
  }
}
