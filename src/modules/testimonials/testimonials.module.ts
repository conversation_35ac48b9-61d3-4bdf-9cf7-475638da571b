import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClassificationApiModule } from '../classification/classification-api/classification-api.module';
import { SurveyAnswerEntity } from '../database/entities/surv-surveyanswer.entity';
import { FeedbackReportModule } from '../feedback/feedbac-report/feedback-report.module';
import { TestimonialsController } from './testimonials.controller';
import { TestimonialsService } from './testimonials.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('tertimonials'),
    TypeOrmModule.forFeature([SurveyAnswerEntity]),
    ClassificationApiModule,
    FeedbackReportModule,
  ],
  controllers: [TestimonialsController],
  providers: [TestimonialsService],
})
export class TestimonialsModule {}
