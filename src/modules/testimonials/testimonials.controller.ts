import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiQuery, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { TestimonialsService } from './testimonials.service';

@Controller({
  path: '/testimonials',
  version: '2',
})
@ApiTags('Testimonials')
@ApiBearerAuth()
export class TestimonialsController {
  constructor(private readonly testimonialsService: TestimonialsService) {}

  @Get()
  @UseGuards(AuthGuard)
  @ApiQuery({ required: false })
  getTestimonials(@Query() query: any) {
    return this.testimonialsService.getTestimonials(query, null);
  }

  @Post()
  @UseGuards(AuthGuard)
  @ApiBody({ type: Object, required: false })
  @ApiQuery({ required: false })
  getTestimonialsParams(@Query() query: any, @Body() body: any) {
    return this.testimonialsService.getTestimonials(query, body);
  }
}
