import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import * as moment from 'moment';

import { UserCompanyEntity, UserEntity } from '@entities';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import { UpsertUserBodyDetails, GetCompanyUsersServiceDto } from './dto';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class UsersService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserCompanyEntity)
    private readonly userCompanyRepo: Repository<UserCompanyEntity>,
  ) {}

  async getCompanyUsers(params: GetCompanyUsersServiceDto) {
    let take = 10;

    let { all, name } = params;
    const { user, company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(
      `getCompanyUsers for user ${user.id} company ${company.id}`,
      { all, name },
    );

    if (all == true) {
      take = undefined;
    }
    if (name?.length > 0) {
      name = name.replace(/[^a-zA-Z0-9_-]/gi, '');
    }
    let isSuperUser = await this.isSuperUser(user.id);

    const sql = `
    SELECT u.sec_user_id, u.firstname, u.username
    FROM sec_users u
    LEFT JOIN sec_user_company suc  ON u.sec_user_id = suc.sec_user_id
    WHERE suc.sec_company_id = ${company.id}
    ${
      isSuperUser
        ? `AND u.sec_user_id not in (SELECT sec_user_id FROM sec_user_company WHERE sec_company_id=1)`
        : ''
    }
    ${
      name?.length > 0
        ? `AND (u.firstname ilike '%${name}%' OR u.username ilike '%${name}%')`
        : ''
    } 
    ORDER BY sec_user_id DESC
    ${take ? ` LIMIT ${take}` : ''}
    `;

    this.logger.debug(`getCompanyUsers query`, { sql });
    let list = await this.userRepo.query(sql);

    this.logger.debug(`getCompanyUsers result`, { list });

    return list.map((item) => {
      return {
        id: item.sec_user_id,
        name: `${item.firstname || ''} ${item.username}`.trim(),
      };
    });
  }

  async getDetailedUsers() {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug('getDetailedUsers', { user: user.id, company });
    let isSuperUser = await this.isSuperUser(user.id);
    let addSql =
      isSuperUser &&
      `AND u.sec_user_id not in (SELECT sec_user_id FROM sec_user_company WHERE sec_company_id=1)`;

    let list = await this.userRepo.query(
      `
      SELECT u.sec_user_id, u.firstname, u.username AS lastname, u.email, u.salutation, u.phone,
      u.job_title, u.date_of_birth, u.work_anniversary, suc.sec_group
      FROM sec_users u
      INNER JOIN sec_user_company suc ON u.sec_group = suc.sec_group
      WHERE suc.sec_company_id=$1 ${addSql || ''}
      `,
      [company.id],
    );

    this.logger.debug('getDetailedUsers result', { list });

    return list;
  }

  async getDetailedUserByEmail(email) {
    this.logger.debug(`getDetailedUserByEmail ${email}`);

    let user = await this.userRepo.findOne({
      where: { email: ILike(email) },
      select: [
        'sec_user_id',
        'firstname',
        'username',
        'email',
        'salutation',
        'phone',
        'job_title',
        'date_of_birth',
        'work_anniversary',
      ],
    });

    let result = {
      ...user,
      lastname: user.username,
      username: undefined,
    };

    this.logger.debug(`getDetailedUserByEmail result for ${email}`, { result });

    return result;
  }

  async isSuperUser(sec_user_id: number) {
    let total = await this.userCompanyRepo.count({
      where: { sec_user_id, sec_company_id: 1 },
    });
    if (+total > 0) return true;
    else return false;
  }

  getAvatar(image, type) {
    return image && type
      ? type + ',' + Buffer.from(image).toString('base64')
      : '';
  }

  getUser(sec_user_id: number) {
    return this.userRepo
      .createQueryBuilder()
      .where('sec_user_id =:sec_user_id', { sec_user_id })
      .getOne();
  }

  public getFullName(user: UserEntity) {
    if (!user.firstname?.length) return user.username;
    return user.firstname + ' ' + user.username;
  }

  async createUser(params: UpsertUserBodyDetails) {
    const {
      date_of_birth,
      email,
      firstname,
      group,
      job_title,
      lastname,
      phone,
      phone_code_country,
      salutation,
      work_anniversary,
    } = params;

    this.logger.debug(`createUser`, { params });

    try {
      let resp = await this.userRepo
        .createQueryBuilder()
        .insert()
        .values({
          firstname,
          username: lastname,
          email,
          date_of_birth: date_of_birth?.toString()?.length
            ? moment(date_of_birth).toISOString()
            : null,
          sec_group: group,
          job_title,
          phone,
          phone_code_country,
          salutation,
          work_anniversary: work_anniversary?.toString()?.length
            ? moment(work_anniversary).toISOString()
            : null,
          cration_date: moment().toISOString(),
        })
        .returning('sec_user_id')
        .execute();

      let user_id = resp.generatedMaps[0]?.sec_user_id;

      let result = { id: user_id, message: 'Record Saved' };
      this.logger.debug(`createUser result`, { result });
      return result;
    } catch (e) {
      this.logger.error(`createUser error`, { error: e });
      delete e.query;
      throw new HttpException(e, 400);
    }
  }
  async updateUser(params: UpsertUserBodyDetails) {
    const {
      date_of_birth,
      email,
      firstname,
      group,
      job_title,
      lastname,
      phone,
      phone_code_country,
      salutation,
      sec_user_id,
      work_anniversary,
    } = params;
    try {
      this.logger.error(`updateUser`, { params });
      await this.userRepo
        .createQueryBuilder()
        .update({
          firstname,
          username: lastname,
          email,
          date_of_birth: moment(date_of_birth).toISOString(),
          sec_group: group,
          job_title,
          phone,
          phone_code_country,
          salutation,
          work_anniversary: moment(work_anniversary).toISOString(),
        })
        .where('sec_user_id =:sec_user_id', { sec_user_id })
        .execute();
      let result = {
        id: sec_user_id,
        message: 'Record Updated',
      };

      this.logger.debug('updateUser result', { result });
      return result;
    } catch (error) {
      this.logger.debug('updateUser error', { error });
    }
  }

  async getUserPasswords(id: number) {
    let sql = `SELECT p.pass FROM sec_passwords p WHERE p.sec_user_id = $1 `;
    let list = await this.userRepo.query(sql, [id]);
    return list;
  }
  async updatePassword(sec_user_id: number, hash: string, validDays: number) {
    await this.userRepo.query(
      `UPDATE sec_passwords SET is_active =0 WHERE sec_user_id =$1`,
      [sec_user_id],
    );
    let validTill = new Date();
    validTill.setDate(validTill.getDate() + validDays);
    await this.userRepo.query(
      `INSERT INTO sec_passwords (sec_user_id, is_active, valid_till, pass) VALUES($1,$2,$3,$4)`,
      [sec_user_id, 1, validTill, hash],
    );
    return true;
  }
}
