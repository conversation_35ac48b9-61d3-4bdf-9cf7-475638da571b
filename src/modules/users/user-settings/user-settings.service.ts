import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import * as moment from 'moment';

import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import { UpsertUserSettingsBodyDto, UpsertUserSettingsQueryDto } from './dto';

import { UserSettingsEntity } from '@entities';
import { parseJSON } from '@/shared/utils';
import { USER_SETTINGS_TYPES, ClsProperty } from '@/config/contents';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

export class UserSettingsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(UserSettingsEntity)
    private readonly userSettingsEntity: Repository<UserSettingsEntity>,
  ) {}
  async getUserSettings({
    user_setting_id = null,
    user_setting_type = null,
  } = {}) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(
      `getUserSettings id ${user_setting_id} type ${user_setting_type}`,
      { company },
    );
    let where: FindOptionsWhere<UserSettingsEntity> = {};
    if (user_setting_id) {
      where.user_setting_id = user_setting_id;
      where.company_id = company.id;
    }
    if (user_setting_type) {
      where.user_setting_type = user_setting_type;
      where.company_id = company.id;
    }
    let result = await this.userSettingsEntity.find({ where });

    let list = result.map((item) => {
      return {
        user_setting_id: item.user_setting_id,
        name: item.name,
        setting_object: this.formatSettings(
          parseJSON(item.setting_object) || {},
        ),
      };
    });

    this.logger.debug(`getUserSettings result`, { list });
    return list;
  }
  async getUserSettingsByUserId(user_id, company_id, user_setting_type) {
    const { company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(
      `getUserSettingsByUserId user ${user_id} company ${company_id}`,
      { company },
    );

    let where: FindOptionsWhere<UserSettingsEntity> = {
      sec_user_id: user_id,
      company_id: company_id || company.id,
      user_setting_type: user_setting_type,
    };
    let item = await this.userSettingsEntity.findOne({ where });

    let result = !item
      ? null
      : {
          user_setting_id: item.user_setting_id,
          name: item.name,
          setting_object: this.formatSettings(JSON.parse(item.setting_object)),
        };
    this.logger.debug(
      `getUserSettingsByUserId result for user ${user_id} company ${company_id}`,
      { result },
    );

    return result;
  }

  async upsertUserSettings(
    query: UpsertUserSettingsQueryDto,
    body: UpsertUserSettingsBodyDto,
  ) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let { user_setting_id = null, user_setting_type = null } = query;
    let where: FindOptionsWhere<UserSettingsEntity> = {};
    let item;
    if (user_setting_id) {
      where.user_setting_id = user_setting_id;
      item = await this.userSettingsEntity.findOne({ where });
    } else {
      item = await this.userSettingsEntity.create({
        sec_user_id: user.id,
      });
      item.user_setting_type = USER_SETTINGS_TYPES[query.user_setting_type];
      item.company_id = company.id;
    }
    if (!item) return [];

    item.name = body.name;
    item.setting_object = JSON.stringify(body.setting_object);

    if (!item.user_setting_id) {
      let resp = await this.userSettingsEntity
        .createQueryBuilder()
        .insert()
        .values(item)
        .returning('user_setting_id')
        .execute();
      user_setting_id = resp.raw[0].user_setting_id || 0;
    } else {
      await this.userSettingsEntity.update(
        {
          user_setting_id,
        },
        item,
      );
    }
    return await this.getUserSettings({
      user_setting_id,
    });
  }

  async deleteUserSettings(user_setting_id: number) {
    const { user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let item = await this.userSettingsEntity.findOne({
      where: {
        user_setting_id,
      },
    });
    if (!item || item.sec_user_id != user.id)
      return {
        status: 1001,
        message: `Could not find user_setting_id ${user_setting_id}`,
      };

    await this.userSettingsEntity.delete({
      user_setting_id,
    });
    return {
      status: 1000,
      message: `Record with ${user_setting_id} deleted`,
    };
  }

  formatSettings(obj: any) {
    //for now only the date is replaced, but other values can follow, I can't change the object so I create a new one

    for (let [key, val] of Object.entries(obj)) {
      if (val instanceof Date)
        obj[key] = moment(val).format('dd-MM-yyyy HH:mm');
      else if (val instanceof Object) obj[key] = this.formatSettings(val);
    }
    return obj;
  }
}
