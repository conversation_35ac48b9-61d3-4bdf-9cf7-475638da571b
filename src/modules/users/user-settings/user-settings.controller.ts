import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiProperty,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from '@/shared/guards/auth.guard';

import { AuthorizedRequest } from '../../auth/interfaces/authorized-request.interface';

import { UserSettingsService } from './user-settings.service';
import {
  GetUserSettingsDto,
  UpsertUserSettingsBodyDto,
  UpsertUserSettingsQueryDto,
} from './dto';
import { USER_SETTINGS_TYPES } from '@/config/contents';

@ApiTags('User Settings')
@ApiBearerAuth()
@Controller({
  path: 'user_settings',
  version: '2',
})
export class UserSettingsController {
  constructor(private userSettingsService: UserSettingsService) {}

  @Get()
  @UseGuards(AuthGuard)
  @ApiResponse({
    description: 'Get User Settings',
  })
  async getUserSettings(@Query() query: GetUserSettingsDto) {
    const { user_setting_id, user_setting_type } = query;

    if (!user_setting_id && !user_setting_type) {
      throw new HttpException(
        'you need to pass user_setting_id or user_setting_type',
        HttpStatus.BAD_REQUEST,
      );
    }
    let result = await this.userSettingsService.getUserSettings({
      user_setting_id,
      user_setting_type: USER_SETTINGS_TYPES[user_setting_type],
    });
    if (user_setting_id && result.length == 0)
      return {
        status: 1001,
        message: 'Could not find user_setting_id ' + user_setting_id,
      };
    if (user_setting_type && result.length == 0)
      return { status: 1001, message: 'No filters available' };

    return {
      user_settings: result,
    };
  }

  @Post()
  @UseGuards(AuthGuard)
  async upsertUserSettings(
    @Query() query: UpsertUserSettingsQueryDto,
    @Body() body: UpsertUserSettingsBodyDto,
    @Req() { user }: AuthorizedRequest,
  ) {
    const { user_setting_id } = query;
    let result = await this.userSettingsService.upsertUserSettings(query, body);

    if (user_setting_id && result.length == 0)
      return {
        status: 1001,
        message: 'Could not find user_setting_id ' + user_setting_id,
      };
    return {
      user_settings: result,
      message: `Save with id ${result[0].user_setting_id}`,
    };
  }

  @Delete()
  @UseGuards(AuthGuard)
  @ApiProperty({
    name: 'user_setting_id',
    required: true,
  })
  deleteUserSettings(@Query('user_setting_id') user_setting_id: number) {
    return this.userSettingsService.deleteUserSettings(user_setting_id);
  }
}
