import { Type } from 'class-transformer';
import { IsIn, <PERSON>N<PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import {
  USER_SETTINGS_TYPES,
  USER_SETTINGS_TYPE_KEYS,
} from '@/config/contents';

export class GetUserSettingsDto {
  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  user_setting_id: number;

  @Type()
  @IsIn(USER_SETTINGS_TYPE_KEYS)
  @ApiProperty({
    required: false,
    enum: USER_SETTINGS_TYPE_KEYS,
  })
  @IsOptional()
  user_setting_type: keyof typeof USER_SETTINGS_TYPES;
}
