import {
  USER_SETTINGS_TYPES,
  USER_SETTINGS_TYPE_KEYS,
} from '@/config/contents';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsIn, IsNumber, IsOptional, IsString } from 'class-validator';

export class UpsertUserSettingsQueryDto {
  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  user_setting_id: number;

  @Type()
  @IsIn(USER_SETTINGS_TYPE_KEYS)
  @ApiProperty({
    required: false,
    enum: USER_SETTINGS_TYPE_KEYS,
  })
  @IsOptional()
  user_setting_type: keyof typeof USER_SETTINGS_TYPES;
}

export class UpsertUserSettingsBodyDto {
  @Type()
  @IsString()
  @ApiProperty({
    required: true,
  })
  name: string;

  @Type()
  @ApiProperty({
    required: true,
    type: Object,
  })
  setting_object: Object;
}
