import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { UpsertItemParams } from './interfaces/reports-service-params';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';

import { ReportLayoutEntity, UserReportsEntity } from '@entities';
import { parseJSON } from '@/shared/utils';
import { ClsProperty } from '@/config/contents';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class UserReportsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(UserReportsEntity)
    private readonly userReportsEntity: Repository<UserReportsEntity>,
    @InjectRepository(ReportLayoutEntity)
    private readonly reportLayoutEntity: Repository<ReportLayoutEntity>,
  ) {}

  async upsertItem(params: UpsertItemParams) {
    const {
      filters,
      name,
      report_id,
      saveExtraOptions,
      sec_user_id,
      template_id,
    } = params;

    this.logger.debug(`upsertItem`, { params });

    let userReport = await this.userReportsEntity.findOne({
      where: { user_report_id: report_id || 0 },
    });
    let data: Partial<UserReportsEntity> = {};

    if (name) data.report_name = name;
    if (template_id) data.report_template = template_id;
    if (filters) data.filters = filters;
    if (sec_user_id) data.sec_user_id = sec_user_id;
    if (params.sec_user_id) data.modified_by = params.sec_user_id;

    if (report_id) data.user_report_id = report_id;
    else {
      data.created_by = params.sec_user_id;
      data.created_on = new Date();
    }

    if (saveExtraOptions && Object.keys(saveExtraOptions).length > 0) {
      if (saveExtraOptions.type) {
        let reportProps = parseJSON(userReport?.report_properties) || {};
        if (saveExtraOptions.type == 'datalabels')
          reportProps.datalabels = { ...saveExtraOptions, root: undefined };

        reportProps.root = saveExtraOptions.root;
        data.report_properties = JSON.stringify(reportProps);
      } else {
        data.report_properties = JSON.stringify({
          legends: saveExtraOptions.legends || [],
          datalabels: saveExtraOptions.datalabels || [],
          root: saveExtraOptions.root || {},
        });
      }
    }

    let result = await this.userReportsEntity.save(data);
    this.logger.debug('upsertItem result', { result });
    if (report_id && saveExtraOptions?.type == 'root') {
      return {
        message: `Extra options succesfull saved for user report: ${report_id}`,
      };
    }

    return { id: result.user_report_id, ...result, user_report_id: null };
  }

  async deleteUserReport(id: string) {
    const { user } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug(`deleteUserReport id ${id}`);

    let userReport = await this.userReportsEntity
      .createQueryBuilder()
      .where('user_report_id =:id', { id })
      .getOne();
    if (!userReport) throw new HttpException('Not Found', HttpStatus.NOT_FOUND);
    else if (userReport.sec_user_id != user.id) {
      throw new HttpException('Access Deined', HttpStatus.FORBIDDEN);
    }
    let reportLayouts: Partial<ReportLayoutEntity>[];
    let sqlCode = `select report_layout_id,layout_data from report_layout, json_array_elements(layout_data::json) items where items ->> 'id' ='${id}'`;
    reportLayouts = await this.reportLayoutEntity.query(sqlCode);

    for (let { report_layout_id, layout_data } of reportLayouts) {
      let layoutData = parseJSON(layout_data) || [];
      layoutData = layoutData?.filter((i) => i.id != id);
      await this.reportLayoutEntity
        .createQueryBuilder()
        .update({
          layout_data: JSON.stringify(layoutData),
        })
        .where('report_layout_id =:report_layout_id', { report_layout_id })
        .execute();
    }

    let result = await this.userReportsEntity
      .createQueryBuilder()
      .delete()
      .whereInIds([id])
      .execute();

    this.logger.debug('deleteUserReport result', { result });
    return {
      message: 'Successfully deleted',
    };
  }

  async copyReport(id: string) {
    const { user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let report = await this.userReportsEntity
      .createQueryBuilder()
      .where('user_report_id =:id', { id })
      .getOne();

    let newReport = await this.userReportsEntity
      .createQueryBuilder()
      .insert()
      .values({
        ...report,
        user_report_id: undefined,
        created_by: user.id,
        created_on: new Date(),
      })
      .returning('user_report_id')
      .execute();
    return {
      id: newReport.generatedMaps[0]?.user_report_id,
      message: 'Chart copied.',
    };
  }

  public getExtraOptions(extraProperties, data) {
    let k;
    if (!extraProperties) return data;

    if (extraProperties.root) {
      for (var rootLabel in extraProperties.root) {
        data[rootLabel] = extraProperties.root[rootLabel];
      }
    }

    for (var j in extraProperties) {
      if (extraProperties[j] && extraProperties[j].datasets) {
        for (var i = 0; i < extraProperties[j].datasets.length; i++) {
          /** @type {{dataset:Number}} */
          var oDatasetProp = extraProperties[j].datasets[i];
          if (data && data.dataSets && data.dataSets[oDatasetProp.dataset]) {
            if (j == 'datasets') {
              for (k = 0; k < extraProperties[j].saveProperties.length; k++) {
                data.dataSets[oDatasetProp.dataset][
                  extraProperties[j].saveProperties[k]
                ] = oDatasetProp[j][extraProperties[j].saveProperties[k]];
              }
            } else {
              if (!data.dataSets[oDatasetProp.dataset][j]) {
                data.dataSets[oDatasetProp.dataset][j] = {};
              }
              for (k = 0; k < extraProperties[j].saveProperties.length; k++) {
                data.dataSets[oDatasetProp.dataset][j][
                  extraProperties[j].saveProperties[k]
                ] = oDatasetProp[j][extraProperties[j].saveProperties[k]];
              }
            }
          }
        }
      }
    }
    return data;
  }
}
