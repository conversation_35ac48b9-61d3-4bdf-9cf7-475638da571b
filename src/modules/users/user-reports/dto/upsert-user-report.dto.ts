import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON><PERSON>rray,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class UserReportPropertiesDto {
  @IsArray()
  @ApiProperty()
  legends: [];

  @IsArray()
  @ApiProperty()
  datalabels: [];

  @IsArray()
  @ApiProperty({ type: Object })
  @ValidateNested()
  root: {};

  type?: string = null;

  saveProperties?: string[] = null;
}

export class UpsertUserReportQueryDto {
  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({ required: false })
  report_id: number;
}

export class UpsertUserReportDto {
  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({ required: false, nullable: true })
  report_id: number;

  @Type()
  @IsOptional()
  @IsString()
  @ApiProperty()
  name: string;

  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({ required: false })
  template_id: number;

  @ValidateNested()
  @ApiProperty({
    type: Object,
    description: 'filters',
    required: true,
  })
  filters: any;

  @ValidateNested()
  @ApiProperty({
    type: Object,
    description: 'extra options',
  })
  saveExtraOptions: UserReportPropertiesDto;
}
