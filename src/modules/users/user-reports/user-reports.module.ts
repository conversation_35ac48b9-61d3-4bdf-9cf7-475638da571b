import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReportLayoutEntity } from '../../database/entities/report-layout.entity';
import { UserReportsEntity } from '../../database/entities/user-reports.entity';

import {
  CopyUserReportConroller,
  UserReportsConroller,
} from './user-reports.controller';
import { UserReportsService } from './user-reports.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserReportsEntity, ReportLayoutEntity]),
    ModuleLoggerModule.register('user-reports'),
  ],
  controllers: [UserReportsConroller, CopyUserReportConroller],
  providers: [UserReportsService],
  exports: [UserReportsService],
})
export class UserReportsModule {}
