import {
  Body,
  Controller,
  Delete,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiB<PERSON>erAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../../auth/interfaces/authorized-request.interface';
import {
  UpsertUserReportDto,
  UpsertUserReportQueryDto,
} from './dto/upsert-user-report.dto';
import { UserReportsService } from './user-reports.service';

@ApiTags('UserReports')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'user_reports',
})
export class UserReportsConroller {
  constructor(private userReportsService: UserReportsService) {}

  @UseGuards(AuthGuard)
  @Post()
  @ApiOkResponse({
    status: HttpStatus.OK,
  })
  upsertData(
    @Req() { user }: AuthorizedRequest,
    @Query() query: UpsertUserReportQueryDto,
    @Body() body: UpsertUserReportDto,
  ) {
    const sec_user_id = user.id;
    const report_id = query.report_id > 0 ? query.report_id : body.report_id;
    if (body.filters && !body?.filters?.chart_model)
      throw new HttpException(
        { message: 'Invalid chart/filter information' },
        400,
      );
    return this.userReportsService.upsertItem({
      ...body,
      ...query,
      report_id,
      sec_user_id,
    });
  }

  @Delete(':id')
  @UseGuards(AuthGuard)
  deleteUserReport(@Param('id') id: string) {
    return this.userReportsService.deleteUserReport(id);
  }
}

@ApiTags('UserReports')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'copy_user_report',
})
export class CopyUserReportConroller {
  constructor(private userReportsService: UserReportsService) {}

  @UseGuards(AuthGuard)
  @Post(':id')
  @ApiOkResponse({
    status: HttpStatus.OK,
  })
  copyReport(@Param('id') id: string, @Req() { user }: AuthorizedRequest) {
    return this.userReportsService.copyReport(id);
  }
}
