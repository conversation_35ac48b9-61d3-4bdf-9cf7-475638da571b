import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { GetCompanyUsersQueryDto } from './dto/get-company-users.dto';
import { UpsertUserBodyDto } from './dto/post-user.dto';
import { UsersService } from './users.service';
import { GetSecUsersQueryDto } from './dto/get-sec-users.dto';
import { AdminUsersRegionsService } from '../admin/users/users-regions.service';

@ApiBearerAuth()
@ApiTags('Users')
@Controller({
  path: 'users',
  version: '2',
})
export class UsersController {
  constructor(
    private userService: UsersService,
    private adminUserRegionService: AdminUsersRegionsService,
  ) {}

  @ApiTags('Users')
  @Get()
  @ApiOkResponse({
    description: 'Company users',
  })
  @UseGuards(AuthGuard)
  async getCompanyUsers(@Query() query: GetCompanyUsersQueryDto) {
    const { all, name } = query;
    let users = await this.userService.getCompanyUsers({
      name,
      all,
    });
    return {
      users,
    };
  }

  @Post()
  @UseGuards(AuthGuard)
  async createUser(@Body() body: UpsertUserBodyDto) {
    let createdUser = await this.userService.createUser(body.user);
    if (createdUser?.id) {
      await this.adminUserRegionService.addRegion({
        user_id: createdUser.id,
        group_id: body.user.group,
      });
    }
  }

  @Post(':id')
  @UseGuards(AuthGuard)
  async updateUser(@Body() body: UpsertUserBodyDto, @Param('id') id: number) {
    body.user.sec_user_id = id;
    return this.userService.updateUser(body.user);
  }
}

@ApiBearerAuth()
@ApiTags('Users')
@Controller({
  path: 'sec_users',
  version: '2',
})
export class SecUsersController {
  constructor(private usersService: UsersService) {}

  @Get()
  @UseGuards(AuthGuard)
  async getUsers(@Query() query: GetSecUsersQueryDto) {
    if (query.email?.length > 0) {
      let data = await this.usersService.getDetailedUserByEmail(query.email);
      return {
        users: [data],
      };
    }
    let users = await this.usersService.getDetailedUsers();
    return {
      users,
    };
  }
}
