import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsEmail,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export class UserDetailsBaseDto {
  @Type()
  @ApiProperty({ default: 'John' })
  @IsString()
  @MaxLength(50, { context: '50' })
  firstname: string;

  @Type()
  @ApiProperty({ default: 'Doe' })
  @IsString()
  @MaxLength(50, { context: '50' })
  lastname: string;

  @Type()
  @ApiProperty()
  @IsString()
  @IsEmail()
  email: string;

  @Type()
  @ApiProperty({ required: false, default: null })
  @IsOptional()
  @IsString()
  @MaxLength(5, { context: '5' })
  salutation: string;

  @Type()
  @ApiProperty({ required: false, default: null })
  @IsOptional()
  @IsString()
  @MaxLength(50, { context: '50' })
  phone: string;

  @Type()
  @ApiProperty({ required: false, default: null })
  @IsOptional()
  @IsString()
  phone_code_country: string;

  @Type()
  @ApiProperty({ required: false, default: null })
  @IsString()
  @IsOptional()
  @MaxLength(50, { context: '50' })
  job_title: string;

  @Type()
  @ApiProperty({ required: false, default: null })
  @IsDate()
  @IsOptional()
  date_of_birth: Date;

  @Type()
  @ApiProperty({ required: false, default: null })
  @IsDate()
  @IsOptional()
  work_anniversary: Date;
}

export class UpsertUserBodyDetails extends UserDetailsBaseDto {
  @Type()
  @IsNumber()
  @IsOptional()
  sec_user_id?: number = null;

  @Type()
  @ApiProperty()
  @IsNumber()
  group: number;
}
export class UpsertUserBodyDto {
  @Type()
  @ApiProperty()
  user: UpsertUserBodyDetails;
}
