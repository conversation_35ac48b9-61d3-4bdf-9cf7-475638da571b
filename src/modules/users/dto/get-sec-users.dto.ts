import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEmail, IsOptional } from 'class-validator';

export class GetSecUsersQueryDto {
  @Type()
  @IsOptional()
  @IsEmail()
  @ApiProperty({ required: false })
  email?: string = null;
}

export class GetUserPrivilegesFilter {
  sec_keys?: number[];
  names?: string[];
}
export class GetUserPrivilegesParams {
  userId: number;
  companyId: number;
  filter?: GetUserPrivilegesFilter;
}
