import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class GetCompanyUsersQueryDto {
  @Transform(({ value }: any) => {
    return value === 'true' || value === true || value === 1 || value === '1';
  })
  @ApiProperty({ required: false })
  all: Boolean;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  name: string;
}

export class GetCompanyUsersServiceDto {
  name: string;
  all: Boolean;
}
