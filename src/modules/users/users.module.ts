import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AdminUsersModule } from '../admin/users/users.module';
import { SecUsersController, UsersController } from './users.controller';
import { UsersService } from './users.service';

import {
  CompanyEntity,
  UserCompanyEntity,
  UserEntity,
  UserPassword,
} from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('user'),
    TypeOrmModule.forFeature([
      UserEntity,
      UserCompanyEntity,
      CompanyEntity,
      UserPassword,
    ]),
    AdminUsersModule,
  ],
  controllers: [UsersController, SecUsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
