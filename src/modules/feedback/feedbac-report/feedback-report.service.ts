import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SurvSurveyEntity } from '../../database/entities/surv-survey.entity';

@Injectable()
export class FeedbackReportService {
  constructor(
    @InjectRepository(SurvSurveyEntity)
    private readonly sRepo: Repository<SurvSurveyEntity>,
  ) {}

  public async hasAnswerTableFilters(obj: any) {
    let aNames = Object.getOwnPropertyNames(obj).filter(function (s) {
      return /answertable/.test(s);
    });
    let aIDs = aNames.map(function (s) {
      return s.replace('answertable_', '');
    });
    let sql = `SELECT answertable_id, array_agg(surv_surveyquestion_id) as surv_surveyquestion_id FROM surv_surveyquestion WHERE answertable_id = ANY($1) group by answertable_id`;
    let result = await this.sRepo.query(sql, [aIDs]);
    return result.map((item, rowIndex) => {
      return {
        rowIndex: rowIndex + 1,
        surv_surveyquestion_id: item.surv_surveyquestion_id,
        answertable_id: item.answertable_id,
        name: `answertable_${item.answertable_id}`,
      };
    });
  }
}
