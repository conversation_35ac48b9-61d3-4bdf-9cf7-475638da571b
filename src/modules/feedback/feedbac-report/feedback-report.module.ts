import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SurvSurveyEntity } from '../../database/entities/surv-survey.entity';
import { FeedbackReportService } from './feedback-report.service';

@Module({
  imports: [TypeOrmModule.forFeature([SurvSurveyEntity])],
  providers: [FeedbackReportService],
  exports: [FeedbackReportService],
})
export class FeedbackReportModule {}
