import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClfCallEntity } from '../../database/entities/clf-call.entity';
import { EmailCustomerEntity } from '../../database/entities/email-customer-ec.entity';
import { MClassResponse } from '../../database/entities/mclass-response.entity';
import { SurveyAnswerEntity } from '../../database/entities/surv-surveyanswer.entity';
import { FeedbackDetailsController } from './feedback-details.controller';
import { FeedbackDetailsSerivce } from './feedback-details.service';
import { AuthModule } from '../../auth/auth.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SurveyAnswerEntity,
      ClfCallEntity,
      MClassResponse,
      EmailCustomerEntity,
    ]),
    AuthModule,
    ModuleLoggerModule.register('feedback-details'),
  ],
  controllers: [FeedbackDetailsController],
  providers: [FeedbackDetailsSerivce],
  exports: [FeedbackDetailsSerivce],
})
export class FeedbackDetailsModule {}
