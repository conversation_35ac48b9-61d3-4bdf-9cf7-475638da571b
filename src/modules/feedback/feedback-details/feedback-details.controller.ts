import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetFeedbackDetailsQueryDto } from './dto/get-feedback-details.dto';
import { FeedbackDetailsSerivce } from './feedback-details.service';

@Controller({
  path: 'feedback_details',
  version: '2',
})
@ApiBearerAuth()
@ApiTags('Feedback Details')
export class FeedbackDetailsController {
  constructor(private readonly fdService: FeedbackDetailsSerivce) {}

  @Get(':id')
  @UseGuards(AuthGuard)
  getFeedbackDetails(
    @Param('id') id: number,
    @Query() query: GetFeedbackDetailsQueryDto,
  ) {
    return this.fdService.getFeedbackDetails(id, query);
  }
}
