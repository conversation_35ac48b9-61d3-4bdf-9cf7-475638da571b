import { HttpException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { Repository } from 'typeorm';
import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import { CompanyType } from '../../../shared/enums/company-type.enum';
import { ClfStatus } from '../../../shared/enums/clf.enum';
import { ClfCallEntity } from '../../database/entities/clf-call.entity';
import { EmailCustomerEntity } from '../../database/entities/email-customer-ec.entity';
import { MClassResponse } from '../../database/entities/mclass-response.entity';
import { SurveyAnswerEntity } from '../../database/entities/surv-surveyanswer.entity';
import { GetFeedbackDetailsQueryDto } from './dto/get-feedback-details.dto';
import { AuthService } from '@/modules/auth/auth.service';
import { RESTRICTED_CONTENT } from '../../../config/restricted_content';
import { ClsService } from 'nestjs-cls';
import { PrivilegeService } from '@/modules/auth/privilege.service';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { rawQuery } from '@/shared/utils';
import { MclassRecordStatus } from '@/shared/enums';

export class FeedbackDetailsSerivce {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private privilegeService: PrivilegeService,
    @InjectRepository(SurveyAnswerEntity)
    private readonly surveyAnswerRepo: Repository<SurveyAnswerEntity>,
    @InjectRepository(ClfCallEntity)
    private readonly clfCallRepo: Repository<ClfCallEntity>,
    @InjectRepository(EmailCustomerEntity)
    private readonly ecRepo: Repository<EmailCustomerEntity>,
    @InjectRepository(MClassResponse)
    private readonly mrRepo: Repository<MClassResponse>,
  ) {}

  async getFeedbackDetails(id: number, query: GetFeedbackDetailsQueryDto) {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`getFeedbackDetails answer id ${id}`, {
      query,
      user,
      company,
    });
    let privileges = await this.privilegeService.filteredKeys({
      userId: user.id,
      companyId: company.id,
      filter: { names: ['SHOW_CUSTOMER_DATA', 'SHOW_EMPLOYEE_DATA'] },
    });
    const SHOW_CUSTOMER_DATA = privileges.includes('SHOW_CUSTOMER_DATA');
    const SHOW_EMPLOYEE_DATA = privileges.includes('SHOW_EMPLOYEE_DATA');

    if (query.additionalquestions === 'true') {
      return await this.getOtherQuestions(company.id, id, true);
    }

    let item = await this.clfCallRepo
      .createQueryBuilder('c')
      .select(['c.clf_call_id', 'c.clf_summary', 'c.c_answer_id'])
      .addSelect(['tags.tag_id', 'tag.tag_id', 'tag.tag_name'])
      .addSelect(['a.creation_date', 'a.device', 'a.os'])
      .addSelect(['ec'])
      .addSelect(['et.companyname'])
      .addSelect(['s.internal_name'])
      .addSelect(['region.map_description'])
      .addSelect(['planning.techgrp_firstname', 'planning.techgrp_name'])
      .addSelect(['prepep.techgrp_firstname', 'prepep.techgrp_name'])
      .leftJoin('c.tags', 'tags')
      .leftJoin('tags.tag', 'tag')
      .leftJoin('c.survey_answer', 'a')
      .leftJoin('c.email_customer', 'ec')
      .leftJoin('ec.email_template', 'et')
      .leftJoin('ec.survey', 's')
      .leftJoin('ec.region', 'region', `region.sec_company_id = ${company.id}`)
      .leftJoin(
        'ec.planning',
        'planning',
        `planning.sec_company_id = ${company.id}`,
      )
      .leftJoin('ec.prepep', 'prepep', `prepep.sec_company_id = ${company.id}`)
      .where('a.surv_surveyanswer_id =:id', { id })
      .getOne();
    this.logger.debug(`getFeedbackDetails item for answer id ${id}`, { item });

    if (!item) throw new HttpException('Item not found', 404);
    if (!item.email_customer)
      throw new HttpException(
        'Email Customer data is not found for this item',
        404,
      );

    for (let key in item.email_customer) {
      if (!SHOW_CUSTOMER_DATA) {
        if (RESTRICTED_CONTENT.SHOW_CUSTOMER_DATA.includes(key)) {
          delete item.email_customer[key];
        }
      }
      if (!SHOW_EMPLOYEE_DATA) {
        if (RESTRICTED_CONTENT.SHOW_EMPLOYEE_DATA.includes(key)) {
          delete item.email_customer[key];
        }
      }
    }

    let detailsQuery = this.ecRepo
      .createQueryBuilder('ec')
      .where('ec.email_customer_id =:id', {
        id: item.email_customer.email_customer_id,
      });

    if (item.email_customer.xml_region) {
      detailsQuery
        .select('ec.email_customer_id')
        .addSelect(['region.map_description'])
        .leftJoin(
          'ec.region',
          'region',
          `region.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_planning) {
      detailsQuery
        .addSelect(['planning.techgrp_firstname', 'planning.techgrp_name'])
        .leftJoin(
          'ec.planning',
          'planning',
          `planning.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_wvb) {
      detailsQuery
        .addSelect(['prepep.techgrp_firstname', 'prepep.techgrp_name'])
        .leftJoin(
          'ec.prepep',
          'prepep',
          `prepep.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_acceptance_by) {
      detailsQuery
        .addSelect(['callcenter.techgrp_firstname', 'callcenter.techgrp_name'])
        .leftJoin(
          'ec.callcenter',
          'callcenter',
          `callcenter.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_custcreditnr) {
      detailsQuery
        .addSelect([
          'service_partner.techgrp_firstname',
          'service_partner.techgrp_name',
        ])
        .leftJoin(
          'ec.service_partner',
          'service_partner',
          `service_partner.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_technician) {
      detailsQuery
        .addSelect(['engineer.techgrp_firstname', 'engineer.techgrp_name'])
        .leftJoin(
          'ec.engineer',
          'engineer',
          `engineer.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_dealer) {
      detailsQuery
        .addSelect(['dealer.techgrp_firstname', 'dealer.techgrp_name'])
        .leftJoin(
          'ec.dealer',
          'dealer',
          `dealer.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_employee) {
      detailsQuery
        .addSelect(['counselor.techgrp_firstname', 'counselor.techgrp_name'])
        .leftJoin(
          'ec.counselor',
          'counselor',
          `counselor.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_prodarea) {
      detailsQuery
        .addSelect(['prodarea.map_description'])
        .leftJoin(
          'ec.prodarea',
          'prodarea',
          `prodarea.sec_company_id = ${company.id}`,
        );
    }

    if (item.email_customer.xml_internal_external) {
      detailsQuery
        .addSelect(['project.project_name', 'project.project_nr'])
        .leftJoin('ec.project', 'project');

      detailsQuery
        .addSelect(['project_stage.project_stage_id'])
        .leftJoin('ec.project_stage', 'project_stage');
      detailsQuery.leftJoinAndSelect('project_stage.contact', 'contact');
    }

    let details = await detailsQuery.getOne();

    let questions = await this.getOtherQuestions(company.id, id);
    let classification = await this.getClassification(item);

    let result: any = {
      clf_call_id: item.clf_call_id,
      customer_name: item.email_customer.xml_customername,
      language: item.email_customer.xml_language,
      warranty: ['01', '83', '84'].includes(item.email_customer.xml_la),
      status: item.status && {
        id: item.status,
        name: ClfStatus[item.status || 0],
      },
      ris_number: item.email_customer.xml_rasnumber,
      survey_id: item.email_customer.surv_survey_id,
      touchpoint: item.email_customer.survey.internal_name,
      brand: item.email_customer.email_template.companyname,
      response_date: moment(item.survey_answer.creation_date).format(
        'DD-MM-yyyy HH:mm',
      ),
      visit_date: moment(item.email_customer.xml_readydate).format(
        'DD-MM-yyyy HH:mm',
      ),
      e_no: item.email_customer.xml_enumber,
      send_date: moment(item.email_customer.send_date).format(
        'DD-MM-yyyy HH:mm',
      ),
      summary: item.clf_summary || 'No summary',
      tag_arr: item.tags.map((i) => {
        return {
          id: i.tag.tag_id,
          name: i.tag.tag_name,
        };
      }),
      questions,
      classification,
      phone: item.email_customer.xml_phone2,
      email: item.email_customer.xml_email,
      device: item.survey_answer.device,
      os: item.survey_answer.os,
    };

    if (details.region) {
      result.region = details.region.map_description;
    }
    if (details.planning) {
      result.dispatch = details.planning.fullname;
    }
    if (details.prepep) {
      result.prepep = details.prepep.fullname;
    }
    if (details.prepep) {
      result.prepep = details.prepep.fullname;
    }
    if (details.callcenter) {
      result.callcenter = details.callcenter.fullname;
    }
    if (details.service_partner) {
      result.service_partner = details.service_partner.fullname;
    }
    if (details.engineer) {
      result.engineer = details.engineer.fullname;
    }
    if (details.dealer) {
      result.dealer = details.dealer.fullname;
    }

    result.counselor =
      details.counselor?.fullname || item.email_customer.xml_employee;

    result.medium = !item.email_customer.sms_id ? 'Email' : 'SMS';
    this;
    if (details.prodarea) {
      result.prodarea = details.prodarea.map_description;
    }

    if (details.proddivision) {
      result.proddivision = details.proddivision.map_description;
    }

    if (company.type == CompanyType.BSH) {
      result.rasnumber = item.email_customer.xml_rasnumber;
    } else {
      result.project_nr = item.email_customer.project?.project_nr;
      result.project_name = item.email_customer.project?.project_name;
      if (item.email_customer.project_stage?.contact) {
        let contact = item.email_customer.project_stage.contact;
        result = {
          ...result,
          build_no: contact.object_buildnr,
          house_no: contact.object_house_number,
          city: contact.object_city,
          postal_code: contact.object_postal_code,
          address: contact.object_address,
        };
      } else {
        result = {
          ...result,
          build_no: item.email_customer.xml_techgrp,
          city: item.email_customer.xml_city,
          postal_code: item.email_customer.xml_acceptance_by,
          address: item.email_customer.xml_address,
        };
      }
    }
    for (let key in result) {
      if (result[key] === null) delete result[key];
    }

    this.logger.debug(`getFeedbackDetails details query`, {
      sql: rawQuery(detailsQuery),
    });
    this.logger.debug(`getFeedbackDetails result`, { result });

    return result;
  }

  async getOtherQuestions(
    company_id: number,
    answerId,
    additionalquestions = false,
  ) {
    this.logger.debug(
      `getOtherQuestions for answerID ${answerId}, company: ${company_id}`,
      { additionalquestions },
    );
    let result = await this.surveyAnswerRepo.query(
      `
        SELECT p.title , COALESCE(ss.question_shortlabel ,return_value) as return_value  from surv_surveyansweritem ai 
        left JOIN surv_surveyquestion q on ai.surv_surveyquestion_id = q.surv_surveyquestion_id 
        LEFT JOIN surv_surveypart p ON p.surv_surveypart_id=q.surv_surveypart_id 
        LEFT JOIN surv_surveyanswer  a ON ai.surv_surveyanswer_id = a.surv_surveyanswer_id 
        LEFT JOIN  surv_surveyquestionrow ss ON ss.question_returnvalue = return_value and ss.surv_surveyquestion_id = ai.surv_surveyquestion_id 
        LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id 
        WHERE ai.surv_surveyanswer_id  = $1 AND a.sec_company_id = $2
        ${
          additionalquestions
            ? `AND ai.surv_surveyquestion_id NOT IN (s.nps_question, s.clf_question1, s.clf_question2)`
            : ''
        }

        ORDER BY CASE WHEN q.question_isnps IS NULL THEN 0 ELSE q.question_isnps END DESC, p.ordernumber ASC 
        `,
      [answerId, company_id],
    );

    let response = result.map((item) => {
      return {
        question: item.title,
        answer:
          item.return_value.slice(0, 1) == '['
            ? item.return_value.replace(/[\[\]"']/g, '')
            : item.return_value,
      };
    });

    this.logger.debug(
      `getOtherQuestions result for answerID ${answerId}, company: ${company_id}`,
      { response },
    );

    return response;
  }

  async getClassification(clf: ClfCallEntity) {
    const { company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`getClassification`, { clf, company });

    let data = await this.mrRepo
      .createQueryBuilder('mr')
      .select(['il.item_label_locale', 'i.item_label'])
      .addSelect([
        'mr.istype',
        'mr.is_liked',
        'mr.is_potential',
        'mr.is_excluded',
      ])
      .leftJoin('mr.item', 'i')
      .leftJoin('mr.item_local', 'il', `il.sec_company_id = ${company.id}`)
      .where('mr.answer_id =:id', { id: clf.c_answer_id })
      .andWhere(`mr.record_status != ${MclassRecordStatus.ManualRemoved}`)
      .getMany();

    if (data[0]?.is_excluded) data = [data[0]]; // if its excluded just show one item
    let result = data.map((mr) => {
      let key = { 1: 'pos', 2: 'neg', 3: 'neutral' }[mr.istype];
      let result: any = {
        label: mr.is_excluded
          ? 'Excluded'
          : mr.item_local?.item_label_locale || mr.item?.item_label,
        is_liked: mr.is_liked,
        is_potential: mr.is_potential,
        is_excluded: mr.is_excluded,
      };
      if (key && !mr.is_excluded) result[key] = 1;
      return result;
    });

    this.logger.debug(`getClassification result`, { result });

    return result;
  }
}
