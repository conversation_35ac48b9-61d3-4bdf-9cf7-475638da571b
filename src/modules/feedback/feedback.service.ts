import { ClsService } from 'nestjs-cls';
import { HtmlToImageService } from '../lib/html-to-image/html-to-image.service';
import { PublishService } from '../publish/publish.service';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { CompanyType } from '@/shared/enums';
import { HtmlToImageGenerateDto } from '../lib/html-to-image/dto/html-to-image.dto';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { join } from 'path';
import { env } from '@/app.env';
import { existsSync } from 'fs';
import { ProgramSettingsService } from '../common/program-settings/program-settings.service';

@Injectable()
export class FeedbackService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private programSettings: ProgramSettingsService,
    private html2image: HtmlToImageService,
    private publishService: PublishService,
  ) {}

  async shareFeedback(id: number) {
    const { company } = this.cls.get<AuthorizedData>('user');
    let companyType: CompanyType = company.type;

    let settings = await this.programSettings.get(company.id);
    let props = settings?.company_properties;
    let storyWidget = props?.storyWidget || {};

    const feedbackField = (storyWidget.feedback_field || 0).toString();
    const rn = (+storyWidget.respondent_name || 0).toString();
    const scoreType = storyWidget.useCESasScore
      ? 1
      : storyWidget.useCSATasScore
      ? 2
      : 0;

    const dynamicName = `${feedbackField}-${rn}-${scoreType}`;
    const fileName = `share-feedback-${id}-${dynamicName}.jpeg`;
    const folder = 'html-to-image/share-feedback';
    const folderPath = join(env.APP_PATH, `uploads/${folder}`);
    const filePath = join(folderPath, fileName);
    let item = await this.publishService.getData(
      { id },
      {},
      { throwError: true },
    );

    const restrictedContent = !item.answers?.some(
      (answer) => +answer.type === 11 && +answer.value === 1,
    );

    if (existsSync(filePath)) {
      return {
        url: `/uploads/${folder}/${fileName}`,
        restrictedContent,
      };
    }

    this.logger.debug(`shareFeedback ${id}`, { company });

    if (!item) {
      this.logger.debug(`shareFeedback item not found ${id}`, { company });

      throw new NotFoundException('item is not found');
    }

    let bgImage = 'ff-bg.png';
    let logoImage = 'ff-logo.png';
    if (companyType === CompanyType.SPORT) {
      bgImage = 'fs-bg.png';
      logoImage = 'fs-logo.png';
    }

    let params: HtmlToImageGenerateDto = {
      template: 'share-feedback',
      fileName,
      folder: 'share-feedback',
      parameters: {
        title: item.name,
        score: item.score,
        strengths: item.strengths,
        improvements: item.improvements,
        bgUrl: `/assets/html-to-image/images/${bgImage}`,
        logoUrl: `/assets/html-to-image/images/${logoImage}`,
      },
    };

    this.logger.debug(`shareFeedback params for ${id}`, { params });

    let result = await this.html2image.generateImage(params, true);
    if ('url' in result) {
      return {
        ...result,
        restrictedContent,
      };
    }
    return result;
  }
}
