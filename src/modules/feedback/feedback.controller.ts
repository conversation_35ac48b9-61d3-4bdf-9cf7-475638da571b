import { <PERSON>, Get, Param, <PERSON>s, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { FeedbackService } from './feedback.service';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { Response } from 'express';

@Controller({
  path: 'feedback',
  version: '2',
})
@ApiTags('Feedback')
@ApiBearerAuth()
export class FeedbackController {
  constructor(private feedbackService: FeedbackService) {}

  @Get('/share/:id')
  @UseGuards(AuthGuard)
  async shareFeedback(@Param('id') id: number) {
    return this.feedbackService.shareFeedback(id);
  }
}
