import { Modu<PERSON> } from '@nestjs/common';
import { FeedbackController } from './feedback.controller';
import { HtmlToImageModule } from '../lib/html-to-image/html-to-image.module';
import { FeedbackService } from './feedback.service';
import { PublishModule } from '../publish/publish.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';

@Module({
  imports: [
    HtmlToImageModule,
    PublishModule,
    ModuleLoggerModule.register('feedback'),
    ProgramSettingsModule,
  ],
  controllers: [FeedbackController],
  providers: [FeedbackService],
})
export class FeedbackModule {}
