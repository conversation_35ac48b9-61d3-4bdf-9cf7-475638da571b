import {
  BrandEntity,
  SurveyBrandThemeEntity,
  SurvSurveyEntity,
} from '@entities';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { DataSource, FindManyOptions, In, Repository } from 'typeorm';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { adminCompanies } from '@/config/company.config';
import { PaginationDto } from '@/shared/dto/pagination.dto';
import { UpsertItemParams } from '../datatable/interfaces/datatable-service-params';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class BrandsService {
  private readonly brandRepo: Repository<BrandEntity>;
  private readonly brandThemeRepo: Repository<SurveyBrandThemeEntity>;
  private i18n: I18nService;

  constructor(
    private cls: ClsService,
    @InjectDataSource() private dataSource: DataSource,
  ) {
    this.brandRepo = this.dataSource.getRepository(BrandEntity);
    this.brandThemeRepo = this.dataSource.getRepository(SurveyBrandThemeEntity);
  }
  async getBrands() {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let result = await this.brandRepo
      .createQueryBuilder('b')
      .select(['b.brand_id', 'b.brand_name'])
      .where('b.sec_company_id = :cid', { cid: company.id })
      .getMany();

    return result.map((item) => ({
      id: item.brand_id,
      name: item.brand_name,
    }));
  }

  async dataTable(params: PaginationDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let list = await this.brandRepo.find({
      where: {
        sec_company_id: In([...adminCompanies, company.id]),
      },
      select: {
        brand_id: true,
        brand_name: true,
        sec_company_id: true,
        surveys: {
          surv_survey_id: true,
          sec_company_id: true,
        },
      },
      relations: ['surveys'],
      take: params.limit,
      skip: (params.page - 1) * params.limit,
      order: {
        brand_name: 'ASC',
      },
    });

    list.sort((a, b) => {
      const aIsAdmin = adminCompanies.includes(a.sec_company_id);
      const bIsAdmin = adminCompanies.includes(b.sec_company_id);

      if (aIsAdmin !== bIsAdmin) return bIsAdmin ? 1 : -1;
      return a.brand_name.localeCompare(b.brand_name);
    });
    return list.map((item) => {
      return {
        id: item.brand_id,
        name: item.brand_name,
        local_name: null,
        is_global: adminCompanies.includes(item.sec_company_id),
        can_edit: item.sec_company_id === company.id,
        can_delete: item.sec_company_id === company.id,
        touchpoints: item.surveys
          .filter((survey) =>
            [...adminCompanies, company.id].includes(survey.sec_company_id),
          )
          .map((survey) => survey.surv_survey_id),
      };
    });
  }

  async upsert(params: UpsertItemParams) {
    let id = +params.id;
    const { name } = params;
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    if (!name) throw new BadRequestException(`please fill name`);

    let brand =
      id && (await this.brandRepo.findOne({ where: { brand_id: id } }));
    let added = false;
    if (id && !brand) throw new BadRequestException(`brand not found`);
    if (!id) {
      added = true;
      brand = this.brandRepo.create({
        brand_name: name,
        sec_company_id: company.id,
      });
    } else if (
      brand?.sec_company_id == company.id ||
      adminCompanies.includes(company.id)
    ) {
      brand.brand_name = name;
    }

    await this.brandRepo.save(brand);

    return {
      message: added
        ? this.i18n.t('props.brand.added', { args: { name } })
        : this.i18n.t('props.brand.updated', { args: { name } }),
      brand,
    };
  }

  async surveys(brandId?: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let query: FindManyOptions<SurveyBrandThemeEntity> = {
      where: {
        // sec_company_id: In([...new Set(adminCompanies.concat([company.id]))]),
      },
      select: {
        id: true,
        survey: {
          surv_survey_id: true,
          internal_name: true,
          sec_company_id: true,
        },
      },
      relations: ['survey'],
    };

    if (brandId) {
      (query.where as any).brand_id = brandId;
    }

    let list = await this.brandThemeRepo.find(query);

    list = list.filter((item) => {
      return (
        item.survey &&
        [...adminCompanies, company.id].includes(item.survey.sec_company_id)
      );
    });
    const surveyIds = [
      ...new Set(list.map((item) => item.survey.surv_survey_id)),
    ];

    return surveyIds.map((surveyId) => {
      const { survey } = list.find(
        (item) => item.survey.surv_survey_id === surveyId,
      );
      return {
        id: survey.surv_survey_id,
        name: survey.internal_name,
      };
    });
  }

  async deleteItems(ids: any) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    ids = Array.isArray(ids) ? ids : ids.split(',').filter(Boolean);

    let list = await this.brandRepo.find({ where: { brand_id: In(ids) } });

    if (list.length !== ids.length) {
      throw new NotFoundException('props.brand.some_brands_not_found');
    }

    let errors: any = [];

    for (const item of list) {
      if (
        item.sec_company_id != company.id &&
        adminCompanies.includes(company.id)
      ) {
        errors.push({
          message: this.i18n.t('props.brand.brand_not_found'),
          id: item.brand_id,
        });
        continue;
      }

      let surveys = await this.surveys(item.brand_id);

      if (surveys.length > 0) {
        errors.push({
          message: this.i18n.t('props.brand.brand_has_touchpoints'),
          id: item.brand_id,
          touchpoints: surveys.map((survey) => survey.id),
        });
        continue;
      }

      await this.brandRepo.delete({ brand_id: item.brand_id });
    }

    if (errors.length > 0) {
      throw new BadRequestException({ errors, success: ids });
    }

    return {
      success: true,
      message: this.i18n.t('props.brand.brands_deleted'),
      ids,
    };
  }
}
