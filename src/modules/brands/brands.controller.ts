import { Controller, Get } from '@nestjs/common';
import { BrandsService } from './brands.service';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from '@/shared/decorators';

@Controller({
  path: 'brands',
  version: '2',
})
@Auth()
@ApiTags('Brands')
export class BrandsController {
  constructor(private readonly brandsService: BrandsService) {}

  @Get()
  list() {
    return this.brandsService.getBrands();
  }

  @Get('surveys')
  surveys() {
    return this.brandsService.surveys();
  }
}
