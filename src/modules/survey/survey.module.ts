import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserCompanyEntity } from '../database/entities/sec-user-company.entity';
import { SurvSurveyEntity } from '../database/entities/surv-survey.entity';
import { SurveyController } from './survey.controller';
import { SurveyService } from './survey.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserCompanyEntity, SurvSurveyEntity]),
    ModuleLoggerModule.register('survey'),
  ],
  controllers: [SurveyController],
  providers: [SurveyService],
  exports: [SurveyService],
})
export class SurveyModule {}
