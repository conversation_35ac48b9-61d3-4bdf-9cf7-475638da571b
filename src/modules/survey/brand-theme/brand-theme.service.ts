import {
  BrandEntity,
  SurveyBrandThemeEntity,
  SurvSurveyEntity,
  ThemeEntity,
} from '@entities';
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';

import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { UpdateSurveyBrandThemeDto } from './dto/post-brand-theme.dto';
import { SurveyService } from '../survey.service';
import { adminCompanies } from '@/config/company.config';

@Injectable()
export class SurveyBrandThemeService {
  constructor(
    private cls: ClsService,
    private surveyService: SurveyService,
    @InjectRepository(ThemeEntity)
    private readonly themeRepository: Repository<ThemeEntity>,

    @InjectRepository(BrandEntity)
    private readonly brandRepository: Repository<BrandEntity>,
    @InjectRepository(SurveyBrandThemeEntity)
    private readonly surveyBrandThemeRepository: Repository<SurveyBrandThemeEntity>,
  ) {}

  async list(surveyId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let list = await this.surveyBrandThemeRepository.find({
      where: {
        sec_company_id: company.id,
        surv_survey_id: surveyId,
      },
      select: { id: true, brand_id: true, theme_id: true },
    });

    return list;
  }

  // async create(surveyId: number, params: CreateSurveyBrandThemeDto) {
  //   const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);
  //   let promises = [];
  //   promises.push(
  //     this.surveyService.survey({
  //       where: { sec_company_id: company.id, surv_survey_id: surveyId },
  //       select: { surv_survey_id: true },
  //     }),
  //   );

  //   promises.push(
  //     this.themeRepository.exists({
  //       where: {
  //         sec_company_id: In([...adminCompanies, company.id]),
  //         theme_id: params.theme_id,
  //       },
  //     }),
  //   );

  //   promises.push(
  //     this.brandRepository.exists({
  //       where: { sec_company_id: company.id, brand_id: params.brand_id },
  //     }),
  //   );

  //   promises.push(
  //     this.surveyBrandThemeRepository.exists({
  //       where: {
  //         surv_survey_id: surveyId,
  //         theme_id: params.theme_id,
  //         brand_id: params.brand_id,
  //         sec_company_id: company.id,
  //       },
  //     }),
  //   );

  //   const [survey, theme, brand, item] = await Promise.all(promises);
  //   if (!survey) throw new NotFoundException('Survey is not found');
  //   if (!theme) throw new NotFoundException('Theme is not found');
  //   if (!brand) throw new NotFoundException('Brand is not found');
  //   if (item)
  //     throw new ConflictException('This survey brand theme is already exists');

  //   return this.surveyBrandThemeRepository.save(
  //     this.surveyBrandThemeRepository.create({
  //       surv_survey_id: surveyId,
  //       brand_id: params.brand_id,
  //       theme_id: params.theme_id,
  //       sec_company_id: company.id,
  //       created_by: user.id,
  //       created_on: new Date(),
  //       modified_by: user.id,
  //       modified_on: new Date(),
  //     }),
  //   );
  // }

  async update(
    brandThemeId: number,
    surveyId: number,
    params: UpdateSurveyBrandThemeDto,
  ) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let promises = [];

    let brandTheme = await this.surveyBrandThemeRepository.findOne({
      where: { id: brandThemeId },
    });

    if (!brandTheme)
      throw new NotFoundException('props.survey.errors.brand_theme_not_found');
    if (brandTheme.sec_company_id !== company.id)
      throw new NotFoundException(
        'props.survey.errors.brand_theme_company_different',
      );

    promises.push(
      this.surveyService.survey({
        where: { sec_company_id: company.id, surv_survey_id: surveyId },
        select: { surv_survey_id: true },
      }),
    );

    promises.push(
      this.themeRepository.exists({
        where: {
          sec_company_id: In([...adminCompanies, company.id]),
          theme_id: params.theme_id || 1,
        },
      }),
    );

    promises.push(
      this.brandRepository.exists({
        where: {
          sec_company_id: company.id,
          brand_id: brandTheme.brand_id || 1,
        },
      }),
    );

    promises.push(
      this.surveyBrandThemeRepository.findOne({
        where: {
          surv_survey_id: surveyId,
          theme_id: params.theme_id,
          brand_id: brandTheme.brand_id,
          sec_company_id: company.id,
        },
      }),
    );

    const result: [SurvSurveyEntity, boolean, boolean, SurveyBrandThemeEntity] =
      (await Promise.all(promises)) as any;
    const [survey, theme, brand, item] = result;

    if (!survey) throw new NotFoundException('props.survey.errors.not_found');
    if (!brandTheme)
      throw new NotFoundException('props.survey.errors.brand_theme_not_found');
    if (params.theme_id && !theme)
      throw new NotFoundException('props.survey.errors.theme_not_found');
    if (brandTheme.brand_id && !brand)
      throw new NotFoundException('props.survey.errors.brand_not_found');
    if (item && item.id !== brandThemeId)
      throw new ConflictException(
        'props.survey.errors.survey_brand_theme_exists',
      );

    Object.assign(brandTheme, params);

    return this.surveyBrandThemeRepository.save(brandTheme);
  }
}
