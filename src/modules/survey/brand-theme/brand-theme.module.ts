import { BrandEntity, SurveyBrandThemeEntity, ThemeEntity } from '@entities';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyBrandThemeService } from './brand-theme.service';
import { SurveyBrandThemeController } from './brand-theme.controller';
import { SurveyModule } from '../survey.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ThemeEntity,
      BrandEntity,
      SurveyBrandThemeEntity,
    ]),
    SurveyModule,
  ],
  controllers: [SurveyBrandThemeController],
  providers: [SurveyBrandThemeService],
})
export class SurveyBrandThemeModule {}
