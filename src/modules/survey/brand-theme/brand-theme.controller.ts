import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { SurveyBrandThemeService } from './brand-theme.service';
import {
  CreateSurveyBrandThemeDto,
  UpdateSurveyBrandThemeDto,
} from './dto/post-brand-theme.dto';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { Auth } from '@/shared/decorators';

@Controller({
  path: '/survey/:surveyId/brand-theme',
  version: '2',
})
@ApiTags('Survey Brand Theme')
@Auth()
export class SurveyBrandThemeController {
  constructor(
    private readonly surveyBrandThemeService: SurveyBrandThemeService,
  ) {}

  @Get()
  @ApiParam({ name: 'surveyId', type: 'number' })
  async list(@Param('surveyId') surveyId: number) {
    return await this.surveyBrandThemeService.list(surveyId);
  }

  // @Post()
  // @ApiParam({ name: 'surveyId', type: 'number' })
  // async create(
  //   @Param('surveyId') surveyId: number,
  //   @Body() body: CreateSurveyBrandThemeDto,
  // ) {
  //   return await this.surveyBrandThemeService.create(surveyId, body);
  // }

  @Post('/:brandThemeId')
  @ApiParam({ name: 'surveyId', type: 'number' })
  async update(
    @Param('surveyId') surveyId: number,
    @Param('brandThemeId') id: number,
    @Body() body: UpdateSurveyBrandThemeDto,
  ) {
    return await this.surveyBrandThemeService.update(id, surveyId, body);
  }
}
