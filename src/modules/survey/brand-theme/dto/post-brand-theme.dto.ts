import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, Min } from 'class-validator';

export class CreateSurveyBrandThemeDto {
  @Type()
  @IsInt()
  @Min(1)
  @ApiProperty({ default: 1 })
  brand_id: number;

  @Type()
  @IsInt()
  @Min(1)
  @ApiProperty({ default: 1 })
  theme_id: number;
}

export class UpdateSurveyBrandThemeDto {
  @Type()
  @IsInt()
  @Min(1)
  @ApiProperty({ default: 1 })
  theme_id: number;
}
