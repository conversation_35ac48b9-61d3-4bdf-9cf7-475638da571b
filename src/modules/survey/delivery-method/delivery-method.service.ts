import { ClsProperty } from '@/config/contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ProgramSettingsService } from '@/modules/common/program-settings/program-settings.service';
import { MediumTypeID } from '@/shared/enums/medium-type.enum';
import { SurveyMediumEntity, SurvSurveyEntity } from '@entities';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { CreateDeliveryMethodDto } from './dto/post-delivery-method.dto';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class SurveyDeliveryMethodService {
  constructor(
    private cls: ClsService,
    private i18n: I18nService,
    private programSettingsService: ProgramSettingsService,

    @InjectRepository(SurveyMediumEntity)
    private surveyMediumRepository: Repository<SurveyMediumEntity>,

    @InjectRepository(SurvSurveyEntity)
    private surveyRepository: Repository<SurvSurveyEntity>,
  ) {}

  async list(surveyId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const survey = await this.surveyRepository.findOne({
      where: { surv_survey_id: surveyId, sec_company_id: company.id },
      select: {
        surv_survey_id: true,
        send_reminder_mail: true,
        send_thank_mail: true,
        reminder_afterdays: true,
        reminder_count: true,
      },
    });
    if (!survey) throw new NotFoundException('props.survey.errors.not_found');

    const settings = await this.programSettingsService.get(company.id);
    const props = settings?.company_properties || {};
    let list = await this.surveyMediumRepository.find({
      where: {
        surv_survey_id: surveyId,
      },
      select: {
        surv_surveymedium_id: true,
        medium_id: true,
        medium_order: true,
      },
      order: {
        medium_order: 'ASC',
      },
    });

    let methods = list.map((item) => {
      if (item.medium_id === MediumTypeID.SMS) {
        (item as any).active = !!props?.sms?.api;
      } else {
        (item as any).active = true;
      }
      return item;
    });

    return {
      send_reminder_mail: !!survey.send_reminder_mail,
      send_thank_mail: !!survey.send_thank_mail,
      reminder_afterdays: survey.reminder_afterdays,
      reminder_count: survey.reminder_count,
      methods: this.transformMethods(methods),
    };
  }

  async reset(surveyId: number, params: CreateDeliveryMethodDto) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const survey = await this.surveyRepository.findOne({
      where: { surv_survey_id: surveyId, sec_company_id: company.id },
      select: { surv_survey_id: true },
    });
    if (!survey) throw new NotFoundException('props.survey.errors.not_found');

    await this.surveyMediumRepository.delete({
      surv_survey_id: surveyId,
    });

    await this.surveyRepository.update(
      { surv_survey_id: surveyId },
      {
        send_reminder_mail: params.send_reminder_mail ? 1 : 0,
        send_thank_mail: params.send_thank_mail ? 1 : 0,
        reminder_afterdays: params.reminder_afterdays,
        reminder_count: params.reminder_count,
        modified_by: user.id,
        modified_on: new Date(),
      },
    );

    let result = [];
    for (let [index, item] of Object.entries(params.methods)) {
      let medium_id = MediumTypeID[item.toUpperCase()];
      if (!medium_id) continue;
      let medium = await this.surveyMediumRepository.save(
        this.surveyMediumRepository.create({
          surv_surveymedium_id: v4(),
          surv_survey_id: surveyId,
          medium_id,
          medium_order: +index,
          creator: user.id,
          creation_date: new Date(),
          modifier: user.id,
          modification_date: new Date(),
        }),
      );
      result.push(medium);
    }
    return {
      message: this.i18n.t('props.survey.delivery_config_updated'),
      methods: this.transformMethods(result),
    };
  }

  transformMethods(list: SurveyMediumEntity[]) {
    return list
      .filter((item) => item.medium_id)
      .map((item) => (item.medium_id === MediumTypeID.EMAIL ? 'email' : 'sms'));
  }
}
