import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsOptional, Min } from 'class-validator';
import { IsArray, IsIn } from 'class-validator';

export class CreateDeliveryMethodDto {
  @Type()
  @IsArray()
  @IsIn(['sms', 'email'], { each: true })
  @ApiProperty({
    type: 'array',
    items: { type: 'string', enum: ['sms', 'email'] },
  })
  methods: string[];

  @IsBoolean()
  @ApiProperty()
  @IsOptional()
  @Type()
  send_reminder_mail: boolean;

  @IsBoolean()
  @ApiProperty()
  @IsOptional()
  @Type()
  send_thank_mail: boolean;

  @IsNumber()
  @ApiProperty()
  @IsOptional()
  @Type()
  reminder_afterdays: number;

  @IsNumber()
  @ApiProperty()
  @IsOptional()
  @Type()
  reminder_count: number;
}
