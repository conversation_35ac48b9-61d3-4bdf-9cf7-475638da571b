import { Auth } from '@/shared/decorators';
import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { SurveyDeliveryMethodService } from './delivery-method.service';
import { CreateDeliveryMethodDto } from './dto/post-delivery-method.dto';

@Controller({
  path: 'survey/delivery_config/:surveyId',
  version: '2',
})
@Auth()
@ApiTags('Survey Delivery Method')
export class SurveyDeliveryMethodController {
  constructor(
    private surveyDeliveryMethodService: SurveyDeliveryMethodService,
  ) {}

  @Get()
  async list(@Param('surveyId') surveyId: number) {
    return this.surveyDeliveryMethodService.list(surveyId);
  }

  @Post()
  async reset(
    @Param('surveyId') surveyId: number,
    @Body() body: CreateDeliveryMethodDto,
  ) {
    return this.surveyDeliveryMethodService.reset(surveyId, body);
  }
}
