import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyDeliveryMethodController } from './delivery-method.controller';
import { SurveyDeliveryMethodService } from './delivery-method.service';
import { SurveyMediumEntity, SurvSurveyEntity } from '@entities';
import { ProgramSettingsModule } from '@/modules/common/program-settings/program-settings.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SurveyMediumEntity, SurvSurveyEntity]),
    ProgramSettingsModule,
  ],
  controllers: [SurveyDeliveryMethodController],
  providers: [SurveyDeliveryMethodService],
  exports: [],
})
export class SurveyDeliveryMethodModule {}
