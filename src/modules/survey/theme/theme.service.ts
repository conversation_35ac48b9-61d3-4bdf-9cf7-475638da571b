import { BrandEntity, SurveyBrandThemeEntity, ThemeEntity } from '@entities';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  THEME_ITEM_QUERY,
  THEME_LIST_QUERY,
  THEME_LIST_WITH_SURVEY_QUERY,
} from './theme.sql';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import {
  CreateSurveyThemeDto,
  UpdateSurveyThemeDto,
} from './dto/post-theme.dto';
import { parseJSON } from '@/shared/utils';
import { SurveyThemeListQueryDto } from './dto/get-theme.dto';
import { adminCompanies } from '@/config/company.config';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class SurveyThemeService {
  constructor(
    private cls: ClsService,
    private i18n: I18nService,
    @InjectRepository(ThemeEntity)
    private readonly themeRepository: Repository<ThemeEntity>,

    @InjectRepository(BrandEntity)
    private readonly brandRepository: Repository<BrandEntity>,
    @InjectRepository(SurveyBrandThemeEntity)
    private readonly surveyBrandThemeRepository: Repository<SurveyBrandThemeEntity>,
  ) {}

  async list(query: SurveyThemeListQueryDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let list = await this.themeRepository.query(
      query.surveyId ? THEME_LIST_WITH_SURVEY_QUERY : THEME_LIST_QUERY,
      query.surveyId ? [+query.surveyId, company.id] : [company.id],
    );

    list = list.map((item) => {
      if (typeof item.data === 'string') {
        item.data = parseJSON(item.data);
      }
      item.survey_usage = item.survey_usage || 0;
      item.global = adminCompanies.includes(item.sec_company_id);
      return item;
    });

    return list;
  }

  async theme(themeId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let result = await this.themeRepository.query(THEME_ITEM_QUERY, [
      company.id,
      themeId,
    ]);

    let item = result?.[0];
    if (!item)
      throw new NotFoundException('props.survey.errors.theme_not_found');
    if (typeof item?.data === 'string') {
      item.data = parseJSON(item.data);
    }

    item.global = adminCompanies.includes(item.sec_company_id);
    item.survey_usage = item.survey_usage || 0;

    return item;
  }

  async create(params: CreateSurveyThemeDto) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const brand =
      params.default_brand &&
      (await this.brandRepository.findOne({
        where: { brand_id: params.default_brand },
      }));

    if (params.default_brand) {
      if (brand) {
        await this.themeRepository.update(
          {
            default_brand: params.default_brand,
            sec_company_id: company.id,
          },
          {
            default_brand: 0,
          },
        );
      } else {
        throw new NotFoundException('props.survey.errors.brand_not_found');
      }
    }
    const theme = this.themeRepository.create({
      ...params,
      sec_company_id: company.id,
      created_by: user.id,
      modified_by: user.id,
      created_on: new Date(),
      modified_on: new Date(),
    });
    let result = await this.themeRepository.save(theme);
    if (typeof result.data === 'string') {
      result.data = parseJSON(result.data);
    }
    return result;
  }

  async update(themeId: number, params: UpdateSurveyThemeDto) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let theme = await this.themeRepository.findOne({
      where: { theme_id: themeId, sec_company_id: company.id },
    });

    if (!theme) {
      throw new NotFoundException('props.survey.errors.theme_not_found');
    }

    if (params.default_brand && theme.default_brand !== params.default_brand) {
      const brand = await this.brandRepository.findOne({
        where: { brand_id: params.default_brand },
      });
      if (brand) {
        await this.themeRepository.update(
          {
            default_brand: params.default_brand,
            sec_company_id: company.id,
          },
          {
            default_brand: null,
          },
        );
      } else {
        throw new NotFoundException('props.survey.errors.brand_not_found');
      }
    }

    await this.themeRepository.update(
      {
        theme_id: themeId,
        sec_company_id: company.id,
      },
      {
        ...params,
        modified_by: user.id,
        modified_on: new Date(),
      },
    );

    return {
      message: this.i18n.t('props.survey.theme_updated'),
    };
  }

  async clone(themeId: number) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const theme = await this.themeRepository.findOne({
      where: { theme_id: themeId, sec_company_id: company.id },
    });

    if (!theme) {
      throw new NotFoundException('props.survey.errors.theme_not_found');
    }

    const newTheme = this.themeRepository.create({
      ...theme,
      name: `${theme.name} - copy`,
      theme_id: undefined,
      default_brand: undefined,
      sec_company_id: company.id,
      created_by: user.id,
      modified_by: user.id,
      created_on: new Date(),
      modified_on: new Date(),
    });

    let result = await this.themeRepository.save(newTheme);
    if (typeof result.data === 'string') {
      result.data = parseJSON(result.data);
    }
    return result;
  }

  async deleteTheme(themeId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const theme = await this.themeRepository.findOne({
      where: { theme_id: themeId, sec_company_id: company.id },
    });

    if (!theme) {
      throw new NotFoundException('props.survey.errors.theme_not_found');
    }

    let checkConnectedSurvey = await this.surveyBrandThemeRepository.exists({
      where: { theme_id: themeId },
    });

    if (checkConnectedSurvey) {
      throw new BadRequestException('props.survey.errors.theme_linked');
    }

    await this.themeRepository.delete({
      theme_id: themeId,
      sec_company_id: company.id,
    });

    return {
      message: this.i18n.t('props.survey.theme_deleted'),
    };
  }
}
