import { adminCompanies } from '@/config/company.config';

export const THEME_LIST_WITH_SURVEY_QUERY = `
WITH theme_count AS (
    SELECT t.theme_id,
           COUNT(DISTINCT sbt.surv_survey_id) AS cnt
    FROM survey_brand_theme sbt
    LEFT JOIN themes t ON sbt.theme_id = t.theme_id
    WHERE t.sec_company_id = $2
    GROUP BY t.theme_id
)
SELECT DISTINCT ON (t.theme_id) t.theme_id,
       t.name,
       t.description,
       t.image,
       t.language,
       t.default_brand,
       t.sec_company_id,
       sbt.surv_survey_id,
       CAST(tc.cnt AS INTEGER) as survey_usage
FROM themes t
LEFT JOIN survey_brand_theme sbt ON t.theme_id = sbt.theme_id AND sbt.surv_survey_id = $1
LEFT JOIN theme_count tc ON t.theme_id = tc.theme_id
WHERE sbt.surv_survey_id IS NOT NULL
AND t.sec_company_id IN ($2, ${adminCompanies.join(', ')})
ORDER BY t.theme_id, t.modified_on DESC`;

export const THEME_LIST_QUERY = `
WITH theme_count AS (
    SELECT t.theme_id,
           COUNT(DISTINCT sbt.surv_survey_id) AS cnt
    FROM survey_brand_theme sbt
    LEFT JOIN themes t ON sbt.theme_id = t.theme_id
    WHERE t.sec_company_id = $1
    GROUP BY t.theme_id
)
SELECT
     t.theme_id,
       t.name,
       t.description,
       t.image, 
       t.language,
       t.default_brand,
       t.sec_company_id,
       CAST(tc.cnt AS INTEGER) as survey_usage
FROM
    themes t
    LEFT JOIN theme_count tc ON t.theme_id = tc.theme_id
WHERE
    t.sec_company_id IN ($1, ${adminCompanies.join(',')})
ORDER BY t.modified_on DESC`;

export const THEME_ITEM_QUERY = `
WITH theme_count AS (
    SELECT t.theme_id,
           COUNT(DISTINCT sbt.surv_survey_id) AS cnt
    FROM survey_brand_theme sbt
    LEFT JOIN themes t ON sbt.theme_id = t.theme_id
    WHERE t.sec_company_id = $1
    GROUP BY t.theme_id
)
SELECT
    t.name,
    t.description,
    t.default_brand,
    t.image,
    t.data,
    t.sec_company_id,
    CAST(COALESCE(tc.cnt, 0) AS INTEGER) as survey_usage
FROM themes t
LEFT JOIN theme_count tc ON t.theme_id = tc.theme_id
WHERE 
    t.sec_company_id IN ($1, ${adminCompanies.join(',')})
    AND t.theme_id = $2`;
