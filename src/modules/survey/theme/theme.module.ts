import { BrandEntity, SurveyBrandThemeEntity, ThemeEntity } from '@entities';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyThemeService } from './theme.service';
import { SurveyThemeController } from './theme.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ThemeEntity,
      BrandEntity,
      SurveyBrandThemeEntity,
    ]),
  ],
  controllers: [SurveyThemeController],
  providers: [SurveyThemeService],
})
export class SurveyThemeModule {}
