import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { SurveyThemeService } from './theme.service';
import {
  CreateSurveyThemeDto,
  UpdateSurveyThemeDto,
} from './dto/post-theme.dto';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from '@/shared/decorators';
import { SurveyThemeListQueryDto } from './dto/get-theme.dto';

@Controller({
  path: '/survey/theme',
  version: '2',
})
@ApiTags('Survey Theme')
@Auth()
export class SurveyThemeController {
  constructor(private readonly surveyThemeService: SurveyThemeService) {}

  @Get()
  async list(@Query() query: SurveyThemeListQueryDto) {
    return await this.surveyThemeService.list(query);
  }

  @Get('/:themeId')
  async theme(@Param('themeId') themeId: number) {
    return await this.surveyThemeService.theme(themeId);
  }

  @Post()
  async create(@Body() body: CreateSurveyThemeDto) {
    return await this.surveyThemeService.create(body);
  }

  @Post('/:themeId')
  async update(
    @Param('themeId') themeId: number,
    @Body() body: UpdateSurveyThemeDto,
  ) {
    return await this.surveyThemeService.update(themeId, body);
  }

  @Post('/:themeId/clone')
  async clone(@Param('themeId') themeId: number) {
    return await this.surveyThemeService.clone(themeId);
  }

  @Delete('/:themeId')
  async delete(@Param('themeId') themeId: number) {
    return await this.surveyThemeService.deleteTheme(themeId);
  }
}
