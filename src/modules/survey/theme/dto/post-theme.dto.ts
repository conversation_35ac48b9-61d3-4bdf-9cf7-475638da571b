import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  IsUrl,
  Min,
  MinLength,
} from 'class-validator';

export class CreateSurveyThemeDto {
  @Type()
  @ApiProperty()
  @IsString()
  @MinLength(1, { context: '1' })
  name: string;

  @Type()
  @ApiProperty()
  @IsString()
  @IsOptional()
  @IsUrl()
  image: string;

  @Type()
  @ApiProperty()
  @IsString()
  @IsOptional()
  @MinLength(1, { context: '1' })
  description: string;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @IsPositive()
  default_brand: number;

  @Type()
  @IsObject()
  @IsOptional()
  @ApiProperty({ default: {} })
  data: any;
}

export class UpdateSurveyThemeDto extends PartialType(CreateSurveyThemeDto) {}
