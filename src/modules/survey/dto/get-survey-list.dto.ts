import { ApiProperty } from '@nestjs/swagger';

export class SurveyListItemDto {
  @ApiProperty({ type: Number })
  id: number;
  @ApiProperty({ type: String })
  name: string;
}
export class GetSurveyListDto {
  @ApiProperty({ type: [SurveyListItemDto] })
  survey_list: SurveyListItemDto[];
}

export class GetSurveyListResponse extends GetSurveyListDto {
  // @ApiProperty()
  // data: GetSurveyListDto;
}
