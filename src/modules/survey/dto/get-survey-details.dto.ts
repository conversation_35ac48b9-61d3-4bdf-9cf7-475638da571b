import { ApiProperty } from '@nestjs/swagger';

export class BrandDto {
  @ApiProperty()
  id: number;
  @ApiProperty()
  name: string;
}
export class QuestionDto {
  @ApiProperty()
  id: number;
  @ApiProperty()
  name: string;
  @ApiProperty()
  type: number;
}

export class GetSurveyDetailsDto {
  @ApiProperty({ type: [BrandDto] })
  brands: BrandDto[];
  @ApiProperty({ type: [QuestionDto] })
  questions: QuestionDto[];
}

export class GetSurveyDetailsResponse extends GetSurveyDetailsDto {
  // @ApiProperty()
  // data: GetSurveyDetailsDto;
}
