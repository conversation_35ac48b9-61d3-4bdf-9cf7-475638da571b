import { SurvImportedContacts } from '@/modules/database/entities/surv-imported-contacts.entity';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CreateSurveyRecipientDto,
  UpdateSurveyRecipientDto,
} from './dto/post-survey-recipients.dto';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class SurveyRecipientsService {
  constructor(
    private cls: ClsService,
    private i18n: I18nService,
    @InjectRepository(SurvImportedContacts)
    private survImportedContactsRepository: Repository<SurvImportedContacts>,
  ) {}

  async list(surveyId: number) {
    let list = await this.survImportedContactsRepository.find({
      where: { surv_survey_id: surveyId },
    });

    return list.map(this.mapRecipient);
  }

  async addRecipient(surveyId: number, data: CreateSurveyRecipientDto) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    if (!data.email && !data.phone) {
      throw new BadRequestException(
        'Email or phone is required for this delivery method.',
      );
    }

    let updateData: Partial<SurvImportedContacts> = {
      email: data.email,
      phone1: data.phone,
      salutation: data.salutation,
      initials: data.initials,
      customername: data.customer_name,
      contact_date: data.contact_date,
      contact_language: data.contact_language,
      region: data.region,
    };

    let result = await this.survImportedContactsRepository.save(
      this.survImportedContactsRepository.create({
        ...updateData,
        rasnumber: 'TST' + Math.floor(Math.random() * 1000000),
        brand: data.brand.toString(),
        surv_survey_id: surveyId,
        sec_company_id: company.id,
        creation_date: new Date(),
        creator: user.fullname,
        modification_date: new Date(),
        modifier: user.fullname,
      }),
    );

    return this.mapRecipient(result);
  }

  async addRecipients(surveyId: number, data: CreateSurveyRecipientDto[]) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    for (const item of data) {
      if (!item.email && !item.phone) {
        throw new BadRequestException(
          'Email or phone is required for this delivery method.',
        );
      }
    }

    let result = await this.survImportedContactsRepository.save(
      data.map((item) => {
        const updateData: Partial<SurvImportedContacts> = {
          email: item.email,
          phone1: item.phone,
          salutation: item.salutation,
          initials: item.initials,
          customername: item.customer_name,
          contact_date: item.contact_date,
          contact_language: item.contact_language,
          region: item.region,
        };

        return this.survImportedContactsRepository.create({
          ...updateData,
          rasnumber: 'TST' + Math.floor(Math.random() * 1000000),
          brand: item.brand.toString(),
          surv_survey_id: surveyId,
          sec_company_id: company.id,
          creation_date: new Date(),
          creator: user.fullname,
          modification_date: new Date(),
          modifier: user.fullname,
        });
      }),
    );
    return result.map(this.mapRecipient);
  }

  async update(id: string, data: UpdateSurveyRecipientDto) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const item = await this.survImportedContactsRepository.findOne({
      where: { surv_imported_contact_id: id, sec_company_id: company.id },
    });

    if (!item) throw new NotFoundException();

    await this.survImportedContactsRepository.update(
      { surv_imported_contact_id: id, sec_company_id: company.id },
      {
        email: data.email === '' ? null : data.email || item.email,
        phone1: data.phone === '' ? null : data.phone || item.phone1,
        salutation:
          data.salutation === '' ? null : data.salutation || item.salutation,
        initials: data.initials === '' ? null : data.initials || item.initials,
        customername:
          data.customer_name === ''
            ? null
            : data.customer_name || item.customername,
        contact_date: data.contact_date,
        contact_language:
          data.contact_language === ''
            ? null
            : data.contact_language || item.contact_language,
        region: data.region === '' ? null : data.region || item.region,
        brand: data.brand && data.brand.toString(),
        modification_date: new Date(),
        modifier: user.fullname,
      },
    );

    return {
      message: this.i18n.t('props.survey.recipient_updated'),
    };
  }

  async delete(id: string) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    await this.survImportedContactsRepository.delete({
      surv_imported_contact_id: id,
      sec_company_id: company.id,
    });
    return {
      message: this.i18n.t('props.survey.recipient_deleted'),
    };
  }

  mapRecipient(item: SurvImportedContacts) {
    return {
      id: item.surv_imported_contact_id,
      brand: +item.brand,
      contact_language: item.contact_language,
      email: item.email,
      phone: item.phone1,
      initials: item.initials,
      salutation: item.salutation,
      customer_name: item.customername,
      region: item.region,
      contact_date: item.contact_date,
    };
  }
}
