import { SurvImportedContacts } from '@/modules/database/entities/surv-imported-contacts.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyRecipientsController } from './survey-recipients.controller';
import { SurveyRecipientsService } from './survey-recipients.service';

@Module({
  imports: [TypeOrmModule.forFeature([SurvImportedContacts])],
  controllers: [SurveyRecipientsController],
  providers: [SurveyRecipientsService],
  exports: [],
})
export class SurveyRecipientsModule {}
