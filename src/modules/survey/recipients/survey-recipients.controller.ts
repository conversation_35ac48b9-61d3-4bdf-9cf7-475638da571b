import { Auth } from '@/shared/decorators';
import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiBody, ApiParam, ApiTags } from '@nestjs/swagger';
import { SurveyRecipientsService } from './survey-recipients.service';
import {
  CreateSurveyRecipientDto,
  UpdateSurveyRecipientDto,
} from './dto/post-survey-recipients.dto';

@Controller({
  path: '/survey/:surveyId/recipients',
  version: '2',
})
@ApiTags('Survey Recipients')
@Auth()
export class SurveyRecipientsController {
  constructor(private surveyRecipientsService: SurveyRecipientsService) {}

  @Get()
  @ApiParam({ name: 'surveyId', type: 'number' })
  async list(@Param('surveyId') surveyId: number) {
    return this.surveyRecipientsService.list(surveyId);
  }

  @Post()
  @ApiParam({ name: 'surveyId', type: 'number' })
  async addRecipient(
    @Param('surveyId') surveyId: number,
    @Body() body: CreateSurveyRecipientDto,
  ) {
    return this.surveyRecipientsService.addRecipient(surveyId, body);
  }

  @Post('/multiple')
  @ApiParam({ name: 'surveyId', type: 'number' })
  @ApiBody({ type: CreateSurveyRecipientDto, isArray: true })
  async addRecipients(
    @Param('surveyId') surveyId: number,
    @Body() body: CreateSurveyRecipientDto[],
  ) {
    return this.surveyRecipientsService.addRecipients(surveyId, body);
  }

  @Post(':id')
  @ApiParam({ name: 'surveyId', type: 'number' })
  async update(
    @Param('id') id: string,
    @Body() body: UpdateSurveyRecipientDto,
  ) {
    return this.surveyRecipientsService.update(id, body);
  }

  @Delete(':id')
  @ApiParam({ name: 'surveyId', type: 'number' })
  async delete(@Param('id') id: string) {
    return this.surveyRecipientsService.delete(id);
  }
}
