import {
  IsString,
  IsEmail,
  IsNumber,
  IsDate,
  IsOptional,
  Length,
  Matches,
  IsDecimal,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, PartialType } from '@nestjs/swagger';

export class CreateSurveyRecipientDto {
  @Type(() => Number)
  @ApiProperty({
    description: 'Brand identifier',
    example: 2,
  })
  @IsNumber()
  brand: number;

  @ApiProperty({
    description: 'Customer email address',
    example: '<EMAIL>',
  })
  @ValidateIf((e) => e.email !== '')
  @IsOptional()
  @IsEmail()
  email: string;

  @Type()
  @ApiProperty({
    description: 'Customer salutation',
    example: 'Mr.',
    minLength: 1,
    maxLength: 20,
  })
  @IsString()
  @IsOptional()
  @Length(1, 20)
  salutation: string;

  @Type()
  @IsOptional()
  @ApiProperty({
    description: 'Customer initials',
    example: '<PERSON>',
  })
  @IsString()
  initials: string;

  @Type()
  @IsOptional()
  @ApiProperty({
    description: 'Customer name',
    example: 'Do',
  })
  @IsString()
  customer_name: string;

  @ApiProperty({
    description: 'Contact date',
    example: '2024-01-01 00:00:00',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  contact_date: Date;

  @Type()
  @ApiProperty({
    description: 'Contact language code',
    example: 'nl',
    minLength: 2,
    maxLength: 2,
  })
  @IsString()
  @Length(2, 2)
  contact_language: string;

  @Type()
  @IsOptional()
  @ApiProperty({
    description: 'Phone number in E.164 format',
    example: '+31061234567',
  })
  @Matches(/^(\+[1-9]\d{1,18})?$/, {
    message: 'Phone number must be in E.164 format',
  })
  phone: string;

  @Type()
  @IsOptional()
  @ApiProperty({
    description: 'Region',
    example: 'North',
  })
  @IsOptional()
  @IsString()
  region: string;

  // @Type()
  // @IsOptional()
  // @ApiProperty({
  //   description: 'Phone number in E.164 format',
  //   example: '+31061234567',
  // })
  // @Matches(/^\+[1-9]\d{1,18}$/, {
  //   message: 'Phone number must be in E.164 format',
  // })
  // phone2: string;

  // @Type()
  // @IsOptional()
  // @ApiProperty({
  //   description: 'RAS number',
  //   example: '123ABC',
  // })
  // @IsString()
  // rasnumber: string;

  // @Type()
  // @IsOptional()
  // @ApiProperty({
  //   description: 'E-number',
  //   example: '123ABC',
  // })
  // @IsString()
  // enumber: string;

  // @Type(() => Number)
  // @IsOptional()
  // @ApiProperty({
  //   description: 'FD number',
  //   example: 123,
  // })
  // @IsNumber()
  // fdnumber: number;

  // @Type()
  // @IsOptional()
  // @ApiProperty({
  //   description: 'Product area',
  //   example: 'A',
  // })
  // @IsString()
  // prodarea: string;

  // @Type()
  // @IsOptional()
  // @ApiProperty({
  //   description: 'Technician identifier',
  //   example: '123ABC',
  // })
  // @IsString()
  // technician: string;

  // @ApiProperty({
  //   description: 'Purchase date',
  //   example: '2024-01-01 00:00:00',
  // })
  // @IsOptional()
  // @Type(() => Date)
  // @IsDate()
  // purchase_date: Date;

  // @Type()
  // @IsOptional()
  // @ApiProperty({
  //   description: 'Call code',
  //   example: '123',
  // })
  // @IsString()
  // call_code: string;

  // @IsOptional()
  // @ApiProperty({
  //   description: 'Consecutive number',
  //   example: 12.12,
  // })
  // @Type(() => Number)
  // @IsDecimal({ decimal_digits: '2' })
  // consecnr: number;

  // @Type()
  // @IsOptional()
  // @ApiProperty({
  //   description: 'Employee identifier',
  //   example: '123ABC',
  // })
  // @IsString()
  // employee: string;

  // @IsOptional()
  // @ApiProperty({
  //   description: 'Company name',
  //   example: 'Focus Feedback',
  // })
  // @IsString()
  // companyname: string;

  // @Type()
  // @IsOptional()
  // @ApiProperty({
  //   description: 'Acceptance by identifier',
  //   example: '123ABC',
  // })
  // @IsString()
  // acceptance_by: string;
}

export class UpdateSurveyRecipientDto extends PartialType(
  CreateSurveyRecipientDto,
) {}
