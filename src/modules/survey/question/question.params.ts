export const QUESTION_TYPES = {
  HORIZONTAL_SCALE: {
    id: 1,
    type: 'SELECT_QUESTION',
    layout: 'card_view',
    selection_type: 'single',
  },
  DESCRIPTIVE_TEXT: { id: 2, type: 'PARAGRAPH' },
  VERTICAL_CHECK: {
    id: 3,
    type: 'SELECT_QUESTION',
    layout: 'list_view',
    selection_type: 'multiple',
  },
  SHORT_QUESTION: { id: 4, type: 'SHORT_QUESTION' },
  OPEN_QUESTION: { id: 5, type: 'OPEN_QUESTION' },
  VERTICAL_SCALE: {
    id: 6,
    type: 'SELECT_QUESTION',
    layout: 'list_view',
    selection_type: 'single',
  },
  HORIZONTAL_CHECK: {
    id: 7,
    type: 'SELECT_QUESTION',
    layout: 'card_view',
    selection_type: 'multiple',
  },
  NPS: { id: 9, type: 'NPS_QUESTION' },
  NUMBER_SCALE: { id: 10, type: 'NUMBER_SELECT_QUESTION' },
  PUBLISH: { id: 11, type: 'PUBLISH_QUESTION' },
  CSAT: { id: 12, type: 'CSAT_QUESTION' },
  CES: { id: 13, type: 'CES_QUESTION' },
  ANSWER_TABLE_QUESTION: { id: 14, type: 'ANSWER_TABLE_QUESTION' },
  MATRIX: { id: 15, type: 'MATRIX_QUESTION' },
  CALLBACK_QUESTION: { id: 16, type: 'CALLBACK_QUESTION' },
};

export const QUESTION_TEMPLATE_TYPES = [
  {
    id: 1,
    type: 'SELECT_QUESTION',
    layout: 'card_view',
    selection_type: 'single',
  },
  {
    id: 2,
    type: 'PARAGRAPH',
  },
  {
    id: 3,
    type: 'SELECT_QUESTION',
    layout: 'list_view',
    selection_type: 'multiple',
  },
  {
    id: 4,
    type: 'SHORT_QUESTION',
  },
  {
    id: 5,
    type: 'OPEN_QUESTION',
  },
  {
    id: 6,
    type: 'SELECT_QUESTION',
    layout: 'list_view',
    selection_type: 'single',
  },
  {
    id: 7,
    type: 'SELECT_QUESTION',
    layout: 'card_view',
    selection_type: 'multiple',
  },
  {
    id: 9,
    type: 'NPS_QUESTION',
  },
  {
    id: 10,
    type: 'NUMBER_SELECT_QUESTION',
  },
  {
    id: 11,
    type: 'PUBLISH_QUESTION',
  },
  {
    id: 12,
    type: 'CSAT_QUESTION',
  },
  {
    id: 13,
    type: 'CES_QUESTION',
  },
  {
    id: 14,
    type: 'ANSWER_TABLE_QUESTION',
  },
  {
    id: 15,
    type: 'MATRIX_QUESTION',
  },
  {
    id: 16,
    type: 'CALLBACK_QUESTION',
  },
];

export const MULTIPLE_CHOICE_QUESTION_TYPES = Object.values(QUESTION_TYPES)
  .filter(
    (value) => 'selection_type' in value && value.selection_type === 'multiple',
  )
  .map((value) => value.id);
