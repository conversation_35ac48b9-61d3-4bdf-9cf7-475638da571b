import { Auth } from '@/shared/decorators';
import { Body, Controller, Param, Post } from '@nestjs/common';
import { SurveyQuestionService } from './question.service';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import {
  CreateBlankSurveyAnswerDto,
  CreateBlankSurveyQuestionDto,
} from './dto/post-survey-question.dto';

@Controller({
  path: 'survey/:surveyId/question',
  version: '2',
})
@ApiTags('Survey Question')
@Auth()
export class SurveyQuestionController {
  constructor(private questionService: SurveyQuestionService) {}

  @Post()
  create(
    @Param('surveyId') surveyId: number,
    @Body() body: CreateBlankSurveyQuestionDto,
  ) {
    return this.questionService.createBlankQuestion(surveyId, body);
  }
  @Post(':questionId/answer')
  @ApiParam({ name: 'surveyId', type: 'number' })
  createAnswer(
    @Param('questionId') questionId: number,
    @Body() body: CreateBlankSurveyAnswerDto,
  ) {
    return this.questionService.createBlankAnswers(questionId, body.count);
  }

  @Post(':questionId/copy')
  clone(
    @Param('surveyId') surveyId: number,
    @Param('questionId') questionId: number,
  ) {
    return this.questionService.cloneQuestion(surveyId, questionId);
  }

  // this api is for tetsing question type, please remove after testing
  // @Post('/check-question-type')
  // checkQuestionType(@Body() body: UpdateSurveyQuestionDto) {
  //   return this.questionService.questionType({
  //     type: body.type,
  //     layout: body?.properties?.layout,
  //     selection_type: body?.properties?.selection_type,
  //   });
  // }

  // @Post(':questionId')
  // @Auth()
  // update(
  //   @Param('surveyId') surveyId: number,
  //   @Param('questionId') questionId: number,
  //   @Body() body: UpdateSurveyQuestionDto,
  // ) {
  //   body.id = questionId;
  //   return this.questionService.upsert(surveyId, body);
  // }

  //   @Post(':id')
  //   @Auth()
  //   update(@Param('id') id: number, @Body() body: UpdateSurveyDto) {
  //     return this.questionService.upsert({ ...body, surv_survey_id: id });
  //   }
}
