import {
  SurveyQuestionEntity,
  SurveyQuestionLocalEntity,
  SurveyQuestionRowEntity,
  SurveyQuestionRowLocalEntity,
  SurvSurveyEntity,
} from '@entities';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyQuestionController } from './question.controller';
import { SurveyQuestionService } from './question.service';
import { SurveyModule } from '../survey.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SurvSurveyEntity,
      SurveyQuestionEntity,
      SurveyQuestionLocalEntity,
      SurveyQuestionRowEntity,
      SurveyQuestionRowLocalEntity,
    ]),
    SurveyModule,
  ],
  controllers: [SurveyQuestionController],
  providers: [SurveyQuestionService],
  exports: [SurveyQuestionService],
})
export class SurveyQuestionModule {}
