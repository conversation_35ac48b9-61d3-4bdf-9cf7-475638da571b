import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsArray,
  ValidateNested,
  IsOptional,
  MinLength,
  MaxLength,
  IsBoolean,
  IsInt,
  Min,
  Max,
  IsPositive,
} from 'class-validator';
import { Type } from 'class-transformer';

export class SurveyQuestionAnswerDto {
  @Type()
  @ApiProperty()
  @IsString()
  id: string;

  @Type()
  @ApiProperty()
  @IsString()
  label: string;
}

export class SurveyQuestionRowOptionsLabel {
  @Type()
  @ApiProperty()
  @IsOptional()
  start: any;

  @Type()
  @ApiProperty()
  @IsOptional()
  end: any;
}
export class SurveyQuestionLanguage {
  @Type()
  @ApiProperty()
  @IsString()
  @MinLength(2, { context: '2' })
  @MaxLength(2, { context: '2' })
  language: string;

  @Type()
  @ApiProperty()
  @IsString()
  question_text: string;

  @Type()
  @ApiProperty()
  @IsString()
  @IsOptional()
  question_title: string;

  @Type()
  @ApiProperty()
  @IsString()
  @IsOptional()
  placeholder: string;

  @Type(() => SurveyQuestionAnswerDto)
  @IsArray()
  @ApiProperty({ type: () => SurveyQuestionAnswerDto, isArray: true })
  @IsOptional()
  options_labels: SurveyQuestionRowOptionsLabel[];

  @ApiProperty({ type: () => SurveyQuestionAnswerDto, isArray: true })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => SurveyQuestionAnswerDto)
  answers: SurveyQuestionAnswerDto[];

  @Type(() => SurveyQuestionRowOptionsLabel)
  @IsOptional()
  @ApiProperty({ type: () => SurveyQuestionRowOptionsLabel })
  scale_labels: SurveyQuestionRowOptionsLabel;
}

export class SurveyQuestionProperties {
  @Type()
  @ApiProperty()
  @IsNumber()
  required: number;

  @Type()
  @ApiProperty()
  @IsString()
  @IsOptional()
  question: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsNumber()
  show_other: number;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsNumber()
  template_id: number;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsString()
  layout: string;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  csi_number: number;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsString()
  selection_type: string;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  leading_nps_question: boolean;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  leading_csat_question: boolean;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  leading_ces_question: boolean;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  improvements: boolean;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  strengths: boolean;

  @Type()
  @ApiProperty()
  @IsOptional()
  @IsString()
  answer_type: string;

  @Type()
  @IsOptional()
  @ApiProperty({ type: () => Object, default: {} })
  table_config: any;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  selected_answer_table_id: string;

  @ApiProperty({ type: () => SurveyQuestionLanguage, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SurveyQuestionLanguage)
  languages: SurveyQuestionLanguage[];

  @Type(() => SurveyQuestionAnswerDto)
  @IsArray()
  @ApiProperty({ type: () => SurveyQuestionAnswerDto, isArray: true })
  @IsOptional()
  answers: SurveyQuestionAnswerDto[];

  @Type(() => SurveyQuestionRowOptionsLabel)
  @IsOptional()
  @ApiProperty({ type: () => SurveyQuestionRowOptionsLabel })
  scale: SurveyQuestionRowOptionsLabel;
}

export class CreateSurveyQuestionDto {
  @Type()
  @ApiProperty()
  @IsString()
  label: string;

  @Type()
  @ApiProperty()
  @IsString()
  description: string;

  @Type()
  @ApiProperty()
  @IsString()
  type: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  icon: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsNumber()
  is_active: number;

  @ApiProperty({ type: () => SurveyQuestionProperties })
  @ValidateNested()
  @Type(() => SurveyQuestionProperties)
  properties: SurveyQuestionProperties;
}

export class UpdateSurveyQuestionDto extends CreateSurveyQuestionDto {
  id: number;
}

export class CreateBlankSurveyQuestionDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  question_template_id: number;
}

export class CreateBlankSurveyAnswerDto {
  @Type()
  @IsInt()
  @Min(0)
  @Max(100)
  @ApiProperty({ default: 1 })
  count: number;
}
