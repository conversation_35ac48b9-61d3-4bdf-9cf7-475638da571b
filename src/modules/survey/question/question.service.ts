import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SurveyQuestionEntity, SurveyQuestionRowEntity } from '@entities';
import { FindOptionsSelect, FindOptionsWhere, In, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { SurveyUtils } from '../survey.utils';
import { SurveyService } from '../survey.service';
import { v4 } from 'uuid';
import { CreateBlankSurveyQuestionDto } from './dto/post-survey-question.dto';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class SurveyQuestionService {
  constructor(
    private cls: ClsService,
    private i18n: I18nService,
    private surveyUtils: SurveyUtils,
    private surveyService: SurveyService,
    @InjectRepository(SurveyQuestionEntity)
    private questionRepository: Repository<SurveyQuestionEntity>,
    @InjectRepository(SurveyQuestionRowEntity)
    private questionRowRepo: Repository<SurveyQuestionRowEntity>,
  ) {}

  detailedQuestions({
    where,
    addSelect = {},
  }: {
    where?: FindOptionsWhere<SurveyQuestionEntity>;
    addSelect?: FindOptionsSelect<SurveyQuestionEntity>;
  } = {}) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    return this.questionRepository.find({
      where: {
        ...where,
        survey: {
          sec_company_id: company.id,
        },
      },
      select: {
        surv_surveyquestion_id: true,
        question_required: true,
        show_other: true,
        csi_number: true,
        is_active: true,
        questiontype: true,
        surv_survquest_temp_id: true,
        answertable_id: true,
        created_by: true,
        modified_by: true,
        surv_survey_id: true,
        languages: {
          surv_surveyquestion_local_id: true,
          language: true,
          question_text: true,
          question_title: true,
          question_left_text: true,
          question_right_text: true,
        },
        answers: {
          surv_surveyquestionrow_id: true,
          ordernumber: true,
          next_surveypart_id: true,
          next_question_id: true,
          isactive: true,
          surv_survquest_temp_id: true,
          question_label: true,
          question_shortlabel: true,
          question_returnvalue: true,
          created_by: true,
          modified_by: true,
        },
        ...addSelect,
      },
      relations: ['survey', 'languages', 'answers'],
    });
  }

  async cloneQuestion(surveyId: number, questionId: number) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let survey = await this.surveyService.survey({
      where: { surv_survey_id: surveyId, sec_company_id: company.id },
    });

    if (!survey) throw new NotFoundException('props.survey.errors.not_found');

    let question = await this.questionRepository.findOne({
      where: { surv_surveyquestion_id: questionId },
      relations: ['languages', 'answers'],
    });

    if (!question)
      throw new NotFoundException('props.survey.errors.question_not_found');

    let newQuestion = await this.questionRepository.save({
      surv_survey_id: surveyId,
      question_required: question.question_required,
      show_other: question.show_other,
      csi_number: question.csi_number,
      is_active: question.is_active,
      questiontype: question.questiontype,
      surv_survquest_temp_id: question.surv_survquest_temp_id,
      answertable_id: question.answertable_id,
      created_by: user.id,
      created_on: new Date(),
      modified_by: user.id,
      modified_on: new Date(),
    });

    newQuestion.languages = question.languages.map((lang) => ({
      ...lang,
      surv_surveyquestion_id: newQuestion.surv_surveyquestion_id,
      created_by: user.id,
      created_on: new Date(),
      modified_by: user.id,
      modified_on: new Date(),
    }));

    let newAnswers = question.answers.map((ans) => ({
      ...ans,
      surv_surveyquestion_id: newQuestion.surv_surveyquestion_id,
      created_by: user.id,
      created_on: new Date(),
      modified_by: user.id,
      modified_on: new Date(),
    }));

    await this.questionRepository.save(newQuestion);
    await this.questionRowRepo.save(newAnswers);

    return {
      status: 'cloned',
      message: this.i18n.t('props.survey.question_cloned'),
      questionId: newQuestion.surv_surveyquestion_id,
    };
  }

  async createBlankQuestion(
    surveyId: number,
    params: CreateBlankSurveyQuestionDto,
  ) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let survey = await this.surveyService.survey({
      where: { surv_survey_id: surveyId, sec_company_id: company.id },
      relations: ['editingBy'],
    });

    if (!survey) throw new NotFoundException('props.survey.errors.not_found');
    this.surveyUtils.checkSurveyIsLocked(survey.editingBy, survey.editing_dt);

    let question = await this.questionRepository.save(
      this.questionRepository.create({
        surv_survey_id: surveyId,
        question_template_id: params.question_template_id,
        created_by: user.id,
        created_on: new Date(),
        modified_by: user.id,
        modified_on: new Date(),
      }),
    );

    return {
      status: 'created',
      message: this.i18n.t('props.survey.blank_question_created'),
      questionId: question.surv_surveyquestion_id,
    };
  }

  async createBlankAnswers(questionId: number, count: number) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let question = await this.questionRepository.findOne({
      where: { surv_surveyquestion_id: questionId },
    });

    if (!question)
      throw new NotFoundException('props.survey.errors.question_not_found');

    let survey = await this.surveyService.survey({
      where: {
        surv_survey_id: question.surv_survey_id,
        sec_company_id: company.id,
      },
      relations: ['editingBy'],
    });

    if (!survey) throw new NotFoundException('props.survey.errors.not_found');
    this.surveyUtils.checkSurveyIsLocked(survey.editingBy, survey.editing_dt);

    let answers = Array(count)
      .fill(0)
      .map(() =>
        this.questionRowRepo.save(
          this.questionRowRepo.create({
            surv_surveyquestion_id: questionId,
            isactive: 1,
            question_returnvalue: v4(),
            created_by: user.id,
            created_on: new Date(),
            modified_by: user.id,
            modified_on: new Date(),
          }),
        ),
      );

    let result = await Promise.all(answers);

    return {
      status: 'created',
      message: this.i18n.t('props.survey.blank_answers_created'),
      answers: result.map((item) => item.question_returnvalue),
    };
  }
}
