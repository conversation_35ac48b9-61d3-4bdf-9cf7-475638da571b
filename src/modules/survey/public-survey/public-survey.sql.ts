export const PUBLIC_SURVEY_UNSUBSCRIBE_GET_QUERY = `
SELECT
        ec.surv_survey_id,
        ec.xml_brand,
        ec.xml_email,
        ec.xml_phone2,
        ec.sec_company_id,
        optout_entry->>'link' AS optout_link,
        (sp.company_properties::jsonb->>'OptoutCDC')::int = 1 AS optout_cdc_enabled
    FROM email_customer ec
    JOIN sec_programsettings sp
        ON ec.sec_company_id = sp.sec_company_id    
    LEFT JOIN LATERAL json_array_elements(
        COALESCE(sp.company_properties::json->'optout', '[]'::json)
    ) AS optout_entry ON LOWER(optout_entry->>'brand') = LOWER(ec.xml_brand)
    WHERE ec.email_customer_id = $1;
`;
