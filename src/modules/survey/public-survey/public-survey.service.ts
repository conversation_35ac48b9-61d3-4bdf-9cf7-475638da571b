import {
  AnswerNotification,
  ClfCallEntity,
  CompanyEntity,
  CustomerBlacklistEntity,
  EmailCustomerEntity,
  SurveyAnswerEntity,
  SurveyAnswerItemEntity,
  SurveyPublished,
  SurveyQuestionEntity,
  SurvSurveyEntity,
  SurvI18nEntity,
  SurvMessagesLocal,
} from '@entities';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { PUBLIC_SURVEY_UNSUBSCRIBE_GET_QUERY } from './public-survey.sql';
import { MULTIPLE_CHOICE_QUESTION_TYPES } from '../question/question.params';
import { PresidioService } from '@libs/presidio';
import { QuestionType } from '@/shared/enums/question-type.enum';
import { AnswerItem } from '@/modules/database/entities/surv-surveyanswer.entity';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';
import { md5 } from '@/shared/utils/md5.util';
import { I18nService } from 'nestjs-i18n';
import { v4 } from 'uuid';
import { parseJSON } from 'date-fns';

const CryptoJS = require('crypto-js');

@Injectable()
export class PublicSurveyService {
  private surveyRepo: Repository<SurvSurveyEntity>;
  private answerRepo: Repository<SurveyAnswerEntity>;
  private answerItemRepo: Repository<SurveyAnswerItemEntity>;
  private questiionRepo: Repository<SurveyQuestionEntity>;
  private surveyPublishedRepo: Repository<SurveyPublished>;
  private ecRepo: Repository<EmailCustomerEntity>;
  private customerBlacklistRepo: Repository<CustomerBlacklistEntity>;
  private companyRepo: Repository<CompanyEntity>;
  private answerNotificationRepo: Repository<AnswerNotification>;
  private clfCallRepo: Repository<ClfCallEntity>;
  private survI18nRepo: Repository<SurvI18nEntity>;
  private survMessagesLocalRepo: Repository<SurvMessagesLocal>;

  constructor(
    private logger: ModuleLogger,
    private i18n: I18nService,
    @InjectDataSource() private dataSource: DataSource,
    private readonly presidioService: PresidioService,
  ) {
    this.surveyRepo = this.dataSource.getRepository(SurvSurveyEntity);
    this.ecRepo = this.dataSource.getRepository(EmailCustomerEntity);
    this.surveyPublishedRepo = this.dataSource.getRepository(SurveyPublished);
    this.customerBlacklistRepo = this.dataSource.getRepository(
      CustomerBlacklistEntity,
    );
    this.companyRepo = this.dataSource.getRepository(CompanyEntity);
    this.answerRepo = this.dataSource.getRepository(SurveyAnswerEntity);
    this.questiionRepo = this.dataSource.getRepository(SurveyQuestionEntity);
    this.answerItemRepo = this.dataSource.getRepository(SurveyAnswerItemEntity);
    this.answerNotificationRepo =
      this.dataSource.getRepository(AnswerNotification);
    this.clfCallRepo = this.dataSource.getRepository(ClfCallEntity);
    this.survI18nRepo = this.dataSource.getRepository(SurvI18nEntity);
    this.survMessagesLocalRepo =
      this.dataSource.getRepository(SurvMessagesLocal);
  }

  async getSurveyMessages(languages:string[], sec_company_id: number, surv_survey_id: number) {



    return this.survMessagesLocalRepo
      .createQueryBuilder('surv_messages_local')
      .where('surv_messages_local.sec_company_id = :sec_company_id', {
        sec_company_id,
      })
      .andWhere('surv_messages_local.language IN (:...languages)', {
        languages,
      })
      .getMany();
  }

  async getSurveyByKey(key: string) {
    let item = await this.ecRepo.findOne({
      where: { surv_key: key },
      select: {
        surv_survey_id: true,
        xml_brand: true,
        sec_company_id: true,
      },
    });

    if (!item)
      throw new NotFoundException(
        'props.survey.errors.survey_with_key_not_found',
      );

    let publishedSurvey = await this.surveyPublishedRepo.findOne({
      where: {
        surv_survey_id: item.surv_survey_id,
        brand_id: Number(item.xml_brand) || null,
      },
      select: {
        surv_survey_published_id: true,
        data: true,
        theme_data: true,
        additional_settings: true,
      },
    });

    if (!publishedSurvey)
      throw new NotFoundException(
        'props.survey.errors.survey_with_key_not_published',
      );

    let surveyChecks = await this.ecRepo
      .createQueryBuilder('ec')
      .select('coalesce(ec.survey_done, 0) = 1', 'survey_done')
      .addSelect(
        `(ec.send_date + (coalesce(s.survey_expire_time, 10) || ' days')::interval) < now()`,
        'is_expired',
      )
      .leftJoin('ec.survey', 's')
      .where('ec.surv_key =:key', { key })
      .getRawOne();

    // TODO is_expired = true if its Archived

    const surveyMessages = publishedSurvey.data?.languages?.length && await this.getSurveyMessages(
      publishedSurvey.data?.languages.map((l) => l.code),
      item.sec_company_id,
      publishedSurvey.surv_survey_id,
    );

    return {
      ...publishedSurvey,
      data: publishedSurvey.data?.data,
      connections: publishedSurvey.data?.connections,
      languages: publishedSurvey.data?.languages,
      default_language: publishedSurvey.data?.default_language,
      messages: surveyMessages,
      ...surveyChecks,
    };
  }

  async unsubscribe(encodedId: string) {
    const id = parseInt(Buffer.from(encodedId, 'base64').toString('utf8'), 10);
    const list = await this.surveyRepo.query(
      PUBLIC_SURVEY_UNSUBSCRIBE_GET_QUERY,
      [id],
    );
    const item = list[0];

    if (!item)
      throw new NotFoundException(
        'props.survey.errors.survey_with_key_not_found',
      );

    let company = await this.companyRepo.findOne({
      where: { sec_company_id: item.sec_company_id },
    });

    if (!company)
      throw new NotFoundException(
        'props.survey.errors.company_with_key_not_found',
      );

    if (item.optout_link) {
      if (item.optout_cdc_enabled) {
        const encryptedValue = this.encrypt(
          {
            brand: this.encodeBrand(item.xml_brand),
            countryExt: company.company_code.slice(-2),
            mail: item.xml_email,
          },
          '2lrRKQfHzxACgOG0D1hDDAK3a3b98zfe',
        );
        return {
          url: item.optout_link + encryptedValue,
        };
      }

      return {
        url: item.optout_link,
      };
    } else {
      await this.customerBlacklistRepo.save(
        this.customerBlacklistRepo.create({
          brand: item.xml_brand,
          customer_email: item.xml_email?.toLowerCase(),
          customer_email_md5: md5(item.xml_email?.toLowerCase()),
          phone: item.xml_phone2,
          phone_md5: md5(item.xml_phone2),
          sec_company_id: company.sec_company_id,
          date_blacklisted: new Date(),
        }),
      );

      return {
        email: item.xml_email,
        phone: item.xml_phone2,
        message: this.i18n.t('props.survey.unsubscribe_success'),
      };
    }
  }

  encodeBrand(brand: string) {
    switch (brand.toLowerCase()) {
      case 'bosch':
        return 'a01';
      case 'siemens':
        return 'a02';
      case 'constructa':
        return 'a03';
      case 'neff':
        return 'a04';
      case 'gaggenau':
        return 'a15';
      case 'solitaire':
        return 'a16';
      case 'inspiratiehuis 20|20':
      case 'inspiratiehuis':
        return 'p02';
      case 'benefits':
      case 'benefitsbybsh':
        return 'axx';
      default:
        return 'a01';
    }
  }

  encrypt(data, keyString) {
    const jsonStr = JSON.stringify(data);

    const key = CryptoJS.enc.Utf8.parse(keyString);
    const message = CryptoJS.enc.Utf8.parse(jsonStr);

    const encrypted = CryptoJS.AES.encrypt(message, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
      format: {
        stringify: function (cipherParams) {
          return cipherParams.ciphertext.toString(CryptoJS.enc.Base64);
        },
        parse: function () {
          throw new Error('Parsing not implemented');
        },
      },
    });

    return encodeURIComponent(encrypted.toString());
  }

  async saveSurvey(body: any) {
    const answers = Object.keys(body)
      .filter((key) => !isNaN(Number(key)))
      .reduce((acc, key) => {
        acc[key] = body[key];
        return acc;
      }, {});

    this.logger.debug(`Starting to save survey with key: ${body.key}`);
    this.logger.debug(
      `Extracted ${Object.keys(answers).length} numeric answer keys`,
    );
    try {
      const customer = await this.ecRepo.findOne({
        where: { surv_key: body.key },
      });

      if (!customer) {
        this.logger.error(`Customer not found with key: ${body.key}`);
        throw new NotFoundException('props.survey.errors.customer_not_found');
      }

      this.logger.debug(
        `Found customer with ID: ${customer.email_customer_id}`,
      );

      if (customer.survey_done) {
        this.logger.error(
          `Survey already completed for customer ID: ${customer.email_customer_id}`,
        );
        throw new BadRequestException(
          this.i18n.t('props.survey.errors.survey_already_done', {
            args: { customerId: customer.email_customer_id },
          }),
        );
      }

      const survey = await this.surveyRepo.findOne({
        where: { surv_survey_id: customer.surv_survey_id },
      });

      if (!survey) {
        this.logger.error(
          `Survey not found with ID: ${customer.surv_survey_id}`,
        );
        throw new NotFoundException('props.survey.errors.not_found');
      }

      this.logger.debug(`Found survey with ID: ${survey.surv_survey_id}`);

      try {
        const surveyAnswer = this.answerRepo.create({
          email_customer_id: customer.email_customer_id,
          surv_survey_id: customer.surv_survey_id,
          sec_company_id: customer.sec_company_id,
          creation_date_system: new Date().toISOString(),
          creation_date: this.validDate(body.date),
          modification_date: new Date(),
          creator: 'system',
          modifier: 'system',
          browser: body.browser,
          browser_version: body.browser_version,
          os: body.os,
          os_version: body.os_version,
          device: body.device,
          comments_length: 0,
          comments_neg_length: 0,
          comments_pos_length: 0,
          has_text: 0,
          nps_value: 0,
          publication_state: 0,
        });

        await this.answerRepo.save(surveyAnswer);
        this.logger.debug(
          `Created and saved survey answer with ID: ${surveyAnswer.surv_surveyanswer_id}`,
        );

        const promises: Promise<any>[] = [];
        const answerItems: AnswerItem[] = [];

        this.logger.debug(`Fetching questions for answer processing`);
        const questions = await this.questiionRepo.find({
          where: {
            surv_surveyquestion_id: In(Object.keys(answers || [])),
          },
        });

        this.logger.debug(
          `Processing ${Object.keys(answers || []).length} answers`,
        );
        for (const [questionId, value] of Object.entries(answers)) {
          try {
            let id = +questionId;
            const question = questions.find(
              (q) => q.surv_surveyquestion_id === id,
            );

            if (!question) {
              this.logger.debug(`Question with ID ${id} not found, skipping`);
              continue;
            }

            let returnValue: any = value;
            if (
              MULTIPLE_CHOICE_QUESTION_TYPES.includes(question.questiontype) &&
              Array.isArray(value)
            ) {
              returnValue = JSON.stringify(value);
            }

            const answerItem = this.answerItemRepo.create({
              surv_surveyanswer_id: surveyAnswer.surv_surveyanswer_id,
              surv_surveyquestion_id: id,
              creation_date: new Date(),
              return_value: returnValue,
            });

            await this.answerItemRepo.save(answerItem);
            this.logger.debug(`Saved answer item for question ID: ${id}`);

            answerItems.push({
              id: answerItem.surv_surveyanswer_id,
              type: question.questiontype,
              value: returnValue,
              question_id: id,
            });

            if (id === survey.nps_question) {
              surveyAnswer.nps_value = +returnValue;
              this.logger.debug(`Updated NPS value: ${surveyAnswer.nps_value}`);
            }

            if (
              [survey.clf_question1, survey.clf_question2].includes(id) &&
              returnValue
            ) {
              let length = +returnValue.length || 0;
              surveyAnswer.has_text = 1;
              surveyAnswer.comments_length =
                (+surveyAnswer.comments_length || 0) + length;

              if (id === survey.clf_question1)
                surveyAnswer.comments_neg_length = length;
              if (id === survey.clf_question2)
                surveyAnswer.comments_pos_length = length;

              this.logger.debug(
                `Updated comments length for question ID ${id}: ${length}`,
              );
            }
          } catch (error) {
            this.logger.error(
              `Error processing answer for question ID ${questionId}: ${error.message}`,
              error.stack,
            );
          }
        }

        const skipAnswerTypes = [
          QuestionType.OpenQuestion,
          QuestionType.SingleTextboxQuestion,
        ];
        surveyAnswer.answers = answerItems
          .filter((item) => !skipAnswerTypes.includes(item.type))
          .map((item) => ({
            ...item,
            value: parseJSON(item.value) || item.value,
            id: undefined,
          }));
        await this.answerRepo.save(surveyAnswer);
        this.logger.debug(
          `Updated survey answer with ${answerItems.length} items`,
        );

        customer.survey_done = 1;
        customer.xml_language = body.language || customer.xml_language;
        customer.send_date = customer.send_date || surveyAnswer.creation_date;

        promises.push(this.ecRepo.save(customer));
        this.logger.debug(
          `Updating customer record: ${customer.email_customer_id}`,
        );

        const notification = this.answerNotificationRepo.create({
          q_notification_id: v4(),
          surveyAnswerId: surveyAnswer.surv_surveyanswer_id,
          isProcessed: 0,
          companyId: customer.sec_company_id,
        });

        promises.push(this.answerNotificationRepo.save(notification));
        this.logger.debug(
          `Created notification for answer ID: ${surveyAnswer.surv_surveyanswer_id}`,
        );

        if (!survey.is_clf) {
          this.logger.debug(`Processing CLF for non-CLF survey`);
          let clf = await this.clfCallRepo.findOne({
            where: {
              sec_company_id: customer.sec_company_id,
              email_customer_id: customer.email_customer_id,
            },
          });

          if (!clf) {
            const newClf = this.clfCallRepo.create({
              email_customer_id: customer.email_customer_id,
              c_answer_id: surveyAnswer.surv_surveyanswer_id,
              c_nps_q_id: survey.nps_question,
              c_callback_q_id: survey.callback_question,
              c_clf1_q_id: survey.clf_question1,
              c_clf2_q_id: survey.clf_question2,
              sec_company_id: customer.sec_company_id,
            });

            promises.push(this.clfCallRepo.save(newClf));
            this.logger.debug(
              `Created new CLF record for customer: ${customer.email_customer_id}`,
            );
          }
        }

        await Promise.all(promises);
        this.logger.debug(`All promises resolved successfully`);

        const openQuestions = answerItems.filter(
          (a) => a.type === QuestionType.OpenQuestion,
        );
        this.logger.debug(
          `Processing ${openQuestions.length} open question answers`,
        );

        await this.pushAnswerProcessing(
          customer.sec_company_id,
          openQuestions,
          customer.xml_language || 'en',
        );

        this.logger.debug(
          `Survey saved successfully for customer: ${customer.email_customer_id}`,
        );
        return { success: true };
      } catch (error) {
        this.logger.error(
          `Error saving survey answers: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    } catch (error) {
      this.logger.error(`Error in saveSurvey: ${error.message}`, error.stack);
      throw error;
    }
  }

  async pushAnswerProcessing(
    companyId: number,
    answerItems: AnswerItem[],
    language: string,
  ) {
    this.logger.debug(
      `Starting to process ${answerItems.length} answers for company ID: ${companyId}, language: ${language}`,
    );

    try {
      for (const answer of answerItems) {
        try {
          this.logger.debug(
            `Processing answer ID: ${answer.id}, question ID: ${answer.question_id}`,
          );
          await this.presidioService.pushAnswer(
            companyId,
            answer,
            language,
            answer.question_id,
          );
          this.logger.debug(`Successfully processed answer ID: ${answer.id}`);
        } catch (error) {
          this.logger.error(
            `Error processing answer ID: ${answer.id}, question ID: ${answer.question_id}: ${error.message}`,
            error.stack,
          );
        }
      }
      this.logger.debug(
        `Completed processing all ${answerItems.length} answers`,
      );
    } catch (error) {
      this.logger.error(
        `Error in pushAnswerProcessing: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private validDate(inputDate: Date): Date {
    if (!inputDate) return new Date();
    const date = new Date(inputDate);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (date < yesterday || date > tomorrow) {
      return new Date();
    }
    return date;
  }
}
