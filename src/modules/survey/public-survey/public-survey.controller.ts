import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { PublicSurveyService } from './public-survey.service';
import { ApiBody } from '@nestjs/swagger';

@Controller('public-survey')
export class PublicSurveyController {
  constructor(private publicSurveyService: PublicSurveyService) {}

  @Get(':surveyKey')
  async getSurveyByKey(@Param('surveyKey') surveyKey: string) {
    return this.publicSurveyService.getSurveyByKey(surveyKey);
  }

  @Post('/unsubscribe/:encodedId')
  async unsubscribe(@Param('encodedId') encodedId: string) {
    return this.publicSurveyService.unsubscribe(encodedId);
  }

  @Post()
  @ApiBody({ type: Object })
  async saveSurvey(@Body() body: any) {
    return this.publicSurveyService.saveSurvey(body);
  }
}
