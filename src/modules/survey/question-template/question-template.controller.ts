import { Auth } from '@/shared/decorators';
import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { QuestionTemplateService } from './question-template.service';
import { ApiTags } from '@nestjs/swagger';
import {
  CreateSurveyQuestionTemplateDto,
  UpdateSurveyQuestionTemplateDto,
} from './dto/post-questiontemplate.dto';
import { CreateBlankSurveyAnswerDto } from '../question/dto/post-survey-question.dto';

@Controller({
  path: 'survey/question_template',
  version: '2',
})
@ApiTags('Survey Question Template')
@Auth()
export class QuestionTemplateController {
  constructor(private questionTemplateService: QuestionTemplateService) {}

  @Get()
  list() {
    return this.questionTemplateService.list();
  }

  @Get(':id')
  item(@Param('id') id: number) {
    return this.questionTemplateService.item(id);
  }

  @Post(':questionTemplateId/publish')
  publishTemplate(@Param('questionTemplateId') id: number) {
    this.questionTemplateService.publishTemplate(id);
  }

  @Post('blank')
  createBlankQuestion() {
    return this.questionTemplateService.createBlankQuestion();
  }
  @Post(':questionTemplateId/blank/answer')
  createBlankAnswer(
    @Param('questionTemplateId') questionTemplateId: number,
    @Body() body: CreateBlankSurveyAnswerDto,
  ) {
    return this.questionTemplateService.createBlankAnswers(
      questionTemplateId,
      body.count,
    );
  }

  @Post()
  createItem(@Body() body: CreateSurveyQuestionTemplateDto) {
    return this.questionTemplateService.create(body);
  }

  @Post(':id')
  updateItem(
    @Param('id') id: number,
    @Body() body: UpdateSurveyQuestionTemplateDto,
  ) {
    return this.questionTemplateService.update(id, body);
  }

  @Post(':id/copy')
  copyTemplate(@Param('id') id: number) {
    return this.questionTemplateService.copy(id);
  }

  @Delete(':id')
  deleteItem(@Param('id') id: number) {
    return this.questionTemplateService.delete(id);
  }
}
