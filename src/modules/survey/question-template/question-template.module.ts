import { Module } from '@nestjs/common';
import { QuestionTemplateController } from './question-template.controller';
import { QuestionTemplateService } from './question-template.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  SurveyQuestionTemplateEntity,
  SurveyQuestionTemplateRowEntity,
} from '@entities';
import { SurveyModule } from '../survey.module';
import { SurveyPublishModule } from '../publish/survey-publish.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SurveyQuestionTemplateEntity,
      SurveyQuestionTemplateRowEntity,
    ]),
    SurveyModule,
    SurveyPublishModule,
  ],
  controllers: [QuestionTemplateController],
  providers: [QuestionTemplateService],
})
export class QuestionTemplateModule {}
