export const QUESTION_TEMPLATE_LIST_QUERY = `
    WITH template_languages AS (SELECT surv_surveyquestion_template_id,  
                                  JSONB_AGG((lang_obj ->> 'language')::TEXT) AS languages  
                           FROM surv_surveyquestion_template,  
                                LATERAL JSONB_ARRAY_ELEMENTS(data #> '{properties,languages}') AS lang_obj  
                           WHERE sec_company_id = $1  
                           GROUP BY surv_surveyquestion_template_id),  
    matching_surveys AS (select t.surv_surveyquestion_template_id                                     as template_id,  
                                JSONB_AGG(jsonb_build_object('id', q.surv_survey_id, 'name', s.name)) AS survey_info  
                         from surv_surveyquestion q  
                              left join surv_survey s on q.surv_survey_id = s.surv_survey_id,  
                              template_languages t  
                         where surv_survquest_temp_id in (t.surv_surveyquestion_template_id)  
                         group by t.surv_surveyquestion_template_id)  
SELECT t.surv_surveyquestion_template_id,  
      st.title as name,  
      st.description,  
      st.questiontype, 
       st.created_on, 
       st.created_by, 
       st.modified_on, 
       st.modified_by, 
       st.published_on, 
       t.languages,  
      m.survey_info  
FROM template_languages t  
LEFT JOIN  matching_surveys m ON t.surv_surveyquestion_template_id = m.template_id  
INNER JOIN surv_surveyquestion_template st on t.surv_surveyquestion_template_id = st.surv_surveyquestion_template_id;
`;

export const CHECK_LINKED_SURVEY_QUERY = `
  select COUNT(q.surv_survquest_temp_id) as id
                         from surv_surveyquestion q  
                              left join surv_survey s on q.surv_survey_id = s.surv_survey_id
                         where surv_survquest_temp_id = $1
                         LIMIT 1
                         `;
