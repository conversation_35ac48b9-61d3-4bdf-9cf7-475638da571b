import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsInt,
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested,
} from 'class-validator';
import {
  CreateSurveyQuestionDto,
  SurveyQuestionProperties,
} from '../../question/dto/post-survey-question.dto';

export class UpdateSurveyQuestionTemplateDto {
  @Type()
  @IsNumber()
  @ApiProperty({ default: 1 })
  template_id: number;

  @Type()
  @IsString()
  @ApiProperty({ default: 'title' })
  name: string;

  @Type()
  @IsString()
  @ApiProperty({ default: 'description' })
  description: string;

  @Type()
  @ApiProperty({ default: {} })
  @IsObject()
  @IsOptional()
  question: any;
}

export class CreateSurveyQuestionTemplatePropDto {
  @Type()
  @ApiProperty()
  @IsInt()
  @IsPositive()
  @IsOptional()
  id: number;

  @Type()
  @ApiProperty()
  @IsString()
  @IsOptional()
  name: string;

  @Type()
  @ApiProperty()
  @IsString()
  @IsOptional()
  description: string;

  @Type()
  @ApiProperty()
  @IsString()
  @IsOptional()
  type: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsString()
  icon: string;

  @Type()
  @IsOptional()
  @ApiProperty()
  @IsNumber()
  is_active: number;

  @ApiProperty({ type: () => SurveyQuestionProperties })
  @ValidateNested()
  @Type(() => SurveyQuestionProperties)
  properties: SurveyQuestionProperties;
}

export class CreateSurveyQuestionTemplateDto {
  @Type()
  @IsInt()
  @IsPositive()
  @ApiProperty({ default: 1 })
  id: number;

  @Type()
  @IsString()
  @ApiProperty({ default: 'name' })
  name: string;

  @Type()
  @IsString()
  @ApiProperty({ default: 'description' })
  description: string;

  @ApiProperty({ type: () => CreateSurveyQuestionTemplatePropDto })
  @ValidateNested()
  @Type(() => CreateSurveyQuestionTemplatePropDto)
  question: CreateSurveyQuestionTemplatePropDto;
}
