import { parseJSON } from '@/shared/utils';
import {
  SurveyQuestionEntity,
  SurveyQuestionRowEntity,
  SurveyQuestionTemplateEntity,
  SurveyQuestionTemplateRowEntity,
  SurvSurveyEntity,
} from '@entities';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import {
  CreateSurveyQuestionTemplateDto,
  UpdateSurveyQuestionTemplateDto,
} from './dto/post-questiontemplate.dto';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import {
  CHECK_LINKED_SURVEY_QUERY,
  QUESTION_TEMPLATE_LIST_QUERY,
} from './question-template.sql';
import { QUESTION_TEMPLATE_TYPES } from '../question/question.params';
import { SurveyUtils } from '../survey.utils';
import { v4 } from 'uuid';
import { SurveyPublishService } from '../publish/survey-publish.service';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class QuestionTemplateService {
  private surveyRepo: Repository<SurvSurveyEntity>;
  private questionRepo: Repository<SurveyQuestionEntity>;
  private questionTemplateRepo: Repository<SurveyQuestionTemplateEntity>;
  private questionRowRepo: Repository<SurveyQuestionRowEntity>;
  private questionTemplateRowRepo: Repository<SurveyQuestionTemplateRowEntity>;
  constructor(
    private cls: ClsService,
    private i18n: I18nService,
    private surveyUtils: SurveyUtils,
    private readonly surveyPublishService: SurveyPublishService,
    @InjectDataSource() private dataSource: DataSource,
  ) {
    this.surveyRepo = this.dataSource.getRepository(SurvSurveyEntity);

    this.questionRepo = this.dataSource.getRepository(SurveyQuestionEntity);

    this.questionTemplateRepo = this.dataSource.getRepository(
      SurveyQuestionTemplateEntity,
    );

    this.questionRowRepo = this.dataSource.getRepository(
      SurveyQuestionRowEntity,
    );

    this.questionTemplateRowRepo = this.dataSource.getRepository(
      SurveyQuestionTemplateRowEntity,
    );
  }
  async createBlankQuestion() {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let question = await this.questionTemplateRepo.save({
      sec_company_id: company.id,
      created_by: user.id,
      created_on: new Date(),
      modified_by: user.id,
      modified_on: new Date(),
    });

    return {
      status: 'created',
      message: this.i18n.t('props.survey.blank_question_template_created'),
      questionId: question.surv_surveyquestion_template_id,
    };
  }

  async createBlankAnswers(qtId: number, count: number) {
    const { user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let question = await this.questionTemplateRepo.findOne({
      where: { surv_surveyquestion_template_id: qtId },
    });

    if (!question)
      throw new NotFoundException('props.survey.errors.question_not_found');

    let answers = Array(count)
      .fill(0)
      .map(() =>
        this.questionTemplateRowRepo.save(
          this.questionTemplateRowRepo.create({
            surv_surveyquestion_template_id: qtId,
            isactive: 1,
            question_returnvalue: v4(),
            created_by: user.id,
            created_on: new Date(),
            modified_by: user.id,
            modified_on: new Date(),
          }),
        ),
      );

    let result = await Promise.all(answers);

    return {
      status: 'created',
      message: this.i18n.t(
        'props.survey.blank_question_template_answers_created',
      ),
      answers: result.map((item) => item.question_returnvalue),
    };
  }

  async publishTemplate(id: number) {
    const { company, user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let questionTemplate = await this.questionTemplateRepo.findOne({
      where: {
        surv_surveyquestion_template_id: id,
        sec_company_id: company.id,
      },
      select: {
        surv_surveyquestion_template_id: true,
        sec_company_id: true,
        data: true,
        questiontype: true,
        question_required: true,
        show_other: true,
        question_isnps: true,
        csi: true,
        answertable_id: true,
        created_on: true,
      },
    });

    if (!questionTemplate) {
      throw new NotFoundException(
        'props.survey.errors.question_template_not_found',
      );
    }

    const qids = await this.questionRepo.find({
      where: {
        question_template_id: questionTemplate.surv_surveyquestion_template_id,
      },
      select: {
        surv_surveyquestion_id: true,
        surv_survey_id: true,
      },
    });

    let surveyIds = new Set(qids.map((item) => item.surv_survey_id));

    let surveys: SurvSurveyEntity[];
    if (surveyIds.size === 0) {
      surveys = [];
    } else {
      surveys = await this.surveyRepo.find({
        where: {
          surv_survey_id: In([...surveyIds]),
          sec_company_id: company.id,
        },
        select: {
          surv_survey_id: true,
          editing_dt: true,
          survey_logic: true,
          publishes: {
            surv_survey_published_id: true,
            created_on: true,
          },
        },
        relations: ['publishes'],
      });
    }

    let questions = await this.questionRepo.find({
      where: {
        question_template_id: questionTemplate.surv_surveyquestion_template_id,
        surv_survey_id: In([...surveyIds]),
      },
    });

    await this.questionRepo.update(
      {
        surv_surveyquestion_id: In(
          questions.map((item) => item.surv_surveyquestion_id),
        ),
      },
      {
        questiontype: questionTemplate.questiontype,
        question_required: questionTemplate.question_required,
        show_other: questionTemplate.show_other,
        question_isnps: questionTemplate.question_isnps,
        csi_number: questionTemplate.csi,
        answertable_id: questionTemplate.answertable_id,
        modified_by: user.id,
        modified_on: new Date(),
      },
    );

    // UPDATE QUESTION ROWS
    const templateRows = await this.questionTemplateRowRepo.find({
      where: {
        surv_surveyquestion_template_id: id,
        isactive: 1,
      },
    });

    if (templateRows.length > 0) {
      const questionIds = questions.map((q) => q.surv_surveyquestion_id);

      const existingRows = questionIds.length
        ? await this.questionRowRepo.find({
            where: {
              surv_surveyquestion_id: In(questionIds),
              isactive: 1,
            },
          })
        : [];

      const questionRowMap = new Map();
      existingRows.forEach((row) => {
        const key = `${row.surv_surveyquestion_id}-${row.question_returnvalue}`;
        questionRowMap.set(key, row);
      });

      const rowsToUpdate = [];
      const rowsToInsert = [];

      for (const question of questions) {
        for (const templateRow of templateRows) {
          const key = `${question.surv_surveyquestion_id}-${templateRow.question_returnvalue}`;
          const existingRow = questionRowMap.get(key);

          if (existingRow) {
            rowsToUpdate.push({
              id: existingRow.surv_surveyquestionrow_id,
              question_label: templateRow.question_label,
              question_shortlabel: templateRow.question_shortlabel,
              modified_by: user.id,
              modified_on: new Date(),
            });
          } else {
            rowsToInsert.push({
              surv_surveyquestion_id: question.surv_surveyquestion_id,
              ordernumber: templateRow.ordernumber,
              question_label: templateRow.question_label,
              question_shortlabel: templateRow.question_shortlabel,
              question_returnvalue: templateRow.question_returnvalue,
              isactive: 1,
              surv_survquest_temp_id:
                templateRow.surv_surveyquestionrow_template_id,
              next_surveypart_id: 0,
              next_question_id: 0,
              created_by: user.id,
              created_on: new Date(),
              modified_by: user.id,
              modified_on: new Date(),
            });
          }
        }
      }

      if (rowsToUpdate.length > 0) {
        await this.dataSource.transaction(async (manager) => {
          for (const row of rowsToUpdate) {
            await manager
              .createQueryBuilder()
              .update(SurveyQuestionRowEntity)
              .set({
                question_label: row.question_label,
                question_shortlabel: row.question_shortlabel,
                modified_by: row.modified_by,
                modified_on: row.modified_on,
              })
              .where('surv_surveyquestionrow_id = :id', { id: row.id })
              .execute();
          }
        });
      }

      if (rowsToInsert.length > 0) {
        await this.dataSource.transaction(async (manager) => {
          await manager
            .createQueryBuilder()
            .insert()
            .into(SurveyQuestionRowEntity)
            .values(rowsToInsert)
            .execute();
        });
      }
    }

    let excludedSurveys = surveys.filter((survey) => {
      return survey.publishes.some(
        (item) =>
          item.created_on.getTime() > questionTemplate.created_on.getTime(),
      );
    });

    surveys = surveys.filter((survey) => {
      return !excludedSurveys.some(
        (item) => item.surv_survey_id === survey.surv_survey_id,
      );
    });

    for (let survey of surveys) {
      let surveyLogic = survey.survey_logic;
      if (surveyLogic?.data && Array.isArray(surveyLogic.data)) {
        for (let [pageIndex, page] of Object.entries(surveyLogic.data) as any) {
          for (let [questionIndex, question] of Object.entries(
            page.questions,
          ) as any) {
            if (question.question_template_id === id) {
              surveyLogic.data[pageIndex].questions[questionIndex] =
                questionTemplate?.data;
            }
          }
        }
      }

      await this.surveyPublishService.publish(survey.surv_survey_id);
    }

    // Update survey logic
    if (surveys.length > 0) {
      await this.dataSource.transaction(async (manager) => {
        for (const survey of surveys) {
          await manager
            .createQueryBuilder()
            .update(SurvSurveyEntity)
            .set({
              survey_logic: survey.survey_logic,
              modified_by: user.id,
              modified_on: new Date(),
            })
            .where('surv_survey_id = :id', { id: survey.surv_survey_id })
            .execute();
        }
      });
    }

    this.questionTemplateRepo.update(
      {
        surv_surveyquestion_template_id: id,
      },
      { published_on: new Date() },
    );

    return {
      message: this.i18n.t('props.survey.question_template_published'),
      notUpdatedMessage: this.i18n.t(
        'props.survey.errors.surveys_not_updated_due_local_changes',
        {
          args: {
            surveys: `(${excludedSurveys
              .map((s) => s.internal_name)
              .join(', ')})`,
          },
        },
      ),
    };
  }

  async create(params: CreateSurveyQuestionTemplateDto) {
    let { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let item = new SurveyQuestionTemplateEntity();
    if (params.id) {
      item.surv_surveyquestion_template_id = params.id;
    }
    item.title = params.name;
    item.description = params.description;
    item.data = params.question;
    item.questiontype = this.surveyUtils.questionType({
      layout: params.question.properties.layout,
      type: params.question.type,
      selection_type: params.question.properties.selection_type,
    })?.id;
    item.sec_company_id = company.id;

    item.modified_by = user.id;
    item.modified_on = new Date();
    item.created_by = user.id;
    item.created_on = new Date();

    await item.save();

    return {
      message: this.i18n.t('props.survey.question_template_created'),
      template: await this.item(item.surv_surveyquestion_template_id),
    };
  }

  async list() {
    let { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let list = await this.questionTemplateRepo.query(
      QUESTION_TEMPLATE_LIST_QUERY,
      [company.id],
    );

    let result = list.map((item) => ({
      ...item,
      survey_usage: item.survey_info?.length || 0,
      survey_info: undefined,
      questiontype: undefined,
      question_type: QUESTION_TEMPLATE_TYPES.find(
        (qt) => qt.id === item.questiontype,
      )?.type,
    }));

    return result.sort(
      (a, b) =>
        new Date(b.modified_on).getTime() - new Date(a.modified_on).getTime(),
    );
  }

  async item(id: number) {
    let item = await this.questionTemplateRepo.findOne({
      where: { surv_surveyquestion_template_id: id },
    });

    if (!item)
      throw new NotFoundException(
        'props.survey.errors.question_template_not_found',
      );

    return {
      template_id: item.surv_surveyquestion_template_id,
      name: item.title,
      description: item.description,
      question:
        typeof item.data === 'string' ? parseJSON(item.data) : item.data,
    };
  }

  async update(id: number, body: UpdateSurveyQuestionTemplateDto) {
    let { user } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let item = await this.questionTemplateRepo.findOne({
      where: { surv_surveyquestion_template_id: id },
    });

    if (!item)
      throw new NotFoundException(
        'props.survey.errors.question_template_not_found',
      );

    item.title = body.name;
    item.description = body.description;
    item.data = body.question;
    item.modified_by = user.id;
    item.modified_on = new Date();

    await item.save();

    return {
      message: this.i18n.t('props.survey.question_template_updated'),
    };
  }

  async copy(id: number) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const originalTemplate = await this.questionTemplateRepo.findOne({
      where: { surv_surveyquestion_template_id: id },
    });

    if (!originalTemplate) {
      throw new NotFoundException(
        'props.survey.errors.question_template_not_found',
      );
    }

    const newTemplate = new SurveyQuestionTemplateEntity();

    newTemplate.title = `${originalTemplate.title} - Copy`;
    newTemplate.description = originalTemplate.description;
    newTemplate.data = originalTemplate.data;
    newTemplate.questiontype = originalTemplate.questiontype;
    newTemplate.sec_company_id = company.id;

    newTemplate.created_by = user.id;
    newTemplate.created_on = new Date();
    newTemplate.modified_by = user.id;
    newTemplate.modified_on = new Date();

    await newTemplate.save();

    return {
      message: this.i18n.t('props.survey.question_template_cloned'),
      template: await this.item(newTemplate.surv_surveyquestion_template_id),
    };
  }

  async delete(id: number) {
    let item = await this.questionTemplateRepo.findOne({
      where: { surv_surveyquestion_template_id: id },
    });

    if (!item)
      throw new NotFoundException(
        'props.survey.errors.question_template_not_found',
      );

    let [linkedSurvey] = await this.questionTemplateRepo.query(
      CHECK_LINKED_SURVEY_QUERY,
      [item.surv_surveyquestion_template_id],
    );

    if (+linkedSurvey?.id) {
      throw new BadRequestException('props.survey.errors.template_linked');
    }

    if (!item)
      throw new NotFoundException(
        'props.survey.errors.question_template_not_found',
      );

    await this.questionTemplateRepo.remove(item);

    return {
      message: this.i18n.t('props.survey.question_template_deleted'),
    };
  }
}
