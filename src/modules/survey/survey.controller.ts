import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from '@/shared/guards/auth.guard';

import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { GetSurveyDetailsResponse } from './dto/get-survey-details.dto';
import { GetSurveyListResponse } from './dto/get-survey-list.dto';

import { SurveyService } from './survey.service';

@ApiTags('survey')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'survey',
})
export class SurveyController {
  constructor(private surveyService: SurveyService) {}

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Survey list/Touchpoints list',
    type: GetSurveyListResponse,
  })
  @Get('/')
  getSurveyList() {
    return this.surveyService.list();
  }

  @ApiOkResponse({
    description: 'Survey details/Touchpoints list',
    type: GetSurveyDetailsResponse,
  })
  @ApiQuery({ name: 'brands', required: false })
  @ApiQuery({ name: 'questions', required: false })
  @UseGuards(AuthGuard)
  @Get('/:id')
  getSurveyDetails(
    @Param('id') id: number,
    @Query('brands') brands?: boolean,
    @Query('questions') questions?: boolean,
  ) {
    return this.surveyService.getSurveyDetails(id, {
      brands,
      questions,
    });
  }
}
