import { TemplateBaseEntity } from '@entities';
import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyTemplateService } from './survey-template.service';
import { SurveyTemplateController } from './survey-template.controller';

@Module({
  imports: [TypeOrmModule.forFeature([TemplateBaseEntity])],
  controllers: [SurveyTemplateController],
  providers: [SurveyTemplateService],
})
export class SurveyTemplateModule {}
