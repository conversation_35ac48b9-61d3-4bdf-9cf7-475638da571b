import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SurveyTemplateService } from './survey-template.service';
import { Auth } from '@/shared/decorators';

@Controller({
  path: 'survey/template',
  version: '2',
})
@ApiTags('Survey Template')
@Auth()
export class SurveyTemplateController {
  constructor(private surveyTemplateService: SurveyTemplateService) {}

  @Get('/base')
  baseTemplates() {
    return this.surveyTemplateService.baseTemplates();
  }

  @Get('/base/:id')
  baseTemplate(@Param('id') id: number) {
    return this.surveyTemplateService.baseTemplate(id);
  }
}
