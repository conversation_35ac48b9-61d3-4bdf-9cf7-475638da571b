import { ClsProperty } from '@/config/contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { TemplateBaseEntity } from '@entities';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { Repository } from 'typeorm';

@Injectable()
export class SurveyTemplateService {
  constructor(
    private cls: ClsService,
    @InjectRepository(TemplateBaseEntity)
    private readonly templateBaseRepo: Repository<TemplateBaseEntity>,
  ) {}

  async baseTemplates() {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let list = await this.templateBaseRepo.find({
      select: {
        template_base_id: true,
        template_name: true,
      },
      where: {
        sec_company_id: company.id,
      },
      order: {
        template_name: 'ASC',
      },
    });

    return list.map((item) => ({
      id: item.template_base_id,
      name: item.template_name,
    }));
  }

  async baseTemplate(id: number) {
    let item = await this.templateBaseRepo.findOne({
      where: { template_base_id: id },
      select: {
        template_base_id: true,
        template_name: true,
        template_text: true,
      },
    });

    if (!item)
      throw new NotFoundException('props.survey.errors.template_not_found');

    return {
      id: item.template_base_id,
      name: item.template_name,
      html: item.template_text,
    };
  }
}
