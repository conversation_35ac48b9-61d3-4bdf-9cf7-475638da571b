import {
  ImportChannelEntity,
  SurveyDepartment,
  SurveyImportChannelEntity,
  SurveyPublished,
  SurveyQuestionEntity,
  SurveyQuestionRowEntity,
  SurvSurveyEntity,
} from '@entities';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyPublishController } from './survey-publish.controller';
import { SurveyPublishService } from './survey-publish.service';
import { SurveyModule } from '../survey.module';
import { ProgramSettingsModule } from '@/modules/common/program-settings/program-settings.module';
import { CompanyModule } from '@/modules/company/company.module';
import { SurveyQuestionModule } from '../question/question.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SurvSurveyEntity,
      SurveyQuestionEntity,
      SurveyQuestionRowEntity,
      ImportChannelEntity,
      SurveyImportChannelEntity,
      SurveyPublished,
      SurveyDepartment,
    ]),
    SurveyModule,
    SurveyQuestionModule,
    ProgramSettingsModule,
    CompanyModule,
  ],
  controllers: [SurveyPublishController],
  providers: [SurveyPublishService],
  exports: [SurveyPublishService],
})
export class SurveyPublishModule {}
