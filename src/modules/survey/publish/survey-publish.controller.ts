import { Auth } from '@/shared/decorators';
import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { SurveyPublishService } from './survey-publish.service';
import {
  PublishSurveyDto,
  SaveSurveyKeyDto,
  UpdateSurveyPublishConfigDto,
} from './dto/post-survey-publish.dto';

@Controller({
  path: 'survey/:surveyId/publish',
  version: '2',
})
@ApiTags('Survey Publish')
@Auth()
export class SurveyPublishController {
  constructor(private publishService: SurveyPublishService) {}

  @Get('/config')
  @ApiParam({ name: 'surveyId', type: 'number' })
  config(@Param('surveyId') surveyId: number) {
    return this.publishService.getConfig(surveyId);
  }

  @Post('/config')
  @ApiParam({ name: 'surveyId', type: 'number' })
  updateConfig(
    @Param('surveyId') surveyId: number,
    @Body() body: UpdateSurveyPublishConfigDto,
  ) {
    return this.publishService.updateConfig(surveyId, body);
  }

  @Post()
  @ApiParam({ name: 'surveyId', type: 'number' })
  publish(@Param('surveyId') surveyId: number, @Body() body: PublishSurveyDto) {
    return this.publishService.publish(surveyId, body);
  }

  @Get('/survey_key')
  @ApiParam({ name: 'surveyId', type: 'number' })
  getSurveyKey(@Param('surveyId') surveyId: number) {
    return this.publishService.getSurveyKey(surveyId);
  }

  @Post('/survey_key')
  @ApiParam({ name: 'surveyId', type: 'number' })
  saveSurveyKey(
    @Param('surveyId') surveyId: number,
    @Body() body: SaveSurveyKeyDto,
  ) {
    return this.publishService.saveSurveyKey(surveyId, body);
  }
}
