import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateNested,
} from 'class-validator';

export class UpdateSurveyPublishConfigDto {
  @Type()
  @IsUUID()
  @IsOptional()
  @ApiProperty({ required: false })
  surv_uuid: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  @IsInt()
  @Min(1)
  survey_expire_time: number;

  @Type()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Object,
    default: {
      sftp_server: true,
      api_integration: false,
    },
  })
  selected: Record<string, boolean>;
}

export class SaveSurveyKeyDto {
  @Type()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ required: true })
  key: string;
}



export class PublicLinkDto {
  @Type()
  @IsInt()
  @ApiProperty({ type: 'number' })
  brand_id: number;

  @Type()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ type: 'string' })
  link_id: string;
}

export class PublishSurveyDto {
  @Type(() => PublicLinkDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ApiProperty({
    required: false,
    type: [PublicLinkDto],
  })
  public_links: PublicLinkDto[];
}