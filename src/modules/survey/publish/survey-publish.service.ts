import {
  ImportChannelEntity,
  SurveyDepartment,
  SurveyImportChannelEntity,
  SurveyPublished,
  SurveyQuestionEntity,
  SurveyQuestionRowEntity,
  SurvSurveyEntity,
} from '@entities';
import {
  BadRequestException,
  ConflictException,
  HttpException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, In, Repository } from 'typeorm';
import { SurveyService } from '../survey.service';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents';
import { SurveyUtils } from '../survey.utils';
import { ProgramSettingsService } from '@/modules/common/program-settings/program-settings.service';
import { CompanyService } from '@/modules/company/company.service';
import {
  PublishSurveyDto,
  SaveSurveyKeyDto,
  UpdateSurveyPublishConfigDto,
} from './dto/post-survey-publish.dto';
import { compareListItems } from '@/shared/utils';
import {
  SurveyQuestionAnswerDto,
  UpdateSurveyQuestionDto,
} from '../question/dto/post-survey-question.dto';
import { SurveyQuestionService } from '../question/question.service';
import { QuestionType } from '@/shared/enums/question-type.enum';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class SurveyPublishService {
  constructor(
    private i18n: I18nService,
    private programSettings: ProgramSettingsService,
    private companyService: CompanyService,
    private cls: ClsService,
    private surveyService: SurveyService,
    private surveyQuestionService: SurveyQuestionService,
    private surveyUtils: SurveyUtils,
    @InjectRepository(SurveyQuestionEntity)
    private questionRepository: Repository<SurveyQuestionEntity>,
    @InjectRepository(SurveyQuestionRowEntity)
    private questionRowResository: Repository<SurveyQuestionRowEntity>,
    @InjectRepository(ImportChannelEntity)
    private importChannelRepo: Repository<ImportChannelEntity>,
    @InjectRepository(SurveyPublished)
    private surveyPublishedRepo: Repository<SurveyPublished>,
    @InjectRepository(SurveyDepartment)
    private surveyDepartmentRepo: Repository<SurveyDepartment>,

    @InjectRepository(SurveyImportChannelEntity)
    private surveyImportChannelRepo: Repository<SurveyImportChannelEntity>,
  ) {}

  async publish(surveyId: number, body: Partial<PublishSurveyDto> = {}) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let survey = await this.surveyService.survey({
      where: { surv_survey_id: surveyId, sec_company_id: company.id },
      relations: ['editingBy', 'brandThemes', 'brandThemes.theme'],
    });

    if (!survey) throw new NotFoundException('Survey not found');

    const lngs = await this.surveyPublishedRepo
      .query(`    SELECT si.surv_survey_id,
    array_to_json(array_agg(si.language_key))      AS language_keys,
    array_to_json(array_agg(si.language_description)) AS language_descriptions,
    array_to_json(array_agg(si.isactive)) AS isactive
    FROM surv_i18n si
    WHERE si.surv_survey_id = ${survey.surv_survey_id}
    GROUP BY si.surv_survey_id`);

    const languages = lngs[0]?.language_keys?.map((lang) => ({
      code: lang,
      hidden: !lngs[0].isactive[lngs[0].language_keys.indexOf(lang)],
      name: lngs[0].language_descriptions[lngs[0].language_keys.indexOf(lang)],
    }));

    this.surveyUtils.checkSurveyIsLocked(survey.editingBy, survey.editing_dt);

    let logic = (survey.survey_logic || {})?.data;

    if (!logic)
      throw new HttpException(
        {
          message: this.i18n.t('props.survey.errors.logic_not_found'),
          errorCode: 'logic_not_found',
        },
        404,
      );

    let hasQuestion = false;
    if (Array.isArray(logic)) {
      for (let page of logic) {
        if (hasQuestion) break;
        for (let question of page.questions || []) {
          hasQuestion = true;
          break;
        }
      }
    }

    if (!hasQuestion) {
      throw new BadRequestException(
        'props.survey.errors.cannot_publish_without_questions',
      );
    }

    let dbQuestions = await this.surveyQuestionService.detailedQuestions({
      where: { surv_survey_id: surveyId },
    });

    const allLogicQuestions = logic.reduce((acc, page) => {
      return [...acc, ...(page.questions || [])];
    }, []);

    const { deletedItems, updatedItems } = this.compareQuestions(
      allLogicQuestions,
      dbQuestions,
    );

    if (deletedItems.length > 0) {
      const deletedIds = deletedItems.map((q) => q.surv_surveyquestion_id);

      // delete related rows
      await this.questionRowResository.delete({
        surv_surveyquestion_id: In(deletedIds),
      });

      // delete questions
      await this.questionRepository.delete({
        surv_surveyquestion_id: In(deletedIds),
      });
    }

    let leadings: Partial<
      Pick<
        SurvSurveyEntity,
        | 'nps_question'
        | 'ces_question'
        | 'csat_question'
        | 'clf_question1'
        | 'clf_question2'
        | 'callback_question'
      >
    > = {};

    // update questions
    for (const question of updatedItems) {
      const properties = question.properties;

      const questionType = this.surveyUtils.questionType({
        layout: properties.layout,
        type: question.type,
        selection_type: properties.selection_type,
      })?.id;

      if (
        questionType === QuestionType.NPSQuestion &&
        properties?.leading_nps_question
      ) {
        leadings.nps_question = question.id;
      }

      if (
        questionType === QuestionType.CSATQuestion &&
        properties?.leading_csat_question
      ) {
        leadings.csat_question = question.id;
      }

      if (
        questionType === QuestionType.CESQuestion &&
        properties?.leading_ces_question
      ) {
        leadings.ces_question = question.id;
      }

      if (questionType === QuestionType.OpenQuestion && properties?.strengths) {
        leadings.clf_question1 = question.id;
      }

      if (questionType === QuestionType.CallbackQuestion) {
        leadings.callback_question = question.id;
      }

      let defaultQuestion = properties?.languages?.find(
        (item) => item.language === survey.survey_language,
      );

      let updateData: DeepPartial<SurveyQuestionEntity> = {
        question: defaultQuestion?.question_text || properties?.question,
        modified_by: user.id,
        modified_on: new Date(),
      };

      let csi_number = properties.csi_number;
      let answertable_id = properties?.selected_answer_table_id;
      let template_id = properties?.template_id;

      if (questionType) {
        updateData.questiontype = questionType;
      }
      if (csi_number) {
        updateData.csi_number = csi_number;
      }
      if (answertable_id) {
        updateData.answertable_id = answertable_id;
      }
      if (template_id) {
        updateData.surv_survquest_temp_id = template_id;
      }

      await this.questionRepository.update(
        { surv_surveyquestion_id: question.id },
        updateData,
      );
    }

    await this.updateQuestionRows(survey, allLogicQuestions, dbQuestions);

    let promises = [];

    promises.push(this.surveyService.updateSurvey(surveyId, leadings));
    promises.push(
      this.surveyPublishedRepo.delete({
        surv_survey_id: survey.surv_survey_id,
      }),
    );

    let publishPromises = [];

    for (let item of survey.brandThemes) {
      const publicLink = body.public_links?.find(
        (link) => link.brand_id === item.brand_id,
      );
      publishPromises.push(
        this.surveyPublishedRepo.save(
          this.surveyPublishedRepo.create({
            surv_survey_id: survey.surv_survey_id,
            brand_id: item.brand_id,
            theme_id: item.theme_id,
            additional_settings: {},
            theme_data: item.theme?.data || {},
            data: {
              languages,
              default_language: survey.survey_language,
              ...(survey.survey_logic || {}),
            },
            public_link : publicLink?.link_id,
            created_by: user.id,
            created_on: new Date(),
          }),
        ),
      );
    }

    await Promise.allSettled(promises);
    let result = await Promise.all(publishPromises);

    return {
      message: this.i18n.t('props.survey.published'),
      published: result.map((item) => ({
        id: item.surv_survey_published_id,
        brand_id: item.brand_id,
        theme_id: item.theme_id,
      })),
    };
  }

  async updateQuestionRows(
    survey: SurvSurveyEntity,
    logicQuestions: UpdateSurveyQuestionDto[],
    dbQuestions: SurveyQuestionEntity[],
  ) {
    const { user } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let promises = [];
    for (let logicQuestion of logicQuestions) {
      let dbQuestion = dbQuestions.find(
        (q) => q.surv_surveyquestion_id === logicQuestion.id,
      );
      const properties = logicQuestion.properties;
      if (!dbQuestion) continue;

      let dbAnswers = dbQuestion.answers || [];
      let logicAnswers =
        properties?.answers ||
        properties?.languages?.find(
          (item) => item.language === survey.survey_language,
        )?.answers ||
        [];

      if (logicAnswers?.length) {
        const result = compareListItems<
          SurveyQuestionAnswerDto,
          SurveyQuestionRowEntity
        >(
          dbAnswers.map((answer) => ({
            ...answer,
            id: answer.question_returnvalue,
          })),
          logicAnswers,
          ['id'],
        );

        const { deletedItems, updatedItems } = result;

        if (updatedItems.length) {
          promises.push(
            ...updatedItems.map((item) => {
              return this.questionRowResository.update(
                {
                  surv_surveyquestion_id: dbQuestion.surv_surveyquestion_id,
                  question_returnvalue: item.id,
                },
                {
                  question_shortlabel: item.label,
                  surv_survquest_temp_id:
                    logicQuestion?.properties?.template_id,
                  isactive: 1,
                  modified_by: user.id,
                  modified_on: new Date(),
                },
              );
            }),
          );
        }

        if (deletedItems.length) {
          let deleteIds = deletedItems.map(
            (item) => item.surv_surveyquestionrow_id,
          );
          promises.push(
            this.questionRowResository.delete({
              surv_surveyquestionrow_id: In(deleteIds),
            }),
          );
        }
      }
    }
    return Promise.allSettled(promises);
  }

  private compareQuestions(
    logicQuestions: UpdateSurveyQuestionDto[],
    dbQuestions: SurveyQuestionEntity[],
  ) {
    const logicIds = new Set(logicQuestions.map((q) => q.id));
    const dbIds = new Set(dbQuestions.map((q) => q.surv_surveyquestion_id));

    const createdItems = logicQuestions.filter((q) => !dbIds.has(q.id));
    const deletedItems = dbQuestions.filter(
      (q) => !logicIds.has(q.surv_surveyquestion_id),
    );
    const updatedItems = logicQuestions.filter((q) => dbIds.has(q.id));

    return { createdItems, deletedItems, updatedItems };
  }

  async getImportChannels(surveyId: number) {
    if (!+surveyId) return [];
    let importChannels = await this.importChannelRepo
      .createQueryBuilder('ic')
      .leftJoinAndSelect(
        'ic.surveyImportChannels',
        'sic',
        'sic.surv_survey_id = :surveyId',
        { surveyId },
      )
      .getMany();
    return importChannels;
  }

  async getConfig(surveyId: number) {
    let { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let secCompany = await this.companyService.findById(company.id, {
      select: ['sec_company_id', 'api_secret'],
    });

    let settings = await this.programSettings.get(company.id);

    let survey = await this.surveyService.survey({
      where: { surv_survey_id: surveyId },
      select: ['surv_survey_id', 'surv_uuid', 'survey_expire_time'],
    });

    if (!survey) throw new NotFoundException('props.survey.errors.not_found');

    let importChannels = await this.getImportChannels(surveyId);
    let selected: any = importChannels.reduce((prev, channel) => {
      let key = channel.name.replace(/ /g, '_').toLowerCase();
      if (!prev[key]) prev[key] = !!channel.surveyImportChannels.length;
      return prev;
    }, {});

    let apiAllowed = secCompany.api_secret ? true : false;
    let sftpAllowed =
      !settings.ftp_password || !settings.ftp_username || !settings.ftp_host
        ? false
        : true;

    if (!apiAllowed) {
      if (selected.api_integration) selected.api_integration = false;
    }

    if (!sftpAllowed) {
      if (selected.sftp_server) selected.sftp_server = false;
    }

    const publishedSurveys = await this.surveyPublishedRepo.find({
      where: { surv_survey_id: surveyId },
      select: ['surv_survey_published_id', 'brand_id', 'public_link'],
    });


    const publicLinks = publishedSurveys.map((item) => ({
      brand_id: item.brand_id,
      link_id: item.public_link,
    }));

    return {
      SFTP_allowed: sftpAllowed,
      API_allowed: apiAllowed,
      survey_base_url: 'https://enquete2.welcomeccs.nl/',
      surv_uuid: survey.surv_uuid,
      survey_expire_time: survey.survey_expire_time, // TODO, needs to implement
      selected,
      public_links: publicLinks,
    };
  }

  async updateConfig(surveyId: number, data: UpdateSurveyPublishConfigDto) {
    let survey = await this.surveyService.survey({
      where: { surv_survey_id: surveyId },
    });

    if (!survey) throw new NotFoundException('props.survey.errors.not_found');

    let importChannels = await this.getImportChannels(surveyId);

    if (data.selected) {
      for (let key in data.selected) {
        let channel = importChannels.find(
          (ic) => ic.name.replace(/ /g, '_').toLowerCase() === key,
        );
        if (!channel) continue;

        let sic = channel.surveyImportChannels.find(
          (sic) => sic.surv_survey_id === surveyId,
        );

        if (data.selected[key] && !sic) {
          await this.surveyImportChannelRepo.save({
            surv_survey_id: surveyId,
            importChannel: channel,
          });
        } else if (!data.selected[key] && sic) {
          await this.surveyImportChannelRepo.delete(sic.id);
        }
      }
      delete data.selected;
    }

    if (data.survey_expire_time || data.surv_uuid) {
      let updateObj: any = {};
      if (data.survey_expire_time)
        updateObj.survey_expire_time = data.survey_expire_time;
      if (data.surv_uuid) updateObj.surv_uuid = data.surv_uuid;

      await this.surveyService.updateSurvey(surveyId, updateObj);
    }

    return {
      message: this.i18n.t('props.survey.publish_config_updated'),
    };
  }

  async getSurveyKey(surveyId: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const department = await this.surveyDepartmentRepo.findOne({
      where: {
        sec_company_id: company.id,
        surv_survey_id: surveyId,
      },
    });

    if (!department) {
      return {
        key: '',
      };
    }

    return {
      key:
        (department.department_key || '') + (department.department_key2 || ''),
    };
  }

  async saveSurveyKey(surveyId: number, body: SaveSurveyKeyDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let department = await this.surveyDepartmentRepo
      .createQueryBuilder('sd')
      .where('sd.sec_company_id = :companyId', { companyId: company.id })
      .andWhere(
        "LOWER(COALESCE(sd.department_key, '') || COALESCE(sd.department_key2, '')) = :key",
        {
          key: body.key.toLowerCase(),
        },
      )
      .getOne();

    if (department && department.surv_survey_id !== surveyId) {
      throw new ConflictException('props.survey.errors.key_already_exists');
    }

    department = await this.surveyDepartmentRepo.findOne({
      where: {
        sec_company_id: company.id,
        surv_survey_id: surveyId,
      },
    });
    if (department) {
      department.department_key = body.key;
    } else {
      department = this.surveyDepartmentRepo.create({
        sec_company_id: company.id,
        surv_survey_id: surveyId,
        department_key: body.key,
      });
    }

    await this.surveyDepartmentRepo.save(department);

    return {
      message: this.i18n.t('props.survey.key_saved'),
    };
  }
}
