import { ClsProperty } from '@/config/contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { SurvSurveyEntity } from '@entities';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { I18nService } from 'nestjs-i18n';
import { Repository } from 'typeorm';

@Injectable()
export class SurveyLogicService {
  constructor(
    private cls: ClsService,
    private i18n: I18nService,
    @InjectRepository(SurvSurveyEntity)
    private readonly surveyRepo: Repository<SurvSurveyEntity>,
  ) {}

  async item(id) {
    let survey = await this.surveyRepo.findOne({
      select: { surv_survey_id: true, survey_logic: true },
      where: { surv_survey_id: id },
    });
    if (!survey) throw new NotFoundException('props.survey.errors.not_found');
    return survey.survey_logic || {};
  }

  async update(id: number, data: any) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const survey = await this.surveyRepo.findOne({
      where: { surv_survey_id: id, sec_company_id: company.id },
      select: { surv_survey_id: true },
    });
    if (!survey) throw new NotFoundException('props.survey.errors.not_found');

    await this.surveyRepo.update(
      { surv_survey_id: id },
      { survey_logic: data },
    );
    return {
      id,
      message: this.i18n.t('props.survey.logic_updated'),
    };
  }
}
