import { Auth } from '@/shared/decorators';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { SurveyLogicService } from './survey-logic.service';

@Controller({
  path: 'survey/logic',
  version: '2',
})
@Auth()
@ApiTags('Survey Logic')
export class SurveyLogicController {
  constructor(private readonly surveyLoigcService: SurveyLogicService) {}

  @Get(':id')
  async get(@Param('id') id: number) {
    return this.surveyLoigcService.item(id);
  }

  @Post(':id')
  @ApiBody({ type: Object })
  async update(@Param('id') id: number, @Body() data: any) {
    if (typeof data !== 'object')
      throw new BadRequestException('Data must be an object');
    return this.surveyLoigcService.update(id, data);
  }
}
