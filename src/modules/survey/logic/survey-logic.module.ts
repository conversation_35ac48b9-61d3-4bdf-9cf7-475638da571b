import { SurvSurveyEntity } from '@entities';
import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyLogicController } from './survey-logic.controller';
import { SurveyLogicService } from './survey-logic.service';

@Module({
  imports: [TypeOrmModule.forFeature([SurvSurveyEntity])],
  controllers: [SurveyLogicController],
  providers: [SurveyLogicService],
  exports: [SurveyLogicService],
})
export class SurveyLogicModule {}
