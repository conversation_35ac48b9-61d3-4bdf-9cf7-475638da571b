import { Body, Controller, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';
import { ClassificationFeedbackService } from './classification-feedback.service';
import { AuthGuard } from '@/shared/guards/auth.guard';

@Controller({
  path: 'classification_feedback',
  version: '2',
})
@ApiTags('Classification Feedback')
@ApiBearerAuth()
export class ClassificationFeedbackController {
  constructor(
    private readonly clfFeedbackService: ClassificationFeedbackService,
  ) {}

  @Post()
  @UseGuards(AuthGuard)
  @ApiBody({ type: Object })
  getClfFeedbackResponses(@Body() body: any, @Query() query: any) {
    return this.clfFeedbackService.getData(query, body);
  }
}
