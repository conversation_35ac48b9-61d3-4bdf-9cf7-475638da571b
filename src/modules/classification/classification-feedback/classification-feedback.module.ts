import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ProgramSettingsModule } from '../../common/program-settings/program-settings.module';
import { ClassificationApiModule } from '../classification-api/classification-api.module';
import { AuthModule } from '../../auth/auth.module';

import { ClassificationFeedbackController } from './classification-feedback.controller';
import { ClassificationFeedbackService } from './classification-feedback.service';
import { ClfCallEntity, SurveyAnswerEntity } from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('classification-feedback'),
    TypeOrmModule.forFeature([ClfCallEntity, SurveyAnswerEntity]),
    ClassificationApiModule,
    ProgramSettingsModule,
    AuthModule,
  ],
  controllers: [ClassificationFeedbackController],
  providers: [ClassificationFeedbackService],
})
export class ClassificationFeedbackModule {}
