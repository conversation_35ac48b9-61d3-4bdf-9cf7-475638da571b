import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { ClfCallEntity, SurveyAnswerEntity } from '@entities';

import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';

import { ClassificationApiService } from '../classification-api/classification-api.service';
import { ProgramSettingsService } from '../../common/program-settings/program-settings.service';
import { AuthService } from '@/modules/auth/auth.service';

import * as moment from 'moment';

import { AI_THRESHOLD_DEFAULT } from '../../../config/ai.config';
import { PrivilegeService } from '@/modules/auth/privilege.service';
import { parseJSON, queryConvert, stringPatternCount } from '@/shared/utils';
import { CLASSIFICATION_FEEDBACK_OPTIONS } from '@/config/contents/clf.contents';
import { MclassRecordStatus } from '@/shared/enums';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class ClassificationFeedbackService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private privilegeService: PrivilegeService,
    private readonly clfApiService: ClassificationApiService,
    private readonly programSettingsService: ProgramSettingsService,
    @InjectRepository(ClfCallEntity)
    private readonly clfRepo: Repository<ClfCallEntity>,
    @InjectRepository(SurveyAnswerEntity)
    private readonly surveyAnswerRepo: Repository<SurveyAnswerEntity>,
  ) {}

  public async getData(query: any = {}, params: any = {}) {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`getData company ${company.id}`, { query, params });
    let limit = 1;
    let offset = query.page * limit;
    let sqlArgs: any = {};
    let filterSql = await this.clfApiService.getFilterData(
      { ...params, ignorePublish: true },
      sqlArgs,
    );
    let labelResult: any = {};
    let questionIds = [];
    let surveyAnswerId = 0;
    let response: any = {};
    let storyWidget: any;

    let privileges = await this.privilegeService.filteredKeys({
      userId: user.id,
      companyId: company.id,
      filter: { names: ['SHOW_CUSTOMER_DATA'] },
    });
    const SHOW_CUSTOMER_DATA = privileges.includes('SHOW_CUSTOMER_DATA');

    if (params.include_feedback) {
      let settings = await this.programSettingsService.get(company.id);
      let props = settings?.company_properties;
      storyWidget = props?.storyWidget;

      const ai_threshold = props?.ai_threshold || AI_THRESHOLD_DEFAULT;
      let tid = storyWidget?.to_publish_ques_temp_id;
      if (tid) {
        if (tid && storyWidget.flag_story_widget) {
          let sql = `SELECT surv_surveyquestion_id FROM 
													surv_surveyquestion ss 
												LEFT JOIN surv_surveypart ss3 on 
													ss3.surv_surveypart_id = ss.surv_surveypart_id 
												LEFT JOIN surv_survey ss2 on ss2.surv_survey_id = ss3.surv_survey_id 
												WHERE
													surv_survquest_temp_id = $1 
													AND ss2.sec_company_id = $2`;
          let result = await this.clfRepo.query(sql, [tid, company.id]);
          questionIds = result.map((i) => i.surv_surveyquestion_id);
        }
      } else {
        response.msg = `There should be a 'check to publish' question selected in the Admin-Settings-Story widget`;
        response.success = false;
      }

      if (params.answerid) {
        surveyAnswerId = params.answerid;
      } else {
        sqlArgs.last15min = new Date(
          new Date().setMinutes(new Date().getMinutes() - 15),
        );
        let sql = `
        SELECT DISTINCT a.surv_surveyanswer_id 
        FROM surv_surveyanswer a
        ${
          stringPatternCount(filterSql, 'ec.')
            ? `INNER JOIN email_customer ec on a.email_customer_id = ec.email_customer_id `
            : ''
        }
        LEFT JOIN surv_survey s on 
						a.surv_survey_id = s.surv_survey_id 
					LEFT JOIN surv_surveyansweritem pos on 
						a.surv_surveyanswer_id = pos.surv_surveyanswer_id 
						and s.clf_question2 = pos.surv_surveyquestion_id 
					LEFT JOIN surv_surveyansweritem neg on 
						a.surv_surveyanswer_id = neg.surv_surveyanswer_id 
						and s.clf_question1 = neg.surv_surveyquestion_id 
					LEFT JOIN mclass_response rs on 
						rs.answer_id = a.surv_surveyanswer_id

          ${
            storyWidget.flag_story_widget || params.publishable?.length
              ? `
          LEFT JOIN surv_surveyansweritem pub on 
          a.surv_surveyanswer_id = pub.surv_surveyanswer_id 
          and pub.surv_surveyquestion_id IN (${
            questionIds.length ? questionIds.join(',') : 'null'
          })
          `
              : ''
          }

          WHERE 
          (a.comments_neg_length > ${
            CLASSIFICATION_FEEDBACK_OPTIONS.MIN_COMMENTS
          } 
          OR a.comments_pos_length > ${
            CLASSIFICATION_FEEDBACK_OPTIONS.MIN_COMMENTS
          }) 
          AND ((a.lock_user is null or a.lock_user = ${
            user.id
          }) or ( a.lock_user != ${
          user.id
        } AND ( a.lock_date < :last15min or a.lock_date is null)))
          ${filterSql}
          LIMIT ${params.quicksearch ? 6 : limit}
          ${query.page && !params.quicksearch ? ` OFFSET ${offset}` : ''}
        `;
        let [sqlQuery, args] = queryConvert(sql, sqlArgs);
        let result = await this.clfRepo.query(sqlQuery, args);
        surveyAnswerId = result?.[0]?.surv_surveyanswer_id;
      }

      let sql = `
      SELECT 
      a.nps_value as nps, 
      clf.clf_call_id, 
      callback.return_value as callback, 
      pos.return_value as strengths, 
      neg.return_value as improvements, 
      a.surv_surveyanswer_id as answerId, 
      coalesce(mapprod.map_description, ec.xml_proddivision )  as proddiv, 
      coalesce(mapreg.map_description, ec.xml_region ) as region, 
      ec.xml_readydate as visit, 
      ss.department as department, 
      ec.xml_brand as brand, 
      a.creation_date as responsedate, 
      ${
        SHOW_CUSTOMER_DATA
          ? `
      ec.xml_rasnumber as rasnr, 
      concat_ws(' ', ec.xml_initials, ec.xml_customername ) as customername, 
      ec.xml_phone1 as phone1, 
      ec.xml_phone2 as phone2, 
      ec.xml_email as email, 
      `
          : ''
      }
      ss.name as survey, 
      ss.surv_survey_id as survey_id, 
      ec.email_customer_id, 
      p.project_name as project, 
      CASE WHEN assigned_to IS NOT NULL THEN CONCAT(sec_assigned_to.firstname, ' ', sec_assigned_to.username) ELSE null END AS username, 
      clf.assigned_to, 
      CASE WHEN (rs.mclass_response_id IS NOT null)  THEN 1 ELSE 0 END as is_classified, 
      a.publication_id , ( select  json_agg( 
                               json_build_object( 
                                              'id', t.tag_id ,
                                              'name', t.tag_name
                                              ) )
                              from clf_tags ct 
                               left join tags t on ct.tag_id = t.tag_id 
                              where ct.clf_call_id = clf.clf_call_id 
                              )  as tag_arr,
      clf.clf_summary
      ${
        storyWidget.flag_story_widget || params.publishable?.length
          ? `, pub.return_value as is_publishable`
          : ''
      }

          FROM 
          surv_surveyanswer a 
        INNER JOIN email_customer ec on 
          a.email_customer_id = ec.email_customer_id 
        INNER JOIN surv_survey ss on 
          ss.surv_survey_id = a.surv_survey_id 
          and ss.sec_company_id = a.sec_company_id 
        LEFT JOIN bsh_mapping mapprod on 
          ec.xml_proddivision = mapprod.map_key and mapprod.map_field = 'xml_proddivision' 
        LEFT JOIN bsh_mapping mapreg on 
          ec.xml_region = mapreg.map_key and mapreg.map_field = 'xml_region' 
        LEFT JOIN surv_survey s on 
          a.surv_survey_id = s.surv_survey_id 
        LEFT JOIN surv_surveyansweritem pos on 
          a.surv_surveyanswer_id = pos.surv_surveyanswer_id 
          and s.clf_question2 = pos.surv_surveyquestion_id 
      ${
        storyWidget.flag_story_widget || params.publishable?.length
          ? `
      LEFT JOIN surv_surveyansweritem pub on 
      a.surv_surveyanswer_id = pub.surv_surveyanswer_id 
      and pub.surv_surveyquestion_id IN (${
        questionIds.length ? questionIds.join(',') : 'null'
      })
      `
          : ''
      }
      LEFT JOIN surv_surveyansweritem neg on 
								a.surv_surveyanswer_id = neg.surv_surveyanswer_id 
								and s.clf_question1 = neg.surv_surveyquestion_id 
								left join surv_surveyansweritem callback on 
								a.surv_surveyanswer_id = callback.surv_surveyanswer_id 
								and s.callback_question = callback.surv_surveyquestion_id 
							LEFT JOIN projects p on 
								ec.xml_internal_external = p.project_id 
							LEFT JOIN clf_call clf ON 
								a.email_customer_id = clf.email_customer_id 
							LEFT JOIN clf_tags ct ON ct.clf_call_id = clf.clf_call_id 
							LEFT JOIN mclass_response rs on 
								rs.answer_id = a.surv_surveyanswer_id 
							LEFT JOIN sec_users sec_assigned_to on 
								clf.assigned_to = sec_assigned_to.sec_user_id 
							WHERE 
								a.surv_surveyanswer_id  = $1
                AND a.sec_company_id = ${company.id}
                LIMIT 1
      `;

      let result = await this.clfRepo.query(sql, [surveyAnswerId]);
      await this.lockSurveyAnswers(
        company.id,
        result.map((i) => i.answerid),
      );
      let data: any;
      let item = result[0];
      data = {
        nps: item.nps,
        customer_name: item.customername,
        callback: item.callback,
        strengths: item.strengths,
        improvements: item.improvements,
        answerid: item.answerid,
        [item.visit ? 'visit_date' : 'response_date']: moment(
          item.visit || item.responsedate,
        ).format('DD-MM-YYYY HH:mm'),
        ris_number: item.rasnr,
        product_division: item.proddiv,
        region: item.region,
        brand: item.brand,
        clf_call_id: item.clf_call_id,
        phone1: item.phone1,
        phone2: item.phone2,
        email: item.email,
        survey: item.survey,
        survey_id: item.survey_id,
        email_customer_id: item.email_customer_id,
        project: item.project,
        assigned_to: { id: item.assigned_to, name: item.username },
        is_classified: item.is_classified == 1,
        is_excluded: false,
        tag_arr: parseJSON(item.tag_arr),
        summary: item.clf_summary || 'No summary',
        is_published: !!item.publication_id,
      };
      if (
        storyWidget.flag_story_widget &&
        ['yes', 'ja', '1'].includes(item.is_publishable?.toLowerCase() || '')
      ) {
        data.is_publishable = true;
      } else data.is_publishable = false;

      if (surveyAnswerId) {
        let menuItems = await this.clfApiService.getLabels(
          company.id,
          data.survey_id,
        );
        let sql = `
        SELECT mr.item_id, 
        mr.is_liked, 
       mr.is_potential, 
       mr.istype, 
       mr.is_excluded, 
       mr.record_status,
       mr.confidence
        FROM mclass_response mr 
        WHERE answer_id = $1  
        `;
        let results = await this.clfRepo.query(sql, [surveyAnswerId]);
        let labels = results.reduce((prev, item) => {
          if (item.confidence != null && item.confidence < ai_threshold)
            return prev;
          let data: any = {
            is_potential: 0,
            is_liked: 0,
            is_excluded: 0,
          };
          if (item.record_status != 3) {
            if (item.is_potential) data.is_potential = item.is_potential;
            if (item.is_liked) data.is_liked = item.is_liked;
            if (item.is_excluded) data.is_excluded = item.is_excluded;
          }

          if (item.istype == 1) data.is_positive = true;
          else if (item.istype == 2) data.is_negative = true;
          else if (item.istype == 3) data.is_neutral = true;

          if (item.record_status) {
            prev.record_status = item.record_status;
          }
          if (item.is_excluded) {
            prev.is_excluded = true;
          }

          prev[item.item_id] = data;
          return prev;
        }, {});

        for (let menu of menuItems) {
          let label: any = {
            id: menu.id,
            name: menu.name,
            isMainLabel: true,
            order: menu.order,
            is_potential: labels[menu.id]?.is_potential == 1,
            is_liked: labels[menu.id]?.is_liked == 1,
            has_active_labels: false,
          };
          labelResult[menu.id] = label;

          for (let item of menu.items) {
            let label: any = {
              id: item.id,
              name: item.name,
              isMainLabel: false,
              mainLabelId: menu.id,
              selected: false,
              order: item.order,
              is_potential: labels[item.id]?.is_potential == 1,
              is_liked: labels[item.id]?.is_liked == 1,
              is_positive: labels[item.id]?.is_positive == 1,
              is_neutral: labels[item.id]?.is_neutral == 1,
              is_negative: labels[item.id]?.is_negative == 1,
              by_user: false,
              by_computer: false,
              by_training: false,
              has_active_labels: !!labels[item.id],
            };
            if (labels[item.id]) {
              switch (labels[item.id].record_status) {
                case MclassRecordStatus.AILabel:
                  label.by_computer = true;
                  break;
                case MclassRecordStatus.AITrainingLabel:
                  label.by_training = true;
                  break;
                default:
                  label.by_computer = true;
                  break;
              }
            }

            labelResult[item.id] = label;
          }
        }
      }

      response = {
        labels: labelResult,
        feedback: data,
      };
    }
    if (params.include_totals) {
      let sql = `
      SELECT count (distinct a.surv_surveyanswer_id ) as cnt
      FROM surv_surveyanswer a 
      ${
        stringPatternCount(filterSql, 'ec.')
          ? `INNER JOIN email_customer ec on a.email_customer_id = ec.email_customer_id `
          : ''
      }

       INNER JOIN surv_survey ss ON 
          ss.surv_survey_id = a.surv_survey_id 
          AND ss.sec_company_id = a.sec_company_id 
        LEFT JOIN surv_survey s ON 
          a.surv_survey_id = s.surv_survey_id 
        LEFT JOIN surv_surveyansweritem pos ON 
          a.surv_surveyanswer_id = pos.surv_surveyanswer_id 
          AND s.clf_question2 = pos.surv_surveyquestion_id 
        LEFT JOIN surv_surveyansweritem neg ON 
          a.surv_surveyanswer_id = neg.surv_surveyanswer_id 
          AND s.clf_question1 = neg.surv_surveyquestion_id 
        LEFT JOIN mclass_response rs ON 
          rs.answer_id = a.surv_surveyanswer_id

          ${
            storyWidget.flag_story_widget || params.publishable?.length
              ? `LEFT JOIN surv_surveyansweritem pub on 
              a.surv_surveyanswer_id = pub.surv_surveyanswer_id 
              and pub.surv_surveyquestion_id IN (${
                questionIds.length ? questionIds.join(',') : 'null'
              })`
              : ''
          }
          WHERE 
          a.sec_company_id = ${company.id} 
          AND (a.comments_neg_length > ${
            CLASSIFICATION_FEEDBACK_OPTIONS.MIN_COMMENTS
          }
          OR a.comments_pos_length > ${
            CLASSIFICATION_FEEDBACK_OPTIONS.MIN_COMMENTS
          })
          ${filterSql} 
      `;

      let [sqlQuery, args] = queryConvert(sql, sqlArgs);
      let result = await this.clfRepo.query(sqlQuery, args);
      let totals = result?.[0]?.cnt;
      if (totals) response.totals = totals;
    }

    this.logger.debug(`getData result for company ${company.id}`, {
      response,
      filterSql,
    });

    return response;
  }

  lockSurveyAnswers(userId, answerIds: any[]) {
    return this.surveyAnswerRepo.update(
      { surv_surveyanswer_id: In(answerIds) },
      { lock_user: userId, lock_date: moment().format() },
    );
  }
}
