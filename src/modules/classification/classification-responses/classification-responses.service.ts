import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';

import { ChartsService } from '../../charts/charts.service';
import { ProgramSettingsService } from '../../common/program-settings/program-settings.service';

import { GetClassificationQueryDto } from './dto/get-classification-query.dto';
import {
  ReportLayoutEntity,
  UserCompanyEntity,
  UserReportsEntity,
} from '@entities';
import { DateUtilsService } from '@/shared/utils';
import { AI_THRESHOLD_DEFAULT } from '@/config/ai.config';
import { ReportTemplate } from '@/shared/enums';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class ClassificationResponsesService {
  @Inject(ChartsService)
  private readonly chartsService: ChartsService;

  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(ReportLayoutEntity)
    private readonly reportLayoutEntity: Repository<ReportLayoutEntity>,

    @InjectRepository(UserReportsEntity)
    private readonly userReportsEntity: Repository<UserReportsEntity>,

    @InjectRepository(UserCompanyEntity)
    private readonly userCompanyEntity: Repository<UserCompanyEntity>,
    private readonly dateUtilService: DateUtilsService,
    private readonly programSettingsService: ProgramSettingsService,
  ) {}

  async getResponse(params: GetClassificationQueryDto) {
    const { id, chart, type, page, count, asHTML } = params;
    const { user, company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`getResponse for company ${company}`, { params });

    const userReport = await this.userReportsEntity.findOne({
      where: {
        user_report_id: chart,
      },
    });
    const {
      report_template: reportTemplate,
      filters: filtersString,
      report_name: reportName,
    } = userReport;

    let groupIds: any = await this.userCompanyEntity.find({
      where: { sec_user_id: user.id, sec_company_id: company.id },
      select: ['sec_group'],
    });

    groupIds = groupIds.map((item) => item.sec_group);

    const filters = await this.chartsService.getCombinedFilters(
      filtersString,
      groupIds,
      reportTemplate,
    );

    let settings = await this.programSettingsService.get(company.id);
    let props = settings?.company_properties;

    const ai_threshold = props?.ai_threshold || AI_THRESHOLD_DEFAULT;

    const { filtersSql, dateField, period = 'week', filtersObject } = filters;

    const { fromDate, toDate } = this.dateUtilService.formatDates({
      fromDate: filtersObject.from_date,
      toDate: filtersObject.to_date,
    });

    const { specialJoinsSql } =
      this.chartsService.getSpecialJoins(filtersObject);

    const whereClause = `
        WHERE item_id = ${id}
        ${filtersSql}
        ${type == 'like' ? `AND is_liked > 0` : ''}
        ${type == 'potential' ? `AND is_potential > 0` : ''}
        ${type == 'pos' ? `AND mr.istype = 1` : ''}
        ${type == 'neg' ? `AND mr.istype = 2` : ''}
        ${
          type == 'all' && reportTemplate == ReportTemplate.Classification
            ? `AND mr.istype IN (1,2)`
            : ''
        } 
        ${
          type == 'all' && reportTemplate == ReportTemplate.PotentialAndLike
            ? `AND (is_liked > 0 OR is_potential > 0)`
            : ''
        }
`;

    const sql = `
WITH labels AS ( 
  SELECT 
      mr.answer_id 
      , mr.istype 
      , STRING_AGG( 
        CASE WHEN COALESCE(mil.item_label_locale,'') = '' THEN COALESCE(mi.item_label,'') ELSE mil.item_label_locale END 
        || ' (' || CASE WHEN COALESCE(milp.item_label_locale,'') = '' THEN COALESCE(mip.item_label,'') ELSE milp.item_label_locale END || ')', ',') 
        AS labels 
  FROM mclass_response mr 
  INNER JOIN surv_surveyanswer a ON mr.answer_id = a.surv_surveyanswer_id 
  INNER JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id 
  JOIN mclass_item mi ON mr.item_id = mi.mclass_item_id 
  JOIN mclass_item mip ON mi.parent_id = mip.mclass_item_id 
  LEFT JOIN mclass_item_local mil ON mil.mclass_item_id = mi.mclass_item_id AND mil.sec_company_id = ${
    company.id
  }
  LEFT JOIN mclass_item_local milp ON milp.mclass_item_id = mi.parent_id AND milp.sec_company_id = ${
    company.id
  }
  WHERE a.sec_company_id = ${company.id}
      AND ${dateField.replace('ec.', '')} BETWEEN  '${fromDate}' AND '${toDate}'
      AND (mr.confidence >= ${ai_threshold} OR mr.confidence IS NULL)
  GROUP BY mr.answer_id, mr.istype 
) 
SELECT s.internal_name AS survey, t.companyname AS brand, ec.xml_rasnumber AS rasnumber, pos.return_value AS strengths, neg.return_value AS improvements 
  , mr.answer_id, lbl_pos.labels as labels_strengths, lbl_neg.labels as labels_improvements, mr.istype, is_liked, is_potential, cc.clf_call_id as call_id 
FROM mclass_response mr 
LEFT JOIN surv_surveyanswer a ON mr.answer_id = a.surv_surveyanswer_id 
LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id 
LEFT JOIN email_customer ec ON a.email_customer_id= ec.email_customer_id 
LEFT JOIN clf_call cc ON a.email_customer_id = cc.email_customer_id 
${specialJoinsSql}
LEFT JOIN email_template t ON ec.email_template_id = t.email_template_id 
LEFT JOIN surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND s.clf_question1 = neg.surv_surveyquestion_id 
LEFT JOIN surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND s.clf_question2 = pos.surv_surveyquestion_id 
LEFT JOIN labels lbl_pos ON mr.answer_id = lbl_pos.answer_id and lbl_pos.istype = 1 
LEFT JOIN labels lbl_neg ON mr.answer_id = lbl_neg.answer_id and lbl_neg.istype = 2 
${whereClause}
${count > 0 ? `  LIMIT ${count}` : ''}
${count > 0 ? `  OFFSET ${count * page}` : ''}
`;

    let list = await this.reportLayoutEntity.query(sql);

    list = list.map((item) => {
      if (asHTML == true) {
        item.labels_strengths = item.labels_strengths?.replace(
          /(?:\r\n|\r|\n)/g,
          '<br>',
        );
        item.strengths = item.strengths?.replace(/(?:\r\n|\r|\n)/g, '<br>');
        item.labels_improvements = item.labels_improvements?.replace(
          /(?:\r\n|\r|\n)/g,
          '<br>',
        );
        item.improvements = item.improvements?.replace(
          /(?:\r\n|\r|\n)/g,
          '<br>',
        );
      }
      let type;
      if (item.istype == 1) type = 'pos';
      if (item.istype == 2) type = 'neg';
      if (item.is_liked == 1) type = 'like';
      if (item.is_potential == 1) type = 'potential';
      return {
        strengths: item.strengths,
        survey: item.survey,
        type,
        brand: item.brand,
        ris: item.rasnumber,
        answer_id: item.answer_id,
        clf_call_id: item.call_id,
        improvements: item.improvements,
      };
    });
    const title = `Responses (${list.length})`;
    if (page == 0 && count > 0) {
      let totals = await this.reportLayoutEntity.query(`
        SELECT count(mr.answer_id) as count
        FROM mclass_response mr
        LEFT JOIN surv_surveyanswer a ON mr.answer_id = a.surv_surveyanswer_id
        LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id
        LEFT JOIN email_customer ec ON a.email_customer_id= ec.email_customer_id
        ${specialJoinsSql}
        ${whereClause} 
        `);
      return {
        totals: +totals?.[0]?.count || 0,
        list,
        title,
      };
    }

    this.logger.debug(`getResponse  result`, { list, title, sql });
    return { list, title };
  }
}
