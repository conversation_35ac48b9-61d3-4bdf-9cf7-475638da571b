import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChartsModule } from '../../charts/charts.module';
import { ReportLayoutEntity } from '../../database/entities/report-layout.entity';
import { UserCompanyEntity } from '../../database/entities/sec-user-company.entity';

import { UserReportsEntity } from '../../database/entities/user-reports.entity';
import { DateUtilsService } from '../../../shared/utils/date-utils.service';

import { ClassificationResponsesConroller } from './classification-responses.controller';
import { ClassificationResponsesService } from './classification-responses.service';
import { ProgramSettingsModule } from '../../common/program-settings/program-settings.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('classification-responses'),
    TypeOrmModule.forFeature([
      ReportLayoutEntity,
      UserReportsEntity,
      UserCompanyEntity,
    ]),
    ChartsModule,
    ProgramSettingsModule,
  ],
  controllers: [ClassificationResponsesConroller],
  providers: [ClassificationResponsesService, DateUtilsService],
})
export class ClassificationResponsesModule {}
