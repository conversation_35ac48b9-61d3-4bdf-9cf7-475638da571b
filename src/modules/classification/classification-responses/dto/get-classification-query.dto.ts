import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsNumber, IsString, Max, Min } from 'class-validator';

export class GetClassificationQueryDto {
  @Type()
  @IsNumber()
  @ApiProperty({
    type: Number,
    description: 'id',
    required: true,
  })
  id: number;

  @Type()
  @IsNumber()
  @ApiProperty({
    type: Number,
    description: 'chart',
    required: true,
  })
  chart: number;

  @Type()
  @IsString()
  @ApiProperty({
    type: String,
    description: 'type',
    required: true,
  })
  type: string;

  @Type(() => Number)
  @IsNumber()
  @ApiProperty({
    type: Number,
    required: false,
  })
  page: number = 0;

  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  @ApiProperty({
    type: Number,
    required: false,
  })
  count: number = 10;

  @Transform(({ value }: any) => {
    return value === 'true' || value === true || value === 1 || value === '1';
  })
  @ApiProperty({ required: false })
  asHTML: boolean;
}
