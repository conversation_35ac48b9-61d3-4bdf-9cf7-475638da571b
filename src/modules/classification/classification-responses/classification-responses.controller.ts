import {
  Controller,
  Get,
  HttpStatus,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ClassificationResponsesService } from './classification-responses.service';
import { GetClassificationQueryDto } from './dto/get-classification-query.dto';

@ApiTags('Classification Responses')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'classification_responses',
})
export class ClassificationResponsesConroller {
  constructor(
    private classificationResponsesService: ClassificationResponsesService,
  ) {}

  @UseGuards(AuthGuard)
  @Get()
  @ApiOkResponse({
    status: HttpStatus.OK,
  })
  upsertData(@Query() query: GetClassificationQueryDto) {
    return this.classificationResponsesService.getResponse(query);
  }
}
