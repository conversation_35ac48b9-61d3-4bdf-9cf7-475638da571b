import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CompanyModule } from '../../company/company.module';
import { ProjectManagerModule } from '../../project/project-manager/project-manager.module';
import { SurveyModule } from '../../survey/survey.module';

import { ClassificationApiService } from './classification-api.service';
import {
  ReportLayoutEntity,
  UserCompanyEntity,
  UserReportsEntity,
} from '@entities';
import { DateUtilsService } from '@/shared/utils';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ReportLayoutEntity,
      UserReportsEntity,
      UserCompanyEntity,
    ]),
    ModuleLoggerModule.register('classification-api'),
    SurveyModule,
    ProjectManagerModule,
    CompanyModule,
  ],
  providers: [ClassificationApiService, DateUtilsService],
  exports: [ClassificationApiService],
})
export class ClassificationApiModule {}
