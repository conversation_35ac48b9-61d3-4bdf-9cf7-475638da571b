import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';

import { ProjectManagerService } from '../../project/project-manager/project-manager.service';
import { SurveyService } from '../../survey/survey.service';

import { UserCompanyEntity } from '@entities';
import { DateUtilsService } from '@/shared/utils';
import { ClfContactCustomerType, CompanyType } from '@/shared/enums';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class ClassificationApiService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private readonly surveyService: SurveyService,
    private readonly projectManagerService: ProjectManagerService,

    @InjectRepository(UserCompanyEntity)
    private readonly userCompanyEntity: Repository<UserCompanyEntity>,
    private readonly dateUtilService: DateUtilsService,
  ) {}

  async getFilterData(params: any = {}, sqlArgs: any = {}) {
    const { company, userCompany } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`getFilterData company ${company.id}`, { params });

    let sql = '';

    if (!params) params = {};

    if (params.survey?.length) {
      sql += ' AND a.surv_survey_id = ANY(:survey)';
      sqlArgs.survey = params.survey.map((i) => i.id);
    } else {
      const survey_list = await this.surveyService.list();
      sql += ' AND a.surv_survey_id = ANY(:survey)';
      sqlArgs.survey = survey_list.map((i) => i.id);
    }

    if (company.type != CompanyType.BSH) {
      sql += `
       AND (project_id NOT IN (
        SELECT project_id
        FROM project_groups)
          OR project_id IN (SELECT project_id
          FROM project_groups
          WHERE group_id = ${userCompany.sec_group}) OR ec.xml_internal_external IS NULL)
      `;
    }

    sql += ` AND a.sec_company_id =:sec_company_id `;
    sqlArgs.sec_company_id = company.id;

    if (params.language?.length) {
      sql += ' AND ec.xml_language = ANY(:language)';
      sqlArgs.language = params.language.map((i) => i.id);
    }

    if (params.contactRequest) {
      sql += ` AND clf.contact_customer = ${ClfContactCustomerType.Requested}`;
    }

    if (
      (params.template_id == 15 || params.isExport || params.deepdive) &&
      params.label_type?.length
    ) {
      sql += ' AND mr.record_status = ANY(:label_type)';
      sqlArgs.label_type = params.label_type.map((i) => i.id);
    }

    if (params.mclass_items?.length) {
      sql += ` AND mr.item_id = ANY(:mclass_items)`;
      sqlArgs.mclass_items = params.mclass_items.map((i) => i.id);
    }

    if (params.product_division?.length) {
      sql += ` AND ec.xml_proddivision = ANY(:product_division)`;
      sqlArgs.product_division = params.product_division.map((i) => i.id);
    }

    if (params.warranty?.[0]?.id) {
      let wid = params.warranty[0].id;
      if (wid == 1) sql += ` AND ec.xml_la IN ('01', '83', '84') `;
      else if (wid == 2)
        sql += ` AND (ec.xml_la NOT IN ('01', '83', '84') OR ec.xml_la IS NULL)`;
    }

    if (params.score?.length) {
      sqlArgs.score = [
        ...new Set(
          [].concat(
            ...params.score.map((item) => {
              if (item.id == 11) return [9, 10];
              else if (item.id == 12) return [7, 8];
              else if (item.id == 13) return [0, 1, 2, 3, 4, 5, 6];
              else return [item.id];
            }),
          ),
        ),
      ];
      sql += ` AND  a.nps_value = ANY(:score::int[])`;
    }

    if (params.bsh_survey_categories?.length) {
      sqlArgs.bshCid = params.bsh_survey_categories.map((item) => item.id);
      sql += ` AND s.bsh_category_id  = ANY(:bshCid::int[])`;
    }

    params.brands = params.brand || params.brands;
    if (params.brands?.length) {
      sql += ' AND LOWER(ec.xml_brand) = ANY(:brands)';
      sqlArgs.brands = params.brands.map((i) => i.id.toString().toLowerCase());
    }

    if (!params.ignoreClassified) {
      if (params.classified?.[0]?.id == 1) {
        sql += ` AND rs.mclass_response_id IS NOT NULL`;
      } else {
        sql += ` AND rs.mclass_response_id IS NULL`;
      }
    }

    if (!params.ignorePublish) {
      let publishId = params.publish?.[0]?.id;
      if (publishId == 3 || typeof publishId === 'undefined') {
        sql += ` AND (a.publication_state IS NULL OR a.publication_state = 0)`;
      } else if (publishId !== 0) {
        sql += ` AND a.publication_state = :publishId`;
        sqlArgs.publishId = +publishId;
      }
    }

    if (!params.ignoreDates) {
      if (params.from_date || params.to_date) {
        const { fromDate, toDate } = this.dateUtilService.formatDates({
          fromDate: params.from_date,
          toDate: params.to_date,
        });
        sql += ` AND ${'a.creation_date'} between '${fromDate.toISOString()}' AND '${toDate.toISOString()}'`;
      }
    }

    if (params.tags?.length) {
      sql += ` AND ct.tag_id = ANY(:tags)`;
      sqlArgs.tags = params.tags.map((i) => i.id);
    }
    if (params.regions?.length) {
      sql += ` AND ${
        company.id == 2 ? 'ec.xml_techgrp' : 'ec.xml_region'
      } = ANY(:regions)`;
      sqlArgs.regions = params.regions.map((i) => i.id);
    }

    if (params.medium?.[0]?.length) {
      sql += ` AND ec.sent_medium =:medium`;
      sqlArgs.medium = params.medium[0].id;
    }

    if (params.project?.length) {
      let pdiv = ['BO-MDA', 'BO-SDA', 'SI-MDA', 'SI-SDA'];
      sqlArgs.projectDiv = params.project.filter((item) =>
        pdiv.includes(item.id),
      ); // item.name
      sqlArgs.projectNr = params.project.filter(
        (item) => !pdiv.includes(item.id),
      ); // item.name

      if (sqlArgs.projectDiv?.length > 0) {
        sql += ` AND ec.xml_projectdiv = ANY(:projectDiv::text[])`;
      }
      if (sqlArgs.projectNr?.length > 0) {
        sql += ` AND ec.xml_projectnr = ANY(:projectNr::text[])`;
      }
    }

    if (company.type == CompanyType.BOUW || company.type == CompanyType.SPORT) {
      if (params.project_category?.length) {
        sqlArgs.project_category = params.project_category.map(
          (item) => item.id,
        );
        sql += ` AND ec.xml_internal_external IN (select p.project_id  from project_pcategories pc, projects p where p.project_id = pc.project_id AND pc.pcategory_id = ANY(:project_category::text[]) )`;
      }

      if (params.project_bouw?.length || params.club?.length) {
        sqlArgs.projectBouw = (params.project_bouw || params.club).map(
          (item) => item.id,
        ); // item.name
        sql += ` AND ec.xml_internal_external = ANY(:projectBouw::text[])`;
      } else {
        sqlArgs.projectBouw =
          await this.projectManagerService.getAllowedProjects();
        sql += ` AND (ec.xml_internal_external = ANY(:projectBouw::text[]) OR ec.xml_internal_external IS NULL)`;
      }
    }

    if (params.productgroup?.length) {
      sql += ` AND ec.xml_prodarea = ANY(:productgroup)`;
      sqlArgs.productgroup = params.productgroup.map((i) => i.id);
    }

    if (params.contact_center?.length) {
      sql += ` AND techDOB.techgrp_id in (${params.contact_center
        .map((i) => i.id)
        .join(', ')})`;
    }

    if (params.dispatcher?.length) {
      sql += ` AND techDispatch.techgrp_id in (${params.dispatcher
        .map((i) => i.id)
        .join(', ')})`;
    }

    if (params.preprepper?.length) {
      sql += ` AND techPreppep.techgrp_id in (${params.preprepper
        .map((i) => i.id)
        .join(', ')})`;
    }

    if (params.service_partner?.length) {
      sql += ` AND techServicePartner.techgrp_id in (${params.service_partner
        .map((i) => i.id)
        .join(', ')})`;
    }

    if (params.counselor?.length) {
      sql += ` AND techCounselor.techgrp_id in (${params.counselor
        .map((i) => i.id)
        .join(', ')})`;
    }

    if (params.engineer?.length) {
      sql += ` AND tech.techgrp_id in (${params.engineer
        .map((i) => i.id)
        .join(', ')})`;
    }

    if (params.dealer?.length) {
      sql += ` AND techDealer.techgrp_id in (${params.dealer
        .map((i) => i.id)
        .join(', ')})`;
    }

    this.logger.debug(`getFilterData result`, { sql, sqlArgs });

    return sql;
  }

  async getLabels(sec_company_id, surveyId) {
    this.logger.debug(
      `getLabels company ${sec_company_id}, survey ${surveyId}`,
    );
    let sql = `
    SELECT ARRAY(
      SELECT JSON_BUILD_OBJECT(
                    'order', ROW_NUMBER() OVER (ORDER BY (CASE WHEN mi.sec_company_id = 25 THEN 1 ELSE 0 END) DESC, mi.order_number ASC),
                      'id',  mpi.item_id,
                    'name', coalesce(mil.item_label_locale, mi.item_label),
                    'items', ( SELECT ARRAY(
                            SELECT JSON_BUILD_OBJECT(
                                          'order', ROW_NUMBER() OVER (ORDER BY (CASE WHEN mi.sec_company_id = 25 THEN 1 ELSE 0 END) DESC, mi2.order_number ASC),
                                          'id',  mpi2.item_id,
                                          'name', coalesce(mil2.item_label_locale, mi2.item_label))
                                            FROM mclass_proces_item AS mpi2
                                            LEFT JOIN mclass_item as mi2 on mi2.mclass_item_id = mpi2.item_id
                                            LEFT JOIN mclass_item_local mil2 ON mil2.mclass_item_id = mi2.mclass_item_id AND mil2.sec_company_id = ${sec_company_id}
                                            WHERE mi2.parent_id = mi.mclass_item_id
                                          and mpi2.survey_id = $1 and mpi2.is_active = 1
                                          and mi2.deleted_by is null  order by (CASE WHEN mi.sec_company_id = 25 THEN 1 ELSE 0 END) DESC, mi2.order_number asc )))
                    FROM mclass_proces_item as mpi
                    LEFT JOIN mclass_item as mi on mi.mclass_item_id = mpi.item_id
                    LEFT JOIN mclass_item_local mil ON mil.mclass_item_id = mi.mclass_item_id AND mil.sec_company_id = ${sec_company_id}
                    WHERE   survey_id = $1
                        AND is_active = 1
                        AND mi.deleted_by is null
                        AND mpi.parent_id is null
                      ORDER BY (CASE WHEN mi.sec_company_id = 25 THEN 1 ELSE 0 END) DESC, mi.order_number asc )
    `;
    let result = await this.userCompanyEntity.query(sql, [surveyId]);
    let data = result?.[0]?.array || [];

    this.logger.debug(`getLabels results`, { data });

    return data;
  }
}
