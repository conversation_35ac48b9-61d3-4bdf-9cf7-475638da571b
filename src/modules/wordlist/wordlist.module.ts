import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SearchListEntity } from '../database/entities/search-list.entity';
import { WordListController } from './wordlist.controller';
import { WordListService } from './wordlist.service';

@Module({
  imports: [TypeOrmModule.forFeature([SearchListEntity])],
  controllers: [WordListController],
  providers: [WordListService],
})
export class WordListModule {}
