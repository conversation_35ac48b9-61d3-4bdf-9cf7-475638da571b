import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, Repository } from 'typeorm';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { SearchListEntity } from '../database/entities/search-list.entity';
import { UpsertWordListBodyDto } from './dto/post-wordlist.dto';
import { DBHelperService } from '../core/db-helper/db-helper.service';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents';

export class WordListService {
  constructor(
    private cls: ClsService,
    private dbHelper: DBHelperService,
    @InjectRepository(SearchListEntity)
    private searchListEntity: Repository<SearchListEntity>,
  ) {}
  async getWordList(sec_company_id: number) {
    let list = await this.searchListEntity
      .createQueryBuilder()
      .where('sec_company_id =:sec_company_id', { sec_company_id })
      .orderBy('sl_name')
      .getMany();
    return list.map((i) => {
      return {
        id: i.sl_id,
        name: i.sl_name,
        items: i.sl_values.split('\n').filter((i) => i.length > 0),
      };
    });
  }

  async upsertWordList(body: UpsertWordListBodyDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let queryList = [];
    let nextId = await this.dbHelper.getNextId(SearchListEntity, 'sl_id');
    for (let item of body.wordlist) {
      if (item.markedForDeletion) {
        if (!item.id) continue;
        let p = new Promise((r) => {
          this.searchListEntity
            .createQueryBuilder()
            .delete()
            .where('sl_id =:id', { id: item.id })
            .andWhere('sec_company_id =:cid', { cid: company.id })
            .execute()
            .then((res: any) => {
              if (res.affected == 1) r({ id: item.id, status: 'DELETED' });
              else r(false);
            })
            .catch(() => r(false));
        });
        queryList.push(p);
      } else {
        let doc: DeepPartial<SearchListEntity> = {
          sl_name: item.name,
          sl_values: item.items.join('\n'),
          sec_company_id: company.id,
        };
        if (item.id?.toString()?.length > 0) {
          doc.sl_id = item.id;
        } else {
          doc.sl_id = nextId;
          nextId++;
        }

        queryList.push(this.searchListEntity.save(doc));
      }
    }
    let result = await Promise.all(queryList);
    return result
      .filter((i) => i)
      .map((i) => {
        if (i.status == 'DELETED') return { id: i.id, deleted: true };
        return {
          id: i.sl_id,
          name: i.sl_name,
          items: i.sl_values?.split('\n')?.filter((i) => i.length > 0),
        };
      });
  }
}
