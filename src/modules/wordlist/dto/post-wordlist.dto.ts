import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class WordListItem {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  id: number;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  name: string;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  items: string[];

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  markedForDeletion: Boolean;
}
export class UpsertWordListBodyDto {
  @Type(() => WordListItem)
  @ApiProperty({ type: [WordListItem] })
  wordlist: WordListItem[];
}
