import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { UpsertWordListBodyDto } from './dto/post-wordlist.dto';
import { WordListService } from './wordlist.service';

@Controller({
  path: 'wordlist',
  version: '2',
})
@ApiTags('Wordlist')
@ApiBearerAuth()
export class WordListController {
  constructor(private wordListService: WordListService) {}

  @Get()
  @UseGuards(AuthGuard)
  async getWordList(@Req() { company }: AuthorizedRequest) {
    return this.wordListService.getWordList(company.id);
  }

  @Post()
  @UseGuards(AuthGuard)
  async upsertWordList(@Body() body: UpsertWordListBodyDto) {
    return this.wordListService.upsertWordList(body);
  }
}
