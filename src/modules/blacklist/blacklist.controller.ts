import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { BlacklistService } from './blacklist.service';
import {
  AddBlacklistBulkDto,
  AddBlacklistDto,
  AddBlacklistErrorResponseDto,
  AddBlacklistResponseDto,
} from './dto/add-blacklist.dto';
import {
  DeleteBlacklistErrorResponseDto,
  DeleteBlacklistResponseDto,
} from './dto/delete-blacklist.dto';
import {
  GetBlacklistQueryDto,
  GetBlacklistsArrayResponseDto,
} from './dto/get-blacklist-array.dto';

@ApiTags('Blacklist')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'blacklist',
})
export class BlacklistController {
  constructor(private blacklistService: BlacklistService) {}

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Blacklists',
    type: GetBlacklistsArrayResponseDto,
  })
  @Get('/')
  getBlacklist(@Query() query: GetBlacklistQueryDto) {
    return this.blacklistService.getBlacklist(query);
  }

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Blacklist CSV',
  })
  @Get('/export')
  async exportBlacklist(@Res() response: Response) {
    const csv = await this.blacklistService.exportBlacklist();
    response.attachment('blacklist.csv');
    response.send(csv);
  }

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'updated',
    type: AddBlacklistResponseDto,
  })
  @ApiResponse({
    description: 'Record already exists',
    status: HttpStatus.FORBIDDEN,
    type: AddBlacklistErrorResponseDto,
  })
  @ApiResponse({
    description: 'added',
    status: HttpStatus.CREATED,
    type: AddBlacklistResponseDto,
  })
  @Post('/')
  async addBlacklist(
    @Res() response: Response,
    @Body() blacklist: AddBlacklistDto,
  ) {
    const { responseStatus, ...data } =
      await this.blacklistService.addBlacklist(blacklist);

    return response.status(responseStatus).send(data);
  }

  @UseGuards(AuthGuard)
  @Post('/bulk')
  async bulkAdd(@Body() list: AddBlacklistBulkDto) {
    const result = await this.blacklistService.bulkAdd(list);
    return result;
  }

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'deleted',
    type: DeleteBlacklistResponseDto,
  })
  @ApiResponse({
    description: "Can't delete blacklist",
    status: HttpStatus.BAD_REQUEST,
    type: DeleteBlacklistErrorResponseDto,
  })
  @Delete('/:id')
  deleteBlacklist(@Req() request: AuthorizedRequest, @Param('id') id: number) {
    return this.blacklistService.deleteBlacklist(id);
  }
}
