import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { CustomerBlacklistEntity } from '../database/entities/customer-blacklist.entity';
import { AddBlacklistBulkDto, AddBlacklistDto } from './dto/add-blacklist.dto';
import { Not, IsNull } from 'typeorm';
import * as moment from 'moment';
import * as md5 from 'md5';
import * as Papa from 'papaparse';
import { GetBlacklistQueryDto } from './dto/get-blacklist-array.dto';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { rawQuery } from '@/shared/utils';
import { isEmail } from 'class-validator';

@Injectable()
export class BlacklistService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(CustomerBlacklistEntity)
    private readonly customerBlacklistEntity: Repository<CustomerBlacklistEntity>,
  ) {}

  public async getBlacklist(params: GetBlacklistQueryDto = {}) {
    const { company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`getBlackList`, { params });

    let { search, page, limit } = params;

    const qb = this.customerBlacklistEntity.createQueryBuilder();
    qb.where('sec_company_id =:cid', { cid: company.id });
    qb.andWhere(
      new Brackets((qb) => {
        qb.where('customer_email IS NOT NULL');
        qb.orWhere('phone IS NOT NULL');
      }),
    );
    if (search?.length > 0) {
      qb.andWhere(
        new Brackets((qb) => {
          qb.where('customer_email =:search', { search });
          qb.orWhere('phone =:search', { search });
        }),
      );
    }

    if (limit && limit > 0 && page > 0) {
      qb.take(limit).skip((page - 1) * limit);
    }

    const blacklists = await qb.getMany();

    const notShownTotal = await this.customerBlacklistEntity.count({
      where: [
        { sec_company_id: company.id, customer_email: IsNull() },
        { sec_company_id: company.id, phone: IsNull() },
      ],
    });
    this.logger.debug(`getBlackList result`, {
      query: rawQuery(qb),
      blacklists,
      notShownTotal,
    });

    return {
      blacklist: blacklists.map((item) => ({
        id: item.customer_blacklist_id,
        phone: item.phone,
        email: item.customer_email,
        brand: item.brand,
        date: moment(item.date_blacklisted).format('YYYY-MM-DD HH:mm'),
      })),
      shown: blacklists.length,
      notShown: notShownTotal,
    };
  }

  public async exportBlacklist() {
    const { company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`exportBlacklist for company ${company.id}`);

    const blacklists = await this.customerBlacklistEntity.find({
      where: {
        sec_company_id: company.id,
        customer_email: Not(IsNull()),
        phone: Not(IsNull()),
      },
    });

    const data = blacklists.map((item) => ({
      phone: item.phone,
      email: item.customer_email,
      brand: item.brand,
      date: moment(item.date_blacklisted).format('YYYY-MM-DD HH:mm'),
    }));

    this.logger.debug(`exportBlacklist for company ${company.id} result`, {
      blacklists,
      data,
    });

    return Papa.unparse(data, { delimiter: ';' });
  }

  public async addBlacklist(params: AddBlacklistDto) {
    const { company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`addBlacklist for ${company.id}`, { blacklist: params });

    const { brand, email, phone } = params;

    const hashedEmail = email ? md5(email) : null;
    const hashedPhone = phone ? md5(phone) : null;

    let query = this.customerBlacklistEntity.createQueryBuilder('b').where(
      new Brackets((qb) => {
        qb.where('b.customer_email_md5 =:hashedEmail', { hashedEmail });
        qb.orWhere('b.phone_md5 =:hashedPhone', { hashedPhone });
      }),
    );
    if (brand?.length) {
      query.andWhere(`b.brand =:brand`, { brand });
    } else {
      query.andWhere(`(b.brand IS NULL OR b.brand = '')`);
    }

    let conflicts = await query.getMany();
    let item = conflicts.find(
      (item) =>
        item.customer_email_md5 === hashedEmail &&
        item.phone_md5 === hashedPhone,
    );
    if (item) {
      throw new HttpException({ error: 'exists' }, HttpStatus.BAD_REQUEST);
    }

    // if (conflicts.length) {
    //   this.logger.debug(`addBacklist record already exists`, {
    //     conflicts,
    //   });
    //   throw new HttpException(
    //     { error: 'alreadyExists' },
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    // removed update logic
    // if (existingEmailBlacklist || existingPhoneBlacklist) {
    //   let id =
    //     existingEmailBlacklist?.customer_blacklist_id ||
    //     existingPhoneBlacklist?.customer_blacklist_id;

    //   let record = await this.customerBlacklistEntity.findOne({
    //     where: { customer_blacklist_id: id },
    //   });

    //   await this.customerBlacklistEntity.update(
    //     {
    //       customer_blacklist_id: id,
    //     },
    //     {
    //       phone_md5: hashedPhone,
    //       phone,
    //       brand: brand || record.brand,
    //       customer_email_md5: hashedEmail,
    //       customer_email: email,
    //     },
    //   );

    //   this.logger.debug(`addBlacklist ${company.id} result`, { id });
    //   return {
    //     status: 'updated',
    //     responseStatus: HttpStatus.OK,
    //     id,
    //   };
    // }

    const newBlacklist = await this.customerBlacklistEntity.save({
      customer_email: email || '[ENCRYPTED]',
      date_blacklisted: moment().utc().format('YYYY-MM-DD HH:mm'),
      sec_company_id: company.id,
      brand,
      customer_email_md5: hashedEmail,
      phone,
      phone_md5: hashedPhone,
    });

    return {
      status: 'created',
      responseStatus: HttpStatus.CREATED,

      id: newBlacklist.customer_blacklist_id,
    };
  }

  public async bulkAdd({ list }: AddBlacklistBulkDto) {
    let result: any = [];

    for (let item of list) {
      if (!item.phone && !item.email) {
        result.push({ status: 'required' });
        continue;
      } else if (item.email && !isEmail(item.email)) {
        result.push({
          status: `invalid`,
        });
        continue;
      }
      try {
        let data = await this.addBlacklist(item);
        result.push({ id: data.id, status: 'created' });
      } catch (err) {
        result.push({
          status: err?.response?.error || err?.message,
        });
      }
    }

    return result;
  }

  public async deleteBlacklist(id: number) {
    const { company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`deleteBlacklist ${id}`);

    const result = await this.customerBlacklistEntity.findOne({
      where: {
        sec_company_id: company.id,
        customer_blacklist_id: id,
      },
    });

    if (!result) {
      throw new HttpException(
        {
          msg: 'Unable to delete record',
          error: 'Unable to delete record',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.customerBlacklistEntity.delete({
      customer_blacklist_id: id,
      sec_company_id: company.id,
    });

    return {
      status: 'deleted',
    };
  }
}
