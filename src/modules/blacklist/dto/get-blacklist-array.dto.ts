import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsN<PERSON>ber, IsOptional, <PERSON>, <PERSON>, MinLength } from 'class-validator';

export class GetBlacklistQueryDto {
  @Type()
  @IsOptional()
  @MinLength(2)
  @ApiProperty({ required: false })
  search?: string;

  @Type()
  @IsOptional()
  @IsNumber()
  @ApiProperty({ required: false })
  page?: number = null;

  @Type()
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(200)
  @ApiProperty({ required: false })
  limit?: number = null;
}

export class BlacklistItemDto {
  @ApiProperty({ type: Number })
  id: number;

  @ApiProperty({ type: String })
  phone: string;

  @ApiProperty({ type: String })
  email: string;

  @ApiProperty({ type: String })
  brand: string;

  @ApiProperty({ type: String })
  date: string;
}

export class GetBlacklistsArrayDto {
  @ApiProperty({
    type: [BlacklistItemDto],
  })
  blacklist: BlacklistItemDto[];

  @ApiProperty()
  shown: number;

  @ApiProperty()
  notShown: number;
}

export class GetBlacklistsArrayResponseDto extends GetBlacklistsArrayDto {
  // @ApiProperty()
  // data: GetBlacklistsArrayDto;
}
