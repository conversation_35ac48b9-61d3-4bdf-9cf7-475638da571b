import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEmail,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

export class AddBlacklistDto {
  @ApiProperty({
    type: String,
    description: 'Phone number. Required if email is missing',
    required: false,
  })
  @IsString()
  @ValidateIf((o: AddBlacklistDto) => {
    return !o.email || !!o.phone;
  })
  phone: string;

  @ApiProperty({
    type: String,
    description: 'Email address. Required if phone is missing',
    required: false,
  })
  @IsEmail()
  @ValidateIf((o: AddBlacklistDto) => {
    return !o.phone || !!o.email;
  })
  email: string;

  @ApiProperty({
    type: String,
    description: 'Brand name',
    required: false,
  })
  @IsString()
  @IsOptional()
  brand: string;
}
export class AddBlacklistBulkDto {
  @Type(() => AddBlacklistDto)
  @ApiProperty({ type: AddBlacklistDto, isArray: true })
  readonly list: AddBlacklistDto[];
}

export class AddBlacklistSuccessDto {
  @ApiProperty()
  msg: string;

  @ApiProperty()
  id: number;
}

export class AddBlacklistErrorDto {
  @ApiProperty()
  msg: string;

  @ApiProperty()
  error: string;
}

export class AddBlacklistResponseDto extends AddBlacklistSuccessDto {
  // @ApiProperty()
  // data: AddBlacklistSuccessDto;
}

export class AddBlacklistErrorResponseDto extends AddBlacklistErrorDto {
  // @ApiProperty()
  // data: AddBlacklistErrorDto;
}
