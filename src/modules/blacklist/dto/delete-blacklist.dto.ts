import { ApiProperty } from '@nestjs/swagger';

export class DeleteBlacklistSuccessDto {
  @ApiProperty()
  msg: string;
}

export class DeleteBlacklistErrorDto {
  @ApiProperty()
  msg: string;

  @ApiProperty()
  error: string;
}

export class DeleteBlacklistResponseDto extends DeleteBlacklistSuccessDto {
  // @ApiProperty()
  // data: DeleteBlacklistSuccessDto;
}

export class DeleteBlacklistErrorResponseDto extends DeleteBlacklistErrorDto {
  // @ApiProperty()
  // data: DeleteBlacklistErrorDto;
}
