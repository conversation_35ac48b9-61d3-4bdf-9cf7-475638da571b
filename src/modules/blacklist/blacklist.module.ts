import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerBlacklistEntity } from '../database/entities/customer-blacklist.entity';
import { BlacklistController } from './blacklist.controller';
import { BlacklistService } from './blacklist.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CustomerBlacklistEntity]),
    ModuleLoggerModule.register('blacklist'),
  ],
  controllers: [BlacklistController],
  providers: [BlacklistService],
})
export class BlacklistModule {}
