import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserCompanyEntity } from '../database/entities/sec-user-company.entity';
import { ClubController, ProjectController } from './project.controller';
import { ProjectService } from './project.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { ProjectsEntity } from '../database/entities/projects.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserCompanyEntity, ProjectsEntity]),
    ModuleLoggerModule.register('project'),
  ],
  controllers: [ProjectController, ClubController],
  providers: [ProjectService],
  exports: [ProjectService],
})
export class ProjectModule {}
