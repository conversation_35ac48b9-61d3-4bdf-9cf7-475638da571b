import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectController } from './project.controller';
import { ProjectService } from './project.service';
import { ProjectsEntity } from '../database/entities/projects.entity';
import { ProjectCategoriesEntity } from '../database/entities/project-categories.entity';
import { ProjectGroupsEntity } from '../database/entities/project-groups.entity';
import { ContactEntity } from '../database/entities/contact.entity';
import { ModuleLoggerModule } from '../lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProjectsEntity,
      ProjectCategoriesEntity,
      ProjectGroupsEntity,
      ContactEntity,
    ]),
    ModuleLoggerModule.register('project'),
  ],
  controllers: [ProjectController],
  providers: [ProjectService],
  exports: [ProjectService],
})
export class ProjectModule {}