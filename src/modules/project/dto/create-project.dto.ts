
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateProjectDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  project_nr: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  project_name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  project_name_extern?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  project_city?: string;
}
