
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class UpdateProjectDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  project_nr?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  project_name?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  project_name_extern?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  project_city?: string;
}
