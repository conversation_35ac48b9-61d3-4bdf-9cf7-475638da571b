import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyAnswerEntity } from '../../database/entities/surv-surveyanswer.entity';
import { ProjectManagerService } from './project-manager.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SurveyAnswerEntity]),
    ModuleLoggerModule.register('project-manager'),
  ],
  providers: [ProjectManagerService],
  exports: [ProjectManagerService],
})
export class ProjectManagerModule {}
