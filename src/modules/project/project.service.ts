import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { ProjectsEntity, ProjectCategoriesEntity, ProjectGroupsEntity, ContactEntity } from '@entities';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { ModuleLogger } from '../lib/logger/module-logger/module-logger.service';

@Injectable()
export class ProjectService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(ProjectsEntity)
    private readonly projectRepository: Repository<ProjectsEntity>,
    @InjectRepository(ProjectCategoriesEntity)
    private readonly projectCategoriesRepository: Repository<ProjectCategoriesEntity>,
    @InjectRepository(ProjectGroupsEntity)
    private readonly projectGroupsRepository: Repository<ProjectGroupsEntity>,
    @InjectRepository(ContactEntity)
    private readonly contactRepository: Repository<ContactEntity>,
    private readonly dataSource: DataSource,
  ) {}

  public async list({ companyId = 0 } = {}) {
    const { company, userCompany } = this.cls.get<AuthorizedData>(
      ClsProperty.user,
    );

    companyId = +(companyId || company.id);

    this.logger.debug(`list for company ${companyId}`, { userCompany });

    let result = await this.projectRepository.find({
      where: {
        sec_company_id: company.id,
      },
    });

    this.logger.debug(`list result for company ${companyId}`, { result });

    return result.map((item) => ({
      id: item.project_id,
      name: item.project_name,
    }));
  }

  public async listByGroup({ companyId = 0 } = {}) {
    const { company, userCompany } = this.cls.get<AuthorizedData>(
      ClsProperty.user,
    );
    companyId = +(companyId || company.id);

    this.logger.debug(`list for company ${companyId}`, { userCompany });

    const groupFilter = `
    AND (project_id NOT IN (
      SELECT project_id
      FROM project_groups)
        OR project_id IN (SELECT project_id
        FROM project_groups
        WHERE group_id = ${userCompany.sec_group}))
    `;

    let result = await this.projectRepository.query(`
      SELECT project_id as id
      FROM projects
      WHERE sec_company_id = ${companyId}
          ${groupFilter}
    `);

    this.logger.debug(`list result for company ${companyId}`, { result });

    return result;
  }

  async findById(id: string) {
    const project = await this.projectRepository.findOne({ where: { project_id: id } });
    if (!project) {
      throw new NotFoundException(`Project with ID ${id} not found`);
    }
    return project;
  }

  async create(projectData: CreateProjectDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const newProject = this.projectRepository.create({
      ...projectData,
      project_id: uuidv4(),
      sec_company_id: company.id,
      creation_date: new Date(),
      modification_date: new Date(),
    });
    return this.projectRepository.save(newProject);
  }

  async update(id: string, projectData: UpdateProjectDto) {
    const project = await this.findById(id);
    Object.assign(project, projectData);
    project.modification_date = new Date();
    return this.projectRepository.save(project);
  }

  async delete(id: string) {
    const contacts = await this.contactRepository.find({ where: { project_id: id } });
    if (contacts.length > 0) {
      throw new ConflictException('Project has contacts and cannot be deleted.');
    }
    const project = await this.findById(id);
    return this.projectRepository.remove(project);
  }

  async addCategory(projectId: string, pcategoryId: string) {
    const project = await this.findById(projectId);
    const category = await this.projectCategoriesRepository.findOne({ where: { project_id: projectId, pcategory_id: pcategoryId } });
    if (category) {
        throw new ConflictException('Category already exists for this project.');
    }
    const projectCategory = this.projectCategoriesRepository.create({
      project_id: projectId,
      pcategory_id: pcategoryId,
    });
    return this.projectCategoriesRepository.save(projectCategory);
  }

  async removeCategory(projectId: string, pcategoryId: string) {
    const projectCategory = await this.projectCategoriesRepository.findOne({ where: { project_id: projectId, pcategory_id: pcategoryId } });
    if (!projectCategory) {
      throw new NotFoundException('Project category not found.');
    }
    return this.projectCategoriesRepository.remove(projectCategory);
  }

  async addGroup(projectId: string, groupId: string) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const project = await this.findById(projectId);
    const group = await this.projectGroupsRepository.findOne({ where: { project_id: projectId, group_id: groupId } });
    if (group) {
        throw new ConflictException('Group already exists for this project.');
    }
    const projectGroup = this.projectGroupsRepository.create({
      project_id: projectId,
      group_id: groupId,
      sec_company_id: company.id,
    });
    return this.projectGroupsRepository.save(projectGroup);
  }

  async removeGroup(projectId: string, groupId: string) {
    const projectGroup = await this.projectGroupsRepository.findOne({ where: { project_id: projectId, group_id: groupId } });
    if (!projectGroup) {
      throw new NotFoundException('Project group not found.');
    }
    return this.projectGroupsRepository.remove(projectGroup);
  }
}