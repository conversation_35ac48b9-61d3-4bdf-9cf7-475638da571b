import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiB<PERSON>erAuth,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from '@/shared/guards/auth.guard';

import { ProjectService } from './project.service';

@ApiTags('Project')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'project',
})
export class ProjectController {
  constructor(private projectService: ProjectService) {}

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Project list',
  })
  @Get('/')
  list() {
    return this.projectService.list();
  }
}

@ApiTags('Club')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'club',
})
export class ClubController {
  constructor(private projectService: ProjectService) {}

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'Club list',
  })
  @Get('/')
  list() {
    return this.projectService.list();
  }
}
