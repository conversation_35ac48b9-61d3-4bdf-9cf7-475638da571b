import { Controller, Get, Post, Body, Param, Delete, UseGuards, Query, ParseUUIDPipe } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiParam,
} from '@nestjs/swagger';
import { AuthGuard } from '@/shared/guards/auth.guard';

import { ProjectService } from './project.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { CreateProjectCategoryDto } from './dto/create-project-category.dto';
import { CreateProjectGroupDto } from './dto/create-project-group.dto';

@ApiTags('Project')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'project',
})
@UseGuards(AuthGuard)
export class ProjectController {
  constructor(private projectService: ProjectService) {}

  @ApiOkResponse({
    description: 'Project list or a single project by id',
  })
  @ApiQuery({ name: 'id', required: false, type: 'string' })
  @Get('/')
  find(@Query('id') id?: string) {
    if (id) {
      return this.projectService.findById(id);
    }
    return this.projectService.list();
  }

  @ApiOperation({ summary: 'Create a new project' })
  @Post('/')
  create(@Body() createProjectDto: CreateProjectDto) {
    return this.projectService.create(createProjectDto);
  }

  @ApiOperation({ summary: 'Update a project' })
  @ApiParam({ name: 'id', type: 'string' })
  @Post('/:id')
  update(@Param('id', ParseUUIDPipe) id: string, @Body() updateProjectDto: UpdateProjectDto) {
    return this.projectService.update(id, updateProjectDto);
  }

  @ApiOperation({ summary: 'Delete a project' })
  @ApiParam({ name: 'id', type: 'string' })
  @Delete('/:id')
  delete(@Param('id', ParseUUIDPipe) id: string) {
    return this.projectService.delete(id);
  }

  @ApiOperation({ summary: 'Add a category to a project' })
  @ApiParam({ name: 'id', type: 'string' })
  @Post('/:id/category')
  addCategory(@Param('id', ParseUUIDPipe) id: string, @Body() createProjectCategoryDto: CreateProjectCategoryDto) {
    return this.projectService.addCategory(id, createProjectCategoryDto.pcategory_id);
  }

  @ApiOperation({ summary: 'Remove a category from a project' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiParam({ name: 'categoryId', type: 'string' })
  @Delete('/:id/category/:categoryId')
  removeCategory(@Param('id', ParseUUIDPipe) id: string, @Param('categoryId', ParseUUIDPipe) categoryId: string) {
    return this.projectService.removeCategory(id, categoryId);
  }

  @ApiOperation({ summary: 'Add a group to a project' })
  @ApiParam({ name: 'id', type: 'string' })
  @Post('/:id/group')
  addGroup(@Param('id', ParseUUIDPipe) id: string, @Body() createProjectGroupDto: CreateProjectGroupDto) {
    return this.projectService.addGroup(id, createProjectGroupDto.group_id);
  }

  @ApiOperation({ summary: 'Remove a group from a project' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiParam({ name: 'groupId', type: 'string' })
  @Delete('/:id/group/:groupId')
  removeGroup(@Param('id', ParseUUIDPipe) id: string, @Param('groupId', ParseUUIDPipe) groupId: string) {
    return this.projectService.removeGroup(id, groupId);
  }
}