import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class GetTagsQueryDto {
  sec_company_id?: number;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  name?: string = null;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  type?: string = null;
}
