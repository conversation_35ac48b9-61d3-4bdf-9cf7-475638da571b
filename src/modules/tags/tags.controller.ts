import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { GetTagsQueryDto } from './dto/get-tags.dto';
import { TagsService } from './tags.service';

@ApiTags('Tags')
@Controller({
  path: 'tags',
  version: '2',
})
@ApiBearerAuth()
export class TagsController {
  constructor(private tagsService: TagsService) {}

  @Get()
  @UseGuards(AuthGuard)
  async getTags(
    @Query() query: GetTagsQueryDto,
    @Req() { company }: AuthorizedRequest,
  ) {
    query.sec_company_id = company.id;
    return this.tagsService.getTagsName(query);
  }

  @Get(':filter')
  @UseGuards(AuthGuard)
  async getFilteredTags(
    @Param('filter') filter: string,
    @Req() { company }: AuthorizedRequest,
  ) {
    let tags = await this.tagsService.getTagsName({
      name: filter,
      sec_company_id: company.id,
    });
    return {
      tags,
    };
  }
}
