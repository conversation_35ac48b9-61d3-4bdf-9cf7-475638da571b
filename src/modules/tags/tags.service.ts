import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, ILike, In, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { UpsertItemParams } from '../datatable/interfaces/datatable-service-params';
import { GetTagsQueryDto } from './dto/get-tags.dto';

import { ClfTagsEntity, TagsEntity } from '@entities';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class TagsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(TagsEntity)
    private readonly tagsEntity: Repository<TagsEntity>,

    @InjectRepository(ClfTagsEntity)
    private readonly clfTagRepo: Repository<ClfTagsEntity>,
  ) {}

  async getTags({ sec_company_id, name, type }: GetTagsQueryDto = {}) {
    this.logger.debug(`getTags for company ${sec_company_id}`, { name, type });
    let where: FindOptionsWhere<TagsEntity> = {
      sec_company_id,
    };
    if (name?.length) {
      where.tag_name = ILike(`%${name}%`);
    }
    if (type?.length) {
      where.tag_type = type;
    }
    let result = await this.tagsEntity.find({ where });
    this.logger.debug(`getTags result for company ${sec_company_id}`, {
      result,
    });
    return result;
  }

  async getTagsName(query: GetTagsQueryDto = {}) {
    let list = await this.getTags(query);
    return list.map((item) => item.tag_name);
  }

  async getDatatableTags(sec_company_id) {
    let list = await this.getTags({ sec_company_id });
    let rows = list.map((item) => ({ id: item.tag_id, name: item.tag_name }));
    return {
      count: rows.length,
      rows,
    };
  }

  async getTag(tag_id: number) {
    let where: FindOptionsWhere<TagsEntity> = {
      tag_id,
    };
    return await this.tagsEntity.findOne({ where });
  }

  async delete(companyId: number, ids: string) {
    this.logger.debug(`delete tag ids ${ids}`, { companyId });
    let tags = await this.tagsEntity.find({
      where: { tag_id: In(ids.split(',')) },
    });
    let errors = [];
    if (tags.length == 0) throw new HttpException('tag not found', 404);
    for (let tag of tags) {
      if (tag?.sec_company_id != companyId) {
        errors.push([`tag not found`, 404]);
        continue;
      }
      let clfTag = await this.clfTagRepo.findOne({
        where: { tag_id: tag.tag_id },
      });

      if (clfTag) {
        errors.push([
          'This tag is in use in CLF and can therefore not be deleted',
          400,
        ]);
        continue;
      }
      await this.tagsEntity.delete({ tag_id: tag.tag_id });
    }
    if (tags.length == errors.length) {
      this.logger.debug(`delete error`, { errors });
      if (errors.length == 1)
        throw new HttpException(errors[0][0], errors[0][1]);
      else throw new HttpException(errors, 400);
    }

    let result = { message: 'Tag(s) deleted', errors };

    this.logger.debug('delete result', { result });
    return result;
  }

  async update(companyId: number, params: UpsertItemParams) {
    const { id, name } = params;

    const { user } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`update id ${id}`, { name, user: user.id });

    let tag = id && (await this.getTag(id));
    if (id && tag?.sec_company_id != companyId)
      throw new BadRequestException('Not Found');
    let result;
    if (!id) {
      result = await this.tagsEntity.save(
        this.tagsEntity.create({
          tag_name: name,
          sec_company_id: companyId,
          created_by: user.id,
          created_on: new Date().toISOString(),
        }),
      );
    } else {
      tag.tag_name = name;
      result = await this.tagsEntity.save(tag);
    }

    this.logger.debug(`update result for id ${id}`, { result });
    return {
      success: true,
      msg: !tag ? 'Added' : 'Updated',
    };
  }
}
