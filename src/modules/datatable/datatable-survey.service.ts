import { Brackets, In, Repository } from 'typeorm';
import { SurvSurveyCategoriesEntity } from '../database/entities/surv-survey-categories.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SurvCategoriesEntity } from '../database/entities/surv-categories.entity';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UpsertItemParams } from './interfaces/datatable-service-params';
import { DBHelperService } from '../core/db-helper/db-helper.service';
import { SurvCategoriesLocalEntity } from '../database/entities/surv-categories-local.entity';
import { alias, rawQuery } from '../../shared/utils/query-convert';
import { adminCompanies } from '../../config/company.config';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { ProjectCategoriesEntity } from '../database/entities/project-categories.entity';
import { PCategoriesEntity } from '../database/entities/pcategories.entity';
import { v4 } from 'uuid';

export class DatatableSurveyService {
  constructor(
    private cls: ClsService,
    private dbHelper: DBHelperService,
    @InjectRepository(SurvCategoriesEntity)
    private readonly categoryRepo: Repository<SurvCategoriesEntity>,
    @InjectRepository(PCategoriesEntity)
    private readonly pcategoryRepo: Repository<PCategoriesEntity>,

    @InjectRepository(SurvCategoriesLocalEntity)
    private readonly categoryLocalRepo: Repository<SurvCategoriesLocalEntity>,

    @InjectRepository(SurvSurveyCategoriesEntity)
    private readonly surveyCategoryRepo: Repository<SurvSurveyCategoriesEntity>,
    @InjectRepository(ProjectCategoriesEntity)
    private readonly projectCategoryRepo: Repository<ProjectCategoriesEntity>,
  ) {}

  getCategory(id: number) {
    return this.categoryRepo.findOne({ where: { category_id: id } });
  }

  async list(sec_company_id: number) {
    const dataQuery = await this.surveyCategoryRepo
      .createQueryBuilder('c')
      .select(alias('c.category_id', 'array_agg(c.surv_survey_id) AS survs'))
      .leftJoin('c.survey', 's')
      .where('s.sec_company_id = :sec_company_id', { sec_company_id })
      .groupBy('c.category_id')
      .getQuery();

    let list = await this.categoryRepo
      .createQueryBuilder('sc')
      .select(
        alias(
          'sc.category_id',
          'sc.sec_company_id',
          'sc.category as category_name',
          'l.category as local_name',
          'd.survs',
          'CASE WHEN sc.sec_company_id = 25 OR sc.sec_company_id = 1 OR sc.sec_company_id IS NULL THEN 1 ELSE 0 END AS is_global',
          'CASE WHEN sc.sec_company_id = :sec_company_id THEN 1 ELSE 0 END AS can_delete',
        ),
      )
      .leftJoin(
        'sc.categoriesLocal',
        'l',
        'l.sec_company_id = :sec_company_id',
        { sec_company_id },
      )
      .leftJoin(`(${dataQuery})`, 'd', 'sc.category_id = d.category_id')
      .where(
        new Brackets((qb) => {
          qb.where('sc.sec_company_id IN(:...ids)', {
            ids: [sec_company_id, ...adminCompanies],
          });
          qb.orWhere('sc.sec_company_id IS NULL');
          return qb;
        }),
      )
      .getRawMany();

    list = list.map((item) => {
      return {
        id: item.category_id,
        name: item.category_name,
        local_name: item.local_name,
        is_global: item.is_global,
        can_edit: item.sec_company_id == sec_company_id,
        can_delete: item.can_delete,
        touchpoints: item.survs || [],
      };
    });

    return {
      count: list.length,
      rows: list,
    };
  }
  async listProject(sec_company_id: number) {
    const dataQuery = rawQuery(
      this.projectCategoryRepo
        .createQueryBuilder('c')
        .select(alias('c.pcategory_id', 'array_agg(c.project_id) AS projects'))
        .leftJoin('c.project', 'p')
        .where('p.sec_company_id = :sec_company_id', { sec_company_id })
        .groupBy('c.pcategory_id'),
    );

    let list = await this.pcategoryRepo
      .createQueryBuilder('pc')
      .select(
        alias(
          'pc.pcategory_id',
          'pc.sec_company_id',
          'pc.category as category_name',
          'd.projects',
        ),
      )
      .leftJoin(`(${dataQuery})`, 'd', 'pc.pcategory_id = d.pcategory_id')
      .where(
        new Brackets((qb) => {
          qb.where('pc.sec_company_id IN(:...ids)', {
            ids: [sec_company_id, ...adminCompanies],
          });
          qb.orWhere('pc.sec_company_id IS NULL');
          return qb;
        }),
      )
      .getRawMany();

    list = list.map((item) => {
      return {
        id: item.pcategory_id,
        name: item.category_name,
        touchpoints: item.projects || [],
      };
    });

    return {
      count: list.length,
      rows: list,
    };
  }

  async update(params: UpsertItemParams) {
    const { id } = params; // user.sec_company_id
    const { name, local_name, touchpoints } = params;

    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    if (!id && (!name || !touchpoints))
      throw new BadRequestException('fill all data');

    let survey = id && (await this.getCategory(id));
    let categoryId = survey?.category_id;
    let added = false;
    if (id && !survey) throw new NotFoundException('Not Found');
    if (!id) {
      categoryId = await this.dbHelper.getNextId(
        SurvCategoriesEntity,
        'category_id',
      );

      await this.categoryRepo.insert({
        category_id: categoryId,
        category: name,
        sec_company_id: company.id,
      });

      added = true;
    } else if (survey?.sec_company_id == company.id && name?.length) {
      await this.categoryRepo.update(
        { category_id: categoryId },
        { category: name },
      );
    }

    if (local_name?.length) {
      const nextId = await this.dbHelper.getNextId(
        SurvCategoriesLocalEntity,
        'surv_category_local_id',
      );

      await this.categoryLocalRepo.delete({
        category_id: categoryId,
        sec_company_id: company.id,
      });

      await this.categoryLocalRepo.insert({
        surv_category_local_id: nextId,
        category_id: categoryId,
        sec_company_id: company.id,
        category: local_name,
      });
    }

    if (touchpoints && Array.isArray(touchpoints)) {
      await this.surveyCategoryRepo.delete({ category_id: categoryId });
      for (let surveyId of touchpoints) {
        await this.surveyCategoryRepo.save(
          this.surveyCategoryRepo.create({
            surv_survey_id: +surveyId,
            category_id: categoryId,
          }),
        );
      }
    }
    return { success: true, msg: added ? 'Added' : 'Updated', id: categoryId };
  }

  async updateProject(params: UpsertItemParams) {
    const { id } = params; // user.sec_company_id
    const { name, touchpoints } = params;

    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    if (!id && (!name || !touchpoints))
      throw new BadRequestException('fill all data');

    let project =
      id && (await this.pcategoryRepo.findOne({ where: { pcategory_id: id } }));
    let categoryId = project?.pcategory_id;
    let added = false;
    if (id && !project) throw new NotFoundException('Not Found');
    if (!id) {
      categoryId = v4();

      await this.pcategoryRepo.insert({
        pcategory_id: categoryId,
        category: name,
        sec_company_id: company.id,
      });

      added = true;
    } else if (project?.sec_company_id == company.id && name?.length) {
      await this.pcategoryRepo.update(
        { pcategory_id: categoryId },
        { category: name },
      );
    }

    if (touchpoints && Array.isArray(touchpoints)) {
      await this.projectCategoryRepo.delete({ pcategory_id: categoryId });
      for (let itemId of touchpoints) {
        await this.projectCategoryRepo.save(
          this.projectCategoryRepo.create({
            project_pcategory_id: v4(),
            project_id: itemId,
            pcategory_id: categoryId,
          }),
        );
      }
    }
    return { success: true, msg: added ? 'Added' : 'Updated', id: categoryId };
  }

  async delete(ids: string) {
    let list = ids.split(',');

    await this.categoryRepo.delete({ category_id: In(list) });
    await this.surveyCategoryRepo.delete({ category_id: In(list) });
    await this.categoryLocalRepo.delete({ category_id: In(list) });

    return { success: true, message: 'Survey(s) deleted', ids };
  }

  async deleteProject(ids: string) {
    let list = ids.split(',');

    await this.pcategoryRepo.delete({ pcategory_id: In(list) });
    await this.projectCategoryRepo.delete({ pcategory_id: In(list) });

    return { success: true, message: 'Project(s) deleted', ids };
  }
}
