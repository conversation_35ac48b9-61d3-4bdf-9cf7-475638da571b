import { In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { SurvCategoriesEntity } from '../database/entities/surv-categories.entity';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UpsertItemParams } from './interfaces/datatable-service-params';
import { SurvCategoriesLocalEntity } from '../database/entities/surv-categories-local.entity';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { ClfCategoryListEntity } from '@entities';
import { DBHelperService } from '../core/db-helper/db-helper.service';

export class DatatableClfService {
  constructor(
    private cls: ClsService,
    private dbHelper: DBHelperService,
    @InjectRepository(ClfCategoryListEntity)
    private readonly clfCategoryListRepo: Repository<ClfCategoryListEntity>,
  ) {}

  async categoryList(listName: string, sec_company_id: number) {
    let list = await this.clfCategoryListRepo
      .createQueryBuilder('c')
      .where('c.sec_company_id = :sec_company_id', { sec_company_id })
      .andWhere('c.list = :listName', { listName })
      .orderBy('c.ordernumber')
      .getMany();

    let result = list.map((item) => {
      return {
        id: item.item_id,
        list: item.list,
        description: item.description,
        template: item.template_id,
        order: item.ordernumber,
      };
    });

    return {
      count: result.length,
      rows: result,
    };
  }

  async item(id) {
    return this.clfCategoryListRepo.findOne({ where: { item_id: id } });
  }

  async update(list: string, params: any) {
    try {
      let { id, description, template, order } = params;
      const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

      if (!id && !list) throw new BadRequestException('fill all data');

      let item = id && (await this.item(id));
      let added = false;
      if (id && !item) throw new NotFoundException('Not Found');
      if (!id) {
        id = await this.dbHelper.getNextId(ClfCategoryListEntity, 'item_id');

        await this.clfCategoryListRepo.insert({
          item_id: id,
          list,
          description,
          ordernumber: order,
          template_id: template,
          email: user.email,
          sec_company_id: company.id,
        });

        added = true;
      } else if (item?.sec_company_id == company.id) {
        await this.clfCategoryListRepo.update(
          { item_id: id },
          {
            list,
            description,
            ordernumber: order,
            template_id: template,
          },
        );
      }
      return { success: true, msg: added ? 'Added' : 'Updated', id };
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  async delete(ids: string) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let list = ids.split(',');

    await this.clfCategoryListRepo.delete({
      item_id: In(list),
      sec_company_id: company.id,
    });

    return { success: true, message: 'Items deleted', ids };
  }
}
