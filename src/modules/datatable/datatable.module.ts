import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TagsModule } from '../tags/tags.module';

import { DatatableConroller } from './datatable.controller';

import { GdprService } from '../gdpr/gdpr.service';
import { DatatableService } from './datatable.service';
import { DatatableSurveyService } from './datatable-survey.service';
import { DatatableSalutationService } from './datatable-salutation.service';

import {
  ClfCategoryListEntity,
  DatatableEntity,
  PCategoriesEntity,
  ProjectCategoriesEntity,
  SalutationEntity,
  SalutationItemEntity,
  SearchListEntity,
  SurvCategoriesEntity,
  SurvCategoriesLocalEntity,
  SurveyAnswerEntity,
  SurvSurveyCategoriesEntity,
  TechgrpEntity,
  UserEntity,
} from '@entities';
import { DatatableClfService } from './datatable-clf.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ClfCategoryListEntity,
      DatatableEntity,
      TechgrpEntity,
      SurveyAnswerEntity,
      UserEntity,
      SalutationEntity,
      SalutationItemEntity,
      SurvSurveyCategoriesEntity,
      SurvCategoriesLocalEntity,
      SurvCategoriesEntity,
      SearchListEntity,
      PCategoriesEntity,
      ProjectCategoriesEntity,
    ]),
    TagsModule,
  ],
  controllers: [DatatableConroller],
  providers: [
    DatatableService,
    DatatableClfService,
    DatatableSurveyService,
    DatatableSalutationService,
    GdprService,
  ],
})
export class DatatableModule {}
