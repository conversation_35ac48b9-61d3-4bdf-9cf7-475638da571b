import { DeepPartial, In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { UpsertItemParams } from './interfaces/datatable-service-params';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import { SalutationEntity, SalutationItemEntity } from '@entities';
import { ClsService } from 'nestjs-cls';

export class DatatableSalutationService {
  constructor(
    private cls: ClsService,

    @InjectRepository(SalutationItemEntity)
    private readonly salutatioItemRepo: Repository<SalutationItemEntity>,

    @InjectRepository(SalutationEntity)
    private readonly salutationRepo: Repository<SalutationEntity>,
  ) {}

  async list(sec_company_id: number) {
    let list = await this.salutationRepo.find({ where: { sec_company_id } });

    const ids = list.map((i) => i.salutation_id);

    let items = ids.length
      ? await this.salutatioItemRepo.find({
          where: {
            salutation_id: In(ids),
          },
        })
      : [];

    let rows = list.map((s) => {
      return {
        id: s.salutation_id,
        name: s.salutation_greet,
        language: s.salutation_language,
        items: items
          .filter((i) => i.salutation_id == s.salutation_id)
          .map((i) => {
            return {
              id: i.salutation_item_id,
              name: i.salutation_item,
            };
          }),
      };
    });
    return {
      count: rows.length,
      rows,
    };
  }

  async update(params: UpsertItemParams) {
    const { row } = params;
    const { id, items, name, language } = row;

    const { user, company } = this.cls.get<AuthorizedData>('user');

    let queryList = [];

    let salutation: DeepPartial<SalutationEntity> = {
      salutation_greet: name,
      salutation_language: language,
      sec_company_id: company.id,
      modified_by: user.id,
      modified_on: new Date(),
    };
    if (id > 0) {
      salutation.salutation_id = id;
    } else {
      salutation.creator = user.id;
      salutation.creation_date = new Date();
    }

    salutation = await this.salutationRepo.save(salutation);
    await this.salutatioItemRepo.delete({
      salutation_id: salutation.salutation_id,
    });
    for (let name of items) {
      let item: DeepPartial<SalutationItemEntity> = {
        salutation_id: salutation.salutation_id,
        salutation_item: name,
        sec_company_id: company.id,
        creator: user.id,
        creation_date: new Date(),
      };
      queryList.push(this.salutatioItemRepo.save(item));
    }

    let result = await Promise.all(queryList);
    return {
      id: salutation.salutation_id,
      name: salutation.salutation_greet,
      language: salutation.salutation_language,
      items: result.map((i) => {
        return {
          id: i.salutation_item_id,
          item: i.salutation_item,
        };
      }),
    };
  }

  async delete(ids, sec_company_id) {
    await this.salutatioItemRepo.delete({
      salutation_id: In(ids?.split(',').map((id) => +id)),
      sec_company_id,
    });
    return await this.salutationRepo.delete({
      salutation_id: In(ids?.split(',').map((id) => +id)),
      sec_company_id,
    });
  }
}
