import { TableSort } from '@/shared/enums';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';

export class GetDataTableListDto {
  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  table: string;

  @Type()
  @ApiProperty({ default: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @Type()
  @ApiProperty({ default: 100 })
  @IsInt()
  @IsOptional()
  @Min(1)
  @Type(() => Number)
  limit?: number = 100;

  @Type()
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  search: string;

  @ApiProperty({
    required: false,
    description: 'sort column',
  })
  @IsOptional()
  @IsString()
  active?: string;

  @ApiProperty({
    required: false,
    enum: TableSort,
    description: 'sort direction',
  })
  @IsOptional()
  @IsEnum(TableSort)
  direction?: TableSort;
}
