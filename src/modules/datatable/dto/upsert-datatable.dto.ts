import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  MaxLength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from 'class-validator';

export class UpsertDatatableQueryDto {
  @ApiProperty({ required: true })
  @MinLength(1)
  table: string;
}

export class UpsertDataTableRowParams {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  id: number;

  @ApiProperty({ required: false })
  key: string;

  @ApiProperty({ required: false })
  name: string;

  @ApiProperty({ required: false })
  description: string;

  @ApiProperty({ required: false })
  code: string;

  @ApiProperty({ required: false })
  firstname: string;

  @ApiProperty({ required: false })
  lastname: string;

  @ApiProperty({ required: false })
  email: string;

  @ApiProperty({ required: false })
  department: string;

  @ApiProperty({ required: false })
  region: string;

  @ApiProperty({ required: false })
  ordernumber: number;

  @ApiProperty({ required: false })
  template_id: number;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  @MaxLength(5)
  language: string;

  @ApiProperty({ required: false })
  page: number;

  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  items: string[];
}

export class UpsertDatatableDto {
  @ApiProperty({ required: false })
  id: any;

  @Type(() => UpsertDataTableRowParams)
  @IsOptional()
  @ValidateNested()
  @ApiProperty({ type: UpsertDataTableRowParams })
  row: UpsertDataTableRowParams;

  @ApiProperty({ required: false })
  name: string;

  @ApiProperty({ required: false })
  local_name: string;

  @ApiProperty({ required: false })
  is_global: number = 0;

  @ApiProperty({ required: false })
  can_delete: number = 1;

  @ApiProperty({ required: false })
  touchpoints: string[];
}
