import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, FindOptionsWhere, ILike, In, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { TagsService } from '../tags/tags.service';
import { DatatableSurveyService } from './datatable-survey.service';
import { DatatableSalutationService } from './datatable-salutation.service';

import { employeeFields } from '@/config/contents/data-table.contents';
import { DatatableEntity, SearchListEntity, TechgrpEntity } from '@entities';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import {
  GetDataTableListParams,
  UpsertItemParams,
} from './interfaces/datatable-service-params';
import { DatatableClfService } from './datatable-clf.service';

@Injectable()
export class DatatableService {
  constructor(
    private cls: ClsService,
    private datatableClfService: DatatableClfService,
    private datatableSurveyService: DatatableSurveyService,
    private datatableSalutationService: DatatableSalutationService,
    private tagService: TagsService,

    @InjectRepository(DatatableEntity)
    private readonly datatableEntity: Repository<DatatableEntity>,
    @InjectRepository(TechgrpEntity)
    private readonly techgrpEntity: Repository<TechgrpEntity>,
    @InjectRepository(SearchListEntity)
    private readonly searchListEntity: Repository<SearchListEntity>,
  ) {}

  async list(params: GetDataTableListParams) {
    let { table } = params;

    const { company } = this.cls.get<AuthorizedData>('user');

    if (this.isTechgrp(table)) table = 'techgrp';

    switch (table) {
      case 'wordlists':
        return this.getSearchlist(company.id);
      case 'tags':
        return this.tagService.getDatatableTags(company.id);
      case 'salutations':
        return this.datatableSalutationService.list(company.id);
      case 'survey_categories':
        return this.datatableSurveyService.list(company.id);
      case 'project_categories':
        return this.datatableSurveyService.listProject(company.id);
      case 'club_categories':
        return this.datatableSurveyService.listProject(company.id);
      case 'clf_results':
        return this.datatableClfService.categoryList('clf_result', company.id);
      case 'techgrp':
        return this.getTechgrpList(params);
      default:
        return this.getBSHList(params);
    }
  }

  async getBSHList(params: GetDataTableListParams) {
    const { table, active, direction, limit, page, search } = params;

    const { company } = this.cls.get<AuthorizedData>('user');

    let convertActive = {
      description: 'map_description',
      key: 'map_key',
      id: 'map_id',
    };
    let orderKey =
      active && convertActive[active] ? convertActive[active] : 'map_id';
    let sortType = direction?.length > 0 ? direction.toUpperCase() : 'DESC';
    let where: FindOptionsWhere<DatatableEntity> = {
      sec_company_id: company.id,
      map_field: table,
    };
    if (search?.length > 0) {
      where.map_key = ILike(`%${search}%`);
    }
    let result = await this.datatableEntity.findAndCount({
      where,
      take: limit,
      skip: (page - 1) * limit,
      order: { [orderKey]: sortType },
    });
    return {
      count: result[1],
      rows: result[0].map((item) => ({
        id: item.map_id,
        key: item.map_key,
        description: item.map_description,
      })),
      order: sortType.toLowerCase(),
    };
  }

  async getTechgrpList(params: GetDataTableListParams) {
    const { table, active, direction, limit, page, search } = params;

    const { company } = this.cls.get<AuthorizedData>('user');

    let convertActive = {
      id: 'techgrp_id',
      code: 'techgrp_techn',
      firstname: 'techgrp_firstname',
      lastname: 'techgrp_name',
      email: 'techgrp_email',
      department: 'techgrp_dep',
      region: 'techgrp_group',
    };
    let orderKey =
      active && convertActive[active] ? convertActive[active] : 'techgrp_id';
    let sortType: any =
      direction?.length > 0 ? direction.toUpperCase() : 'DESC';

    let query = this.techgrpEntity
      .createQueryBuilder()
      .where('sec_company_id =:cid', { cid: company.id })
      .andWhere('techgrp_type =:table', { table });
    if (search?.length) {
      let searchContains = `%${search}%`;
      query.andWhere(
        new Brackets((qb) => {
          qb.where('techgrp_techn ILIKE :search', { search: searchContains });
          qb.orWhere('techgrp_firstname ILIKE :search', {
            search: searchContains,
          });
          qb.orWhere('techgrp_name ILIKE :search', { search: searchContains });
          qb.orWhere('techgrp_dep ILIKE :search', { search: searchContains });
          qb.orWhere('techgrp_email ILIKE :search', { search: searchContains });
          return qb;
        }),
      );
    }

    query
      .orderBy(orderKey, sortType)
      .take(limit)
      .skip((page - 1) * limit);

    let [result, total] = await query.getManyAndCount();

    return {
      count: total,
      rows: result.map((item) => ({
        id: item.techgrp_id,
        code: item.techgrp_techn,
        firstname: item.techgrp_firstname,
        lastname: item.techgrp_name,
        email: item.techgrp_email,
        department: item.techgrp_dep,
        region: item.techgrp_group,
      })),
      order: sortType.toLowerCase(),
    };
  }

  async getSearchlist(sec_company_id: number) {
    let list = await this.searchListEntity.find({ where: { sec_company_id } });
    let rows = list.map((item) => {
      return {
        id: item.sl_id,
        name: item.sl_name,
        values: item.sl_values,
      };
    });
    return {
      count: rows.length,
      rows,
    };
  }

  async upsertItem(params: UpsertItemParams) {
    const { id, row, table } = params;

    const { company } = this.cls.get<AuthorizedData>('user');

    switch (table) {
      case 'survey_categories':
        return this.datatableSurveyService.update(params);
      case 'project_categories':
        return this.datatableSurveyService.updateProject(params);
      case 'club_categories':
        return this.datatableSurveyService.updateProject(params);
      case 'clf_results':
        return this.datatableClfService.update('clf_result', params);
      case 'tags':
        return this.tagService.update(company.id, params);
      case 'salutations':
        return this.datatableSalutationService.update(params);
      default:
        break;
    }
    if (this.isTechgrp(table)) {
      let data: Partial<TechgrpEntity> = {
        sec_company_id: company.id,
        techgrp_techn: row.code,
        techgrp_type: table,
      };
      if (id) data.techgrp_id = id;
      if (row.email) data.techgrp_email = row.email;
      if (row.firstname) data.techgrp_firstname = row.firstname;
      if (row.lastname) data.techgrp_name = row.lastname;
      if (row.department) data.techgrp_dep = row.department;
      if (row.region) data.techgrp_group = row.region;

      let isExists = await this.techgrpEntity
        .createQueryBuilder()
        .where('techgrp_techn =:code', { code: row.code })
        .andWhere('techgrp_type =:table', { table })
        .andWhere('sec_company_id =:cid', { cid: company.id })
        .getOne();
      if (isExists?.techgrp_id && isExists?.techgrp_id != id)
        throw new HttpException('key is exists', 400);

      let item = await this.techgrpEntity.save(data);
      return {
        id: item.techgrp_id,
        code: item.techgrp_techn,
        firstname: item.techgrp_firstname,
        lastname: item.techgrp_name,
        email: item.techgrp_email,
        department: item.techgrp_dep,
      };
    } else {
      let data: Partial<DatatableEntity> = {
        map_field: table,
        sec_company_id: company.id,
        isactive: 1,
        map_key: row.key,
      };
      if (id) data.map_id = id;
      if (row.key) data.map_key = row.key;
      if (row.description) data.map_description = row.description;

      let isExists = await this.datatableEntity
        .createQueryBuilder()
        .where('map_key =:key', { key: row.key })
        .andWhere('map_field =:table', { table })
        .andWhere('sec_company_id =:cid', { cid: company.id })
        .getOne();
      if (isExists?.map_id && isExists?.map_id != id)
        throw new HttpException('key is exists', 400);
      let result = await this.datatableEntity.save(data);
      return {
        id: result.map_id,
        key: result.map_key,
        description: result.map_description,
      };
    }
  }

  deleteItem(ids: any, table: string) {
    const { company } = this.cls.get<AuthorizedData>('user');

    switch (true) {
      case table === 'survey_categories':
        return this.datatableSurveyService.delete(ids);
      case table === 'project_categories':
        return this.datatableSurveyService.deleteProject(ids);
      case table === 'club_categories':
        return this.datatableSurveyService.deleteProject(ids);
      case table === 'clf_results':
        return this.datatableClfService.delete(ids);
      case table === 'tags':
        return this.tagService.delete(company.id, ids);
      case table === 'salutations':
        return this.datatableSalutationService.delete(ids, company.id);
      case this.isTechgrp(table):
        return this.techgrpEntity.delete({
          techgrp_id: In(ids?.split(',').map((id) => +id)),
          techgrp_type: table,
          sec_company_id: company.id,
        });
      default:
        return this.datatableEntity.delete({
          map_id: In(ids?.split(',').map((id) => +id)),
          map_field: table,
          sec_company_id: company.id,
        });
    }
  }

  isTechgrp(field) {
    return employeeFields.includes(field);
  }
}
