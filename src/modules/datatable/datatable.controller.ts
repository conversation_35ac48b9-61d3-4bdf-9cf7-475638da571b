import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';

import {
  DeleteDataTableDto,
  GetDataTableListDto,
  UpsertDatatableDto,
  UpsertDatatableQueryDto,
} from './dto';

import { DatatableService } from './datatable.service';

@ApiTags('Datatable')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'data_table',
})
export class DatatableConroller {
  constructor(private datatableService: DatatableService) {}

  @ApiOkResponse({
    description: 'Datatable',
  })
  @UseGuards(AuthGuard)
  @Get()
  getList(@Query() query: GetDataTableListDto) {
    const { page, search, active, direction, table, limit } = query;
    return this.datatableService.list({
      table,
      page,
      limit,
      search,
      active,
      direction,
    });
  }

  @UseGuards(AuthGuard)
  @Post()
  @ApiOkResponse({
    status: HttpStatus.OK,
  })
  upsertData(
    @Req() request: AuthorizedRequest,
    @Query() query: UpsertDatatableQueryDto,
    @Body() body: UpsertDatatableDto,
  ) {
    return this.datatableService.upsertItem({
      ...body,
      ...query,
      table: query.table,
    });
  }

  @UseGuards(AuthGuard)
  @Delete()
  @ApiOkResponse({
    status: HttpStatus.OK,
  })
  deleteGdrp(@Query() params: DeleteDataTableDto) {
    return this.datatableService.deleteItem(params.id, params.table);
  }
}
