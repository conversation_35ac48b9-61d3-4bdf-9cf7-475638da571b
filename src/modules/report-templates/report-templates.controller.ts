import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ReportTemplatesService } from './report-templates.service';

@ApiTags('Report Templates')
@ApiBearerAuth()
@Controller({
  path: 'report_templates',
  version: '2',
})
export class ReportTemplatesController {
  constructor(private reportTemplatesService: ReportTemplatesService) {}

  @Get()
  @UseGuards(AuthGuard)
  async getReportTemplates() {
    let report_templates =
      await this.reportTemplatesService.getReportTemplates();
    return {
      report_templates,
    };
  }
}
