import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReportTemplatesController } from './report-templates.controller';
import { ReportTemplatesService } from './report-templates.service';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import { ReportTemplatesEntity } from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('report-templates'),
    TypeOrmModule.forFeature([ReportTemplatesEntity]),
    ProgramSettingsModule,
  ],
  controllers: [ReportTemplatesController],
  providers: [ReportTemplatesService],
})
export class ReportTemplatesModule {}
