import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import { ProgramSettingsService } from '../common/program-settings/program-settings.service';

import { ClsProperty } from '@/config/contents';
import { ReportTemplatesEntity } from '@entities';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { rawQuery } from '@/shared/utils';

export class ReportTemplatesService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private programSettingsService: ProgramSettingsService,
    @InjectRepository(ReportTemplatesEntity)
    private readonly reportTemplatesEntity: Repository<ReportTemplatesEntity>,
  ) {}
  async getReportTemplates() {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`getReportTemplates for company ${company.id}`);

    let programSettings = await this.programSettingsService.get(company.id);
    let flagBenchmark = programSettings?.flag_benchmark || false;

    let query = this.reportTemplatesEntity.createQueryBuilder();
    query.where(
      new Brackets((qb) => {
        qb.where(`company_type is NULL`);

        let types: any[] = [company.type];
        if (company.id === 117 && company.code === 'Harmony') {
          types.push(-2);
        }
        qb.orWhere(`company_type IN(:...types)`, { types });
      }),
    );

    if (company.isParent) {
      query.andWhere(`for_parent_company = 1`);
    }

    if (!flagBenchmark) {
      query.andWhere(`report_name != :name`, { name: 'Benchmark' });
    }

    query.orderBy('report_template_id', 'ASC');

    this.logger.debug(`getReportTemplates query for company ${company.id}`, {
      sql: rawQuery(query),
    });

    let list = await query.getMany();
    let result = list.map((item) => {
      return {
        template_id: item.report_template_id,
        template_name: item.report_name,
        short_description: item.short_description,
        preferred_charttype: item.preferred_charttype,
        allowed_charttypes: item.allowed_charttypes,
        image: item.report_image,
      };
    });

    this.logger.debug(`getReportTemplates result for company ${company.id}`, {
      result,
    });

    return result;
  }
}
