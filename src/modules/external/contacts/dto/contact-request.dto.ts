import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsString, ValidateNested, IsNumber, IsOptional, IsBoolean } from 'class-validator';

export class ContactDto {
  @ApiProperty({
    description: 'First name',
    example: 'Jan'
  })
  @IsString()
  firstname: string;

  @ApiProperty({
    description: 'Build number',
    example: '19'
  })
  @IsString()
  buildnumber: string;

  @ApiProperty({
    description: 'Contact phase',
    example: 'naopleveringbbHuur'
  })
  @IsString()
  fase: string;

  @ApiProperty({
    description: 'Street name',
    example: 'Ringlaan'
  })
  @IsString()
  address: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  @IsString()
  mail: string;

  @ApiProperty({
    description: 'City name',
    example: 'Rijen'
  })
  @IsString()
  city: string;

  @ApiProperty({
    description: 'Contact initials',
    example: ''
  })
  @IsString()
  initials: string;

  @ApiProperty({
    description: 'House number',
    example: 190
  })
  @IsNumber()
  house_number: number;

  @ApiProperty({
    description: 'Contact type',
    example: 1
  })
  @IsNumber()
  type: number;

  @ApiProperty({
    description: 'Project number',
    example: '123'
  })
  @IsString()
  project_number: string;

  @ApiProperty({
    description: 'Phone number',
    example: '06123456789'
  })
  @IsString()
  phone: string;

  @ApiProperty({
    description: 'Postal code',
    example: '1234 AA'
  })
  @IsString()
  postalcode: string;

  @ApiProperty({
    description: 'House number addition',
    example: ''
  })
  @IsString()
  @IsOptional()
  house_number_addition?: string;

  @ApiProperty({
    description: 'Contact name',
    example: 'Jansen'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Salutation',
    example: ''
  })
  @IsString()
  salutation: string;
}

export class ContactRequestDto {
  @ApiProperty({
    description: 'Whether to return a link',
    example: true
  })
  @IsBoolean()
  @IsOptional()
  return_link?: boolean;

  @ApiProperty({
    description: 'Array of contacts',
    type: [ContactDto]
  })
  @Type(() => ContactDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsNotEmpty()
  contacts: ContactDto[];
}
