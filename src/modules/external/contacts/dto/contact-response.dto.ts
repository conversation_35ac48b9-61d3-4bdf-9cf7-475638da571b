import { ApiProperty } from '@nestjs/swagger';

export class ContactResponse {
  @ApiProperty({
    description: 'Number of newly created contacts',
    example: 1
  })
  new: number;

  @ApiProperty({
    description: 'Number of updated contacts',
    example: 2
  })
  updated: number;

  @ApiProperty({
    description: 'List of email addresses that have previously been processed',
    example: ['<EMAIL>', '<EMAIL>']
  })
  fase_already_processed: string[];

  @ApiProperty({
    description: 'Array of contact survey links if return_link is true',
    example: [{
      mail: '<EMAIL>',
      fase: 'naopleveringbbHuur',
      link: 'https://example.com/survey/1/abc123def456'
    }],
    required: false
  })
  links?: Array<{
    mail: string;
    fase: string;
    link: string;
  }>;
}
