import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContactsController } from './contacts.controller';
import { ContactsService } from './contacts.service';
import { ContactEntity, StagesEntity, ProjectStageEntity, ProjectsEntity, EmailCustomerEntity, SecProgramSettingsEntity } from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.contacts'),
    TypeOrmModule.forFeature([ContactEntity, StagesEntity, ProjectStageEntity, ProjectsEntity, EmailCustomerEntity, SecProgramSettingsEntity]),
    ExternalAuthModule,
  ],
  controllers: [ContactsController],
  providers: [ContactsService],
  exports: [ContactsService],
})
export class ExternalContactsModule {}
