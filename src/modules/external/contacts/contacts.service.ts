import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { ContactEntity, ProjectsEntity, EmailCustomerEntity, StagesEntity, ProjectStageEntity, SecProgramSettingsEntity } from "@entities";
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ContactRequestDto } from './dto/contact-request.dto';
import { ContactResponse } from "./dto/contact-response.dto";
import { ModuleLogger } from "@helpers/logger/module-logger/module-logger.service";

interface StageData {
  stage_id?: string;
  department_id?: number;
  brand?: string;
  msg?: string;
  email_customer_id?: number;
}

@Injectable()
export class ContactsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private dataSource: DataSource,
    @InjectRepository(ContactEntity)
    private contactsRepo: Repository<ContactEntity>,
    @InjectRepository(ProjectsEntity)
    private projectsRepo: Repository<ProjectsEntity>,
    @InjectRepository(EmailCustomerEntity)
    private emailCustomerRepo: Repository<EmailCustomerEntity>,
    @InjectRepository(StagesEntity)
    private stagesRepo: Repository<StagesEntity>,
    @InjectRepository(ProjectStageEntity)
    private projectStageRepo: Repository<ProjectStageEntity>,
    @InjectRepository(SecProgramSettingsEntity)
    private programSettingsRepo: Repository<SecProgramSettingsEntity>,
  ) {}

  async updateContacts(data: ContactRequestDto): Promise<ContactResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const results: ContactResponse = { new: 0, updated: 0, fase_already_processed: [], links: [] };

    try {
      for (const contact of data.contacts) {
        this.logger.debug(`updateContacts for contact with project number ${contact.project_number}`, { contact });
        const project = await this.projectsRepo.findOne({
          where: {
            project_nr: contact.project_number,
            sec_company_id: company.id,
          },
        });
        if (!project) {
          throw new HttpException(
            `Project with number ${contact.project_number} not found`,
            HttpStatus.BAD_REQUEST
          );
        }

        let recContact = await this.contactsRepo.findOne({
          where: {
            contact_email: contact.mail,
            project_id: project.project_id,
            sec_company_id: company.id,
          },
        });

        const contactData = {
          contact_initials: contact.firstname ? contact.firstname : contact.initials,
          contact_name: contact.name,
          contact_email: contact.mail,
          object_buildnr: contact.buildnumber,
          object_address: contact.address,
          object_house_number: contact.house_number,
          object_nr_addition: contact.house_number_addition,
          object_postal_code: contact.postalcode,
          object_city: contact.city,
          contact_phone1: contact.phone,
          contact_language: 'nl', // Default to Dutch
          contact_type: contact.type,
          contact_salutation: contact.salutation,
          project_id: project.project_id,
          sec_company_id: company.id,
          modification_date: new Date(),
        };

        if (recContact) {
          await this.contactsRepo.update(
            { contact_id: recContact.contact_id },
            contactData
          );
          results.updated++;
        } else {
          const newContact = this.contactsRepo.create({
            ...contactData,
            creation_date: new Date(),
          });
          recContact = await this.contactsRepo.save(newContact);
          results.new++;
        }

        if (contact.fase) {
          let stageData = await this.getStageData(contact.fase, recContact.contact_id);
          if (stageData?.msg) {
            results.fase_already_processed.push(stageData.msg);
            continue;
          }
          stageData.email_customer_id = await this.createEmailCustomer(recContact, stageData);
          const newProjectStage = this.projectStageRepo.create({
            stage_id: stageData.stage_id,
            contact_id: recContact.contact_id,
            email_customer_id: stageData.email_customer_id,
            creation_date: new Date(),
          });
          await this.projectStageRepo.save(newProjectStage);

          // Add survey link if requested
          if (data.return_link && stageData?.email_customer_id) {
            const emailCustomer = await this.emailCustomerRepo.findOne({
              where: { email_customer_id: stageData.email_customer_id }
            });

            if (emailCustomer) {
              const surveyLink = await this.getSurveyLink(emailCustomer);
              if (surveyLink) {
                results.links.push({
                  mail: contact.mail,
                  fase: contact.fase,
                  link: surveyLink
                });
              }
            }
          }
        }
      }

      return results;
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to update contacts',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getStageData(fase: string, contactId: string): Promise<StageData | null> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    fase = fase.toUpperCase();

    this.logger.debug('contacts.getStageData', { fase, contactId });

    try {
      // Query to get stage data
      const stageData = await this.stagesRepo
        .createQueryBuilder('s')
        .leftJoin('surv_departments', 'd', 's.department_id = d.department_id')
        .leftJoin('surv_survey', 'ss', 'd.department_id = ss.department')
        .leftJoin('surv_surveytemplate', 'st', 'ss.surv_survey_id = st.surv_survey_id')
        .leftJoin('email_template', 't', 'st.email_template_id = t.email_template_id')
        .select([
          's.stage_id as stage_id',
          'd.department_id as department_id',
          't.triggername as brand'
        ])
        .where('(UPPER(d.department_key) = :fase OR UPPER(d.department_key) || UPPER(d.department_key2) = :fase)', { fase })
        .andWhere('s.sec_company_id = :companyId', { companyId: company.id })
        .getRawOne();

      if (!stageData) {
        return null;
      }

      const result: StageData = {
        stage_id: stageData.stage_id,
        department_id: stageData.department_id,
        brand: stageData.brand
      };

      // Check if contact already has an invitation for this phase
      if (!['NAZESMND', 'AFWEZIG', 'GROEPSLES'].includes(fase)) {
        const existingStage = await this.projectStageRepo
          .createQueryBuilder('ps')
          .where('ps.stage_id = :stageId', { stageId: result.stage_id })
          .andWhere('ps.contact_id = :contactId', { contactId: contactId })
          .getOne();

        if (existingStage) {
          return {
            msg: `Contact already invited for "${fase}"`
          };
        }
      }

      this.logger.debug('contacts.getStageData result', { result });
      return result;

    } catch (error) {
      this.logger.error('Error in getStageData', { error, fase, contactId });
      throw new HttpException(
        {
          message: 'Failed to get stage data',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private async createEmailCustomer(rec: ContactEntity, stageData: StageData) {
    const emailCustomer = await this.emailCustomerRepo.save({
      xml_salutation: rec.contact_salutation,
      xml_initials: rec.contact_initials,
      xml_customername: rec.contact_name,
      xml_email: rec.contact_email,
      xml_brand: stageData.brand,
      xml_address: rec.object_fulladdress,
      xml_internal_external: rec.project_id,
      xml_techgrp: rec.object_house_number?.toString(),
      xml_hybrisid: rec.object_nr_addition,
      xml_enumber: rec.object_postal_code,
      xml_city: rec.object_city,
      xml_companyname: rec.contact_company,
      xml_phone1: rec.contact_phone1,
      flag_sentthreshold: 0,
      xml_department: stageData.department_id,
      xml_readydate: new Date(),
      xml_language: rec.contact_language ? rec.contact_language : 'nl',
      sec_company_id: rec.sec_company_id,
      xml_rasnumber: stageData.department_id.toString() + rec.contact_nr.toString(),
    });

    return emailCustomer.email_customer_id;
  }

  /**
   * Generates a survey link for an email customer
   * @param emailCustomer The email customer entity
   * @returns A survey link string or null if no survey is found
   */
  async getSurveyLink(emailCustomer: EmailCustomerEntity): Promise<string | null> {
    this.logger.debug('getSurveyLink for email customer', {
      department: emailCustomer.xml_department,
      brand: emailCustomer.xml_brand
    });

    try {
      // Query to get survey and email template information
      const surveyData = await this.dataSource.query(
        `SELECT st.surv_survey_id, st.email_template_id
         FROM surv_surveytemplate st
         INNER JOIN surv_survey s ON st.surv_survey_id = s.surv_survey_id
         LEFT JOIN email_template et ON st.email_template_id = et.email_template_id
         WHERE s.department = $1 AND et.triggername = $2`,
        [emailCustomer.xml_department, emailCustomer.xml_brand]
      );

      if (surveyData && surveyData.length === 1) {
        // Update the email customer with survey information
        emailCustomer.surv_survey_id = surveyData[0].surv_survey_id;
        emailCustomer.email_template_id = surveyData[0].email_template_id;
        await this.emailCustomerRepo.save(emailCustomer);
      } else {
        this.logger.debug('No survey found for email customer', {
          department: emailCustomer.xml_department,
          brand: emailCustomer.xml_brand
        });
        return null;
      }

      // Get survey link from program settings
      const programSettings = await this.programSettingsRepo.findOne({
        where: { sec_company_id: emailCustomer.sec_company_id }
      });

      if (!programSettings || !programSettings.survey_link) {
        this.logger.debug('No survey link found in program settings');
        return null;
      }

      // Create the survey link
      const surveyLink = `${programSettings.survey_link}/1/${emailCustomer.surv_key}`;
      this.logger.debug('Generated survey link', { surveyLink });

      return surveyLink;
    } catch (error) {
      this.logger.error('Error generating survey link', { error });
      return null;
    }
  }
}
