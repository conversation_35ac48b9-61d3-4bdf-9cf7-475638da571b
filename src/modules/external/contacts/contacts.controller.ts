import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ContactsService } from './contacts.service';
import { ContactRequestDto } from './dto/contact-request.dto';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { ContactResponse } from "./dto/contact-response.dto";

@ApiTags('Contacts')
@ApiBearerAuth()
@Controller({
  path: 'external/contacts',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class ContactsController {
  constructor(private readonly contactsService: ContactsService) {}

  @Post()
  @ApiOperation({ summary: 'Create or update contact information (Only for BUILD customers)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Contacts information updated successfully',
    type: ContactResponse
  })
  async updateContacts(@Body() data: ContactRequestDto): Promise<ContactResponse> {
    return this.contactsService.updateContacts(data);
  }
}
