import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MembersController } from './members.controller';
import { MembersService } from './members.service';
import { ContactEntity, StagesEntity, ProjectStageEntity, ProjectsEntity, EmailCustomerEntity } from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.members'),
    TypeOrmModule.forFeature([ContactEntity,StagesEntity,ProjectStageEntity,ProjectsEntity,EmailCustomerEntity]),
    ExternalAuthModule,
  ],
  controllers: [MembersController],
  providers: [MembersService],
  exports: [MembersService],
})
export class ExternalMembersModule {}