import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsString, ValidateNested, IsNumber, IsOptional } from 'class-validator';

export class MemberDto {
  @ApiProperty({
    description: 'Member number',
    example: '14205448'
  })
  @IsNumber()
  @IsNotEmpty()
  member_number: number;

  @ApiProperty({
    description: 'Member phase',
    example: 'Natweewk'
  })
  @IsString()
  fase: string;

  @ApiProperty({
    description: 'Street name',
    example: 'Ter Lips'
  })
  @IsString()
  address: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  @IsString()
  mail: string;

  @ApiProperty({
    description: 'City name',
    example: 'Assen'
  })
  @IsString()
  city: string;

  @ApiProperty({
    description: 'Member initials',
    example: 'Bo'
  })
  @IsString()
  initials: string;

  @ApiProperty({
    description: 'House number',
    example: 138
  })
  @IsNumber()
  house_number: number;

  @ApiProperty({
    description: 'House number addition',
    example: 'A'
  })
  @IsString()
  @IsOptional()
  house_number_addition?: string;

  @ApiProperty({
    description: 'Language code',
    example: 'NL'
  })
  @IsString()
  language: string;

  @ApiProperty({
    description: 'Member type',
    example: 1
  })
  @IsNumber()
  type: number;

  @ApiProperty({
    description: 'Phone number',
    example: '0612345678'
  })
  @IsString()
  phone: string;

  @ApiProperty({
    description: 'Postal code',
    example: '1234 AZ'
  })
  @IsString()
  postalcode: string;

  @ApiProperty({
    description: 'Member name',
    example: 'Blue'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Club number',
    example: 68
  })
  @IsNumber()
  club_number: number;

  @ApiProperty({
    description: 'Visit date',
    example: '2023-12-01'
  })
  @IsString()
  visit_date: string;
}

export class MemberRequestDto {
  @ApiProperty({
    description: 'Array of members',
    type: [MemberDto]
  })
  @Type(() => MemberDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsNotEmpty()
  members: MemberDto[];
}