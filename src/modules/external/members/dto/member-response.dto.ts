import { ApiProperty } from '@nestjs/swagger';

export class MemberResponse {
  @ApiProperty({
    description: 'Number of newly created clubs',
    example: 1
  })
  new: number;

  @ApiProperty({
    description: 'Number of updated clubs',
    example: 2
  })
  updated: number;

  @ApiProperty({
    description: 'List of email addresses that have previously been processed',
    example: ['<EMAIL>', '<EMAIL>']
  })
  fase_already_processed: string[];
}