import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MembersService } from './members.service';
import { MemberRequestDto } from './dto/member-request.dto';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { MemberResponse } from "@/modules/external/members/dto/member-response.dto";

@ApiTags('Members')
@ApiBearerAuth()
@Controller({
  path: 'external/members',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class MembersController {
  constructor(private readonly membersService: MembersService) {}

  @Post()
  @ApiOperation({ summary: 'Create or update member information (Only for SPORTS customers)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Members information updated successfully',
    type: Object
  })
  async updateMembers(@Body() data: MemberRequestDto): Promise<MemberResponse> {
    return this.membersService.updateMembers(data);
  }
}