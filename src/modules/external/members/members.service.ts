import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { ContactEntity, ProjectsEntity, EmailCustomerEntity, StagesEntity, ProjectStageEntity } from "@entities";
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { MemberRequestDto } from './dto/member-request.dto';
import { MemberResponse } from "./dto/member-response.dto";
import { ModuleLogger } from "@helpers/logger/module-logger/module-logger.service";

interface StageData {
  stage_id?: string;
  department_id?: number;
  brand?: string;
  msg?: string;
  email_customer_id?: number;
}

@Injectable()
export class MembersService {
  constructor(
    private logger: ModuleLogger,

    private cls: ClsService,
    @InjectRepository(ContactEntity)
    private contactsRepo: Repository<ContactEntity>,
    @InjectRepository(ProjectsEntity)
    private projectsRepo: Repository<ProjectsEntity>,
    @InjectRepository(EmailCustomerEntity)
    private emailCustomerRepo: Repository<EmailCustomerEntity>,
    @InjectRepository(StagesEntity)
    private stagesRepo: Repository<StagesEntity>,
    @InjectRepository(ProjectStageEntity)
    private projectStageRepo: Repository<ProjectStageEntity>,
  ) {}

  async updateMembers(data: MemberRequestDto): Promise<MemberResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const results: MemberResponse = { new: 0, updated: 0, fase_already_processed: [] };

    try {
      for (const member of data.members) {
        this.logger.debug(`updateMembers for member ${member.member_number}`, { member });
        const club = await this.projectsRepo.findOne({
          where: {
            project_nr: member.club_number.toString(),
            sec_company_id: company.id,
          },
        });
        if (!club) {
          throw new HttpException(
            `Club with number ${member.club_number} not found`,
            HttpStatus.BAD_REQUEST
          );
        }

        let recMember = await this.contactsRepo.findOne({
          where: {
            contact_nr: member.member_number,
            project_id: club.project_id,
            sec_company_id: company.id,
          },
        });

        const memberData = {
          contact_nr: member.member_number,
          object_address: member.address,
          contact_email: member.mail,
          object_city: member.city,
          contact_initials: member.initials,
          object_house_number: member.house_number,
          object_nr_addition: member.house_number_addition,
          contact_language: member.language,
          contact_type: member.type,
          contact_phone1: member.phone,
          object_postal_code: member.postalcode,
          contact_name: member.name,
          project_id: club.project_id,
          sec_company_id: company.id,
          modification_date: new Date(),
        };

        if (recMember) {
          await this.contactsRepo.update(
            { contact_id: recMember.contact_id },
            memberData
          );
          results.updated++;
        } else {
          const newMember = this.contactsRepo.create({
            ...memberData,
            creation_date: new Date(),
          });
          recMember = await this.contactsRepo.save(newMember);
          results.new++;
        }

        if (member.fase) {
          let stageData = await this.getStageData(member.fase, recMember.contact_id);
          if (stageData?.msg) {
            results.fase_already_processed.push(stageData.msg);
            continue;
          }
          stageData.email_customer_id = await this.createEmailCustomer(recMember, stageData, member.visit_date);
          const newProjectStage =  this.projectStageRepo.create({
            stage_id: stageData.stage_id,
            contact_id: recMember.contact_id,
            email_customer_id: stageData.email_customer_id,
            creation_date: new Date(),
          });
          await this.projectStageRepo.save(newProjectStage);
        }
      }

      return results;
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to update members',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getStageData(fase: string, memberId: string): Promise<StageData | null> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    fase = fase.toUpperCase();

    this.logger.debug('members.getStageData', { fase, memberId });

    try {
      // Query to get stage data
      const stageData = await this.stagesRepo
        .createQueryBuilder('s')
        .leftJoin('surv_departments', 'd', 's.department_id = d.department_id')
        .leftJoin('surv_survey', 'ss', 'd.department_id = ss.department')
        .leftJoin('surv_surveytemplate', 'st', 'ss.surv_survey_id = st.surv_survey_id')
        .leftJoin('email_template', 't', 'st.email_template_id = t.email_template_id')
        .select([
          's.stage_id as stage_id',
          'd.department_id as department_id',
          't.triggername as brand'
        ])
        .where('(UPPER(d.department_key) = :fase OR UPPER(d.department_key) || UPPER(d.department_key2) = :fase)', { fase })
        .andWhere('s.sec_company_id = :companyId', { companyId: company.id })
        .getRawOne();

      if (!stageData) {
        return null;
      }

      const result: StageData = {
        stage_id: stageData.stage_id,
        department_id: stageData.department_id,
        brand: stageData.brand
      };

      // Check if member already has an invitation for this phase
      if (!['NAZESMND', 'AFWEZIG', 'GROEPSLES'].includes(fase)) {
        const existingStage = await this.projectStageRepo
          .createQueryBuilder('ps')
          .where('ps.stage_id = :stageId', { stageId: result.stage_id })
          .andWhere('ps.contact_id = :contactId', { contactId: memberId })
          .getOne();

        if (existingStage) {
          return {
            msg: `Member already invited for "${fase}"`
          };
        }
      }

      this.logger.debug('member.getStageData result', { result });
      return result;

    } catch (error) {
      this.logger.error('Error in getStageData', { error, fase, memberId });
      throw new HttpException(
        {
          message: 'Failed to get stage data',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private async createEmailCustomer(rec: ContactEntity, stageData: StageData, visit_date: any) {
    await this.emailCustomerRepo.save({
      xml_salutation: rec.contact_salutation,
      xml_initials: rec.contact_initials,
      xml_customername: rec.contact_name,
      xml_email: rec.contact_email,
      xml_brand: stageData.brand,
      xml_address: rec.object_fulladdress,
      xml_internal_external: rec.project_id,
      xml_techgrp: rec.object_house_number.toString(),
      xml_hybrisid: rec.object_nr_addition,
      xml_enumber: rec.object_postal_code,
      xml_city: rec.object_city,
      xml_companyname: rec.contact_company,
      xml_phone1: rec.contact_phone1,
      xml_phone2: rec.contact_phone1,
      flag_sentthreshold: 0,
      xml_department: stageData.department_id,
      xml_readydate: visit_date ? new Date(visit_date) : new Date(),
      xml_language: rec.contact_language ? rec.contact_language : 'nl',
      sec_company_id: rec.sec_company_id,
      xml_rasnumber: stageData.department_id.toString() + rec.contact_nr.toString(),
    })
    return 0;
  }
}