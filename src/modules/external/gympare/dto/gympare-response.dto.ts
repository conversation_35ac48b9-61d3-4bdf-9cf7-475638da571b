import { ApiProperty } from '@nestjs/swagger';

export class GympareReviewDto {
  @ApiProperty({ description: 'Review ID' })
  id: number;

  @ApiProperty({ description: 'Improvements feedback' })
  improvements: string;

  @ApiProperty({ description: 'Strengths feedback' })
  strengths: string;

  @ApiProperty({ description: 'Date when review was posted' })
  postedOn: string;

  @ApiProperty({ description: 'Review score' })
  score: number;
}

export class GympareClubDto {
  @ApiProperty({ description: 'Club ID (UUID)' })
  id: string;

  @ApiProperty({ description: 'Club name' })
  name: string;

  @ApiProperty({ description: 'Average score' })
  score: number;

  @ApiProperty({ description: 'Number of reviews based on' })
  based_on: number;

  @ApiProperty({ description: 'Reviews array', type: [GympareReviewDto] })
  reviews: GympareReviewDto[];

  @ApiProperty({ description: 'Club link' })
  link: string;
}

export class GympareResponseDto {
  @ApiProperty({ description: 'Clubs array', type: [GympareClubDto] })
  clubs: GympareClubDto[];
}

