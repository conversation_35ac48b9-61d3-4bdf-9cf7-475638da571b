import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ValidateCompanyService } from '../../auth/validate-company.service';
import { SecCompanyApiRightsEntity } from '@/modules/database/entities/sec-company-api-rights.entity';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ApiId } from '@/shared/enums/api-id.enum';

@Injectable()
export class GympareAuthGuard implements CanActivate {
  constructor(
    @InjectRepository(SecCompanyApiRightsEntity)
    private readonly apiRightsRepo: Repository<SecCompanyApiRightsEntity>,
    private readonly cls: ClsService,
    private validateCompanyService: ValidateCompanyService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    try {
      const item = await this.validateCompanyService.validate(token);
      if (!item) {
        throw new UnauthorizedException('Invalid token');
      }
      this.cls.set('user', { ...item });

      // Get the company data from the context
      const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

      // Check if the company has access to the gympare API by joining with the API key table
      const hasAccess = await this.apiRightsRepo
        .createQueryBuilder('rights')
        .leftJoin('rights.apiKey', 'apiKey')
        .where('apiKey.sec_company_id = :companyId', { companyId: company.id })
        .andWhere('rights.sec_company_api_id = :apiId', { apiId: ApiId.GYMPARE })
        .getOne();

      if (!hasAccess) {
        throw new UnauthorizedException('No access to gympare API');
      }

      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
