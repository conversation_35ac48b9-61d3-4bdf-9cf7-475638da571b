# Gympare API

The Gympare API provides access to gym and fitness club information with reviews and ratings. This external API allows authorized clients to retrieve club data including average scores, review counts, and detailed customer feedback.

## 🎯 Overview

The Gympare API is designed to provide comprehensive information about fitness clubs and gyms, including:
- Club basic information (ID, name, average score)
- Review statistics (number of reviews)
- Detailed customer reviews with strengths and improvements
- StoryWidget integration links for enhanced user experience

## 📋 Features

- **Club Information**: Retrieve basic club details and statistics
- **Review System**: Access detailed customer reviews and ratings
- **StoryWidget Integration**: Generate links for enhanced user experience
- **Authentication**: Secure Bearer token authentication
- **Real-time Data**: Live data from the database
- **Performance Optimized**: Efficient queries with proper indexing

## 🔗 API Endpoints

### GET /v2/external/gympare

Retrieves all gympare clubs with their reviews and statistics.

**Authentication**: Bearer token required
**Method**: GET
**Version**: v2

#### Request Headers
```
Authorization: Bearer <your-token>
Content-Type: application/json
```

#### Response

**Success (200)**
```json
{
  "clubs": [
    {
      "id": "uuid-string",
      "name": "Fitness Club Name",
      "score": 8.5,
      "based_on": 25,
      "link": "https://feedback4sports.com/verhalen/?t=<base64-object>",
      "reviews": [
        {
          "id": 12345,
          "improvements": "Could use more equipment",
          "strengths": "Great staff and clean facilities",
          "postedOn": "2024-01-15 14:30",
          "score": 9.0
        }
      ]
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: Invalid or missing authentication token
- `403 Forbidden`: Token doesn't have access to gympare API
- `500 Internal Server Error`: Server error

## 📊 Data Models

### GympareResponseDto
Main response object containing an array of clubs.

| Field | Type | Description |
|-------|------|-------------|
| `clubs` | `GympareClubDto[]` | Array of club objects |

### GympareClubDto
Individual club information.

| Field | Type | Description |
|-------|------|-------------|
| `id` | `string` | Unique club identifier (UUID) |
| `name` | `string` | Club name |
| `score` | `number` | Average score (0-10) |
| `based_on` | `number` | Number of reviews this score is based on |
| `link` | `string` | StoryWidget integration link (may be empty) |
| `reviews` | `GympareReviewDto[]` | Array of detailed reviews |

### GympareReviewDto
Individual review information.

| Field | Type | Description |
|-------|------|-------------|
| `id` | `number` | Review identifier |
| `improvements` | `string` | Customer feedback on areas for improvement |
| `strengths` | `string` | Customer feedback on positive aspects |
| `postedOn` | `string` | Review date and time (YYYY-MM-DD HH:mm format) |
| `score` | `number` | Individual review score (0-10) |

## 🔐 Authentication

The API uses Bearer token authentication. Tokens must:
- Be valid and not expired
- Have appropriate privileges for gympare API access
- Be included in the Authorization header

### Token Requirements
- Valid expiration date
- Proper privilege level
- Associated with an active company

## 🔗 StoryWidget Integration

The API generates StoryWidget links for enhanced user experience. These links:
- Are base64 encoded JSON objects
- Contain server configuration and authentication tokens
- Follow the format: `https://feedback4sports.com/verhalen/?t=<base64-object>`

### Link Object Structure
```json
{
  "token": "<retrieved_token>",
  "server": "https://servoy4.welcomeccs.nl/FF",
  "project": "<project_nr>",
  "companyType": 2
}
```

## 🗄️ Database Schema

The API queries the following database tables:
- `projects`: Club/project information
- `publication_reports`: Club statistics and scores
- `publications`: Individual review data
- `user_tokens`: Authentication and StoryWidget tokens

### Key Queries
- Club data with average scores, review counts, and project numbers
- Recent reviews (max 10 per club) for clubs with 5+ reviews
- StoryWidget token retrieval for link generation with project-specific links

## 🚀 Usage Examples

### cURL Example
```bash
curl -X GET "http://localhost:3000/v2/external/gympare" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json"
```

### JavaScript/Node.js Example
```javascript
const axios = require('axios');

const response = await axios.get('http://localhost:3000/v2/external/gympare', {
  headers: {
    'Authorization': 'Bearer your-token-here',
    'Content-Type': 'application/json'
  }
});

console.log(response.data);
```

### Python Example
```python
import requests

headers = {
    'Authorization': 'Bearer your-token-here',
    'Content-Type': 'application/json'
}

response = requests.get('http://localhost:3000/v2/external/gympare', headers=headers)
data = response.json()
print(data)
```

## 🧪 Testing

The API includes comprehensive testing:

### Unit Tests
```bash
npm run test -- --testPathPattern=gympare.service.spec.ts
```

### Integration Tests
```bash
npm run test:integration:gympare
```

### Manual Testing Scripts
- Node.js: `node test/scripts/gympare/test-gympare-api.js`
- cURL: `./test/scripts/gympare/test-gympare-curl.sh`
- PowerShell: `.\test\scripts\gympare\test-gympare-powershell.ps1`

## 🔧 Configuration

### Environment Variables
- `GYMPARE_API_URL`: Base URL for the API (default: `http://localhost:3000/v2/external/gympare`)
- `GYMPARE_TEST_TOKEN`: Test token for automated testing

### Database Requirements
- PostgreSQL database with required tables
- Proper indexes on `project_id`, `gympare`, and `response_date` columns
- Valid user tokens with StoryWidget privileges

## 📈 Performance Considerations

- Reviews are limited to 10 per club to maintain performance
- Only clubs with 5+ reviews are included in results
- Database queries use efficient CTEs and window functions
- StoryWidget links are generated per club with project-specific information

## 🚨 Error Handling

The API handles various error scenarios:
- Invalid authentication tokens
- Database connection issues
- Missing or expired StoryWidget tokens
- Malformed requests

### Common Error Codes
- `401`: Authentication required or invalid token
- `403`: Insufficient privileges
- `500`: Internal server error

## 🔄 Rate Limiting

Currently, no rate limiting is implemented. Consider implementing rate limiting for production use.

## 📝 Logging

The API uses structured logging with:
- Debug logs for method entry/exit
- Error logs for exceptions
- Performance monitoring capabilities

## 🤝 Contributing

When contributing to the Gympare API:

1. Follow the existing code style and patterns
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure proper error handling
5. Test with various data scenarios

## 📄 License

This API is part of the Focus API project and follows the project's licensing terms.

## 🆘 Support

For issues or questions:
1. Check the test documentation in `test/manual/gympare/`
2. Review the integration test examples
3. Consult the API testing guide
4. Contact the development team

---

**Version**: 2.0  
**Last Updated**: 2025  
**Maintainer**: Focus API Team
