import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { GympareService } from './gympare.service';
import { GympareResponseDto } from './dto/gympare-response.dto';
import { GympareAuthGuard } from './guards/gympare-auth.guard';

@ApiTags('Gympare')
@ApiBearerAuth()
@Controller({
  path: 'external/gympare',
  version: '2',
})
@UseGuards(GympareAuthGuard)
export class GympareController {
  constructor(private readonly gympareService: GympareService) {}

  @Get()
  @ApiOperation({ summary: 'Get gympare clubs information' })
  @ApiResponse({ 
    status: 200, 
    description: 'Gympare clubs information retrieved successfully',
    type: GympareResponseDto
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing token or no access to gympare API' 
  })
  async getGympareClubs(): Promise<GympareResponseDto> {
    return this.gympareService.getGympareClubs();
  }
}
