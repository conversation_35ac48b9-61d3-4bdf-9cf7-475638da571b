import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { Repository } from 'typeorm';

import { ProjectsEntity, PublicationsEntity, SecCompanyApiRightsEntity, UserTokenEntity } from '@entities';
import { ModuleLogger } from "@lib/logger/module-logger/module-logger.service";
import { GympareService } from './gympare.service';
import { TokenPrivilege } from '@/shared/enums';

describe('GympareService', () => {
  let service: GympareService;
  let projectsRepo: Repository<ProjectsEntity>;
  let publicationsRepo: Repository<PublicationsEntity>;
  let userTokenRepo: Repository<UserTokenEntity>;

  const mockClsService = {
    get: jest.fn(),
    set: jest.fn(),
  };

  const mockLogger = {
    debug: jest.fn(),
    log: jest.fn(),
    error: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GympareService,
        {
          provide: getRepositoryToken(ProjectsEntity),
          useValue: {
            query: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PublicationsEntity),
          useValue: {
            query: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserTokenEntity),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: ClsService,
          useValue: mockClsService,
        },
        {
          provide: ModuleLogger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<GympareService>(GympareService);
    projectsRepo = module.get<Repository<ProjectsEntity>>(getRepositoryToken(ProjectsEntity));
    publicationsRepo = module.get<Repository<PublicationsEntity>>(getRepositoryToken(PublicationsEntity));
    userTokenRepo = module.get<Repository<UserTokenEntity>>(getRepositoryToken(UserTokenEntity));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getGympareClubs', () => {
    it('should return empty clubs array when no data found', async () => {
      // Mock the CLS service to return company data
      mockClsService.get.mockReturnValue({
        company: { id: 1 }
      });

      // Mock the token query builder to return null
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };
      jest.spyOn(userTokenRepo, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      jest.spyOn(projectsRepo, 'query').mockResolvedValue([]);
      jest.spyOn(publicationsRepo, 'query').mockResolvedValue([]);

      const result = await service.getGympareClubs();

      expect(result).toEqual({ clubs: [] });
    });

    it('should return clubs with reviews when data is found', async () => {
      const mockClubs = [
        {
          id: 'test-id-1',
          name: 'Test Club 1',
          project_nr: 'PRJ001',
          avg_score: 8.5,
          based_on: 100,
        },
      ];

      const mockReviews = [
        {
          id: 123,
          improvements: 'Could improve equipment',
          strengths: 'Great staff',
          postedon: '2025-01-15 10:30:00',
          score: 9,
        },
      ];

      // Mock the CLS service to return company data
      mockClsService.get.mockReturnValue({
        company: { id: 1 }
      });

      // Mock the token query builder
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue({ token: 'test-token-123' }),
      };
      jest.spyOn(userTokenRepo, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      jest.spyOn(projectsRepo, 'query').mockResolvedValue(mockClubs);
      jest.spyOn(publicationsRepo, 'query').mockResolvedValue(mockReviews);

      const result = await service.getGympareClubs();

      expect(result.clubs).toHaveLength(1);
      expect(result.clubs[0].id).toBe('test-id-1');
      expect(result.clubs[0].name).toBe('Test Club 1');
      expect(result.clubs[0].score).toBe(8.5);
      expect(result.clubs[0].based_on).toBe(100);
      expect(result.clubs[0].reviews).toHaveLength(1);
      expect(result.clubs[0].reviews[0].id).toBe(123);
      
      // Verify the link is built correctly with project_nr
      expect(result.clubs[0].link).toContain('https://feedback4sports.com/verhalen/?t=');
      // Decode the base64 to verify project_nr is included
      const base64Part = result.clubs[0].link.split('?t=')[1];
      const decoded = Buffer.from(base64Part, 'base64').toString('utf-8');
      const linkObject = JSON.parse(decoded);
      expect(linkObject.project).toBe('PRJ001');
      expect(linkObject.token).toBe('test-token-123');
      expect(linkObject.server).toBe('https://servoy4.welcomeccs.nl/FF');
      expect(linkObject.companyType).toBe(2);
    });

    it('should return empty link when no StoryWidget token found', async () => {
      const mockClubs = [
        {
          id: 'test-id-1',
          name: 'Test Club 1',
          project_nr: 'PRJ001',
          avg_score: 8.5,
          based_on: 100,
        },
      ];

      // Mock the CLS service to return company data
      mockClsService.get.mockReturnValue({
        company: { id: 1 }
      });

      // Mock the token query builder to return null (no token found)
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };
      jest.spyOn(userTokenRepo, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      jest.spyOn(projectsRepo, 'query').mockResolvedValue(mockClubs);
      jest.spyOn(publicationsRepo, 'query').mockResolvedValue([]);

      const result = await service.getGympareClubs();

      expect(result.clubs).toHaveLength(1);
      expect(result.clubs[0].link).toBe('');
    });
  });

  describe('StoryWidget token retrieval', () => {
    it('should query for StoryWidget token with correct parameters', async () => {
      // Mock the CLS service to return company data
      mockClsService.get.mockReturnValue({
        company: { id: 123 }
      });

      // Mock the token query builder
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue({ token: 'test-token-456' }),
      };
      jest.spyOn(userTokenRepo, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      // Call the service method that triggers token retrieval
      const mockClubs = [{ id: 'test-id', name: 'Test Club', avg_score: 8.0, based_on: 50 }];
      jest.spyOn(projectsRepo, 'query').mockResolvedValue(mockClubs);
      jest.spyOn(publicationsRepo, 'query').mockResolvedValue([]);

      await service.getGympareClubs();

      // Verify the query builder was called with correct parameters
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('token.sec_company_id = :companyId', { companyId: 123 });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('token.token_privilege = :privilege', { privilege: TokenPrivilege.StoryWidget });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('token.expiry_date > :currentDate', { currentDate: expect.any(Date) });
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(['token.token']);
    });
  });
});

