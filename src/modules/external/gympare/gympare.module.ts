import { Module, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { GympareController } from './gympare.controller';
import { GympareService } from './gympare.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectsEntity, PublicationsEntity, SecCompanyApiRightsEntity, UserTokenEntity, CompanyApiKeyEntity } from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@lib/logger/module-logger/module-logger.module";
import { ExternalCorsMiddleware } from '@/shared/middleware/external-cors.middleware';

@Module({
  imports: [
    ModuleLoggerModule.register('external.gympare'),
    TypeOrmModule.forFeature([
      ProjectsEntity,
      PublicationsEntity,
      SecCompanyApiRightsEntity,
      UserTokenEntity,
      CompanyApiKeyEntity,
    ]),
    ExternalAuthModule,
  ],
  controllers: [GympareController],
  providers: [GympareService],
  exports: [GympareService],
})
export class ExternalGympareModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(ExternalCorsMiddleware)
      .forRoutes({ path: 'external/gympare', method: RequestMethod.ALL });
  }
}

