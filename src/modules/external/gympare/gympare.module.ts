import { Modu<PERSON> } from '@nestjs/common';
import { GympareController } from './gympare.controller';
import { GympareService } from './gympare.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectsEntity, PublicationsEntity, SecCompanyApiRightsEntity, UserTokenEntity } from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@lib/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.gympare'),
    TypeOrmModule.forFeature([
      ProjectsEntity,
      PublicationsEntity,
      SecCompanyApiRightsEntity,
      UserTokenEntity,
    ]),
    ExternalAuthModule,
  ],
  controllers: [GympareController],
  providers: [GympareService],
  exports: [GympareService],
})
export class ExternalGympareModule {}

