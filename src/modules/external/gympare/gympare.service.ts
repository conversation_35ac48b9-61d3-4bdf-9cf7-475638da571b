import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { ProjectsEntity, PublicationsEntity, UserTokenEntity } from '@entities';
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { GympareResponseDto, GympareClubDto, GympareReviewDto } from './dto/gympare-response.dto';
import { ModuleLogger } from "@lib/logger/module-logger/module-logger.service";
import { TokenPrivilege } from '@/shared/enums';

@Injectable()
export class GympareService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(ProjectsEntity)
    private projectsRepo: Repository<ProjectsEntity>,
    @InjectRepository(PublicationsEntity)
    private publicationsRepo: Repository<PublicationsEntity>,
    @InjectRepository(UserTokenEntity)
    private userTokenRepo: Repository<UserTokenEntity>,
  ) {}

  async getGympareClubs(): Promise<GympareResponseDto> {
    this.logger.debug('getGympareClubs called');

    // Get clubs data
    const clubsData = await this.getClubsData();

    this.logger.debug('getGympareClubs clubsData', { clubsData });

    // Get reviews for each club
    const clubsWithReviews = await Promise.all(
      clubsData.map(async (club) => {
        const reviews = await this.getClubReviews(club.id);
        return {
          ...club,
          reviews,
        };
      })
    );

    return {
      clubs: clubsWithReviews,
    };
  }

  private async getClubsData(): Promise<Omit<GympareClubDto, 'reviews'>[]> {
    // Using the exact SQL from requirements
    const clubs = await this.projectsRepo.query(`
      select pr.project_id AS id, p2.project_name AS name, p2.project_nr AS project_nr, avg_score, based_on
      from publication_reports pr
      LEFT JOIN projects p2 ON pr.project_id = p2.project_id
      WHERE p2.gympare = 1
    `);

    return Promise.all(clubs.map(async (club) => {
      // Get the story widget link for each club with its project_nr
      const storyWidgetLink = await this.buildStoryWidgetLink(club.project_nr);
      
      return {
        id: club.id,
        name: club.name || '',
        score: parseFloat(club.avg_score) || 0,
        based_on: parseInt(club.based_on) || 0,
        link: storyWidgetLink,
      };
    }));
  }

  private async getClubReviews(projectId: string): Promise<GympareReviewDto[]> {
    // Using the exact SQL from requirements with CTE
    const reviews = await this.publicationsRepo.query(`
      WITH ranked AS (
        SELECT
          p2.project_id,
          p2.project_name,
          p.respondent_customername,
          p.respondent_initials,
          p.response_date,
          p.score,
          p.strengths,
          p.improvements,
          p.surv_surveyanswer_id AS answer_id,
          ROW_NUMBER() OVER (
            PARTITION BY p2.project_id
            ORDER BY p.response_date DESC
          ) AS rn,
          COUNT(*) OVER (PARTITION BY p2.project_id) AS cnt
        FROM publications p
        JOIN projects p2 ON p.project_id = p2.project_id
        WHERE p2.gympare = 1
      )
      SELECT
        project_id,
        respondent_customername AS last_name,
        respondent_initials AS first_name,
        response_date AS postedOn,
        score,
        strengths,
        improvements,
        answer_id AS id
      FROM ranked
      WHERE rn <= 10          -- max 10 per project
        AND cnt >= 5          -- only projects with at least 5
        AND project_id = $1   -- filter by specific project
      ORDER BY response_date DESC
    `, [projectId]);

    return reviews.map((review) => ({
      id: parseInt(review.id),
      improvements: review.improvements || '',
      strengths: review.strengths || '',
      postedOn: this.formatDate(review.postedOn),
      score: parseFloat(review.score) || 0,
    }));
  }

  private async getStoryWidgetToken(): Promise<string | null> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    
    const tokenData = await this.userTokenRepo
      .createQueryBuilder('token')
      .where('token.sec_company_id = :companyId', { companyId: company.id })
      .andWhere('token.token_privilege = :privilege', { privilege: TokenPrivilege.StoryWidget })
      .andWhere('token.expiry_date > :currentDate', { currentDate: new Date() })
      .select(['token.token'])
      .getOne();

    return tokenData?.token || null;
  }

  private async buildStoryWidgetLink(projectNr?: string): Promise<string> {
    const token = await this.getStoryWidgetToken();
    if (!token) {
      return '';
    }

    const linkObject = {
      token: token,
      server: 'https://servoy4.welcomeccs.nl/FF',
      project: projectNr || '',
      companyType: 2
    };

    const base64Object = Buffer.from(JSON.stringify(linkObject)).toString('base64');
    return `https://feedback4sports.com/verhalen/?t=${base64Object}`;
  }

  private formatDate(date: Date | string): string {
    if (!date) return '';
    
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
}
