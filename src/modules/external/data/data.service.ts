import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { EmailCustomerEntity } from "@entities";
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { DataRequestDto } from './dto/data-request.dto';
import { DataResponse } from "./dto/data-response.dto";
import { ModuleLogger } from "@helpers/logger/module-logger/module-logger.service";
import { TranslateService } from "@libs/translate";
import { SurveyUtils } from '@/modules/survey/survey.utils';

@Injectable()
export class DataService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(EmailCustomerEntity)
    private emailCustomerRepo: Repository<EmailCustomerEntity>,
    private translateService: TranslateService,
    private surveyUtils: SurveyUtils,
  ) {}

  async createData(requestData: DataRequestDto): Promise<DataResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const results: DataResponse = { new: 0, updated: 0, inDB: 0, errors: [] };
    const languageMap = await this.translateService.getLanguageList();
    // Get a list of all department keys using getDepartmentKeys in survey.utils.ts
    const departmentKeys = await this.surveyUtils.getDepartmentKeys();

    // Extract all email addresses from the payload
    const emailAddresses = requestData.data
      .filter(item => item.email)
      .map(item => item.email.toLowerCase());

    // Create a map to store email+brand combinations that already exist
    const existingEmailBrandMap = new Map<string, boolean>();

    if (emailAddresses.length > 0) {
      // Query to check which email+brand combinations already exist
      const query = `
        SELECT lower(xml_email)||'_'||lower(xml_brand) as email_brand_key
        FROM email_customer
        WHERE (send_date > NOW() OR send_date IS NULL)
        AND sec_company_id = $1
        AND flag_sentthreshold != 1
        AND lower(xml_email) IN ($2)
      `;

      try {
        const queryResult = await this.emailCustomerRepo.query(
          query.replace('$2', emailAddresses.map((_, i) => `$${i + 2}`).join(', ')),
          [company.id, ...emailAddresses]
        );

        // Store the results in the map
        queryResult.forEach(row => {
          existingEmailBrandMap.set(row.email_brand_key, true);
        });

        this.logger.debug(`Found ${queryResult.length} existing email+brand combinations`, {
          existingCombinations: queryResult.map(row => row.email_brand_key)
        });
      } catch (error) {
        this.logger.error('Error checking existing email+brand combinations', { error });
      }
    }

    try {
      for (const [index, item] of requestData.data.entries()) {
        try {
          this.logger.debug(`Processing data record #${index + 1}`, { item });

          let language = item.language || 'en';
          if (languageMap[language]) {
            language = languageMap[language];
          }

          // Find department ID if survey is provided
          let departmentId = null;
          if (item.survey && departmentKeys[item.survey]) {
            departmentId = departmentKeys[item.survey];
            this.logger.debug(`Found department ID ${departmentId} for survey ${item.survey}`);
          }

          // Check if this email+brand combination already exists in the database
          let flagSentThreshold = 0;
          if (item.email && item.brand) {
            const emailBrandKey = `${item.email.toLowerCase()}_${item.brand.toLowerCase()}`;
            if (existingEmailBrandMap.has(emailBrandKey)) {
              flagSentThreshold = 1;
              this.logger.debug(`Setting flag_sentthreshold=1 for email+brand combination: ${emailBrandKey}`);
            }
          }

          // Map the incoming data to EmailCustomerEntity fields
          const emailCustomerData = {
            sec_company_id: company.id,
            xml_email: item.email || null,
            xml_initials: item.firstName || null,
            xml_customername: item.lastName || null,
            xml_brand: item.brand?.toLowerCase() || null,
            xml_rasnumber: item.id || null,
            xml_language: language,
            xml_phone2: item.phone || null,
            xml_readydate: item.event_date ? new Date(item.event_date) : new Date(),
            xml_refnr: item.program_name || null,
            xml_fident: item.program_id || null,
            xml_region: item.region || null,
            flag_ext_source: item.flag_source || null,
            flag_sentthreshold: flagSentThreshold,
            creation_date: new Date(),
            modification_date: new Date(),
            import_date: new Date(),
            // 2024-07-04 JB: changed from acceptance_by to technician as it is the same list, only it is used in a different touchpoint, so better to use same field in FF
            xml_technician: item.owner_id ? item.owner_id : item.manager_id || '',
            xml_proddivision: item.team || '',
            xml_payment: item.role || '',
            xml_wvb: item.account_id || '',
            xml_enumber: item.contact_id || '',
            xml_department: departmentId,
            survey_done: 0
          };

          // First check if a record with the same rasnumber (id) already exists
          let existingCustomer = null;

          if (item.id) {
            existingCustomer = await this.emailCustomerRepo.findOne({
              where: {
                xml_rasnumber: item.id,
                sec_company_id: company.id
              }
            });

            if (existingCustomer) {
              // Record with this ID already exists in the database
              results.inDB++;
              this.logger.debug(`Found existing record with ID ${item.id} for record #${index + 1}`, {
                id: existingCustomer.email_customer_id
              });
            }
          }

          if (!existingCustomer) {
            // Create new customer
            const newCustomer = this.emailCustomerRepo.create(emailCustomerData);
            await this.emailCustomerRepo.save(newCustomer);
            results.new++;
            this.logger.debug(`Created new customer record #${index + 1}`);
          }
        } catch (error) {
          this.logger.error(`Error processing data record #${index + 1}`, { error });
          results.errors.push(`Record #${index + 1}: ${error.message}`);
        }
      }

      return results;
    } catch (error) {
      this.logger.error('Error processing data records', { error });
      throw new HttpException(
        {
          message: 'Failed to process data records',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }
}
