import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataController } from './data.controller';
import { DataService } from './data.service';
import { EmailCustomerEntity } from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";
import { SurveyUtils } from '@/modules/survey/survey.utils';
import { UsersModule } from '@/modules/users/users.module';

@Module({
  imports: [
    ModuleLoggerModule.register('external.data'),
    TypeOrmModule.forFeature([EmailCustomerEntity]),
    ExternalAuthModule,
    UsersModule,
  ],
  controllers: [DataController],
  providers: [DataService, SurveyUtils],
  exports: [DataService],
})
export class ExternalDataModule {}
