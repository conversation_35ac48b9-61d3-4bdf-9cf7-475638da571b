import { ApiProperty } from '@nestjs/swagger';

export class DataResponse {
  @ApiProperty({
    description: 'Number of newly created data records',
    example: 1
  })
  new: number;

  @ApiProperty({
    description: 'Number of updated data records',
    example: 0
  })
  updated: number;

  @ApiProperty({
    description: 'Number of duplicate records detected (already in database)',
    example: 2
  })
  inDB: number;

  @ApiProperty({
    description: 'List of errors that occurred during processing',
    example: ['Invalid email format for record #2']
  })
  errors: string[];
}
