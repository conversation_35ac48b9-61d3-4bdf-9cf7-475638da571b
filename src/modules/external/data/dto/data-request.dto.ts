import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  <PERSON>Array, 
  IsNotEmpty, 
  IsString, 
  ValidateNested, 
  IsOptional, 
  IsEmail,
  IsDate,
  IsNumber
} from 'class-validator';

export class DataDto {
  @ApiProperty({
    description: 'First name',
    example: 'John'
  })
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiProperty({
    description: 'Last name',
    example: 'Doe'
  })
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiProperty({
    description: 'Event date',
    example: '2024-05-15'
  })
  @IsDate()
  @IsOptional()
  event_date?: Date;

  @ApiProperty({
    description: 'Language',
    example: 'en'
  })
  @IsString()
  @IsOptional()
  language?: string;

  @ApiProperty({
    description: 'ID',
    example: 'ID12345'
  })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'Brand',
    example: 'Brand XYZ'
  })
  @IsString()
  @IsOptional()
  brand?: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'Phone number',
    example: '+***********'
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({
    description: 'Contact ID',
    example: 'CONT12345'
  })
  @IsString()
  @IsOptional()
  contact_id?: string;

  @ApiProperty({
    description: 'Account ID',
    example: 'ACC12345'
  })
  @IsString()
  @IsOptional()
  account_id?: string;

  @ApiProperty({
    description: 'Owner ID',
    example: 'OWN12345'
  })
  @IsString()
  @IsOptional()
  owner_id?: string;

  @ApiProperty({
    description: 'Team',
    example: 'Sales Team'
  })
  @IsString()
  @IsOptional()
  team?: string;

  @ApiProperty({
    description: 'Region',
    example: 'North'
  })
  @IsString()
  @IsOptional()
  region?: string;

  @ApiProperty({
    description: 'Program ID',
    example: 'PROG12345'
  })
  @IsString()
  @IsOptional()
  program_id?: string;

  @ApiProperty({
    description: 'Program name',
    example: 'Loyalty Program'
  })
  @IsString()
  @IsOptional()
  program_name?: string;

  @ApiProperty({
    description: 'Manager ID',
    example: 'MNG12345'
  })
  @IsString()
  @IsOptional()
  manager_id?: string;

  @ApiProperty({
    description: 'Role',
    example: 'Customer'
  })
  @IsString()
  @IsOptional()
  role?: string;

  @ApiProperty({
    description: 'Survey',
    example: 'Customer Satisfaction'
  })
  @IsString()
  @IsOptional()
  survey?: string;

  @ApiProperty({
    description: 'Flag source',
    example: 1
  })
  @IsNumber()
  @IsOptional()
  flag_source?: number;
}

export class DataRequestDto {
  @ApiProperty({
    description: 'Array of data objects',
    type: [DataDto]
  })
  @Type(() => DataDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsNotEmpty()
  data: DataDto[];
}
