import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { DataService } from './data.service';
import { DataRequestDto } from './dto/data-request.dto';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { DataResponse } from "./dto/data-response.dto";

@ApiTags('Data')
@ApiBearerAuth()
@Controller({
  path: 'external/data',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class DataController {
  constructor(private readonly dataService: DataService) {}

  @Post()
  @ApiOperation({ summary: 'Create or update data records in email_customer table' })
  @ApiResponse({ 
    status: 200, 
    description: 'Data processed successfully',
    type: DataResponse
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing token' 
  })
  async createData(@Body() data: DataRequestDto): Promise<DataResponse> {
    return this.dataService.createData(data);
  }
}
