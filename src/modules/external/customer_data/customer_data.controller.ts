import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CustomerDataService } from './customer_data.service';
import { CustomerDataRequestDto } from './dto/customer-data-request.dto';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { CustomerDataResponse } from "./dto/customer-data-response.dto";

@ApiTags('Customer Data')
@ApiBearerAuth()
@Controller({
  path: 'external/customer_data',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class CustomerDataController {
  constructor(private readonly customerDataService: CustomerDataService) {}

  @Post()
  @ApiOperation({ summary: 'Create or update customer data information' })
  @ApiResponse({ 
    status: 200, 
    description: 'Customer data processed successfully',
    type: CustomerDataResponse
  })
  async createCustomerData(@Body() data: CustomerDataRequestDto): Promise<CustomerDataResponse> {
    return this.customerDataService.createCustomerData(data);
  }
}
