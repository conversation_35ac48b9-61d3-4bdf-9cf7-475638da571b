import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsArray, 
  IsNotEmpty, 
  IsString, 
  ValidateNested, 
  IsOptional, 
  IsEmail,
  IsDate,
  IsNumber,
  ValidateIf,
  IsMobilePhone,
  IsIn
} from 'class-validator';

export class CustomerDataDto {
  @ApiProperty({
    description: 'Touchpoint key',
    example: '修理'
  })
  @IsString()
  @IsNotEmpty()
  touchpoint_key: string;

  @ApiProperty({
    description: 'Language',
    example: 'zh'
  })
  @IsString()
  @IsNotEmpty()
  language: string;

  @ApiProperty({
    description: 'Brand',
    example: '博世'
  })
  @IsString()
  @IsNotEmpty()
  brand: string;

  @ApiProperty({
    description: 'Unique number',
    example: 'abc123'
  })
  @IsString()
  @IsOptional()
  unique_number?: string;

  @ApiProperty({
    description: 'Visit date',
    example: '2024-03-25 10:00:00'
  })
  @IsDate()
  @IsNotEmpty()
  visit_date: Date;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  @ValidateIf(o => !o.mobile_number)
  email?: string;

  @ApiProperty({
    description: 'Mobile number',
    example: '0031612345678'
  })
  @IsMobilePhone()
  @ValidateIf(o => !o.email)
  mobile_number?: string;

  @ApiProperty({
    description: 'Origin service',
    example: 'test'
  })
  @IsString()
  @IsOptional()
  origin_service?: string;

  @ApiProperty({
    description: 'Salutation',
    example: 'Mr'
  })
  @IsString()
  @IsOptional()
  salutation?: string;

  @ApiProperty({
    description: 'First name',
    example: 'jos'
  })
  @IsString()
  @IsOptional()
  firstname?: string;

  @ApiProperty({
    description: 'Last name',
    example: 'test'
  })
  @IsString()
  @IsOptional()
  lastname?: string;

  @ApiProperty({
    description: 'Customer ID',
    example: 'CUST123'
  })
  @IsString()
  @IsOptional()
  customer_id?: string;

  @ApiProperty({
    description: 'Product area',
    example: 'Home'
  })
  @IsString()
  @IsOptional()
  product_area?: string;

  @ApiProperty({
    description: 'Product division',
    example: 'Appliances'
  })
  @IsString()
  @IsOptional()
  product_division?: string;

  @ApiProperty({
    description: 'E-number',
    example: 'E12345'
  })
  @IsString()
  @IsOptional()
  e_number?: string;

  @ApiProperty({
    description: 'Production date',
    example: '2023-01-15'
  })
  @IsString()
  @IsOptional()
  production_date?: string;

  @ApiProperty({
    description: 'Consecutive number',
    example: 12345.0
  })
  @IsNumber()
  @IsOptional()
  consecutive_number?: number;

  @ApiProperty({
    description: 'Purchase date',
    example: '2023-02-20'
  })
  @IsDate()
  @IsOptional()
  purchase_date?: Date;

  @ApiProperty({
    description: 'Dealer',
    example: 'Dealer XYZ'
  })
  @IsString()
  @IsOptional()
  dealer?: string;

  @ApiProperty({
    description: 'Acceptance date',
    example: '2023-03-01'
  })
  @IsDate()
  @IsOptional()
  acceptance_date?: Date;

  @ApiProperty({
    description: 'Front line agent',
    example: 'Agent 007'
  })
  @IsString()
  @IsOptional()
  front_line_agent?: string;

  @ApiProperty({
    description: 'Pre prepper',
    example: 'Prepper 1'
  })
  @IsString()
  @IsOptional()
  pre_prepper?: string;

  @ApiProperty({
    description: 'Dispatcher',
    example: 'Dispatcher 1'
  })
  @IsString()
  @IsOptional()
  dispatcher?: string;

  @ApiProperty({
    description: 'Technician',
    example: 'Tech 1'
  })
  @IsString()
  @IsOptional()
  technician?: string;

  @ApiProperty({
    description: 'Technician group',
    example: 'Group A'
  })
  @IsString()
  @IsOptional()
  technician_group?: string;

  @ApiProperty({
    description: 'Service partner',
    example: 'Partner XYZ'
  })
  @IsString()
  @IsOptional()
  service_partner?: string;

  @ApiProperty({
    description: 'Branch',
    example: 'Branch 1'
  })
  @IsString()
  @IsOptional()
  branch?: string;

  @ApiProperty({
    description: 'Cluster',
    example: 'Cluster A'
  })
  @IsString()
  @IsOptional()
  cluster?: string;

  @ApiProperty({
    description: 'Last visit date',
    example: '2023-03-15'
  })
  @IsDate()
  @IsOptional()
  last_visit_date?: Date;

  @ApiProperty({
    description: 'Number of visits',
    example: 3
  })
  @IsNumber()
  @IsOptional()
  number_of_visits?: number;

  @ApiProperty({
    description: 'F ident',
    example: 'F12345'
  })
  @IsString()
  @IsOptional()
  f_ident?: string;

  @ApiProperty({
    description: 'RC',
    example: 'RC123'
  })
  @IsString()
  @IsOptional()
  rc?: string;

  @ApiProperty({
    description: 'FC',
    example: 'FC123'
  })
  @IsString()
  @IsOptional()
  fc?: string;

  @ApiProperty({
    description: 'LA warranty',
    example: 'LA123'
  })
  @IsString()
  @IsOptional()
  la_warranty?: string;

  @ApiProperty({
    description: 'Closure date',
    example: '2023-04-01'
  })
  @IsString()
  @IsOptional()
  closure_date?: string;

  @ApiProperty({
    description: 'Invoice amount',
    example: '150.00'
  })
  @IsNumber()
  @IsOptional()
  invoice_amount?: number;

  @ApiProperty({
    description: 'Accounting indicator',
    example: 'IND123'
  })
  @IsString()
  @IsOptional()
  accounting_indicator?: string;

  @ApiProperty({
    description: 'Currency',
    example: 'EUR'
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({
    description: 'Payment method',
    example: 'Credit Card'
  })
  @IsString()
  @IsOptional()
  payment_method?: string;

  @ApiProperty({
    description: 'FKZ',
    example: 'FKZ123'
  })
  @IsString()
  @IsOptional()
  fkz?: string;

  @ApiProperty({
    description: 'Employee',
    example: 'EMP123'
  })
  @IsString()
  @IsOptional()
  employee?: string;

  @ApiProperty({
    description: 'Logistic partner',
    example: 'Partner ABC'
  })
  @IsString()
  @IsOptional()
  logistic_partner?: string;

  @ApiProperty({
    description: 'City',
    example: 'Amsterdam'
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty({
    description: 'Company name',
    example: 'Company XYZ'
  })
  @IsString()
  @IsOptional()
  company_name?: string;

  @ApiProperty({
    description: 'Address',
    example: 'Street 123'
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    description: 'Full address',
    example: 'Street 123, 1234 AB Amsterdam'
  })
  @IsString()
  @IsOptional()
  full_address?: string;

  @ApiProperty({
    description: 'Reference',
    example: 'REF123'
  })
  @IsString()
  @IsOptional()
  reference?: string;

  @ApiProperty({
    description: 'Region',
    example: 'North'
  })
  @IsString()
  @IsOptional()
  region?: string;

  @ApiProperty({
    description: 'Order channel',
    example: 'Online'
  })
  @IsString()
  @IsOptional()
  order_channel?: string;

  @ApiProperty({
    description: 'Sales channel',
    example: 'Direct'
  })
  @IsString()
  @IsOptional()
  sales_channel?: string;
}

export class CustomerDataRequestDto {
  @ApiProperty({
    description: 'Array of customer data',
    type: [CustomerDataDto]
  })
  @Type(() => CustomerDataDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsNotEmpty()
  customer_data: CustomerDataDto[];
}
