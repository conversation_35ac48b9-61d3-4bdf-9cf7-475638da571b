import { ApiProperty } from '@nestjs/swagger';

export class CustomerDataResponse {
  @ApiProperty({
    description: 'Number of newly created customer data records',
    example: 1
  })
  new: number;

  @ApiProperty({
    description: 'Number of updated customer data records',
    example: 0
  })
  updated: number;

  @ApiProperty({
    description: 'List of errors that occurred during processing',
    example: ['Invalid email format for record #2']
  })
  errors: string[];
}
