import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerDataController } from './customer_data.controller';
import { CustomerDataService } from './customer_data.service';
import { EmailCustomerEntity } from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.customer_data'),
    TypeOrmModule.forFeature([EmailCustomerEntity]),
    ExternalAuthModule,
  ],
  controllers: [CustomerDataController],
  providers: [CustomerDataService],
  exports: [CustomerDataService],
})
export class ExternalCustomerDataModule {}
