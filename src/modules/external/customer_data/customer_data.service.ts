import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { EmailCustomerEntity } from "@entities";
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { CustomerDataRequestDto } from './dto/customer-data-request.dto';
import { CustomerDataResponse } from "./dto/customer-data-response.dto";
import { ModuleLogger } from "@helpers/logger/module-logger/module-logger.service";

@Injectable()
export class CustomerDataService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(EmailCustomerEntity)
    private emailCustomerRepo: Repository<EmailCustomerEntity>,
  ) {}

  async createCustomerData(data: CustomerDataRequestDto): Promise<CustomerDataResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const results: CustomerDataResponse = { new: 0, updated: 0, errors: [] };

    try {
      for (const [index, customerData] of data.customer_data.entries()) {
        this.logger.debug(`Processing customer data record #${index + 1}`, { customerData });

        // Validate that either email or mobile_number is provided
        if (!customerData.email && !customerData.mobile_number) {
          results.errors.push(`Record #${index + 1}: Either email or mobile_number must be provided`);
          continue;
        }

        try {
          // Check if customer already exists by email or mobile
          let existingCustomer = null;
          if (customerData.email) {
            existingCustomer = await this.emailCustomerRepo.findOne({
              where: {
                xml_email: customerData.email,
                sec_company_id: company.id,
              },
            });
          }

          if (!existingCustomer && customerData.mobile_number) {
            existingCustomer = await this.emailCustomerRepo.findOne({
              where: {
                xml_phone1: customerData.mobile_number,
                sec_company_id: company.id,
              },
            });
          }

          // Prepare customer data
          const emailCustomerData = {
            xml_salutation: customerData.salutation || null,
            xml_initials: customerData.firstname || null,
            xml_customername: customerData.lastname || null,
            xml_email: customerData.email || null,
            xml_brand: customerData.brand,
            xml_address: customerData.address || null,
            xml_fulladdress: customerData.full_address || customerData.address || null,
            xml_city: customerData.city || null,
            xml_phone1: customerData.mobile_number || null,
            xml_companyname: customerData.company_name || null,
            xml_originservice: customerData.origin_service || null,
            xml_prodarea: customerData.product_area || null,
            xml_proddivision: customerData.product_division || null,
            xml_enumber: customerData.e_number || null,
            xml_proddate: customerData.production_date || null,
            xml_consecnr: customerData.consecutive_number ? customerData.consecutive_number : null,
            xml_purchasedate: customerData.purchase_date ? new Date(customerData.purchase_date) : null,
            xml_dealer: customerData.dealer || null,
            xml_rasnumber: customerData.unique_number || null,
            xml_acceptancedate: customerData.acceptance_date ? new Date(customerData.acceptance_date) : null,
            xml_acceptance_by: customerData.front_line_agent || null,
            xml_planning: customerData.pre_prepper || null,
            xml_wvb: customerData.dispatcher || null,
            xml_technician: customerData.technician || null,
            xml_techgrp: customerData.technician_group || null,
            xml_custcreditnr: customerData.service_partner || null,
            xml_projectnm: customerData.branch || null,
            xml_projectdiv: customerData.cluster || null,
            xml_lastvisitdate: customerData.last_visit_date ? new Date(customerData.last_visit_date) : null,
            xml_nrvisits: customerData.number_of_visits ? customerData.number_of_visits : null,
            xml_fident: customerData.f_ident || null,
            xml_rc: customerData.rc || null,
            xml_fc: customerData.fc || null,
            xml_la: customerData.la_warranty || null,
            xml_closuredate: customerData.closure_date ? new Date(customerData.closure_date) : null,
            xml_amount: customerData.invoice_amount ? customerData.invoice_amount : null,
            xml_accountindic: customerData.accounting_indicator || null,
            xml_currency: customerData.currency || null,
            xml_payment: customerData.payment_method || null,
            xml_fkz: customerData.fkz || null,
            xml_employee: customerData.employee || null,
            xml_logisticpartner: customerData.logistic_partner || null,
            xml_refnr: customerData.reference || null,
            xml_region: customerData.region || null,
            xml_readydate: customerData.visit_date ? new Date(customerData.visit_date) : new Date(),
            xml_language: customerData.language,
            flag_sentthreshold: 0,
            sec_company_id: company.id,
            modification_date: new Date(),
          };

          if (existingCustomer) {
            // Update existing customer
            await this.emailCustomerRepo.update(
              { email_customer_id: existingCustomer.email_customer_id },
              emailCustomerData
            );
            results.updated++;
            this.logger.debug(`Updated existing customer record #${index + 1}`, {
              id: existingCustomer.email_customer_id
            });
          } else {
            // Create new customer
            const newCustomer = this.emailCustomerRepo.create({
              ...emailCustomerData,
              creation_date: new Date(),
            });
            await this.emailCustomerRepo.save(newCustomer);
            results.new++;
            this.logger.debug(`Created new customer record #${index + 1}`);
          }
        } catch (error) {
          this.logger.error(`Error processing customer data record #${index + 1}`, { error });
          results.errors.push(`Record #${index + 1}: ${error.message}`);
        }
      }

      return results;
    } catch (error) {
      this.logger.error('Failed to process customer data', { error });
      throw new HttpException(
        {
          message: 'Failed to process customer data',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }
}
