import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ServiceRequestDto {
  @ApiProperty({ description: 'Department name' })
  @IsString()
  department: string;

  @ApiProperty({ description: 'Process key' })
  @IsString()
  process_key: string;

  @ApiProperty({ description: 'Email address' })
  @IsString()
  email: string;

  @ApiProperty({ description: 'Project number' })
  @IsString()
  project_number: string;

  @ApiProperty({ description: 'Club number' })
  @IsString()
  club_number: string;

  @ApiProperty({ description: 'Salutation' })
  @IsString()
  salutation: string;

  @ApiProperty({ description: 'Ticket number' })
  @IsString()
  ticket_number: string;

  @ApiProperty({ description: 'Date' })
  @IsString()
  date: string;

  @ApiProperty({ description: 'Name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Initials' })
  @IsString()
  initials: string;

  @ApiProperty({ description: 'Phone number' })
  @IsString()
  phone: string;

  @ApiProperty({ description: 'Mobile number' })
  @IsString()
  mobile: string;

  @ApiProperty({ description: 'Address' })
  @IsString()
  address: string;

  @ApiProperty({ description: 'Address number' })
  @IsNumber()
  address_number: number;

  @ApiProperty({ description: 'Address addition' })
  @IsString()
  address_addition: string;

  @ApiProperty({ description: 'Postal code' })
  @IsString()
  postal_code: string;

  @ApiProperty({ description: 'City' })
  @IsString()
  city: string;

  @ApiProperty({ description: 'Reference' })
  @IsString()
  reference: string;

  @ApiProperty({ description: 'Subcontractor' })
  @IsOptional()
  @IsString()
  subcontractor?: string;

  @ApiProperty({ description: 'Employee' })
  @IsOptional()
  @IsString()
  employee?: string;

  @ApiProperty({ description: 'Language' })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ description: 'Description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Region' })
  @IsOptional()
  @IsString()
  region?: string;

  @ApiProperty({ description: 'Company' })
  @IsOptional()
  @IsString()
  company?: string;

  @ApiProperty({ description: 'Return links array' })
  @IsOptional()
  return_link?: Array<{ mail: string; link: string }>;
} 