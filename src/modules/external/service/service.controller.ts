import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ServiceService } from './service.service';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { ServiceRequestDto } from './dto/service-request.dto';
import { ServiceResponseDto } from './dto/service-response.dto';

@ApiTags('Service')
@ApiBearerAuth()
@Controller({
  path: 'external/service',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class ServiceController {
  constructor(private readonly serviceService: ServiceService) {}

  @Post()
  @ApiOperation({ summary: 'Create service record' })
  @ApiResponse({ 
    status: 200, 
    description: 'Service record created successfully',
    type: ServiceResponseDto
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Bad request - validation error or business logic error'
  })
  async createService(@Body() data: ServiceRequestDto): Promise<ServiceResponseDto> {
    return this.serviceService.createService(data);
  }
} 