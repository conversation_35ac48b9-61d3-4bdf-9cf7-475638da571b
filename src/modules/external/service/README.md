# External Service API

This module provides the external service API endpoint for creating service records.

## Endpoint

- **URL**: `/v2/external/service`
- **Method**: `POST`
- **Authentication**: Required (Bearer token)

## Request Body

The API expects a JSON payload with the following structure:

```json
{
  "department": "string",
  "process_key": "string", 
  "email": "string",
  "project_number": "string",
  "club_number": "string",
  "salutation": "string",
  "ticket_number": "string",
  "date": "string",
  "name": "string",
  "initials": "string",
  "phone": "string",
  "mobile": "string",
  "address": "string",
  "address_number": "number",
  "address_addition": "string",
  "postal_code": "string",
  "city": "string",
  "reference": "string",
  "subcontractor": "string (optional)",
  "employee": "string (optional)",
  "language": "string (optional)",
  "description": "string (optional)",
  "return_link": "array (optional)"
}
```

### Field Descriptions

- `department`: Department name for lookup
- `process_key`: Process key for department lookup (used if department is not provided)
- `email`: Customer email address
- `project_number`: Project number
- `club_number`: Club number
- `salutation`: Customer salutation
- `ticket_number`: Ticket/reference number
- `date`: Date in format 'yyyy-MM-dd' or 'yyyy-MM-dd HH:mm'
- `name`: Customer name
- `initials`: Customer initials
- `phone`: Phone number
- `mobile`: Mobile number
- `address`: Street address
- `address_number`: House/building number
- `address_addition`: Address addition (apartment, etc.)
- `postal_code`: Postal code
- `city`: City name
- `reference`: Reference number
- `subcontractor`: Subcontractor external ID (optional)
- `employee`: Employee external ID (optional)
- `language`: Language code (2 or 3 letters, defaults to 'nl')
- `description`: Description text (optional)
- `return_link`: Array for generating survey links (optional)

## Response

### Success Response (200)

```json
{
  "new": 1,
  "links": [
    {
      "mail": "<EMAIL>",
      "link": "https://survey.example.com/1/abc123"
    }
  ]
}
```

### Error Responses

- `400 Bad Request`: Validation error or business logic error
- `401 Unauthorized`: Invalid or missing authentication token
- `500 Internal Server Error`: Server error

## Business Logic

The API implements the following steps:

1. **Department Lookup**: Finds department_id using department or process_key
2. **Brand Name Lookup**: Gets brand name from email templates
3. **Threshold Check**: Calculates threshold date and checks if record is blocked
4. **Project Management**: Looks up or creates project record
5. **Language Processing**: Handles 2/3 letter language codes
6. **Salutation Processing**: Looks up proper salutation greeting
7. **Subcontractor/Employee Lookup**: Finds IDs by external_id
8. **Email Customer Creation**: Creates the main record
9. **Link Generation**: Generates survey links if requested

## Global Utilities

The module uses global utility functions that can be reused by other modules:

- `getDepartmentId()`: Department lookup function
- `getThresholdDate()`: Threshold calculation function
- `parseDate()`: Date parsing utility
- `getUUIDString()`: UUID generation utility

## Dependencies

- External authentication module
- Database entities for all related tables
- Global utilities service
- Module logger for debugging 