import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import {
  EmailCustomerEntity,
  ProjectsEntity,
  SurvSurveyEntity,
  EmailTemplateEntity,
  SecProgramSettingsEntity,
  SurveyDepartment,
  LanguagesEntity,
  SalutationEntity,
  SalutationItemEntity,
  SubcontractorsEntity,
  EmployeesEntity,
  CompanyEntity,
} from '@entities';
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ServiceRequestDto } from './dto/service-request.dto';
import { ServiceResponseDto } from './dto/service-response.dto';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';
import { GlobalUtilsService } from '@/shared/utils/global-utils.service';

@Injectable()
export class ServiceService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private globalUtils: GlobalUtilsService,
    @InjectRepository(EmailCustomerEntity)
    private readonly emailCustomerRepo: Repository<EmailCustomerEntity>,
    @InjectRepository(ProjectsEntity)
    private readonly projectsRepo: Repository<ProjectsEntity>,
    @InjectRepository(SurvSurveyEntity)
    private readonly surveyRepo: Repository<SurvSurveyEntity>,
    @InjectRepository(EmailTemplateEntity)
    private readonly emailTemplateRepo: Repository<EmailTemplateEntity>,
    @InjectRepository(SecProgramSettingsEntity)
    private readonly programSettingsRepo: Repository<SecProgramSettingsEntity>,
    @InjectRepository(SurveyDepartment)
    private readonly surveyDepartmentRepo: Repository<SurveyDepartment>,
    @InjectRepository(LanguagesEntity)
    private readonly languagesRepo: Repository<LanguagesEntity>,
    @InjectRepository(SalutationEntity)
    private readonly salutationRepo: Repository<SalutationEntity>,
    @InjectRepository(SalutationItemEntity)
    private readonly salutationItemRepo: Repository<SalutationItemEntity>,
    @InjectRepository(SubcontractorsEntity)
    private readonly subcontractorsRepo: Repository<SubcontractorsEntity>,
    @InjectRepository(EmployeesEntity)
    private readonly employeesRepo: Repository<EmployeesEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    private dataSource: DataSource,
  ) {}

  async createService(data: ServiceRequestDto): Promise<ServiceResponseDto> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    
    this.logger.debug('service.createService', { 
      companyId: company.id,
      email: data.email 
    });

    // Step 1: Lookup department_id
    const departmentId = await this.globalUtils.getDepartmentId(
      data.department,
      data.process_key,
      company.id,
    );

    if (!departmentId) {
      throw new Error('Department not found');
    }

    // Step 2: Lookup brand name
    const brandName = await this.getBrandName(departmentId, company.id);
    if (!brandName) {
      throw new Error('Brand name not found');
    }

    // Step 3: Get threshold value
    const thresholdDate = await this.globalUtils.getThresholdDate(company.id);

    // Step 4: Check if record is blocked by threshold
    const isBlocked = await this.checkThresholdBlock(
      thresholdDate,
      data.email,
      brandName,
      company.id,
    );

    // Step 5: Lookup or create project
    const projectId = await this.getOrCreateProject(
      data.project_number || data.club_number,
      company.id,
    );

    // Step 6: Check language
    const language = await this.getLanguage(data.language);

    // Step 7: Check salutations
    const salutation = await this.getSalutation(
      data.salutation,
      language,
      company.id,
    );

    // Step 8: Lookup subcontractor
    const subcontractorId = data.subcontractor
      ? await this.getSubcontractorId(data.subcontractor, company.id)
      : null;

    // Step 9: Lookup employee
    const employeeId = data.employee
      ? await this.getEmployeeId(data.employee, company.id)
      : null;

    // Step 10: Create email_customer record
    const emailCustomer = await this.createEmailCustomer({
      data,
      departmentId,
      brandName,
      isBlocked,
      projectId,
      salutation,
      subcontractorId,
      employeeId,
      companyId: company.id,
      language,
    });

    // Step 11: Create return object
    const response: ServiceResponseDto = {
      new: 1,
    };

    // Add links if return_link is provided
    if (data.return_link) {
      const links = await this.generateLinks(
        departmentId,
        brandName,
        emailCustomer.surv_key,
        company.id,
      );
      response.links = links;
    }

    this.logger.debug('service.createService result', { 
      emailCustomerId: emailCustomer.email_customer_id,
      new: response.new 
    });

    return response;
  }

  private async getBrandName(departmentId: number, companyId: number): Promise<string | null> {
    const result = await this.dataSource.query(
      `SELECT e.triggername FROM email_template e 
       LEFT JOIN surv_surveytemplate st ON e.email_template_id=st.email_template_id 
       LEFT JOIN surv_survey s ON st.surv_survey_id=s.surv_survey_id 
       WHERE s.department = $1 and s.sec_company_id = $2`,
      [departmentId, companyId],
    );

    return result?.[0]?.triggername || null;
  }

  private async checkThresholdBlock(
    thresholdDate: Date,
    email: string,
    brandName: string,
    companyId: number,
  ): Promise<boolean> {
    const result = await this.dataSource.query(
      `SELECT email_customer_id FROM email_customer 
       WHERE (send_date > $1 OR send_date IS NULL) AND xml_email=$2 AND xml_brand=$3 
       AND sec_company_id = $4 AND flag_sentthreshold != 1`,
      [thresholdDate, email, brandName, companyId],
    );

    return result.length > 0;
  }

  private async getOrCreateProject(projectNr: string, companyId: number): Promise<string> {
    let project = await this.projectsRepo.findOne({
      where: { project_nr: projectNr, sec_company_id: companyId },
      select: ['project_id'],
    });

    if (!project) {
      project = this.projectsRepo.create({
        project_nr: projectNr,
        sec_company_id: companyId,
        creation_date: new Date(),
        project_name: projectNr, // Using project_nr as name for now
      });
      await this.projectsRepo.save(project);
    }

    return project.project_id;
  }

  private async getLanguage(language?: string): Promise<string> {
    if (!language) {
      return 'nl';
    }

    if (language.length === 2) {
      return language;
    }

    if (language.length === 3) {
      const langEntity = await this.languagesRepo.findOne({
        where: { lang_369_2: language },
        select: ['lang_369_1'],
      });
      return langEntity?.lang_369_1 || 'nl';
    }

    return 'nl';
  }

  private async getSalutation(
    salutation: string,
    language: string,
    companyId: number,
  ): Promise<string> {
    const result = await this.dataSource.query(
      `SELECT s.salutation_language, i.salutation_item, s.salutation_greet 
       FROM salutation_items i 
       LEFT JOIN salutations s ON i.salutation_id=s.salutation_id 
       WHERE s.sec_company_id=$1 AND s.salutation_language=$2 AND i.salutation_item=$3`,
      [companyId, language, salutation],
    );

    return result?.[0]?.salutation_greet || salutation;
  }

  private async getSubcontractorId(externalId: string, companyId: number): Promise<string | null> {
    const subcontractor = await this.subcontractorsRepo.findOne({
      where: { external_id: externalId, sec_company_id: companyId },
      select: ['id'],
    });

    return subcontractor?.id || null;
  }

  private async getEmployeeId(externalId: string, companyId: number): Promise<string | null> {
    const employee = await this.employeesRepo.findOne({
      where: { external_id: externalId, sec_company_id: companyId },
      select: ['id'],
    });

    return employee?.id || null;
  }

  private async createEmailCustomer(params: {
    data: ServiceRequestDto;
    departmentId: number;
    brandName: string;
    isBlocked: boolean;
    projectId: string;
    salutation: string;
    subcontractorId: string | null;
    employeeId: string | null;
    companyId: number;
    language: string;
  }): Promise<EmailCustomerEntity> {
    const {
      data,
      departmentId,
      brandName,
      isBlocked,
      projectId,
      salutation,
      subcontractorId,
      employeeId,
      companyId,
      language,
    } = params;

    const emailCustomer = this.emailCustomerRepo.create({
      xml_brand: brandName,
      xml_department: departmentId,
      sec_company_id: companyId,
      creation_date: new Date(),
      import_date: new Date(),
      flag_sentthreshold: isBlocked ? 1 : 0,
      xml_rasnumber: data.ticket_number,
      xml_readydate: data.date.indexOf(':') > 0
        ? this.globalUtils.parseDate(data.date, 'yyyy-MM-dd HH:mm')
        : this.globalUtils.parseDate(data.date, 'yyyy-MM-dd'),
      xml_email: data.email,
      xml_salutation: salutation,
      xml_customername: data.name,
      xml_initials: data.initials,
      xml_phone1: data.phone,
      xml_phone2: data.mobile,
      xml_address: data.address,
      xml_techgrp: data.address_number.toString(),
      xml_technician: data.address_addition,
      xml_enumber: data.postal_code,
      xml_city: data.city,
      xml_internal_external: projectId,
      xml_refnr: data.reference,
      xml_planning: subcontractorId,
      xml_employee: employeeId,
      xml_areatext: data.description || '',
      xml_language: language,
      xml_region: data.region,
      xml_companyname: data.company,
      surv_key: this.globalUtils.getUUIDString(),
    });

    return await this.emailCustomerRepo.save(emailCustomer);
  }

  private async generateLinks(
    departmentId: number,
    brandName: string,
    survKey: string,
    companyId: number,
  ): Promise<Array<{ mail: string; link: string }>> {
    const result = await this.dataSource.query(
      `select st.surv_survey_id, st.email_template_id, p.survey_link
       from surv_surveytemplate st
       inner join surv_survey s on st.surv_survey_id=s.surv_survey_id
       left join email_template et on st.email_template_id = et.email_template_id
       left join sec_programsettings p on s.sec_company_id = p.sec_company_id
       where s.department=$1 and et.triggername=$2`,
      [departmentId, brandName],
    );

    if (!result?.[0]?.survey_link) {
      return [];
    }

    const links = [];
    for (const row of result) {
      const link = `${row.survey_link}/1/${survKey}`;
      links.push({ mail: '', link });

      // Update email_customer with survey and template IDs
      await this.emailCustomerRepo.update(
        { surv_key: survKey },
        {
          surv_survey_id: row.surv_survey_id,
          email_template_id: row.email_template_id,
        },
      );
    }

    return links;
  }
} 