import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServiceController } from './service.controller';
import { ServiceService } from './service.service';
import { 
  EmailCustomerEntity, 
  ProjectsEntity,
  SurvSurveyEntity,
  EmailTemplateEntity,
  SecProgramSettingsEntity,
  SurveyDepartment,
  LanguagesEntity,
  SalutationEntity,
  SalutationItemEntity,
  SubcontractorsEntity,
  EmployeesEntity,
  CompanyEntity,
} from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';
import { GlobalUtilsService } from '@/shared/utils/global-utils.service';

@Module({
  imports: [
    ModuleLoggerModule.register('external.service'),
    TypeOrmModule.forFeature([
      EmailCustomerEntity, 
      ProjectsEntity,
      SurvSurveyEntity,
      EmailTemplateEntity,
      SecProgramSettingsEntity,
      SurveyDepartment,
      LanguagesEntity,
      SalutationEntity,
      SalutationItemEntity,
      SubcontractorsEntity,
      EmployeesEntity,
      CompanyEntity,
    ]),
    ExternalAuthModule,
  ],
  controllers: [ServiceController],
  providers: [ServiceService, GlobalUtilsService],
  exports: [ServiceService],
})
export class ExternalServiceModule {} 