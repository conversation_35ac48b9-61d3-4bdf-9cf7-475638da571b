import { Modu<PERSON> } from '@nestjs/common';
import { ProjectsController } from './projects.controller';
import { ProjectsService } from './projects.service';
import { TypeOrmModule } from "@nestjs/typeorm";
import { ProjectsEntity, PCategoriesEntity, ProjectCategoriesEntity } from "@entities";
import { ExternalAuthModule } from "@/modules/external/auth/external-auth.module";
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.projects'),
    TypeOrmModule.forFeature([
      ProjectsEntity,
      PCategoriesEntity,
      ProjectCategoriesEntity,
    ]),
    ExternalAuthModule,
  ],
  controllers: [ProjectsController],
  providers: [ProjectsService],
  exports: [ProjectsService],
})
export class ExternalProjectsModule {}
