import { ApiProperty } from '@nestjs/swagger';
import { LabelDto } from './label.dto';

export class ProjectResponseDto {
  @ApiProperty({
    description: 'Name of the project',
    example: 'Sample Project',
  })
  name: string;

  @ApiProperty({
    description: 'External name of the project',
    example: 'External Name',
  })
  external_name: string;

  @ApiProperty({
    description: 'Project number',
    example: '123',
  })
  project_number: string;

  @ApiProperty({
    description: 'City of the project',
    example: 'Rijen',
  })
  city: string;

  @ApiProperty({
    description: 'Country of the project',
    example: 'NL',
    nullable: true,
  })
  country: string;

  @ApiProperty({
    description: 'Province of the project',
    example: 'Gelderland',
    nullable: true,
  })
  province: string;

  @ApiProperty({
    description: 'Project labels',
    type: [LabelDto],
    nullable: true,
  })
  labels: LabelDto[];
}
