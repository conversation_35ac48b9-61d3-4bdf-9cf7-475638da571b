import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsString, ValidateNested, IsOptional } from 'class-validator';
import { LabelDto } from './label.dto';

export class ProjectDto {
  @ApiProperty({
    description: 'Project number',
    example: 123,
  })
  @IsString()
  @IsNotEmpty()
  project_nr: string;

  @ApiProperty({
    description: 'Project name',
    example: 'My project',
  })
  @IsString()
  @IsNotEmpty()
  project_nm: string;

  @ApiProperty({
    description: 'External project name',
    example: 'External Name',
  })
  @IsString()
  @IsNotEmpty()
  project_nm_ex: string;

  @ApiProperty({
    description: 'City of the project',
    example: 'Rijen',
  })
  @IsString()
  city: string;

  @ApiProperty({
    description: 'New project number',
    example: 123,
  })
  @IsNumber()
  @IsOptional()
  new_project_nr?: number;

  @ApiProperty({
    description: 'Project labels',
    type: [LabelDto],
  })
  @Type(() => LabelDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  labels?: LabelDto[];
}

export class ProjectRequestDto {
  @ApiProperty({
    description: 'Array of projects',
    type: [ProjectDto],
  })
  @Type(() => ProjectDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsNotEmpty()
  projects: ProjectDto[];
}
