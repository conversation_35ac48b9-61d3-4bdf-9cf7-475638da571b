import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { ProjectsEntity, PCategoriesEntity, ProjectCategoriesEntity } from '@entities';
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ProjectRequestDto } from './dto/project-request.dto';
import { ProjectResponseDto } from './dto/project-response.dto';
import { UpdateProjectResponse } from './dto/update-project-response.dto';
import { ModuleLogger } from "@helpers/logger/module-logger/module-logger.service";
import { LabelDto } from './dto/label.dto';

@Injectable()
export class ProjectsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(ProjectsEntity)
    private projectsRepo: Repository<ProjectsEntity>,
    @InjectRepository(PCategoriesEntity)
    private pcategoriesRepo: Repository<PCategoriesEntity>,
    @InjectRepository(ProjectCategoriesEntity)
    private projectCategoriesRepo: Repository<ProjectCategoriesEntity>,
  ) {}

  async getProjects(): Promise<ProjectResponseDto[]> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const projects = await this.projectsRepo
      .createQueryBuilder()
      .where('sec_company_id = :sec_company_id', { sec_company_id: company.id })
      .orderBy('project_name')
      .getMany();

    const projectIds = projects.map(project => project.project_id);

    // Get all project-category relationships for these projects
    const projectCategories = await this.projectCategoriesRepo.find({
      where: { project_id: In(projectIds) },
      relations: ['category']
    });

    // Group categories by project_id
    const projectCategoriesMap = projectCategories.reduce((map, item) => {
      if (!map[item.project_id]) {
        map[item.project_id] = [];
      }
      map[item.project_id].push({
        id: item.category.category_nr,
        name: item.category.category
      });
      return map;
    }, {});

    return projects.map((project) => ({
      name: project.project_name,
      external_name: project.project_name_extern,
      project_number: project.project_nr,
      city: project.project_city,
      country: project.project_country,
      province: project.project_province,
      labels: projectCategoriesMap[project.project_id] || [],
    }));
  }

  async updateProject(projectData: ProjectRequestDto): Promise<UpdateProjectResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const results: UpdateProjectResponse = { new: 0, updated: 0 };

    try {
      for (const project of projectData.projects) {
        this.logger.debug(`updateProject for project ${project.project_nr}`, { project });

        const existingProject = await this.findProjectByNumberAndCompany(
          project.project_nr,
          company.id
        );

        if (existingProject) {
          await this.updateExistingProject(project, company.id);
          results.updated++;
        } else {
          await this.createNewProject(project, company.id);
          results.new++;
        }
      }

      return results;
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to update projects',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  private async findProjectByNumberAndCompany(
    projectNumber: string,
    companyId: number
  ): Promise<ProjectsEntity | null> {
    return this.projectsRepo
      .createQueryBuilder()
      .where('project_nr = :project_nr', { project_nr: projectNumber })
      .andWhere('sec_company_id = :company_id', { company_id: companyId })
      .getOne();
  }

  private async updateExistingProject(
    project: ProjectRequestDto['projects'][0],
    companyId: number
  ): Promise<void> {
    this.logger.debug(`updateExistingProject for project ${project.project_nr}`, { project });

    // Update project details
    await this.projectsRepo
      .createQueryBuilder()
      .update()
      .set({
        project_name: project.project_nm,
        project_name_extern: project.project_nm_ex,
        project_city: project.city,
        modification_date: new Date(),
      })
      .where('project_nr = :project_nr', { project_nr: project.project_nr.toString() })
      .andWhere('sec_company_id = :company_id', { company_id: companyId })
      .execute();

    // Get the project entity
    const projectEntity = await this.findProjectByNumberAndCompany(project.project_nr, companyId);

    // Handle labels if they exist
    if (project.labels && projectEntity) {
      await this.handleProjectLabels(projectEntity.project_id, project.labels, companyId);
    }
  }

  private async createNewProject(
    project: ProjectRequestDto['projects'][0],
    companyId: number
  ): Promise<void> {
    this.logger.debug(`createNewProject for project ${project.project_nr}`, { project });

    const newProject = this.projectsRepo.create({
      project_nr: project.project_nr.toString(),
      project_name: project.project_nm,
      project_name_extern: project.project_nm_ex,
      project_city: project.city,
      sec_company_id: companyId,
      creation_date: new Date(),
      modification_date: new Date(),
    });

    const savedProject = await this.projectsRepo.save(newProject);

    // Handle labels if they exist
    if (project.labels) {
      await this.handleProjectLabels(savedProject.project_id, project.labels, companyId);
    }
  }

  /**
   * Handle project labels - create or get category IDs and link them to the project
   */
  private async handleProjectLabels(
    projectId: string,
    labels: LabelDto[],
    companyId: number
  ): Promise<void> {
    // Delete existing relationships for this project
    await this.projectCategoriesRepo.delete({ project_id: projectId });

    // Process each label
    for (const label of labels) {
      // Create or get the category ID
      const categoryId = await this.createOrGetPcategoryId(label.id, label.name, companyId);

      // Create new relationship
      const newRelationship = this.projectCategoriesRepo.create({
        project_id: projectId,
        pcategory_id: categoryId
      });

      await this.projectCategoriesRepo.save(newRelationship);
    }
  }

  /**
   * Create a new category or get an existing one
   */
  private async createOrGetPcategoryId(
    labelId: number,
    labelText: string,
    companyId: number
  ): Promise<string> {
    // Try to find existing category
    const existingCategory = await this.pcategoriesRepo.findOne({
      where: {
        category: labelText,
        category_nr: labelId,
        sec_company_id: companyId
      }
    });

    if (existingCategory) {
      return existingCategory.pcategory_id;
    }

    // Create new category
    const newCategory = this.pcategoriesRepo.create({
      category: labelText,
      category_nr: labelId,
      sec_company_id: companyId,
    });

    const savedCategory = await this.pcategoriesRepo.save(newCategory);
    return savedCategory.pcategory_id;
  }
}
