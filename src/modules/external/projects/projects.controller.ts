import { Controller, Get, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProjectsService } from './projects.service';
import { ProjectRequestDto } from './dto/project-request.dto';
import { ProjectResponseDto } from './dto/project-response.dto';
import { UpdateProjectResponse } from './dto/update-project-response.dto';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';

@ApiTags('Projects')
@ApiBearerAuth()
@Controller({
  path: 'external/projects',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Get()
  @ApiOperation({ summary: 'Get projects information  (Only for BUILD customers)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Projects information retrieved successfully',
    type: [ProjectResponseDto]
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing token' 
  })
  async getProjects(): Promise<ProjectResponseDto[]> {
    return this.projectsService.getProjects();
  }

  @Post()
  @ApiOperation({ summary: 'Create or update project information  (Only for BUILD customers)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Project information updated successfully',
    type: UpdateProjectResponse
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing token' 
  })
  async updateProject(@Body() body: ProjectRequestDto): Promise<UpdateProjectResponse> {
    return this.projectsService.updateProject(body);
  }
}
