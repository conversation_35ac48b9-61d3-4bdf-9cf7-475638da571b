import { ApiProperty } from '@nestjs/swagger';

export class ClubResponseDto {
  @ApiProperty({
    description: 'Name of the club',
    example: 'Sample Club',
  })
  name: string;

  @ApiProperty({
    description: 'External name of the club',
    example: 'Sample Club',
  })
  external_name: string;

  @ApiProperty({
    description: 'Club number of external system',
    example: 'A sample club number',
  })
  club_number: string;

  @ApiProperty({
    description: 'City of the club',
    example: 'Assen',
  })
  city: string;

  @ApiProperty({
    description: 'Country of the club',
    example: 'NL',
  })
  country: string;

  @ApiProperty({
    description: 'Province of the club',
    example: 'Gelderland',
  })
  province: string;
} 