import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsString, ValidateNested, IsOptional } from 'class-validator';

export class ClubDto {
  @ApiProperty({
    description: 'Club number',
    example: 68,
  })
  @IsNumber()
  @IsNotEmpty()
  club_nr: number;

  @ApiProperty({
    description: 'Club name',
    example: 'Fitness Club',
  })
  @IsString()
  @IsNotEmpty()
  club_nm: string;

  @ApiProperty({
    description: 'City of the club',
    example: 'Assen',
  })
  @IsString()
  city: string;

  @ApiProperty({
    description: "Country of the club",
    example: "NL"
  })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({
    description: 'Province of the club',
    example: 'Gelderland',
  })
  @IsString()
  @IsOptional()
  province?: string;
}

export class ClubRequestDto {
  @ApiProperty({
    description: 'Array of clubs',
    type: [ClubDto],
  })
  @Type(() => ClubDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsNotEmpty()
  clubs: ClubDto[];
} 