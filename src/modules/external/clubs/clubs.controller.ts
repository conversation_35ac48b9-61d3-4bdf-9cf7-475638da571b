import { Controller, Get, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ClubsService } from './clubs.service';
import { ClubRequestDto } from './dto/club-request.dto';
import { ClubResponseDto } from './dto/club-response.dto';
import { UpdateClubResponse } from './dto/update-club-response.dto';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';

@ApiTags('Clubs')
@ApiBearerAuth()
@Controller({
  path: 'external/clubs',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class ClubsController {
  constructor(private readonly clubsService: ClubsService) {}

  @Get()
  @ApiOperation({ summary: 'Get clubs information (Only for SPORTS customers)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Clubs information retrieved successfully',
    type: [ClubResponseDto]
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing token' 
  })
  async getClubs(): Promise<ClubResponseDto[]> {
    return this.clubsService.getClubs();
  }

  @Post()
  @ApiOperation({ summary: 'Create or update club information (Only for SPORTS customers)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Club information updated successfully',
    type: UpdateClubResponse
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing token' 
  })
  async updateClub(@Body() body: ClubRequestDto): Promise<UpdateClubResponse> {
    return this.clubsService.updateClub(body);
  }
} 