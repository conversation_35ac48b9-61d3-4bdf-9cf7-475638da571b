import { Module } from '@nestjs/common';
import { ClubsController } from './clubs.controller';
import { ClubsService } from './clubs.service';
import { TypeOrmModule } from "@nestjs/typeorm";
import { ProjectsEntity } from "@entities";
import { ExternalAuthModule } from "@/modules/external/auth/external-auth.module";
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.clubs'),
    TypeOrmModule.forFeature([
      ProjectsEntity,
    ]),
    ExternalAuthModule,
  ],
  controllers: [ClubsController],
  providers: [ClubsService],
  exports: [ClubsService],
})
export class ExternalClubsModule {}