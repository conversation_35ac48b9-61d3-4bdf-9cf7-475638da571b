import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { ProjectsEntity } from '@entities';
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClubRequestDto } from './dto/club-request.dto';
import { ClubResponseDto } from './dto/club-response.dto';
import { UpdateClubResponse } from './dto/update-club-response.dto';
import { ModuleLogger } from "@helpers/logger/module-logger/module-logger.service";

@Injectable()
export class ClubsService {
  constructor(
    private logger: ModuleLogger,

    private cls: ClsService,
    @InjectRepository(ProjectsEntity)
    private projectsRepo: Repository<ProjectsEntity>,
  ) {}

  async getClubs(): Promise<ClubResponseDto[]> {
     const { company } = this.cls.get<AuthorizedData>(ClsProperty.user)

    const clubs = await this.projectsRepo
      .createQueryBuilder()
      .where('sec_company_id = :sec_company_id', { sec_company_id: company.id })
      .orderBy('project_name')
      .getMany();

    return clubs.map((club) => ({
      name: club.project_name,
      external_name: club.project_name_extern,
      club_number: club.project_nr,
      city: club.project_city,
      country: club.project_country,
      province: club.project_province,
    }));
  }

  async updateClub(clubData: ClubRequestDto): Promise<UpdateClubResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const results: UpdateClubResponse = { new: 0, updated: 0 };

    try {
      for (const club of clubData.clubs) {
        this.logger.debug(`updateClub for club ${club.club_nr}`, { club });

        const existingClub = await this.findClubByNumberAndCompany(
          club.club_nr,
          company.id
        );

        if (existingClub) {
          await this.updateExistingClub(club, company.id);
          results.updated++;
        } else {
          await this.createNewClub(club, company.id);
          results.new++;
        }
      }

      return results;
    } catch (error) {
      throw new HttpException(
        {
          message: 'Failed to update clubs',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  private async findClubByNumberAndCompany(
    clubNumber: number,
    companyId: number
  ): Promise<ProjectsEntity | null> {
    return this.projectsRepo
      .createQueryBuilder()
      .where('project_nr = :club_nr', { club_nr: clubNumber })
      .andWhere('sec_company_id = :company_id', { company_id: companyId })
      .getOne();
  }

  private async updateExistingClub(
    club: ClubRequestDto['clubs'][0],
    companyId: number
  ): Promise<void> {
    this.logger.debug(`updateExistingClub for club ${club.club_nr}`, { club });

    await this.projectsRepo
      .createQueryBuilder()
      .update()
      .set({
        project_name: club.club_nm,
        project_name_extern: club.club_nm,
        modification_date: new Date(),
      })
      .where('project_nr = :club_nr', { club_nr: club.club_nr })
      .andWhere('sec_company_id = :company_id', { company_id: companyId })
      .execute();
  }

  private async createNewClub(
    club: ClubRequestDto['clubs'][0],
    companyId: number
  ): Promise<void> {
    this.logger.debug(`createNewClub for club ${club.club_nr}`, { club });

    const newClub = this.projectsRepo.create({
      project_nr: club.club_nr.toString(),
      project_name: club.club_nm,
      project_city: club.city,
      project_country: club.country || null,
      project_province: club.province || null,
      sec_company_id: companyId,
      creation_date: new Date(),
      modification_date: new Date(),
    });

    await this.projectsRepo.save(newClub);
  }
} 