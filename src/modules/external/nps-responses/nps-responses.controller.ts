import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NpsResponsesService } from './nps-responses.service';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { NpsResponsesRequestDto } from './dto/nps-responses-request.dto';
import { NpsResponsesResponseDto } from './dto/nps-responses-response.dto';

@ApiTags('NPS Responses')
@ApiBearerAuth()
@Controller({
  path: 'external/nps_responses',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class NpsResponsesController {
  constructor(private readonly npsResponsesService: NpsResponsesService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Get NPS (Net Promoter Score) responses data for the specified date range and filters' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'NPS responses data retrieved successfully',
    type: NpsResponsesResponseDto
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Bad request - invalid parameters' 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - invalid or missing authentication' 
  })
  async getNpsResponses(@Body() data: NpsResponsesRequestDto): Promise<NpsResponsesResponseDto> {
    return this.npsResponsesService.getNpsResponses(data);
  }
}

