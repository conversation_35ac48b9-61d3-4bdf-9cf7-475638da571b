import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NpsResponsesController } from './nps-responses.controller';
import { NpsResponsesService } from './nps-responses.service';
import { 
  SurveyAnswerEntity, 
  SurveyAnswerItemEntity,
  EmailCustomerEntity, 
  ProjectsEntity, 
  SurvSurveyEntity 
} from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.nps-responses'),
    TypeOrmModule.forFeature([
      SurveyAnswerEntity, 
      SurveyAnswerItemEntity,
      EmailCustomerEntity, 
      ProjectsEntity, 
      SurvSurveyEntity
    ]),
    ExternalAuthModule,
  ],
  controllers: [NpsResponsesController],
  providers: [NpsResponsesService],
  exports: [NpsResponsesService],
})
export class ExternalNpsResponsesModule {}
