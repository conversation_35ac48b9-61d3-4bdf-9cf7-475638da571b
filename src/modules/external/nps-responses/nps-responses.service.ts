import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';
import { ClsProperty } from '@/config/contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { 
  SurveyAnswerEntity, 
  SurveyAnswerItemEntity,
  EmailCustomerEntity, 
  ProjectsEntity, 
  SurvSurveyEntity 
} from '@entities';
import { numberFormat } from '@/shared/utils/string-utils';
import { NpsResponsesRequestDto } from './dto/nps-responses-request.dto';
import { NpsResponsesResponseDto } from './dto/nps-responses-response.dto';

@Injectable()
export class NpsResponsesService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private dataSource: DataSource,
    @InjectRepository(SurveyAnswerEntity)
    private surveyAnswerRepo: Repository<SurveyAnswerEntity>,
    @InjectRepository(EmailCustomerEntity)
    private emailCustomerRepo: Repository<EmailCustomerEntity>,
    @InjectRepository(ProjectsEntity)
    private projectsRepo: Repository<ProjectsEntity>,
    @InjectRepository(SurvSurveyEntity)
    private surveyRepo: Repository<SurvSurveyEntity>,
  ) {}

  async getNpsResponses(data: NpsResponsesRequestDto): Promise<NpsResponsesResponseDto> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    
    this.logger.debug('nps-responses.getNpsResponses', { 
      companyId: company.id,
      from: data.from,
      to: data.to,
      project: data.project,
      club: data.club,
      survey: data.survey,
      brand: data.brand
    });

    // Set default 'to' date if not provided
    const toDate = data.to || new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Build the base query
    let baseQuery = `
      with scores AS ( 
        SELECT  a.nps_value as nps, 
          CASE WHEN  a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor, 
          CASE WHEN  a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive, 
          CASE WHEN  a.nps_value > 8 THEN 1 ELSE 0 END             AS promoter, 
          CASE WHEN  	pos.return_value is not null THEN 1 WHEN  neg.return_value is not null THEN 1 ELSE 0 END AS feedback 
          FROM surv_surveyanswer a  
          INNER JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id 
          LEFT JOIN surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND s.clf_question1 = neg.surv_surveyquestion_id 
          LEFT JOIN surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND s.clf_question2 = pos.surv_surveyquestion_id
    `;

    // Add conditional joins based on parameters
    if (data.project || data.club || data.brand) {
      baseQuery += ` INNER JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id`;
    }

    if (data.project || data.club) {
      baseQuery += ` INNER JOIN projects ON ec.xml_internal_external = projects.project_id`;
    }

    // Add WHERE clause
    baseQuery += ` WHERE a.sec_company_id = $1 AND a.creation_date BETWEEN $2 AND $3`;

    // Add conditional criteria
    const params: any[] = [company.id, data.from, toDate];
    let paramIndex = 4;

    if (data.project || data.club) {
      const projectNumber = data.project || data.club;
      baseQuery += ` AND projects.project_nr = $${paramIndex}`;
      params.push(projectNumber);
      paramIndex++;
    }

    if (data.survey) {
      baseQuery += ` AND a.surv_survey_id = $${paramIndex}`;
      params.push(data.survey);
      paramIndex++;
    }

    if (data.brand) {
      baseQuery += ` AND lower(ec.xml_brand) = $${paramIndex}`;
      params.push(data.brand.toLowerCase());
      paramIndex++;
    }

    // Complete the query
    baseQuery += `) SELECT count(nps) total, sum(detractor) neg, sum(passive) pas, sum(promoter) pos, sum(feedback) feedback FROM scores`;

    this.logger.debug('nps-responses.getNpsResponses query', { 
      query: baseQuery,
      params 
    });

    try {
      const result = await this.surveyAnswerRepo.query(baseQuery, params);
      
      if (!result || result.length === 0) {
        return this.createEmptyResponse();
      }

      const ds = result[0];
      
      // Calculate NPS and percentages
      const response: NpsResponsesResponseDto = {
        NPS: Math.round(((ds.pos / ds.total) - (ds.neg / ds.total)) * 100),
        Responses: ds.total,
        FeedbackPercentage: numberFormat((100 / ds.total) * ds.feedback, 2) + '%',
        Promoters: numberFormat((100 / ds.total) * ds.pos, 2) + '%',
        Passives: numberFormat((100 / ds.total) * ds.pas, 2) + '%',
        Detractors: numberFormat((100 / ds.total) * ds.neg, 2) + '%'
      };

      this.logger.debug('nps-responses.getNpsResponses result', { response });

      return response;
    } catch (error) {
      this.logger.error('nps-responses.getNpsResponses error', { error });
      throw error;
    }
  }

  private createEmptyResponse(): NpsResponsesResponseDto {
    return {
      NPS: 0,
      Responses: 0,
      FeedbackPercentage: '0.00%',
      Promoters: '0.00%',
      Passives: '0.00%',
      Detractors: '0.00%'
    };
  }
}
