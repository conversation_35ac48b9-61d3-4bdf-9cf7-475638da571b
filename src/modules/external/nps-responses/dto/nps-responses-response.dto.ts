import { ApiProperty } from '@nestjs/swagger';

export class NpsResponsesResponseDto {
  @ApiProperty({
    description: 'Net Promoter Score (NPS)',
    example: 45,
    type: Number
  })
  NPS: number;

  @ApiProperty({
    description: 'Total number of responses',
    example: 1250,
    type: Number
  })
  Responses: number;

  @ApiProperty({
    description: 'Percentage of responses with feedback',
    example: '67.50%',
    type: String
  })
  FeedbackPercentage: string;

  @ApiProperty({
    description: 'Percentage of promoters (scores 9-10)',
    example: '52.30%',
    type: String
  })
  Promoters: string;

  @ApiProperty({
    description: 'Percentage of passives (scores 7-8)',
    example: '28.40%',
    type: String
  })
  Passives: string;

  @ApiProperty({
    description: 'Percentage of detractors (scores 0-6)',
    example: '19.30%',
    type: String
  })
  Detractors: string;
}

