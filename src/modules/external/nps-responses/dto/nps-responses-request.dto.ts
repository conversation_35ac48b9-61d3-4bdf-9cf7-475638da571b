import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, Matches, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

export class NpsResponsesRequestDto {
  @ApiProperty({
    description: 'Start date in yyyy-MM-dd HH:mm:ss format',
    example: '2024-01-01 00:00:00',
    required: true
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/, {
    message: 'from must be in yyyy-MM-dd HH:mm:ss format'
  })
  from: string;

  @ApiProperty({
    description: 'End date in yyyy-MM-dd HH:mm:ss format (optional, defaults to current date)',
    example: '2024-12-31 23:59:59',
    required: false
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/, {
    message: 'to must be in yyyy-MM-dd HH:mm:ss format'
  })
  to?: string;

  @ApiProperty({
    description: 'Project number (optional)',
    example: 'PRJ001',
    required: false
  })
  @IsOptional()
  @IsString()
  project?: string;

  @ApiProperty({
    description: 'Club number (optional)',
    example: 'CLUB001',
    required: false
  })
  @IsOptional()
  @IsString()
  club?: string;

  @ApiProperty({
    description: 'Survey ID (optional)',
    example: 123,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  survey?: number;

  @ApiProperty({
    description: 'Brand name (optional)',
    example: 'Bosch',
    required: false
  })
  @IsOptional()
  @IsString()
  brand?: string;
}

