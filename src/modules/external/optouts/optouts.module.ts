import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OptoutsController } from './optouts.controller';
import { OptoutsService } from './optouts.service';
import { 
  CustomerBlacklistEntity, 
  CompanyEntity
} from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('external.optouts'),
    TypeOrmModule.forFeature([
      CustomerBlacklistEntity, 
      CompanyEntity
    ]),
    ExternalAuthModule,
  ],
  controllers: [OptoutsController],
  providers: [OptoutsService],
  exports: [OptoutsService],
})
export class ExternalOptoutsModule {} 