import { Test, TestingModule } from '@nestjs/testing';
import { OptoutsController } from './optouts.controller';
import { OptoutsService } from './optouts.service';
import { OptoutsResponse } from './dto/optouts-response.dto';
import { OptoutsStatusResponse } from './dto/optouts-status-response.dto';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';

describe('OptoutsController', () => {
  let controller: OptoutsController;
  let service: OptoutsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OptoutsController],
      providers: [
        {
          provide: OptoutsService,
          useValue: {
            getOptouts: jest.fn(),
            createOptouts: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(ExternalAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<OptoutsController>(OptoutsController);
    service = module.get<OptoutsService>(OptoutsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getOptouts', () => {
    it('should return optouts list', async () => {
      const mockResponse: OptoutsResponse = {
        list: [
          {
            country: 'DE',
            email: '<EMAIL>',
            phone: '+49123456789',
            date: '2024-01-15',
          },
        ],
        next_page: false,
      };

      jest.spyOn(service, 'getOptouts').mockResolvedValue(mockResponse);

      const result = await controller.getOptouts(100, 1, '2024-01-01', '2024-01-31');

      expect(result).toEqual(mockResponse);
      expect(service.getOptouts).toHaveBeenCalledWith(100, 1, '2024-01-01', '2024-01-31');
    });
  });

  describe('createOptouts', () => {
    it('should process optouts and return status', async () => {
      const mockResponse: OptoutsStatusResponse = {
        count: 1,
        added: 1,
        removed: 0,
        failed: 0,
        updated: 0,
      };

      jest.spyOn(service, 'createOptouts').mockResolvedValue(mockResponse);

      const payload = {
        items: [
          {
            country: 'DE',
            is_blacklisted: true,
            email: '<EMAIL>',
            phone: '+49123456789',
            brand: 'a01',
          },
        ],
      };

      const result = await controller.createOptouts(payload);

      expect(result).toEqual(mockResponse);
      expect(service.createOptouts).toHaveBeenCalledWith(payload);
    });
  });
}); 