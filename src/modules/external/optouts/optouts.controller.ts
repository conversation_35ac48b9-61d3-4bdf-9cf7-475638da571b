import { Controller, Get, Post, Body, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { OptoutsService } from './optouts.service';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { OptoutsResponse } from './dto/optouts-response.dto';
import { OptoutsRequest } from './dto/optouts-request.dto';
import { OptoutsStatusResponse } from './dto/optouts-status-response.dto';

@ApiTags('Optouts')
@ApiBearerAuth()
@Controller({
  path: 'external/optouts',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class OptoutsController {
  constructor(private readonly optoutsService: OptoutsService) {}

  @Get()
  @ApiOperation({ summary: 'Get optouts list' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of records to return (default: 100)' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'from', required: true, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'to', required: false, type: String, description: 'End date (YYYY-MM-DD, default: current day)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Optouts retrieved successfully',
    type: OptoutsResponse
  })
  async getOptouts(
    @Query('limit') limit?: number,
    @Query('page') page?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ): Promise<OptoutsResponse> {
    return this.optoutsService.getOptouts(limit, page, from, to);
  }

  @Post()
  @ApiOperation({ summary: 'Create or update optouts' })
  @ApiResponse({ 
    status: 200, 
    description: 'Optouts processed successfully',
    type: OptoutsStatusResponse
  })
  async createOptouts(@Body() payload: OptoutsRequest): Promise<OptoutsStatusResponse> {
    return this.optoutsService.createOptouts(payload);
  }
} 