# External Optouts API

This module provides external API endpoints for managing customer optouts/blacklist data.

## Endpoints

### GET /external/optouts

Retrieves a list of optouts based on the specified criteria.

#### Query Parameters

- `limit` (optional): Number of records to return (default: 100)
- `page` (optional): Page number (default: 1)
- `from` (required): Start date in YYYY-MM-DD format (forced to start of day)
- `to` (optional): End date in YYYY-MM-DD format (default: current day, forced to end of day)

#### Response

```json
{
  "list": [
    {
      "country": "DE",
      "email": "<EMAIL>",
      "phone": "+***********",
      "date": "2024-01-15"
    }
  ],
  "next_page": 2
}
```

- `list`: Array of optout records
- `next_page`: Next page number or `false` if no more pages

### POST /external/optouts

Creates or updates optout records based on the provided payload.

#### Request Body

```json
{
  "items": [
    {
      "country": "DE",
      "is_blacklisted": true,
      "email": "<EMAIL>",
      "phone": "+***********",
      "brand": "a01"
    }
  ]
}
```

#### Response

```json
{
  "count": 100,
  "added": 25,
  "removed": 10,
  "failed": 5,
  "updated": 15
}
```

## Business Logic

### GET Method

- Uses SQL query to join `customer_blacklist` and `sec_company` tables
- Filters out encrypted email records (`[ENCRYPTED]`)
- Applies date range filtering with start/end of day adjustments
- For non-BSHGlobal companies, includes `sec_company_id` filter
- Supports pagination with limit and offset
- Returns country code extracted from company_code

### POST Method

- Processes each item in the payload array
- Skips items without country information
- For BSHGlobal companies with BSH type, loads country mapping from database
- Checks for existing records using MD5 hashes of email and phone
- Handles duplicate records (deletes one if 2 found)
- Maps brand codes to brand names
- Tracks processing statistics (added, removed, updated, failed)

### Brand Code Mapping

- `a01` → `bosch`
- `a02` → `siemens`
- `a03` → `constructa`
- `a04` → `neff`
- `a15` → `gaggenau`
- `a16` → `solitaire`
- `p02` → `inspiratiehuis`
- Default → `bosch`

## Authentication

All endpoints require Bearer token authentication using the ExternalAuthGuard.

## Error Handling

- Returns 400 Bad Request for missing required parameters
- Logs processing errors for individual items
- Continues processing remaining items even if some fail 