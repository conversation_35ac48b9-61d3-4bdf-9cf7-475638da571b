import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import * as moment from 'moment';
import * as md5 from 'md5';

import { 
  CustomerBlacklistEntity, 
  CompanyEntity
} from '@entities';
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { OptoutsResponse, OptoutDto } from './dto/optouts-response.dto';
import { OptoutsRequest, OptoutItemDto } from './dto/optouts-request.dto';
import { OptoutsStatusResponse } from './dto/optouts-status-response.dto';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';
import { CompanyType } from '@/shared/enums';

@Injectable()
export class OptoutsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(CustomerBlacklistEntity)
    private readonly blacklistRepo: Repository<CustomerBlacklistEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
  ) {}

  async getOptouts(
    limit?: number,
    page?: number,
    from?: string,
    to?: string,
  ): Promise<OptoutsResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    
    this.logger.debug('optouts.getOptouts', { 
      companyId: company.id, 
      limit, 
      page, 
      from, 
      to 
    });

    // Set defaults
    const limitValue = limit || 100;
    const pageValue = page || 1;
    const offset = (pageValue - 1) * limitValue;

    // Validate and parse dates
    if (!from) {
      throw new BadRequestException('from parameter is required');
    }

    const fromDate = moment(from).startOf('day').toDate();
    const toDate = to ? moment(to).endOf('day').toDate() : moment().endOf('day').toDate();

    // Build query parameters
    const queryParams: any[] = [fromDate, toDate, limitValue, offset];
    let query = `
      SELECT right(company_code,2) as country, customer_email, phone, date_blacklisted 
      FROM customer_blacklist b 
      INNER JOIN sec_company c ON b.sec_company_id = c.sec_company_id 
      WHERE customer_email != '[ENCRYPTED]' 
      AND date_blacklisted BETWEEN ? AND ?
    `;

    // Add company filter if not BSHGlobal
    if (company.id !== 25) {
      query += ' AND b.sec_company_id = ?';
      queryParams.splice(2, 0, company.id);
    }

    query += ' LIMIT ? OFFSET ?';

    // Execute query
    const results = await this.blacklistRepo.query(query, queryParams);

    // Check if there are more records
    let countQuery = `
      SELECT COUNT(*) as total
      FROM customer_blacklist b 
      INNER JOIN sec_company c ON b.sec_company_id = c.sec_company_id 
      WHERE customer_email != '[ENCRYPTED]' 
      AND date_blacklisted BETWEEN ? AND ?
    `;

    const countParams: any[] = [fromDate, toDate];
    if (company.id !== 25) {
      countQuery += ' AND b.sec_company_id = ?';
      countParams.push(company.id);
    }

    const totalResult = await this.blacklistRepo.query(countQuery, countParams);
    const total = totalResult[0]?.total || 0;

    // Build response
    const list: OptoutDto[] = results.map((row: any) => ({
      country: row.country,
      email: row.customer_email,
      phone: row.phone,
      date: this.dateFormat(row.date_blacklisted, 'yyyy-MM-dd'),
    }));

    // Determine next_page
    const nextPage = (pageValue * limitValue) < total ? pageValue + 1 : false;

    this.logger.debug('optouts.getOptouts result', { 
      count: list.length, 
      total, 
      nextPage 
    });

    return {
      list,
      next_page: nextPage,
    };
  }

  async createOptouts(payload: OptoutsRequest): Promise<OptoutsStatusResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    
    this.logger.debug('optouts.createOptouts', { 
      companyId: company.id, 
      itemCount: payload.items.length 
    });

    // Get company type
    const companyData = await this.companyRepo.findOne({
      where: { sec_company_id: company.id },
      select: ['company_type']
    });

    if (!companyData) {
      throw new BadRequestException('Company not found');
    }

    // Load countries list if BSHGlobal
    let countriesMap: Map<string, number> | null = null;
    if (companyData.company_type === CompanyType.BSH && company.id === 25) {
      const countries = await this.companyRepo.query(`
        SELECT sec_company_id, upper(substr(company_code,4)) AS country 
        FROM sec_company
      `);
      
      countriesMap = new Map();
      countries.forEach((country: any) => {
        countriesMap!.set(country.country, country.sec_company_id);
      });
    }

    const status: OptoutsStatusResponse = {
      count: 0,
      added: 0,
      removed: 0,
      failed: 0,
      updated: 0,
    };

    for (const item of payload.items) {
      try {
        status.count++;

        // Skip if no country provided
        if (!item.country) {
          continue;
        }

        // Get sec_company_id for the country
        let secCompanyId = company.id;
        if (companyData.company_type === CompanyType.BSH && company.id === 25 && countriesMap) {
          const countryId = countriesMap.get(item.country.toUpperCase());
          if (!countryId) {
            status.failed++;
            continue;
          }
          secCompanyId = countryId;
        }

        // Check if record already exists
        const existingRecords = await this.findExistingRecords(item, secCompanyId);

        if (item.is_blacklisted) {
          if (existingRecords.length === 0) {
            // Create new record
            await this.createBlacklistRecord(item, secCompanyId);
            status.added++;
          } else if (existingRecords.length === 2) {
            // Delete one of the duplicates
            await this.blacklistRepo.delete({ customer_blacklist_id: existingRecords[1].customer_blacklist_id });
            await this.updateBlacklistRecord(existingRecords[0], item);
            status.updated++;
          } else if (existingRecords.length === 1) {
            // Update existing record
            await this.updateBlacklistRecord(existingRecords[0], item);
            status.updated++;
          }
        } else {
          // Remove blacklist records
          if (existingRecords.length > 0) {
            await this.blacklistRepo.remove(existingRecords);
            status.removed++;
          }
        }
      } catch (error) {
        this.logger.error('optouts.createOptouts item processing error', { 
          error: error.message, 
          item 
        });
        status.failed++;
      }
    }

    this.logger.debug('optouts.createOptouts result', status);

    return status;
  }

  private async findExistingRecords(item: OptoutItemDto, secCompanyId: number): Promise<CustomerBlacklistEntity[]> {
    const conditions: any = { sec_company_id: secCompanyId };

    if (item.email) {
      conditions.customer_email_md5 = md5(item.email.toLowerCase());
    }

    if (item.phone) {
      conditions.phone_md5 = md5(item.phone);
    }

    // Use OR condition for email and phone
    const queryBuilder = this.blacklistRepo.createQueryBuilder('b')
      .where('b.sec_company_id = :secCompanyId', { secCompanyId });

    if (item.email && item.phone) {
      queryBuilder.andWhere('(b.customer_email_md5 = :emailMd5 OR b.phone_md5 = :phoneMd5)', {
        emailMd5: md5(item.email.toLowerCase()),
        phoneMd5: md5(item.phone)
      });
    } else if (item.email) {
      queryBuilder.andWhere('b.customer_email_md5 = :emailMd5', {
        emailMd5: md5(item.email.toLowerCase())
      });
    } else if (item.phone) {
      queryBuilder.andWhere('b.phone_md5 = :phoneMd5', {
        phoneMd5: md5(item.phone)
      });
    }

    return queryBuilder.getMany();
  }

  private async createBlacklistRecord(item: OptoutItemDto, secCompanyId: number): Promise<void> {
    const record = new CustomerBlacklistEntity();
    record.sec_company_id = secCompanyId;
    record.date_blacklisted = new Date();

    if (item.email) {
      record.customer_email = item.email.toLowerCase();
      record.customer_email_md5 = md5(record.customer_email);
    }

    if (item.phone) {
      record.phone = item.phone;
      record.phone_md5 = md5(record.phone);
    }

    if (item.brand) {
      record.brand = this.mapBrandCode(item.brand);
    }

    await this.blacklistRepo.save(record);
  }

  private async updateBlacklistRecord(existingRecord: CustomerBlacklistEntity, item: OptoutItemDto): Promise<void> {
    if (item.email) {
      existingRecord.customer_email = item.email.toLowerCase();
      existingRecord.customer_email_md5 = md5(existingRecord.customer_email);
    }

    if (item.phone) {
      existingRecord.phone = item.phone;
      existingRecord.phone_md5 = md5(existingRecord.phone);
    }

    if (item.brand) {
      existingRecord.brand = this.mapBrandCode(item.brand);
    }

    await this.blacklistRepo.save(existingRecord);
  }

  private mapBrandCode(brandCode: string): string {
    switch (brandCode) {
      case 'a01':
        return 'bosch';
      case 'a02':
        return 'siemens';
      case 'a03':
        return 'constructa';
      case 'a04':
        return 'neff';
      case 'a15':
        return 'gaggenau';
      case 'a16':
        return 'solitaire';
      case 'p02':
        return 'inspiratiehuis';
      default:
        return 'bosch';
    }
  }

  private dateFormat(date: Date, format: string): string {
    return moment(date).format('YYYY-MM-DD');
  }
} 