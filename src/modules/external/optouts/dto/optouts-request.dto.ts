import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString, IsBoolean, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class OptoutItemDto {
  @ApiProperty({
    description: 'Country code',
    example: 'DE',
    required: false
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({
    description: 'Whether the customer is blacklisted',
    example: true
  })
  @IsBoolean()
  is_blacklisted: boolean;

  @ApiProperty({
    description: 'Customer email',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    description: 'Customer phone number',
    example: '+49123456789',
    required: false
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Brand code',
    example: 'a01',
    required: false
  })
  @IsOptional()
  @IsString()
  brand?: string;
}

export class OptoutsRequest {
  @ApiProperty({
    description: 'Array of optout items',
    type: [OptoutItemDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OptoutItemDto)
  items: OptoutItemDto[];
} 