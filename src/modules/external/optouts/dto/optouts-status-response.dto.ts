import { ApiProperty } from '@nestjs/swagger';

export class OptoutsStatusResponse {
  @ApiProperty({
    description: 'Total number of items processed',
    example: 100
  })
  count: number;

  @ApiProperty({
    description: 'Number of items added',
    example: 25
  })
  added: number;

  @ApiProperty({
    description: 'Number of items removed',
    example: 10
  })
  removed: number;

  @ApiProperty({
    description: 'Number of items that failed to process',
    example: 5
  })
  failed: number;

  @ApiProperty({
    description: 'Number of items updated',
    example: 15
  })
  updated: number;
} 