import { ApiProperty } from '@nestjs/swagger';

export class OptoutDto {
  @ApiProperty({
    description: 'Country code',
    example: 'DE'
  })
  country: string;

  @ApiProperty({
    description: 'Customer email',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Customer phone number',
    example: '+49123456789'
  })
  phone: string;

  @ApiProperty({
    description: 'Date when customer was blacklisted',
    example: '2024-01-15'
  })
  date: string;
}

export class OptoutsResponse {
  @ApiProperty({
    description: 'Array of optouts',
    type: [OptoutDto]
  })
  list: OptoutDto[];

  @ApiProperty({
    description: 'Next page number or false if no more pages',
    example: 2,
    oneOf: [
      { type: 'number' },
      { type: 'boolean' }
    ]
  })
  next_page: number | false;
} 