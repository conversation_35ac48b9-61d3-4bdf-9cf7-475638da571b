# Survey URL External API

This API provides functionality identical to the Service API but with additional fields for survey URL management.

## Endpoint

- **POST** `/v2/external/survey-url`

## Authentication

This API requires external authentication using the `ExternalAuthGuard`.

## Request Body

The request body is identical to the Service API with two additional optional fields:

### Example Request

```json
{
  "department": "Customer Service",
  "process_key": "CS001",
  "email": "<EMAIL>",
  "project_number": "PRJ123",
  "club_number": "CLUB456",
  "salutation": "Mr.",
  "ticket_number": "TKT789",
  "date": "2024-01-15",
  "name": "<PERSON>",
  "initials": "JD",
  "phone": "+1234567890",
  "mobile": "+1234567891",
  "address": "123 Main St",
  "address_number": 123,
  "address_addition": "Apt 4B",
  "postal_code": "12345",
  "city": "New York",
  "reference": "REF001",
}
```

## Response

The response is identical to the Service API:

```json
{
  "new": 1,
  "links": [
    {
      "mail": "",
      "link": "https://survey.example.com/1/abc123-def456"
    }
  ]
}
```

## Key Differences from Service API

1. **Additional Fields**: The `send_date` and `sent_medium` fields are automatically populated in the email_customer record
2. **Default Values**: If `sent_medium` is not provided, it defaults to EMAIL (1)
3. **Date Handling**: The `send_date` field accepts ISO date strings and is stored as a timestamp

## Business Logic

The API follows the same business logic as the Service API:

1. Validates department and process key
2. Checks threshold blocking rules
3. Creates or updates project records
4. Handles language and salutation mapping
5. Creates email_customer records with the additional fields
6. Generates survey links if requested

## Error Handling

The API returns appropriate error responses for:
- Invalid department or process key
- Missing required fields
- Authentication failures
- Database errors
