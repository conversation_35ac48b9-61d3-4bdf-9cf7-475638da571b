import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyUrlController } from './survey-url.controller';
import { SurveyUrlService } from './survey-url.service';
import { 
  EmailCustomerEntity, 
  ProjectsEntity,
  SurvSurveyEntity,
  EmailTemplateEntity,
  SecProgramSettingsEntity,
  SurveyDepartment,
  LanguagesEntity,
  SalutationEntity,
  SalutationItemEntity,
  SubcontractorsEntity,
  EmployeesEntity,
  CompanyEntity,
} from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';
import { GlobalUtilsService } from '@/shared/utils/global-utils.service';

@Module({
  imports: [
    ModuleLoggerModule.register('external.survey-url'),
    TypeOrmModule.forFeature([
      EmailCustomerEntity, 
      ProjectsEntity,
      SurvSurveyEntity,
      EmailTemplateEntity,
      SecProgramSettingsEntity,
      SurveyDepartment,
      LanguagesEntity,
      SalutationEntity,
      SalutationItemEntity,
      SubcontractorsEntity,
      EmployeesEntity,
      CompanyEntity,
    ]),
    ExternalAuthModule,
  ],
  controllers: [SurveyUrlController],
  providers: [SurveyUrlService, GlobalUtilsService],
  exports: [SurveyUrlService],
})
export class ExternalSurveyUrlModule {}
