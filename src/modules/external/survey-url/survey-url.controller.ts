import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SurveyUrlService } from './survey-url.service';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { SurveyUrlRequestDto } from './dto/survey-url-request.dto';
import { SurveyUrlResponseDto } from './dto/survey-url-response.dto';

@ApiTags('Survey URL')
@ApiBearerAuth()
@Controller({
  path: 'external/survey-url',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class SurveyUrlController {
  constructor(private readonly surveyUrlService: SurveyUrlService) {}

  @Post()
  @ApiOperation({ summary: 'Create survey URL record' })
  @ApiResponse({ 
    status: 200, 
    description: 'Survey URL record created successfully',
    type: SurveyUrlResponseDto
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Bad request - validation error or business logic error'
  })
  async createSurveyUrl(@Body() data: SurveyUrlRequestDto): Promise<SurveyUrlResponseDto> {
    return this.surveyUrlService.createSurveyUrl(data);
  }
}
