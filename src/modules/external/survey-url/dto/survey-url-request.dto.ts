import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsNumber } from 'class-validator';

export class SurveyUrlRequestDto {
  @ApiProperty({ description: 'Customer email address' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'Department identifier' })
  @IsString()
  @IsNotEmpty()
  department: string;

  @ApiProperty({ description: 'Process key' })
  @IsString()
  @IsNotEmpty()
  process_key: string;

  @ApiProperty({ description: 'Project number', required: false })
  @IsOptional()
  @IsString()
  project_number?: string;

  @ApiProperty({ description: 'Club number', required: false })
  @IsOptional()
  @IsString()
  club_number?: string;

  @ApiProperty({ description: 'Language code', required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ description: 'Salutation', required: false })
  @IsOptional()
  @IsString()
  salutation?: string;

  @ApiProperty({ description: 'Subcontractor external ID', required: false })
  @IsOptional()
  @IsString()
  subcontractor?: string;

  @ApiProperty({ description: 'Employee external ID', required: false })
  @IsOptional()
  @IsString()
  employee?: string;

  @ApiProperty({ description: 'Return link flag', required: false })
  @IsOptional()
  return_link?: boolean;

  @ApiProperty({ description: 'Ticket number', required: false })
  @IsOptional()
  @IsString()
  ticket_number?: string;

  @ApiProperty({ description: 'Date in yyyy-MM-dd or yyyy-MM-dd HH:mm format', required: false })
  @IsOptional()
  @IsString()
  date?: string;

  @ApiProperty({ description: 'Customer name', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Customer initials', required: false })
  @IsOptional()
  @IsString()
  initials?: string;

  @ApiProperty({ description: 'Phone number', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ description: 'Mobile number', required: false })
  @IsOptional()
  @IsString()
  mobile?: string;

  @ApiProperty({ description: 'Address', required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ description: 'Address number', required: false })
  @IsOptional()
  @IsNumber()
  address_number?: number;

  @ApiProperty({ description: 'Address addition', required: false })
  @IsOptional()
  @IsString()
  address_addition?: string;

  @ApiProperty({ description: 'Postal code', required: false })
  @IsOptional()
  @IsString()
  postal_code?: string;

  @ApiProperty({ description: 'City', required: false })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({ description: 'Reference number', required: false })
  @IsOptional()
  @IsString()
  reference?: string;

  @ApiProperty({ description: 'Description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Region', required: false })
  @IsOptional()
  @IsString()
  region?: string;

  @ApiProperty({ description: 'Company name', required: false })
  @IsOptional()
  @IsString()
  company?: string;
}

