import { Controller, Get, Post, Query, Body, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { LabelsService } from './labels.service';
import { LabelsQueryDto, LabelType } from './dto/labels-query.dto';
import { LabelsResponseDto } from './dto/labels-response.dto';
import { LabelsPostDto, LabelsPostResponseDto } from './dto/labels-post.dto';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';

@ApiTags('Labels')
@ApiBearerAuth()
@Controller({
  path: 'external/labels',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class LabelsController {
  constructor(private readonly labelsService: LabelsService) {}

  @Get()
  @ApiOperation({ summary: 'Get labels data' })
  @ApiResponse({
    status: 200,
    description: 'Labels data retrieved successfully',
    type: LabelsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiQuery({
    name: 'from',
    required: true,
    description: 'Start date for filtering labels (YYYY-MM-DD)',
    type: String,
  })
  @ApiQuery({
    name: 'to',
    required: false,
    description:
      'End date for filtering labels (YYYY-MM-DD), defaults to current date',
    type: String,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of records to return, defaults to 5000',
    type: Number,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'companycode',
    required: false,
    description: 'company code to filter by',
    enum: LabelType,
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Type of labels to return',
    enum: LabelType,
  })
  async getLabels(@Query() query: LabelsQueryDto): Promise<LabelsResponseDto> {
    return this.labelsService.getLabels(query);
  }

  @Post()
  @ApiOperation({ summary: 'Create or update automatic labels' })
  @ApiResponse({
    status: 200,
    description: 'Labels created or updated successfully',
    type: LabelsPostResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid payload',
  })
  async createLabels(
    @Body() payload: LabelsPostDto,
  ): Promise<LabelsPostResponseDto> {
    return this.labelsService.createLabels(payload);
  }
}
