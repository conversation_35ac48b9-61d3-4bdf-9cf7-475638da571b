import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsInt,
  IsString,
  IsOptional,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum LabelType {
  MANUAL = 'manual',
  AI = 'ai',
  TRAINING = 'training',
}

export class LabelsQueryDto {
  @ApiProperty({
    description: 'Start date for filtering labels (required)',
    example: '2023-01-01',
  })
  @IsDateString()
  from: string;

  @ApiProperty({
    description: 'End date for filtering labels (defaults to current date)',
    example: '2023-12-31',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  to?: string;

  @ApiProperty({
    description: 'Maximum number of records to return (defaults to 5000)',
    example: 1000,
    required: false,
    default: 5000,
  })
  @IsInt()
  @Min(1)
  @IsOptional()
  @Type(() => Number)
  limit?: number;

  @ApiProperty({
    description: 'Page number for pagination',
    example: 0,
    required: false,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  page?: number;

  @ApiProperty({
    description: 'Company code to filter by',
    example: 'manual',
    required: false,
  })
  @IsString()
  @IsOptional()
  companycode?: string;

  @ApiProperty({
    description: 'Type of labels to return',
    enum: LabelType,
    example: 'manual',
    required: false,
  })
  @IsEnum(LabelType)
  @IsOptional()
  type?: LabelType;
}
