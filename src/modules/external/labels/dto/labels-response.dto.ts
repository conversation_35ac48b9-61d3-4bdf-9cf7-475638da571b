import { ApiProperty } from '@nestjs/swagger';

export class LabelDto {
  @ApiProperty({
    description: 'Response ID',
    example: 12345,
  })
  response_id: number;

  @ApiProperty({
    description: 'Attribute ID',
    example: 'attribute_123',
  })
  attribute_id: string;

  @ApiProperty({
    description: 'Label value',
    example: 'positive',
    enum: ['positive', 'negative', 'neutral'],
  })
  value: string;

  @ApiProperty({
    description: 'Whether the label is deleted',
    example: false,
  })
  is_deleted: boolean;

  @ApiProperty({
    description: 'Type of label',
    example: 'manual',
    enum: ['manual', 'ai', 'training'],
  })
  type: string;
}

export class ResponseWithLabelsDto {
  @ApiProperty({
    description: 'Response ID',
    example: 12345,
  })
  response_id: number;

  @ApiProperty({
    description: 'Array of labels for this response',
    type: [LabelDto],
  })
  manual_labels: LabelDto[];
}

export class LabelsResponseDto {
  @ApiProperty({
    description: 'Array of responses with their labels',
    type: [ResponseWithLabelsDto],
  })
  manual_responses: ResponseWithLabelsDto[];

  @ApiProperty({
    description: 'Next page number for pagination, or false if no more pages',
    example: 2,
    oneOf: [{ type: 'number' }, { type: 'boolean' }],
  })
  next_page: number | false;
}
