import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

export enum LabelValue {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
  NEUTRAL = 'neutral',
}

export enum LabelType {
  AI = 'AI',
}

export class AutomaticLabelDto {
  @ApiProperty({
    description: 'Type of label',
    example: 'AI',
    enum: LabelType,
  })
  @IsEnum(LabelType)
  type: LabelType;

  @ApiProperty({
    description: 'Label value',
    example: 'positive',
    enum: LabelValue,
  })
  @IsEnum(LabelValue)
  value: string;

  @ApiProperty({
    description: 'Attribute ID',
    example: 'ffb3bc03-8d53-4ee5-9cc3-f5dd0834dd1e',
  })
  @IsUUID()
  attribute_id: string;

  @ApiProperty({
    description: 'Confidence score',
    example: 0,
  })
  @IsNumber()
  confidence: number;
}

export class AutomaticResponseDto {
  @ApiProperty({
    description: 'Response ID',
    example: 7316760,
  })
  @IsNumber()
  response_id: number;

  @ApiProperty({
    description: 'Array of automatic labels',
    type: [AutomaticLabelDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AutomaticLabelDto)
  automatic_labels: AutomaticLabelDto[];
}

export class LabelsPostDto {
  @ApiProperty({
    description: 'Array of automatic responses with their labels',
    type: [AutomaticResponseDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AutomaticResponseDto)
  automatic_responses: AutomaticResponseDto[];
}

export class LabelsPostResponseDto {
  @ApiProperty({
    description: 'Number of new or updated labels',
    example: 5,
  })
  new_or_updated: number;

  @ApiProperty({
    description: 'Number of labels not updated',
    example: 2,
  })
  not_updated: number;

  @ApiProperty({
    description: 'Number of failed labels',
    example: 0,
  })
  failed: number;

  @ApiProperty({
    description: 'Number of labels with no answer ID',
    example: 0,
  })
  no_answer_id: number;

  @ApiProperty({
    description: 'Number of labels with non-existing response ID',
    example: 1,
  })
  non_existing_response_id: number;

  @ApiProperty({
    description: 'Number of labels with unknown sentiment',
    example: 0,
  })
  unknown_sentiment: number;

  @ApiProperty({
    description: 'Number of labels with attribute not found',
    example: 0,
  })
  attribute_not_found: number;

  @ApiProperty({
    description:
      'Number of labels that cannot be updated because they are manually set',
    example: 0,
  })
  manually_not_allowed: number;
}
