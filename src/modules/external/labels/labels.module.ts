import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LabelsController } from './labels.controller';
import { LabelsService } from './labels.service';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';
import { MClassItem, MClassResponse } from '@entities';

@Module({
  imports: [
    ModuleLoggerModule.register('external.labels'),
    TypeOrmModule.forFeature([MClassItem, MClassResponse]),
    ExternalAuthModule,
  ],
  controllers: [LabelsController],
  providers: [LabelsService],
  exports: [LabelsService],
})
export class ExternalLabelsModule {}
