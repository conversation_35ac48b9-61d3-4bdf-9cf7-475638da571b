import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { LabelsQueryDto, LabelType } from './dto/labels-query.dto';
import { LabelsResponseDto } from './dto/labels-response.dto';
import {
  LabelsPostDto,
  LabelsPostResponseDto,
  LabelValue,
} from './dto/labels-post.dto';
import { MclassRecordStatus, MclassIsType } from '@/shared/enums';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents/cls.contents';

@Injectable()
export class LabelsService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly cls: ClsService,
    private readonly logger: ModuleLogger,
  ) {}

  async getLabels(query: LabelsQueryDto): Promise<LabelsResponseDto> {
    const {
      from,
      to: rawTo,
      limit = 5000,
      page = 0,
      type,
      companycode,
    } = query;

    // Set default to date to today if not provided
    const to = rawTo || new Date().toISOString().split('T')[0];

    // Ensure the to date includes the entire day by adding time component
    const toDateWithTime = `${to}T23:59:59.999Z`;

    // Get the company ID from the CLS context (set by the ExternalAuthGuard)
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const companyId = company.id;

    this.logger.debug('Getting labels', {
      from,
      to,
      toDateWithTime,
      limit,
      page,
      type,
      companyId,
    });

    // Build the base query
    let sql = `
      SELECT
        mr.answer_id,
        COALESCE(mi.external_id, mr.item_id::varchar(255)) as external_id,
        mr.record_status,
        mr.istype
      FROM mclass_response mr
      LEFT JOIN mclass_item mi ON mi.mclass_item_id = mr.item_id AND mi.sec_company_id = $1
      WHERE mr.istype IN (1,2,3)
        AND mr.answer_id IN (
          SELECT sa.surv_surveyanswer_id
          FROM surv_surveyanswer sa
          JOIN mclass_response mr2 ON mr2.answer_id = sa.surv_surveyanswer_id
          WHERE mr2.created_on BETWEEN $2 AND $3
    `;
    const params = [companyId, from, toDateWithTime];

    // Add company filter
    const globalCompanyId = 25; // Based on the Servoy code (globals.sec_company_BSHGlobal)
    if (companyId === globalCompanyId) {
      if (companycode) {
        sql += ` AND sa.sec_company_id = (SELECT sc.sec_company_id FROM sec_company sc WHERE sc.company_name = $4)`;
        params.push(companycode);
      }
    } else {
      sql += ` AND sa.sec_company_id = $1`;
    }

    sql += `)`;

    // Filter by type if provided
    if (type) {
      if (type === LabelType.MANUAL) {
        sql += ` AND mr.record_status IN (${MclassRecordStatus.ManualLabel}, ${MclassRecordStatus.ManualRemoved}, ${MclassRecordStatus.TrainingManualRemoved})`;
      } else if (type === LabelType.AI) {
        sql += ` AND mr.record_status = ${MclassRecordStatus.AILabel}`;
      } else if (type === LabelType.TRAINING) {
        sql += ` AND mr.record_status = ${MclassRecordStatus.AITrainingLabel}`;
      }
    }
    // Exclude excluded items
    sql += `
      AND mr.answer_id NOT IN (
        SELECT sa.surv_surveyanswer_id
        FROM surv_surveyanswer sa
        JOIN mclass_response mr3 ON mr3.answer_id = sa.surv_surveyanswer_id
        WHERE mr3.created_on BETWEEN $2 AND $3
        AND mr3.is_excluded = 1
    `;

    // Add company filter for exclusion
    if (companyId === globalCompanyId) {
      if (companycode) {
        sql += ` AND sa.sec_company_id = (SELECT sc.sec_company_id FROM sec_company sc WHERE sc.company_name = $4)`;
      }
    } else {
      sql += ` AND sa.sec_company_id = $1`;
    }

    sql += `)`;

    // Add ordering and pagination
    sql += ` ORDER BY external_id`;
    sql += ` LIMIT ${limit + 1}`; // Add one to check if there's a next page

    if (page > 0) {
      sql += ` OFFSET ${limit * page}`;
    }

    this.logger.debug('Executing SQL query', {
      sql,
      params,
      fromDate: from,
      toDate: to,
      toDateWithTime,
    });

    // Execute the query
    const results = await this.dataSource.query(sql, params);
    const count = results.length;

    this.logger.debug(`Retrieved ${count} labels`);

    // Process the results
    const labels = [];
    const answers = [];
    let answerId = null;

    // Process all records except the extra one we added to check for next page
    const processCount = Math.min(count, limit);
    for (let i = 0; i < processCount; i++) {
      const ds = results[i];

      // New grouping
      if (answerId !== ds.answer_id) {
        if (answerId) {
          answers.push({
            response_id: answerId,
            manual_labels: labels.slice(),
          }); // Create a copy of labels
        }
        // Prepare new object
        labels.length = 0; // Clear the array
        answerId = ds.answer_id;
      }

      // Determine the value based on istype
      let value = '';
      switch (ds.istype) {
        case MclassIsType.POSITIVE:
          value = 'positive';
          break;
        case MclassIsType.NEGATIVE:
          value = 'negative';
          break;
        case MclassIsType.NEUTRAL:
          value = 'neutral';
          break;
        default:
          break;
      }

      // Determine the type based on record_status
      let labelType = '';
      switch (ds.record_status) {
        case MclassRecordStatus.ManualRemoved:
        case MclassRecordStatus.AILabel:
          labelType = 'ai';
          break;
        case MclassRecordStatus.TrainingManualRemoved:
        case MclassRecordStatus.AITrainingLabel:
          labelType = 'training';
          break;
        case MclassRecordStatus.ManualLabel:
          labelType = 'manual';
          break;
      }

      labels.push({
        response_id: ds.answer_id,
        attribute_id: ds.external_id,
        value: value,
        is_deleted:
          ds.record_status === MclassRecordStatus.ManualRemoved ||
          ds.record_status === MclassRecordStatus.TrainingManualRemoved,
        type: labelType,
      });
    }

    // Push the last answer if there is one
    if (answerId) {
      answers.push({ response_id: answerId, manual_labels: labels });
    }

    // Determine if there's a next page
    const hasNextPage = count > limit;

    // Return the response
    return {
      manual_responses: answers,
      next_page: hasNextPage ? page + 1 : false,
    };
  }

  async createLabels(payload: LabelsPostDto): Promise<LabelsPostResponseDto> {
    const status: LabelsPostResponseDto = {
      new_or_updated: 0,
      not_updated: 0,
      failed: 0,
      no_answer_id: 0,
      non_existing_response_id: 0,
      unknown_sentiment: 0,
      attribute_not_found: 0,
      manually_not_allowed: 0,
    };

    const { automatic_responses } = payload;

    for (const response of automatic_responses) {
      const { response_id, automatic_labels } = response;

      if (!response_id) {
        status.no_answer_id++;
        continue;
      }

      // Check if response_id exists in surv_surveyanswer table
      const surveyAnswerExists =
        await this.checkSurveyAnswerExists(response_id);
      if (!surveyAnswerExists) {
        status.non_existing_response_id++;
        continue;
      }

      const checked: string[] = [];

      for (const label of automatic_labels) {
        const { attribute_id, value, confidence, type } = label;

        try {
          // Check if it's allowed to update the label
          const manualLabelExists = await this.checkManualLabelExists(
            response_id,
            attribute_id,
          );
          if (manualLabelExists) {
            status.manually_not_allowed++;
            continue;
          }

          // Delete old AI labels if not already checked
          if (!checked.includes(attribute_id)) {
            await this.deleteOldAILabels(response_id, attribute_id);
            checked.push(attribute_id);
          }

          // Validate sentiment value
          if (!Object.values(LabelValue).includes(value as LabelValue)) {
            status.unknown_sentiment++;
            continue;
          }

          // Process the label
          await this.processLabel(
            response_id,
            attribute_id,
            value,
            confidence,
            status,
          );
        } catch (error) {
          this.logger.error('Error processing label', {
            error,
            response_id,
            attribute_id,
          });
          status.failed++;
        }
      }
    }

    this.logger.debug('Labels creation completed', status);
    return status;
  }

  private async checkSurveyAnswerExists(responseId: number): Promise<boolean> {
    const sql =
      'SELECT surv_surveyanswer_id FROM surv_surveyanswer WHERE surv_surveyanswer_id = $1';
    const result = await this.dataSource.query(sql, [responseId]);
    return result.length > 0;
  }

  private async checkManualLabelExists(
    responseId: number,
    attributeId: string,
  ): Promise<boolean> {
    const sql = `
      SELECT item_id 
      FROM mclass_response mr 
      LEFT JOIN mclass_item mi ON mr.item_id = mi.mclass_item_id 
      WHERE answer_id = $1 
        AND record_status IN ($2, $3) 
        AND COALESCE(mi.external_id, mr.item_id::varchar(255)) = $4
    `;
    const result = await this.dataSource.query(sql, [
      responseId,
      MclassRecordStatus.ManualLabel,
      MclassRecordStatus.ManualRemoved,
      attributeId,
    ]);
    return result.length > 0;
  }

  private async deleteOldAILabels(
    responseId: number,
    attributeId: string,
  ): Promise<void> {
    const sql = `
      DELETE FROM mclass_response 
      WHERE mclass_response_id IN (
        SELECT mr.mclass_response_id 
        FROM mclass_response mr 
        LEFT JOIN mclass_item mi ON mr.item_id = mi.mclass_item_id 
        WHERE answer_id = $1 
          AND record_status = $2 
          AND COALESCE(mi.external_id, mr.item_id::varchar(255)) = $3
      )
    `;
    await this.dataSource.query(sql, [
      responseId,
      MclassRecordStatus.AILabel,
      attributeId,
    ]);
  }

  private async processLabel(
    responseId: number,
    attributeId: string,
    value: string,
    confidence: number,
    status: LabelsPostResponseDto,
  ): Promise<void> {
    // Check if record already exists
    const existingRecord = await this.findExistingRecord(
      responseId,
      attributeId,
      value,
    );

    if (existingRecord) {
      if (
        existingRecord.record_status !== MclassRecordStatus.AILabel &&
        existingRecord.record_status !== MclassRecordStatus.AITrainingLabel
      ) {
        status.not_updated++;
        return;
      }

      // Update existing record
      await this.updateExistingRecord(
        existingRecord.mclass_response_id,
        confidence,
        value,
      );
      status.new_or_updated++;
    } else {
      // Find item_id from mclass_item table
      const itemId = await this.findItemIdByExternalId(attributeId);
      if (!itemId) {
        status.attribute_not_found++;
        return;
      }

      // Create new record
      await this.createNewRecord(responseId, itemId, value, confidence);
      status.new_or_updated++;
    }
  }

  private async findExistingRecord(
    responseId: number,
    attributeId: string,
    value: string,
  ): Promise<any> {
    const istype = this.getValueToIstype(value);
    const sql = `
      SELECT mr.mclass_response_id, mr.record_status
      FROM mclass_response mr
      LEFT JOIN mclass_item mi ON mr.item_id = mi.mclass_item_id
      WHERE mr.answer_id = $1 
        AND COALESCE(mi.external_id, mr.item_id::varchar(255)) = $2
        AND mr.istype = $3
    `;
    const result = await this.dataSource.query(sql, [
      responseId,
      attributeId,
      istype,
    ]);
    return result.length > 0 ? result[0] : null;
  }

  private async updateExistingRecord(
    mclassResponseId: number,
    confidence: number,
    value: string,
  ): Promise<void> {
    const istype = this.getValueToIstype(value);
    const sql = `
      UPDATE mclass_response 
      SET confidence = $1, istype = $2, modification_date = NOW()
      WHERE mclass_response_id = $3
    `;
    await this.dataSource.query(sql, [confidence, istype, mclassResponseId]);
  }

  private async findItemIdByExternalId(
    externalId: string,
  ): Promise<number | null> {
    const sql = 'SELECT mclass_item_id FROM mclass_item WHERE external_id = $1';
    const result = await this.dataSource.query(sql, [externalId]);
    return result.length > 0 ? result[0].mclass_item_id : null;
  }

  private async createNewRecord(
    responseId: number,
    itemId: number,
    value: string,
    confidence: number,
  ): Promise<void> {
    const istype = this.getValueToIstype(value);
    const sql = `
      INSERT INTO mclass_response (
        answer_id, item_id, record_status, istype, confidence, 
        is_liked, is_excluded, is_potential, creation_date, modification_date
      ) VALUES ($1, $2, $3, $4, $5, 0, 0, 0, NOW(), NOW())
    `;
    await this.dataSource.query(sql, [
      responseId,
      itemId,
      MclassRecordStatus.AILabel,
      istype,
      confidence,
    ]);
  }

  private getValueToIstype(value: string): MclassIsType {
    switch (value) {
      case LabelValue.POSITIVE:
        return MclassIsType.POSITIVE;
      case LabelValue.NEGATIVE:
        return MclassIsType.NEGATIVE;
      case LabelValue.NEUTRAL:
        return MclassIsType.NEUTRAL;
      default:
        throw new Error(`Invalid value: ${value}`);
    }
  }
}
