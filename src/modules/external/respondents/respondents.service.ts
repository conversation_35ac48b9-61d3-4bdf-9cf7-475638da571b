import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { startOfDay, endOfDay, format } from 'date-fns';

import { 
  EmailCustomerEntity, 
  ProjectsEntity, 
  SurvSurveyEntity, 
  ContactEntity, 
  ProjectStageEntity,
  CompanyEntity
} from "@entities";
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { GetRespondentsQueryDto } from './dto/get-respondents-query.dto';
import { RespondentsResponse, RespondentDto } from './dto/respondents-response.dto';
import { ModuleLogger } from "@helpers/logger/module-logger/module-logger.service";
import { CompanyType } from '@/shared/enums';

@Injectable()
export class RespondentsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private dataSource: DataSource,
    @InjectRepository(EmailCustomerEntity)
    private emailCustomerRepo: Repository<EmailCustomerEntity>,
    @InjectRepository(ProjectsEntity)
    private projectsRepo: Repository<ProjectsEntity>,
    @InjectRepository(SurvSurveyEntity)
    private surveyRepo: Repository<SurvSurveyEntity>,
    @InjectRepository(ContactEntity)
    private contactRepo: Repository<ContactEntity>,
    @InjectRepository(ProjectStageEntity)
    private projectStageRepo: Repository<ProjectStageEntity>,
    @InjectRepository(CompanyEntity)
    private companyRepo: Repository<CompanyEntity>,
  ) {}

  async getRespondents(query: GetRespondentsQueryDto): Promise<RespondentsResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    
    this.logger.debug('respondents.getRespondents', { query, companyId: company.id });

    // Parse dates and set to start/end of day
    const fromDate = startOfDay(this.parseDate(query.from));
    const toDate = query.to ? endOfDay(this.parseDate(query.to)) : endOfDay(new Date());

    // Get company type
    const companyData = await this.companyRepo.findOne({
      where: { sec_company_id: company.id },
      select: ['company_type']
    });

    if (!companyData) {
      throw new Error('Company not found');
    }

    let respondents: RespondentDto[] = [];

    if (companyData.company_type === CompanyType.BSH) {
      respondents = await this.getBshRespondents(fromDate, toDate, company.id);
    } else {
      respondents = await this.getOtherCompanyTypeRespondents(fromDate, toDate, company.id, companyData.company_type);
    }

    this.logger.debug('respondents.getRespondents result', { count: respondents.length });

    return { respondents };
  }

  private async getBshRespondents(fromDate: Date, toDate: Date, companyId: number): Promise<RespondentDto[]> {
    const query = `
      SELECT 
        ec.send_date, 
        ec.xml_rasnumber AS risnr, 
        ec.xml_customername AS name, 
        ec.xml_email AS mail, 
        s.name AS survey, 
        ec.email_customer_id as customer_id 
      FROM email_customer ec 
      LEFT JOIN surv_survey s ON ec.surv_survey_id=s.surv_survey_id 
      WHERE ec.send_date BETWEEN $1 AND $2 AND ec.sec_company_id=$3
    `;

    const results = await this.dataSource.query(query, [fromDate, toDate, companyId]);

    return results.map((row: any) => ({
      survey: row.survey || '',
      name: row.name || '',
      risnr: row.risnr || '',
      mail: row.mail || '',
      date: format(new Date(row.send_date), 'yyyy-MM-dd HH:mm:ss'),
      customer_id: row.customer_id
    }));
  }

  private async getOtherCompanyTypeRespondents(fromDate: Date, toDate: Date, companyId: number, companyType: number): Promise<RespondentDto[]> {
    const query = `
      SELECT 
        ec.send_date, 
        ec.xml_customername AS name, 
        ec.xml_email AS mail, 
        p.project_nr, 
        c.object_buildnr, 
        s.name AS survey, 
        ec.email_customer_id as customer_id, 
        c.contact_nr 
      FROM email_customer ec 
      LEFT JOIN projects p ON ec.xml_internal_external=p.project_id 
      LEFT JOIN project_stages ps ON ec.email_customer_id=ps.email_customer_id 
      LEFT JOIN contacts c ON ps.contact_id=c.contact_id 
      LEFT JOIN surv_survey s ON ec.surv_survey_id=s.surv_survey_id 
      WHERE ec.send_date BETWEEN $1 AND $2 AND ec.sec_company_id=$3
    `;

    const results = await this.dataSource.query(query, [fromDate, toDate, companyId]);

    return results.map((row: any) => {
      const respondent: RespondentDto = {
        survey: row.survey || '',
        name: row.name || '',
        mail: row.mail || '',
        date: format(new Date(row.send_date), 'yyyy-MM-dd HH:mm:ss'),
        customer_id: row.customer_id
      };

      // Add company type specific fields
      if (companyType === CompanyType.BOUW) {
        respondent.project_nr = row.project_nr || '';
        respondent.build_nr = row.object_buildnr || '';
        respondent.contact_nr = row.contact_nr || '';
      } else if (companyType === CompanyType.SPORT) {
        respondent.member_nr = row.contact_nr || '';
        respondent.club_nr = row.project_nr || '';
      }

      return respondent;
    });
  }

  private parseDate(dateString: string): Date {
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(year, month - 1, day); // month is 0-indexed in Date constructor
  }

} 