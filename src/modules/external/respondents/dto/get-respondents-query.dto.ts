import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, Matches } from 'class-validator';

export class GetRespondentsQueryDto {
  @ApiProperty({
    description: 'Start date in yyyy-mm-dd format',
    example: '2024-01-01',
    required: true
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'from must be in yyyy-mm-dd format'
  })
  from: string;

  @ApiProperty({
    description: 'End date in yyyy-mm-dd format (optional, defaults to current date)',
    example: '2024-12-31',
    required: false
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'to must be in yyyy-mm-dd format'
  })
  to?: string;
} 