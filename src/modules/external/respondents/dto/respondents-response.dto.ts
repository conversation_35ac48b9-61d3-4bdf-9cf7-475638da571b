import { ApiProperty } from '@nestjs/swagger';

export class RespondentDto {
  @ApiProperty({
    description: 'Survey name',
    example: 'Customer Satisfaction Survey'
  })
  survey: string;

  @ApiProperty({
    description: 'Customer name',
    example: '<PERSON>'
  })
  name: string;

  @ApiProperty({
    description: 'RIS number (only for BSH company type)',
    example: 'RIS123456',
    required: false
  })
  risnr?: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  mail: string;

  @ApiProperty({
    description: 'Project number (only for BOUW company type)',
    example: 'PRJ001',
    required: false
  })
  project_nr?: string;

  @ApiProperty({
    description: 'Build number (only for BOUW company type)',
    example: 'BLD001',
    required: false
  })
  build_nr?: string;

  @ApiProperty({
    description: 'Date in yyyy-MM-dd hh:mm:ss format',
    example: '2024-01-15 14:30:00'
  })
  date: string;

  @ApiProperty({
    description: 'Customer ID',
    example: 12345
  })
  customer_id: number;

  @ApiProperty({
    description: 'Contact number (only for BOUW company type)',
    example: 'CNT001',
    required: false
  })
  contact_nr?: string;

  @ApiProperty({
    description: 'Member number (only for SPORT company type)',
    example: 'MEM001',
    required: false
  })
  member_nr?: string;

  @ApiProperty({
    description: 'Club number (only for SPORT company type)',
    example: 'CLB001',
    required: false
  })
  club_nr?: string;
}

export class RespondentsResponse {
  @ApiProperty({
    description: 'Array of respondents',
    type: [RespondentDto]
  })
  respondents: RespondentDto[];
} 