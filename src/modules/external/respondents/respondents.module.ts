import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RespondentsController } from './respondents.controller';
import { RespondentsService } from './respondents.service';
import { 
  EmailCustomerEntity, 
  ProjectsEntity, 
  SurvSurveyEntity, 
  ContactEntity, 
  ProjectStageEntity,
  CompanyEntity
} from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.respondents'),
    TypeOrmModule.forFeature([
      EmailCustomerEntity, 
      ProjectsEntity, 
      SurvSurveyEntity, 
      ContactEntity, 
      ProjectStageEntity,
      CompanyEntity
    ]),
    ExternalAuthModule,
  ],
  controllers: [RespondentsController],
  providers: [RespondentsService],
  exports: [RespondentsService],
})
export class ExternalRespondentsModule {} 