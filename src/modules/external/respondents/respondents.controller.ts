import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { RespondentsService } from './respondents.service';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { RespondentsResponse } from './dto/respondents-response.dto';
import { GetRespondentsQueryDto } from './dto/get-respondents-query.dto';

@ApiTags('Respondents')
@ApiBearerAuth()
@Controller({
  path: 'external/respondents',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class RespondentsController {
  constructor(private readonly respondentsService: RespondentsService) {}

  @Get()
  @ApiOperation({ 
    summary: 'Get respondents data for the specified date range' 
  })
  @ApiQuery({ 
    name: 'from', 
    required: true, 
    description: 'Start date (yyyy-mm-dd)', 
    example: '2024-01-01' 
  })
  @ApiQuery({ 
    name: 'to', 
    required: false, 
    description: 'End date (yyyy-mm-dd)', 
    example: '2024-12-31' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Respondents data retrieved successfully',
    type: RespondentsResponse
  })
  async getRespondents(@Query() query: GetRespondentsQueryDto): Promise<RespondentsResponse> {
    return this.respondentsService.getRespondents(query);
  }
} 