import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ValidateCompanyService } from './validate-company.service';
import { UserTokenEntity } from '@entities';

describe('ValidateCompanyService', () => {
  let service: ValidateCompanyService;
  let userTokenRepo: Repository<UserTokenEntity>;

  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ValidateCompanyService,
        {
          provide: getRepositoryToken(UserTokenEntity),
          useValue: {
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
      ],
    }).compile();

    service = module.get<ValidateCompanyService>(ValidateCompanyService);
    userTokenRepo = module.get<Repository<UserTokenEntity>>(getRepositoryToken(UserTokenEntity));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validate', () => {
    it('should return company data for valid token', async () => {
      const mockToken = 'Bearer valid-token-123';
      const mockTokenData = {
        sec_user_ws_token_id: 1,
        token: 'valid-token-123',
        expiry_date: new Date(Date.now() + 3600000), // 1 hour from now
        company: {
          sec_company_id: 1,
          company_code: 'TEST_COMPANY',
          company_name: 'Test Company',
          company_type: 1,
          is_parent_company: 1,
        },
      };

      mockQueryBuilder.getOne.mockResolvedValue(mockTokenData);

      const result = await service.validate(mockToken);

      expect(result).toEqual({
        tokenId: mockTokenData.sec_user_ws_token_id,
        token: mockTokenData.token,
        expire: mockTokenData.expiry_date,
        company: {
          id: mockTokenData.company.sec_company_id,
          code: mockTokenData.company.company_code,
          name: mockTokenData.company.company_name,
          type: mockTokenData.company.company_type,
          isParent: true,
        },
      });

      expect(userTokenRepo.createQueryBuilder).toHaveBeenCalledWith('userToken');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith('userToken.company', 'company');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('userToken.token = :token', { token: 'valid-token-123' });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('userToken.expiry_date > :currentDate', { currentDate: expect.any(Date) });
    });

    it('should handle token without Bearer prefix', async () => {
      const mockToken = 'valid-token-123';
      const mockTokenData = {
        sec_user_ws_token_id: 1,
        token: 'valid-token-123',
        expiry_date: new Date(Date.now() + 3600000),
        company: {
          sec_company_id: 1,
          company_code: 'TEST_COMPANY',
          company_name: 'Test Company',
          company_type: 1,
          is_parent_company: 0,
        },
      };

      mockQueryBuilder.getOne.mockResolvedValue(mockTokenData);

      const result = await service.validate(mockToken);

      expect(result).toBeDefined();
      expect(result.company.isParent).toBe(false);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('userToken.token = :token', { token: 'valid-token-123' });
    });

    it('should return null for invalid token', async () => {
      const mockToken = 'Bearer invalid-token';

      mockQueryBuilder.getOne.mockResolvedValue(null);

      const result = await service.validate(mockToken);

      expect(result).toBeNull();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('userToken.token = :token', { token: 'invalid-token' });
    });

    it('should return null for expired token', async () => {
      const mockToken = 'Bearer expired-token';

      mockQueryBuilder.getOne.mockResolvedValue(null);

      const result = await service.validate(mockToken);

      expect(result).toBeNull();
    });

    it('should select correct fields from database', async () => {
      const mockToken = 'Bearer test-token';
      
      mockQueryBuilder.getOne.mockResolvedValue(null);

      await service.validate(mockToken);

      expect(mockQueryBuilder.select).toHaveBeenCalledWith([
        'userToken.sec_user_ws_token_id',
        'userToken.token',
        'userToken.expiry_date',
        'company.sec_company_id',
        'company.company_code',
        'company.company_type',
        'company.is_parent_company',
      ]);
    });

    it('should handle company with missing name gracefully', async () => {
      const mockToken = 'Bearer valid-token-123';
      const mockTokenData = {
        sec_user_ws_token_id: 1,
        token: 'valid-token-123',
        expiry_date: new Date(Date.now() + 3600000),
        company: {
          sec_company_id: 1,
          company_code: 'TEST_COMPANY',
          company_name: undefined, // Missing name
          company_type: 1,
          is_parent_company: 0,
        },
      };

      mockQueryBuilder.getOne.mockResolvedValue(mockTokenData);

      const result = await service.validate(mockToken);

      expect(result).toBeDefined();
      expect(result.company.name).toBeUndefined();
    });
  });
});
