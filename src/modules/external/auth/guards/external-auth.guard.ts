import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ValidateCompanyService } from '@/modules/external/auth/validate-company.service';
import { ClsService } from "nestjs-cls";

@Injectable()
export class ExternalAuthGuard implements CanActivate {
  constructor(
    private readonly cls: ClsService,
    private validateCompanyService: ValidateCompanyService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    try {
      const item = await this.validateCompanyService.validate(token);
      if (!item) {
        throw new UnauthorizedException('Invalid token');
      }
      this.cls.set('user', { ...item });
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
} 