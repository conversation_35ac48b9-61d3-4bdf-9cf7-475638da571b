import { ApiProperty } from '@nestjs/swagger';

export class TokenResponseDto {
  @ApiProperty({
    description: 'The generated access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRJZCI6ImNsaWVudDEyMyIsInRpbWVzdGFtcCI6MTcwOTg3NjQwMDAwMCwiaWF0IjoxNzA5ODc2NDAwLCJleHAiOjE3MDk4ODAwMDB9.abc123...',
    type: String,
  })
  token: string;

  @ApiProperty({
    description: 'Token type',
    example: 'Bearer',
    enum: ['Bearer'],
  })
  token_type: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 3600,
    minimum: 0,
    type: Number,
  })
  expires_in: number;
} 