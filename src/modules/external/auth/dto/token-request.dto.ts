import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class TokenRequestDto {
  @ApiProperty({
    description: 'Client ID for token generation',
    example: 'AD10B64E3754449D884CC29EB47E6679',
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Optional scope for the token',
    example: 'DE303C7A4B884674A87BE266A5ECC1E4',
    type: String,
    required: false,
    enum: ['read:data', 'write:data', 'admin'],
  })
  @IsString()
  secret: string;
} 