import { Mo<PERSON><PERSON>, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { TokenController } from './token.controller';
import { TokenService } from './token.service';
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserTokenEntity, CompanyApiKeyEntity } from "@entities";
import { ModuleLoggerModule } from "@lib/logger/module-logger/module-logger.module";
import { ExternalCorsMiddleware } from '@/shared/middleware/external-cors.middleware';

@Module({
  imports: [
    ModuleLoggerModule.register("external.token"),
    TypeOrmModule.forFeature([
      UserTokenEntity,
      CompanyApiKeyEntity
    ])
  ],
  controllers: [TokenController],
  providers: [TokenService],
  exports: [TokenService],
})
export class ExternalTokenModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(ExternalCorsMiddleware)
      .forRoutes({ path: 'external/token', method: RequestMethod.ALL });
  }
}