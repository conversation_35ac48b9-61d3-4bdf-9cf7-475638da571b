import { Module } from '@nestjs/common';
import { TokenController } from './token.controller';
import { TokenService } from './token.service';
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserTokenEntity, CompanyApiKeyEntity } from "@entities";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserTokenEntity,
      CompanyApiKeyEntity,
    ]),
  ],
  controllers: [TokenController],
  providers: [TokenService],
  exports: [TokenService],
})
export class ExternalTokenModule {}