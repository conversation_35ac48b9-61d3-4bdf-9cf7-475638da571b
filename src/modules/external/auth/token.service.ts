import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { TokenRequestDto } from './dto/token-request.dto';
import { TokenResponseDto } from './dto/token-response.dto';
import { SecurityService } from '@/modules/auth/security.service';
import { AuthService } from '@/modules/auth/auth.service';

import { TokenPrivilege } from '@/shared/enums';
import { ModuleLogger } from "@lib/logger/module-logger/module-logger.service";

@Injectable()
export class TokenService {
  constructor(
    private logger: ModuleLogger,

    private securityService: SecurityService,
    private authService: AuthService,
  ) {}

  async generateToken(body: TokenRequestDto): Promise<TokenResponseDto> {
    // TODO: Add validation logic for the request body
    let passwordResult = await this.validatePassword(body);
    if(!passwordResult || !passwordResult.success){
      throw new HttpException('props.external.errors.invalid_api_key_or_secret', HttpStatus.UNAUTHORIZED);
    }

    let tokenResult = await this.authService.generateToken({
      sec_user_id: null,
      sec_company_id: passwordResult.sec_company_id,
      token_privilege: TokenPrivilege.CompanyAPI,
      expiry_hours: 1,
      hash: null,
    })
    if(!tokenResult){
      throw new HttpException('props.external.errors.invalid_api_key_or_secret', HttpStatus.UNAUTHORIZED);
    }
    return {
      token: tokenResult.access_token,
      expires_in: tokenResult.expires_in, // 1 hour in seconds
      token_type: tokenResult.token_type,
    };
  }

  async validatePassword(body: TokenRequestDto) {
    const { id, secret } = body;

    this.logger.debug(`validatePassword for company ${id}`);

    const passwordData = await this.securityService.getCompanyPassword({id});
    if(!passwordData){
      this.logger.debug(`validatePassword for company ${id} no password data found`);
      return {success: false, sec_company_id: null};
    }

    let validatePassword = this.securityService.validate(
      secret,
      passwordData.api_secret,
    );

    if (!validatePassword) {
      this.logger.debug(`validatePassword for company ${id} failed`);
      return {success: false, sec_company_id: null};
    }

    this.logger.debug(`validatePassword for company ${id} success`);

    return {
      success: true,
      sec_company_id: passwordData.sec_company_id,
    };
  }
} 
