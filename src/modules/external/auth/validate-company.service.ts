import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserTokenEntity } from '@entities';
import { AuthorizedCompanyData } from '@/modules/auth/interfaces/authorized-company-request.interface';

@Injectable()
export class ValidateCompanyService {
  constructor(
    @InjectRepository(UserTokenEntity)
    private readonly userTokenEntity: Repository<UserTokenEntity>,
  ) {}

  async validate(
    token: string,
  ): Promise<AuthorizedCompanyData | null> {
    const tokenData = await this.userTokenEntity
      .createQueryBuilder('userToken')
      .leftJoinAndSelect('userToken.company', 'company')
      .where('userToken.token = :token', {
        token: token.startsWith('Bearer ') ? token.split(' ')[1] : token,
      })
      .andWhere('userToken.expiry_date > :currentDate', {
        currentDate: new Date(),
      })
      .select([
        'userToken.sec_user_ws_token_id',
        'userToken.token',
        'userToken.expiry_date',
        'company.sec_company_id',
        'company.company_code',
        'company.company_type',
        'company.is_parent_company',
      ])
      .getOne();

    if (!tokenData) {
      return null;
    }

    return {
      tokenId: tokenData.sec_user_ws_token_id,
      token: tokenData.token,
      expire: tokenData.expiry_date,
      company: {
        id: tokenData.company.sec_company_id,
        code: tokenData.company.company_code,
        name: tokenData.company.company_name,
        type: tokenData.company.company_type,
        isParent: !!tokenData.company.is_parent_company,
      },
    };
  }
} 