import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TokenService } from './token.service';
import { TokenRequestDto } from './dto/token-request.dto';
import { TokenResponseDto } from './dto/token-response.dto';
import { ExternalCors } from '@/shared/decorators/external-cors.decorator';

@ApiTags('Auth')
@Controller({
  path: '/external/token',
  version: '2',
})
export class TokenController {
  constructor(private readonly tokenService: TokenService) {}

  @Post()
  @ExternalCors()
  @ApiOperation({ summary: 'Generate external token' })
  @ApiResponse({
    status: 200,
    description: 'Token generated successfully',
    type: TokenResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters'
  })
  async generateToken(@Body() body: TokenRequestDto): Promise<TokenResponseDto> {
    return this.tokenService.generateToken(body);
  }
} 