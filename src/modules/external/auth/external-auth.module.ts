import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserTokenEntity } from '@entities';
import { ValidateCompanyService } from './validate-company.service';
import { ExternalAuthGuard } from './guards/external-auth.guard';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserTokenEntity])
  ],
  providers: [ValidateCompanyService, ExternalAuthGuard],
  exports: [ValidateCompanyService, ExternalAuthGuard]  // Make sure both are exported
})
export class ExternalAuthModule {}