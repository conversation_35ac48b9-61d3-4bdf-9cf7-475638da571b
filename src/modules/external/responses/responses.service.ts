import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { startOfDay, endOfDay, format } from 'date-fns';

import { 
  EmailCustomerEntity, 
  ProjectsEntity, 
  SurvSurveyEntity, 
  ContactEntity, 
  ProjectStageEntity,
  CompanyEntity,
  SurveyAnswerEntity,
  SurveyAnswerItemEntity,
  BshCategoriesEntity
} from "@entities";
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { GetResponsesQueryDto } from './dto/get-responses-query.dto';
import { ResponsesResponse, ResponseDto } from './dto/responses-response.dto';
import { ModuleLogger } from "@helpers/logger/module-logger/module-logger.service";
import { CompanyType } from '@/shared/enums';

@Injectable()
export class ResponsesService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private dataSource: DataSource,
    @InjectRepository(EmailCustomerEntity)
    private emailCustomerRepo: Repository<EmailCustomerEntity>,
    @InjectRepository(ProjectsEntity)
    private projectsRepo: Repository<ProjectsEntity>,
    @InjectRepository(SurvSurveyEntity)
    private surveyRepo: Repository<SurvSurveyEntity>,
    @InjectRepository(ContactEntity)
    private contactRepo: Repository<ContactEntity>,
    @InjectRepository(ProjectStageEntity)
    private projectStageRepo: Repository<ProjectStageEntity>,
    @InjectRepository(CompanyEntity)
    private companyRepo: Repository<CompanyEntity>,
    @InjectRepository(SurveyAnswerEntity)
    private surveyAnswerRepo: Repository<SurveyAnswerEntity>,
    @InjectRepository(SurveyAnswerItemEntity)
    private surveyAnswerItemRepo: Repository<SurveyAnswerItemEntity>,
    @InjectRepository(BshCategoriesEntity)
    private bshCategoriesRepo: Repository<BshCategoriesEntity>,
  ) {}

  async getResponses(query: GetResponsesQueryDto): Promise<ResponsesResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    
    this.logger.debug('responses.getResponses', { query, companyId: company.id });

    // Parse dates and set to start/end of day
    const fromDate = startOfDay(this.parseDate(query.from));
    const toDate = query.to ? endOfDay(this.parseDate(query.to)) : endOfDay(new Date());

    // Get company type
    const companyData = await this.companyRepo.findOne({
      where: { sec_company_id: company.id },
      select: ['company_type']
    });

    if (!companyData) {
      throw new Error('Company not found');
    }

    // Set default values for pagination
    const limit = query.limit || 5000;
    const page = query.page || 1;
    const offset = (page - 1) * limit;
    const actualLimit = limit + 1; // Add one to check if there are more pages

    let responses: ResponseDto[] = [];
    let totalCount = 0;

    if (companyData.company_type === CompanyType.BSH) {
      const result = await this.getBshResponses(fromDate, toDate, company.id, query, actualLimit, offset);
      responses = result.responses;
      totalCount = result.totalCount;
    } else {
      const result = await this.getOtherCompanyTypeResponses(fromDate, toDate, company.id, companyData.company_type, query, actualLimit, offset);
      responses = result.responses;
      totalCount = result.totalCount;
    }

    // Check if there are more pages
    const hasMorePages = responses.length > limit;
    if (hasMorePages) {
      responses = responses.slice(0, limit); // Remove the extra item
    }

    const response: ResponsesResponse = {
      responses,
      count: totalCount
    };

    // Add next_page if there are more pages
    if (hasMorePages) {
      response.next_page = page + 1;
    }

    this.logger.debug('responses.getResponses result', { count: responses.length, hasMorePages });

    return response;
  }

  private async getBshResponses(
    fromDate: Date, 
    toDate: Date, 
    companyId: number, 
    query: GetResponsesQueryDto,
    limit: number,
    offset: number
  ): Promise<{ responses: ResponseDto[], totalCount: number }> {
    let baseQuery = `
      SELECT 
        sa.surv_surveyanswer_id, 
        ec.xml_brand brand, 
        sa.creation_date, 
        ec.xml_rasnumber, 
        ec.xml_customername AS name, 
        ec.xml_email AS email, 
        s.name AS survey, 
        sa.nps_value AS nps, 
        pos.return_value AS pos, 
        neg.return_value AS neg, 
        ec.email_customer_id as customer_id, 
        grandparent.bsh_category_uuid grandparent_id, 
        grandparent.bsh_category_name grandparent_name, 
        parent.bsh_category_uuid parent_id, 
        parent.bsh_category_name parent_name, 
        child.bsh_category_uuid child_id, 
        child.bsh_category_name child_name, 
        s.surv_survey_id, 
        ec.xml_prodarea, 
        xml_readydate, 
        xml_acceptance_by, 
        xml_wvb, 
        xml_planning, 
        xml_language, 
        xml_proddate, 
        xml_purchasedate, 
        xml_nrvisits, 
        xml_originservice, 
        xml_acceptancedate, 
        xml_lastvisitdate, 
        xml_amount, 
        xml_payment, 
        xml_consecnr, 
        xml_fident, 
        xml_callcode, 
        xml_fc, 
        xml_accountindic, 
        xml_proddivision, 
        xml_enumber, 
        xml_hybrisid, 
        xml_currency, 
        xml_dealer, 
        xml_region, 
        xml_techgrp, 
        xml_technician, 
        xml_custcreditnr, 
        xml_la, 
        xml_employee, 
        sent_medium, 
        xml_phone2, 
        xml_internal_external, 
        ec.website_url, 
        sa.os, 
        sa.device 
      FROM surv_surveyanswer sa 
      LEFT JOIN email_customer ec ON sa.email_customer_id=ec.email_customer_id 
      LEFT JOIN surv_survey s ON sa.surv_survey_id=s.surv_survey_id 
      LEFT JOIN surv_surveyansweritem pos ON s.clf_question2=pos.surv_surveyquestion_id AND sa.surv_surveyanswer_id=pos.surv_surveyanswer_id 
      LEFT JOIN surv_surveyansweritem neg ON s.clf_question1=neg.surv_surveyquestion_id AND sa.surv_surveyanswer_id=neg.surv_surveyanswer_id 
      LEFT JOIN bsh_categories child ON s.bsh_category_id = child.bsh_category_id  
      LEFT JOIN bsh_categories parent ON child.parent_bsh_category_id = parent.bsh_category_id  
      LEFT JOIN bsh_categories grandparent ON parent.parent_bsh_category_id = grandparent.bsh_category_id  
      WHERE sa.creation_date BETWEEN $1 AND $2
    `;

    const queryParams: any[] = [fromDate, toDate];

    // Add company filter
    if (query.company_code) {
      baseQuery += ` AND sa.sec_company_id=(select sc.sec_company_id from sec_company sc where sc.company_name = $${queryParams.length + 1})`;
      queryParams.push(query.company_code);
    } else {
      baseQuery += ` AND sa.sec_company_id=$${queryParams.length + 1}`;
      queryParams.push(companyId);
    }

    // Add survey filter
    if (query.survey) {
      baseQuery += ` AND sa.surv_survey_id = $${queryParams.length + 1}`;
      queryParams.push(query.survey);
    }

    // Add brand filter
    if (query.brand) {
      baseQuery += ` AND lower(ec.xml_brand) = $${queryParams.length + 1}`;
      queryParams.push(query.brand.toLowerCase());
    }

    // Add ordering and pagination
    baseQuery += ` ORDER BY sa.creation_date LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
    queryParams.push(limit, offset);

    const results = await this.dataSource.query(baseQuery, queryParams);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM surv_surveyanswer sa 
      LEFT JOIN email_customer ec ON sa.email_customer_id=ec.email_customer_id 
      LEFT JOIN surv_survey s ON sa.surv_survey_id=s.surv_survey_id 
      WHERE sa.creation_date BETWEEN $1 AND $2
    `;

    const countParams: any[] = [fromDate, toDate];

    if (query.company_code) {
      countQuery += ` AND sa.sec_company_id=(select sc.sec_company_id from sec_company sc where sc.company_name = $${countParams.length + 1})`;
      countParams.push(query.company_code);
    } else {
      countQuery += ` AND sa.sec_company_id=$${countParams.length + 1}`;
      countParams.push(companyId);
    }

    if (query.survey) {
      countQuery += ` AND sa.surv_survey_id = $${countParams.length + 1}`;
      countParams.push(query.survey);
    }

    if (query.brand) {
      countQuery += ` AND lower(ec.xml_brand) = $${countParams.length + 1}`;
      countParams.push(query.brand.toLowerCase());
    }

    const countResult = await this.dataSource.query(countQuery, countParams);
    const totalCount = parseInt(countResult[0].total);

    const responses = results.map((row: any) => ({
      id: row.surv_surveyanswer_id,
      survey: row.survey || '',
      name: row.name || '',
      risnr: row.xml_rasnumber || '',
      mail: row.email || '',
      date: format(new Date(row.creation_date), 'yyyy-MM-dd HH:mm:ss'),
      nps: row.nps || 0,
      positive_remarks: row.pos || '',
      customer_id: row.customer_id,
      negative_remarks: row.neg || '',
      survey_BSH_grandparent_category_id: row.grandparent_id || '',
      survey_BSH_grandparent_category_name: row.grandparent_name || '',
      survey_BSH_parent_category_id: row.parent_id || '',
      survey_BSH_parent_category_name: row.parent_name || '',
      survey_BSH_child_category_id: row.child_id || '',
      survey_BSH_child_category_name: row.child_name || '',
      product_area: row.xml_prodarea || '',
      first_visit_date: row.xml_readydate ? new Date(row.xml_readydate) : undefined,
      frontline_agent: row.xml_acceptance_by || '',
      pre_prepper: row.xml_wvb || '',
      dispatcher: row.xml_planning || '',
      language: row.xml_language || '',
      production_date: row.xml_proddate || '',
      purchase_date: row.xml_purchasedate ? new Date(row.xml_purchasedate) : undefined,
      number_of_visits: row.xml_nrvisits || 0,
      origin_service: row.xml_originservice || '',
      order_acceptance_date: row.xml_acceptancedate ? new Date(row.xml_acceptancedate) : undefined,
      last_visit_date: row.xml_lastvisitdate ? new Date(row.xml_lastvisitdate) : undefined,
      total_amount: row.xml_amount || '',
      payment_method: row.xml_payment || '',
      consecutive_number: row.xml_consecnr || '',
      f_ident: row.xml_fident || '',
      repair_code: row.xml_callcode || '',
      fault_code: row.xml_fc || '',
      account_indicator: row.xml_accountindic || '',
      product_division: row.xml_proddivision || '',
      e_number: row.xml_enumber || '',
      hybris_id: row.xml_hybrisid || '',
      currency: row.xml_currency || '',
      dealer: row.xml_dealer || '',
      region: row.xml_region || '',
      engineer_group: row.xml_techgrp || '',
      engineer: row.xml_technician || '',
      service_partner: row.xml_custcreditnr || '',
      la: row.xml_la || '',
      employee: row.xml_employee || '',
      medium: row.sent_medium || 0,
      phone_number: row.xml_phone2 || '',
      service_type: row.xml_internal_external || '',
      device: row.device || '',
      os: row.os || '',
      website_url: row.website_url || ''
    }));

    return { responses, totalCount };
  }

  private async getOtherCompanyTypeResponses(
    fromDate: Date, 
    toDate: Date, 
    companyId: number, 
    companyType: number,
    query: GetResponsesQueryDto,
    limit: number,
    offset: number
  ): Promise<{ responses: ResponseDto[], totalCount: number }> {
    let baseQuery = `
      SELECT 
        sa.surv_surveyanswer_id, 
        ec.xml_brand brand, 
        sa.creation_date, 
        ec.xml_customername AS name, 
        ec.xml_email AS email, 
        p.project_nr, 
        c.object_buildnr, 
        s.name AS survey, 
        sa.nps_value AS nps, 
        pos.return_value AS pos, 
        neg.return_value AS neg, 
        ec.email_customer_id as customer_id, 
        c.contact_nr, 
        sa.os, 
        sa.device 
      FROM surv_surveyanswer sa 
      LEFT JOIN email_customer ec ON sa.email_customer_id=ec.email_customer_id 
      LEFT JOIN projects p ON ec.xml_internal_external=p.project_id 
      LEFT JOIN project_stages ps ON ec.email_customer_id=ps.email_customer_id 
      LEFT JOIN contacts c ON ps.contact_id=c.contact_id 
      LEFT JOIN surv_survey s ON sa.surv_survey_id=s.surv_survey_id 
      LEFT JOIN surv_surveyansweritem pos ON s.clf_question2=pos.surv_surveyquestion_id AND sa.surv_surveyanswer_id=pos.surv_surveyanswer_id 
      LEFT JOIN surv_surveyansweritem neg ON s.clf_question1=neg.surv_surveyquestion_id AND sa.surv_surveyanswer_id=neg.surv_surveyanswer_id 
      WHERE sa.creation_date BETWEEN $1 AND $2 AND sa.sec_company_id=$3
    `;

    const queryParams: any[] = [fromDate, toDate, companyId];

    // Add project/club filter
    if (query.project || query.club) {
      baseQuery += ` AND ec.xml_internal_external = $${queryParams.length + 1}`;
      queryParams.push(query.project || query.club);
    }

    // Add survey filter
    if (query.survey) {
      baseQuery += ` AND sa.surv_survey_id = $${queryParams.length + 1}`;
      queryParams.push(query.survey);
    }

    // Add brand filter
    if (query.brand) {
      baseQuery += ` AND lower(ec.xml_brand) = $${queryParams.length + 1}`;
      queryParams.push(query.brand.toLowerCase());
    }

    // Add ordering and pagination
    baseQuery += ` ORDER BY sa.creation_date LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
    queryParams.push(limit, offset);

    const results = await this.dataSource.query(baseQuery, queryParams);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM surv_surveyanswer sa 
      LEFT JOIN email_customer ec ON sa.email_customer_id=ec.email_customer_id 
      LEFT JOIN projects p ON ec.xml_internal_external=p.project_id 
      LEFT JOIN project_stages ps ON ec.email_customer_id=ps.email_customer_id 
      LEFT JOIN contacts c ON ps.contact_id=c.contact_id 
      WHERE sa.creation_date BETWEEN $1 AND $2 AND sa.sec_company_id=$3
    `;

    const countParams: any[] = [fromDate, toDate, companyId];

    if (query.project || query.club) {
      countQuery += ` AND ec.xml_internal_external = $${countParams.length + 1}`;
      countParams.push(query.project || query.club);
    }

    if (query.survey) {
      countQuery += ` AND sa.surv_survey_id = $${countParams.length + 1}`;
      countParams.push(query.survey);
    }

    if (query.brand) {
      countQuery += ` AND lower(ec.xml_brand) = $${countParams.length + 1}`;
      countParams.push(query.brand.toLowerCase());
    }

    const countResult = await this.dataSource.query(countQuery, countParams);
    const totalCount = parseInt(countResult[0].total);

    const responses = results.map((row: any) => {
      const response: ResponseDto = {
        id: row.surv_surveyanswer_id,
        survey: row.survey || '',
        name: row.name || '',
        mail: row.email || '',
        date: format(new Date(row.creation_date), 'yyyy-MM-dd HH:mm:ss'),
        nps: row.nps || 0,
        positive_remarks: row.pos || '',
        customer_id: row.customer_id,
        negative_remarks: row.neg || '',
        device: row.device || '',
        os: row.os || ''
      };

      // Add company type specific fields
      if (companyType === CompanyType.BOUW) {
        response.project_nr = row.project_nr || '';
        response.build_nr = row.object_buildnr || '';
        response.contact_nr = row.contact_nr || '';
      } else if (companyType === CompanyType.SPORT) {
        response.member_nr = parseInt(row.contact_nr) || 0;
        response.club_nr = row.project_nr || '';
      }

      return response;
    });

    return { responses, totalCount };
  }

  private parseDate(dateString: string): Date {
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(year, month - 1, day); // month is 0-indexed in Date constructor
  }
} 