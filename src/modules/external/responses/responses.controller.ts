import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ResponsesService } from './responses.service';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { ResponsesResponse } from './dto/responses-response.dto';
import { GetResponsesQueryDto } from './dto/get-responses-query.dto';

@ApiTags('Responses')
@ApiBearerAuth()
@Controller({
  path: 'external/responses',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class ResponsesController {
  constructor(private readonly responsesService: ResponsesService) {}

  @Get()
  @ApiOperation({ 
    summary: 'Get survey responses data for the specified date range' 
  })
  @ApiQuery({ 
    name: 'from', 
    required: true, 
    description: 'Start date (yyyy-mm-dd)', 
    example: '2024-01-01' 
  })
  @ApiQuery({ 
    name: 'to', 
    required: false, 
    description: 'End date (yyyy-mm-dd)', 
    example: '2024-12-31' 
  })
  @ApiQuery({ 
    name: 'limit', 
    required: false, 
    description: 'Limit number of responses (default 5000)', 
    example: 1000 
  })
  @ApiQuery({ 
    name: 'page', 
    required: false, 
    description: 'Page number (default 1)', 
    example: 1 
  })
  @ApiQuery({ 
    name: 'project', 
    required: false, 
    description: 'Project UUID', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @ApiQuery({ 
    name: 'club', 
    required: false, 
    description: 'Club UUID', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @ApiQuery({ 
    name: 'brand', 
    required: false, 
    description: 'Brand name', 
    example: 'Bosch' 
  })
  @ApiQuery({ 
    name: 'company_code', 
    required: false, 
    description: 'Company code', 
    example: 'BSH001' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Responses data retrieved successfully',
    type: ResponsesResponse
  })
  async getResponses(@Query() query: GetResponsesQueryDto): Promise<ResponsesResponse> {
    return this.responsesService.getResponses(query);
  }
} 