import { ApiProperty } from '@nestjs/swagger';

export class ResponseDto {
  @ApiProperty({
    description: 'Response ID',
    example: 12345
  })
  id: number;

  @ApiProperty({
    description: 'Survey name',
    example: 'Customer Satisfaction Survey'
  })
  survey: string;

  @ApiProperty({
    description: 'Customer name',
    example: '<PERSON>'
  })
  name: string;

  @ApiProperty({
    description: 'RIS number (only for BSH company type)',
    example: 'RIS123456',
    required: false
  })
  risnr?: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  mail: string;

  @ApiProperty({
    description: 'Project number (only for BOUW company type)',
    example: 'PRJ001',
    required: false
  })
  project_nr?: string;

  @ApiProperty({
    description: 'Build number (only for BOUW company type)',
    example: 'BLD001',
    required: false
  })
  build_nr?: string;

  @ApiProperty({
    description: 'Contact number (only for BOUW company type)',
    example: 'CNT001',
    required: false
  })
  contact_nr?: string;

  @ApiProperty({
    description: 'Member number (only for SPORT company type)',
    example: 'MEM001',
    required: false
  })
  member_nr?: number;

  @ApiProperty({
    description: 'Club number (only for SPORT company type)',
    example: 'CLB001',
    required: false
  })
  club_nr?: string;

  @ApiProperty({
    description: 'Date in yyyy-MM-dd HH:mm:ss format',
    example: '2024-01-15 14:30:00'
  })
  date: string;

  @ApiProperty({
    description: 'NPS value',
    example: 8
  })
  nps: number;

  @ApiProperty({
    description: 'Positive remarks',
    example: 'Great service experience'
  })
  positive_remarks: string;

  @ApiProperty({
    description: 'Customer ID',
    example: 12345
  })
  customer_id: number;

  @ApiProperty({
    description: 'Negative remarks',
    example: 'Could be faster'
  })
  negative_remarks: string;

  @ApiProperty({
    description: 'BSH grandparent category ID (only for BSH company type)',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false
  })
  survey_BSH_grandparent_category_id?: string;

  @ApiProperty({
    description: 'BSH grandparent category name (only for BSH company type)',
    example: 'Kitchen Appliances',
    required: false
  })
  survey_BSH_grandparent_category_name?: string;

  @ApiProperty({
    description: 'BSH parent category ID (only for BSH company type)',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false
  })
  survey_BSH_parent_category_id?: string;

  @ApiProperty({
    description: 'BSH parent category name (only for BSH company type)',
    example: 'Dishwashers',
    required: false
  })
  survey_BSH_parent_category_name?: string;

  @ApiProperty({
    description: 'BSH child category ID (only for BSH company type)',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false
  })
  survey_BSH_child_category_id?: string;

  @ApiProperty({
    description: 'BSH child category name (only for BSH company type)',
    example: 'Built-in Dishwashers',
    required: false
  })
  survey_BSH_child_category_name?: string;

  @ApiProperty({
    description: 'Product area (only for BSH company type)',
    example: 'Kitchen',
    required: false
  })
  product_area?: string;

  @ApiProperty({
    description: 'First visit date (only for BSH company type)',
    example: '2024-01-15',
    required: false
  })
  first_visit_date?: Date;

  @ApiProperty({
    description: 'Frontline agent (only for BSH company type)',
    example: 'Agent Name',
    required: false
  })
  frontline_agent?: string;

  @ApiProperty({
    description: 'Pre-prepper (only for BSH company type)',
    example: 'Prepper Name',
    required: false
  })
  pre_prepper?: string;

  @ApiProperty({
    description: 'Dispatcher (only for BSH company type)',
    example: 'Dispatcher Name',
    required: false
  })
  dispatcher?: string;

  @ApiProperty({
    description: 'Language (only for BSH company type)',
    example: 'en',
    required: false
  })
  language?: string;

  @ApiProperty({
    description: 'Production date (only for BSH company type)',
    example: '2024-01-15',
    required: false
  })
  production_date?: string;

  @ApiProperty({
    description: 'Purchase date (only for BSH company type)',
    example: '2024-01-15',
    required: false
  })
  purchase_date?: Date;

  @ApiProperty({
    description: 'Number of visits (only for BSH company type)',
    example: 3,
    required: false
  })
  number_of_visits?: number;

  @ApiProperty({
    description: 'Origin service (only for BSH company type)',
    example: 'Online',
    required: false
  })
  origin_service?: string;

  @ApiProperty({
    description: 'Order acceptance date (only for BSH company type)',
    example: '2024-01-15',
    required: false
  })
  order_acceptance_date?: Date;

  @ApiProperty({
    description: 'Last visit date (only for BSH company type)',
    example: '2024-01-15',
    required: false
  })
  last_visit_date?: Date;

  @ApiProperty({
    description: 'Total amount (only for BSH company type)',
    example: '1500.00',
    required: false
  })
  total_amount?: string;

  @ApiProperty({
    description: 'Payment method (only for BSH company type)',
    example: 'Credit Card',
    required: false
  })
  payment_method?: string;

  @ApiProperty({
    description: 'Consecutive number (only for BSH company type)',
    example: 'CONS001',
    required: false
  })
  consecutive_number?: string;

  @ApiProperty({
    description: 'F-ident (only for BSH company type)',
    example: 'F001',
    required: false
  })
  f_ident?: string;

  @ApiProperty({
    description: 'Repair code (only for BSH company type)',
    example: 'REP001',
    required: false
  })
  repair_code?: string;

  @ApiProperty({
    description: 'Fault code (only for BSH company type)',
    example: 'FAULT001',
    required: false
  })
  fault_code?: string;

  @ApiProperty({
    description: 'Account indicator (only for BSH company type)',
    example: 'ACC001',
    required: false
  })
  account_indicator?: string;

  @ApiProperty({
    description: 'Product division (only for BSH company type)',
    example: 'Kitchen',
    required: false
  })
  product_division?: string;

  @ApiProperty({
    description: 'E-number (only for BSH company type)',
    example: 'E001',
    required: false
  })
  e_number?: string;

  @ApiProperty({
    description: 'Hybris ID (only for BSH company type)',
    example: 'HYB001',
    required: false
  })
  hybris_id?: string;

  @ApiProperty({
    description: 'Currency (only for BSH company type)',
    example: 'EUR',
    required: false
  })
  currency?: string;

  @ApiProperty({
    description: 'Dealer (only for BSH company type)',
    example: 'Dealer Name',
    required: false
  })
  dealer?: string;

  @ApiProperty({
    description: 'Region (only for BSH company type)',
    example: 'Europe',
    required: false
  })
  region?: string;

  @ApiProperty({
    description: 'Engineer group (only for BSH company type)',
    example: 'Group A',
    required: false
  })
  engineer_group?: string;

  @ApiProperty({
    description: 'Engineer (only for BSH company type)',
    example: 'Engineer Name',
    required: false
  })
  engineer?: string;

  @ApiProperty({
    description: 'Service partner (only for BSH company type)',
    example: 'Partner Name',
    required: false
  })
  service_partner?: string;

  @ApiProperty({
    description: 'LA (only for BSH company type)',
    example: 'LA001',
    required: false
  })
  la?: string;

  @ApiProperty({
    description: 'Employee (only for BSH company type)',
    example: 'Employee Name',
    required: false
  })
  employee?: string;

  @ApiProperty({
    description: 'Medium (only for BSH company type)',
    example: 1,
    required: false
  })
  medium?: number;

  @ApiProperty({
    description: 'Phone number (only for BSH company type)',
    example: '+1234567890',
    required: false
  })
  phone_number?: string;

  @ApiProperty({
    description: 'Service type (only for BSH company type)',
    example: 'Repair',
    required: false
  })
  service_type?: string;

  @ApiProperty({
    description: 'Device',
    example: 'Desktop'
  })
  device: string;

  @ApiProperty({
    description: 'Operating system',
    example: 'Windows'
  })
  os: string;

  @ApiProperty({
    description: 'Website URL (only for BSH company type)',
    example: 'https://example.com',
    required: false
  })
  website_url?: string;
}

export class ResponsesResponse {
  @ApiProperty({
    description: 'Array of responses',
    type: [ResponseDto]
  })
  responses: ResponseDto[];

  @ApiProperty({
    description: 'Total count of responses',
    example: 150
  })
  count: number;

  @ApiProperty({
    description: 'Next page number (only included if there are more pages)',
    example: 2,
    required: false
  })
  next_page?: number;
} 