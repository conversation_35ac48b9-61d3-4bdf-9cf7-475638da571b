import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, Matches, IsNumber, <PERSON>U<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class GetResponsesQueryDto {
  @ApiProperty({
    description: 'Start date in yyyy-mm-dd format',
    example: '2024-01-01',
    required: true
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'from must be in yyyy-mm-dd format'
  })
  from: string;

  @ApiProperty({
    description: 'End date in yyyy-mm-dd format (optional, defaults to current date)',
    example: '2024-12-31',
    required: false
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'to must be in yyyy-mm-dd format'
  })
  to?: string;

  @ApiProperty({
    description: 'Limit number of responses (optional, default 5000)',
    example: 1000,
    required: false,
    minimum: 1,
    maximum: 10000
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(10000)
  limit?: number = 5000;

  @ApiProperty({
    description: 'Page number (optional, default 1)',
    example: 1,
    required: false,
    minimum: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Project UUID (optional)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID()
  project?: string;

  @ApiProperty({
    description: 'Club UUID (optional)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID()
  club?: string;

  @ApiProperty({
    description: 'Brand name (optional)',
    example: 'Bosch',
    required: false
  })
  @IsOptional()
  @IsString()
  brand?: string;

  @ApiProperty({
    description: 'Survey ID (optional)',
    example: 123,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  survey?: number;

  @ApiProperty({
    description: 'Company code (optional)',
    example: 'BSH001',
    required: false
  })
  @IsOptional()
  @IsString()
  company_code?: string;
}