import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ResponsesController } from './responses.controller';
import { ResponsesService } from './responses.service';
import { 
  EmailCustomerEntity, 
  ProjectsEntity, 
  SurvSurveyEntity, 
  ContactEntity, 
  ProjectStageEntity,
  CompanyEntity,
  SurveyAnswerEntity,
  SurveyAnswerItemEntity,
  BshCategoriesEntity
} from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from "@helpers/logger/module-logger/module-logger.module";

@Module({
  imports: [
    ModuleLoggerModule.register('external.responses'),
    TypeOrmModule.forFeature([
      EmailCustomerEntity, 
      ProjectsEntity, 
      SurvSurveyEntity, 
      ContactEntity, 
      ProjectStageEntity,
      CompanyEntity,
      SurveyAnswerEntity,
      SurveyAnswerItemEntity,
      BshCategoriesEntity
    ]),
    ExternalAuthModule,
  ],
  controllers: [ResponsesController],
  providers: [ResponsesService],
  exports: [ResponsesService],
})
export class ExternalResponsesModule {} 