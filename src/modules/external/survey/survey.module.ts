import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyController } from './survey.controller';
import { SurveyService } from './survey.service';
import { 
  SurvSurveyEntity, 
  CompanyEntity
} from '@entities';
import { ExternalAuthModule } from '@/modules/external/auth/external-auth.module';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('external.survey'),
    TypeOrmModule.forFeature([
      SurvSurveyEntity, 
      CompanyEntity
    ]),
    ExternalAuthModule,
  ],
  controllers: [SurveyController],
  providers: [SurveyService],
  exports: [SurveyService],
})
export class ExternalSurveyModule {} 