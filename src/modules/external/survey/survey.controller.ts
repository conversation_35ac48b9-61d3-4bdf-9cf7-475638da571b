import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SurveyService } from './survey.service';
import { ExternalAuthGuard } from '../auth/guards/external-auth.guard';
import { SurveyResponse } from './dto/survey-response.dto';

@ApiTags('Survey')
@ApiBearerAuth()
@Controller({
  path: 'external/survey',
  version: '2',
})
@UseGuards(ExternalAuthGuard)
export class SurveyController {
  constructor(private readonly surveyService: SurveyService) {}

  @Get()
  @ApiOperation({ summary: 'Get surveys list' })
  @ApiResponse({ 
    status: 200, 
    description: 'Surveys retrieved successfully',
    type: SurveyResponse
  })
  async getSurveys(): Promise<SurveyResponse> {
    return this.surveyService.getSurveys();
  }
} 