import { ApiProperty } from '@nestjs/swagger';

export class SurveyDto {
  @ApiProperty({
    description: 'Survey ID',
    example: 1
  })
  survey_id: number;

  @ApiProperty({
    description: 'Survey name',
    example: 'Customer Satisfaction Survey'
  })
  survey_name: string;

  @ApiProperty({
    description: 'Internal survey name',
    example: 'internal_name'
  })
  survey_name_internal: string;

  @ApiProperty({
    description: 'BSH grandparent category ID (only for BSH company type)',
    example: 1,
    required: false
  })
  survey_BSH_grandparent_category_id?: number;

  @ApiProperty({
    description: 'BSH grandparent category name (only for BSH company type)',
    example: 'Grandparent Category',
    required: false
  })
  survey_BSH_grandparent_category_name?: string;

  @ApiProperty({
    description: 'BSH parent category ID (only for BSH company type)',
    example: 2,
    required: false
  })
  survey_BSH_parent_category_id?: number;

  @ApiProperty({
    description: 'BSH parent category name (only for BSH company type)',
    example: 'Parent Category',
    required: false
  })
  survey_BSH_parent_category_name?: string;

  @ApiProperty({
    description: 'BSH child category ID (only for BSH company type)',
    example: 3,
    required: false
  })
  survey_BSH_child_category_id?: number;

  @ApiProperty({
    description: 'BSH child category name (only for BSH company type)',
    example: 'Child Category',
    required: false
  })
  survey_BSH_child_category_name?: string;
}

export class SurveyResponse {
  @ApiProperty({
    description: 'Total count of surveys',
    example: 1
  })
  count: number;

  @ApiProperty({
    description: 'Array of surveys',
    type: [SurveyDto]
  })
  surveys: SurveyDto[];
} 