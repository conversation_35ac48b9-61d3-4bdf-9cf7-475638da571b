import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { 
  SurvSurveyEntity, 
  CompanyEntity
} from '@entities';
import { ClsProperty } from '@/config/contents/cls.contents';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { SurveyResponse, SurveyDto } from './dto/survey-response.dto';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';
import { CompanyType } from '@/shared/enums';

@Injectable()
export class SurveyService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(SurvSurveyEntity)
    private readonly surveyRepo: Repository<SurvSurveyEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
  ) {}

  async getSurveys(): Promise<SurveyResponse> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    
    this.logger.debug('survey.getSurveys', { companyId: company.id });

    // Get company type
    const companyData = await this.companyRepo.findOne({
      where: { sec_company_id: company.id },
      select: ['company_type']
    });

    if (!companyData) {
      throw new Error('Company not found');
    }

    let surveys: SurveyDto[] = [];

    if (companyData.company_type === CompanyType.BSH) {
      surveys = await this.getBshSurveys(company.id);
    } else {
      surveys = await this.getOtherCompanyTypeSurveys(company.id);
    }

    this.logger.debug('survey.getSurveys result', { count: surveys.length });

    return {
      count: surveys.length,
      surveys
    };
  }

  private async getBshSurveys(companyId: number): Promise<SurveyDto[]> {
    const surveys = await this.surveyRepo.query(`
      SELECT 
        s.surv_survey_id as survey_id,
        s.name as survey_name,
        s.internal_name as survey_name_internal,
        grandparent.bsh_category_id as survey_BSH_grandparent_category_id,
        grandparent.bsh_category_name as survey_BSH_grandparent_category_name,
        parent.bsh_category_id as survey_BSH_parent_category_id,
        parent.bsh_category_name as survey_BSH_parent_category_name,
        child.bsh_category_id as survey_BSH_child_category_id,
        child.bsh_category_name as survey_BSH_child_category_name
      FROM surv_survey s
      LEFT JOIN bsh_categories child ON s.bsh_category_id = child.bsh_category_id
      LEFT JOIN bsh_categories parent ON child.parent_bsh_category_id = parent.bsh_category_id
      LEFT JOIN bsh_categories grandparent ON parent.parent_bsh_category_id = grandparent.bsh_category_id
      WHERE s.sec_company_id = $1
      ORDER BY s.name
    `, [companyId]);

    return surveys.map((survey: any) => ({
      survey_id: survey.survey_id,
      survey_name: survey.survey_name,
      survey_name_internal: survey.survey_name_internal,
      survey_BSH_grandparent_category_id: survey.survey_BSH_grandparent_category_id,
      survey_BSH_grandparent_category_name: survey.survey_BSH_grandparent_category_name,
      survey_BSH_parent_category_id: survey.survey_BSH_parent_category_id,
      survey_BSH_parent_category_name: survey.survey_BSH_parent_category_name,
      survey_BSH_child_category_id: survey.survey_BSH_child_category_id,
      survey_BSH_child_category_name: survey.survey_BSH_child_category_name,
    }));
  }

  private async getOtherCompanyTypeSurveys(companyId: number): Promise<SurveyDto[]> {
    const surveys = await this.surveyRepo.query(`
      SELECT 
        surv_survey_id as survey_id,
        name as survey_name,
        internal_name as survey_name_internal
      FROM surv_survey
      WHERE sec_company_id = $1
      ORDER BY name
    `, [companyId]);

    return surveys.map((survey: any) => ({
      survey_id: survey.survey_id,
      survey_name: survey.survey_name,
      survey_name_internal: survey.survey_name_internal,
    }));
  }
} 