# External Survey API

This module provides an external API endpoint for retrieving survey information.

## Endpoints

### GET /v2/external/survey

Retrieves a list of surveys for the authenticated company.

#### Authentication
- Requires Bearer token authentication
- Uses `ExternalAuthGuard`

#### Response Format

For all company types:
```json
{
  "count": 1,
  "surveys": [
    {
      "survey_id": 1,
      "survey_name": "Customer Satisfaction Survey",
      "survey_name_internal": "internal_name"
    }
  ]
}
```

For BSH company type (CompanyType.BSH), additional BSH category fields are included:
```json
{
  "count": 1,
  "surveys": [
    {
      "survey_id": 1,
      "survey_name": "Customer Satisfaction Survey",
      "survey_name_internal": "internal_name",
      "survey_BSH_grandparent_category_id": 1,
      "survey_BSH_grandparent_category_name": "Grandparent Category",
      "survey_BSH_parent_category_id": 2,
      "survey_BSH_parent_category_name": "Parent Category",
      "survey_BSH_child_category_id": 3,
      "survey_BSH_child_category_name": "Child Category"
    }
  ]
}
```

#### Database Queries

- **Non-BSH companies**: Simple query to `surv_survey` table
- **BSH companies**: Joins with `bsh_categories` table to get hierarchical category information

#### Files Created

- `survey.controller.ts` - Controller with GET endpoint
- `survey.service.ts` - Business logic for fetching surveys
- `survey.module.ts` - Module configuration
- `dto/survey-response.dto.ts` - Response DTOs with Swagger documentation

#### Integration

The module is automatically registered in the main application through:
- `src/modules/index.ts` - Exports the module
- `src/app.imports.ts` - Imports all modules including the new survey module 