import { Module } from '@nestjs/common';
import { HtmlToImageService } from './html-to-image.service';
import { HtmlToImageController } from './html-to-image.controller';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';

@Module({
  imports: [ModuleLoggerModule.register('html-to-image')],
  controllers: [HtmlToImageController],
  providers: [HtmlToImageService],
  exports: [HtmlToImageService],
})
export class HtmlToImageModule {}
