import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsObject, IsOptional, IsString, Matches } from 'class-validator';

export class HtmlToImageGenerateDto {
  @Type()
  @IsString()
  @ApiProperty({ required: true })
  template: string;

  @Type()
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  fileName?: string;

  @Type()
  @IsOptional()
  @IsString()
  @Matches(/^[a-zA-Z0-9-]+$/, { message: 'Folder name must be alphanumeric' })
  @ApiProperty({ required: false })
  folder?: string;

  @Type()
  @IsObject()
  @ApiProperty({ required: true, type: Object, default: {} })
  parameters: any;
}
