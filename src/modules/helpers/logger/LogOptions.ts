export enum LogLevel {
  ALL,
  LOG,
  ERROR,
  WARN,
  DEBUG,
  VERBOSE,
}

export type LogLevels = 'all' | 'log' | 'error' | 'warn' | 'debug' | 'verbose';

export const logLevelMapper: Record<LogLevel, LogLevels> = {
  [LogLevel.ALL]: 'all',
  [LogLevel.LOG]: 'log',
  [LogLevel.ERROR]: 'error',
  [LogLevel.WARN]: 'warn',
  [LogLevel.DEBUG]: 'debug',
  [LogLevel.VERBOSE]: 'verbose',
};

export interface LogOptions {
  sec_user_id: number;
  logger_name: string;
  request_id: string;
  client_id: string;
}

export enum LogDestination {
  DB = 'database',
  CONSOLE = 'console',
  BOTH = 'both',
}

export interface LoggerConfig {
  destination?: LogDestination;
  queueConcurrency?: number;
  queueMaxRetries?: number;
  queueMaxTimeout?: number;
}

const DEFAULT_CONFIG: LoggerConfig = {
  destination: LogDestination.BOTH,
  queueConcurrency: 1,
  queueMaxRetries: 0,
  queueMaxTimeout: 60 * 1000,
};
