import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LogEventEntity } from '../../database/entities/log-events.entity';
import { AppLogger } from './handlers/app.logger';
import { <PERSON><PERSON><PERSON>ontroller } from './logger.controller';
import { LoggerService } from './logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([LogEventEntity])],
  providers: [AppLogger, LoggerService],
  controllers: [LoggerController],
  exports: [AppLogger, LoggerService],
})
export class LoggerModule {}
