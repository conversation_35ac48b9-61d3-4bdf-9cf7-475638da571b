import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsN<PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';

export class GetLogsQueryDto {
  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  from: string;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  to: string;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  search: string;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false
  })
  solution_name: string;

  @Type()
  @IsNumber()
  @IsOptional()
  @Min(0)
  @ApiProperty({
    required: false,
  })
  page: number = 0;

  @Type()
  @IsNumber()
  @Max(500)
  @Min(5)
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  limit: number = 50;

}
