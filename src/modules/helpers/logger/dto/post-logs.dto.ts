import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateLogBodyDto {
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  @ApiProperty({
    required: true,
    type: Date,
    description: 'Time of the event',
    default: new Date(),
  })
  event_time?: Date;

  @IsString()
  @ApiProperty({
    required: true,
    type: String,
    description: 'Name of the logger',
  })
  logger_name: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: true,
    type: Number,
    description: 'Log level (severity)',
  })
  log_level: number;

  @IsString()
  @ApiProperty({
    required: true,
    type: String,
    description: 'Log message content',
  })
  log_message: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Name of the solution',
    default: '',
  })
  solution_name?: string;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Number,
    description: 'User ID',
    default: 0,
  })
  sec_user_id?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Number,
    description: 'Company ID',
    default: 0,
  })
  sec_company_id?: number;

  @IsObject()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Object,
    description: 'Additional information',
  })
  additional_info?: any;

  constructor() {
    this.event_time = new Date();
    this.solution_name = '';
    this.sec_user_id = 0;
    this.sec_company_id = 0;
  }
}
