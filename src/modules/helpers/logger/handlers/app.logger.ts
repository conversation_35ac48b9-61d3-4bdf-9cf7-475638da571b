import {
  ConsoleLogger,
  Injectable,
  LoggerService,
  Scope,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LogEventEntity } from '../../../database/entities/log-events.entity';
import { LogLevel } from '../LogOptions';

export interface LoggerContextDTO {
  sec_user_id?: number;
  request_id?: string;
  logger_name?: string;
}

@Injectable({ scope: Scope.TRANSIENT })
export class AppLogger implements LoggerService {
  constructor(
    @InjectRepository(LogEventEntity)
    private readonly logsRepository: Repository<LogEventEntity>,
  ) {}
  async log(message: any, context?: string | LoggerContextDTO) {
    let c: LoggerContextDTO =
      typeof context === 'object'
        ? {
            sec_user_id: context.sec_user_id,
            request_id: context.request_id,
            logger_name: context.logger_name,
          }
        : { logger_name: 'node-api.general' };
    if (
      context !== 'NestFactory' &&
      context !== 'InstanceLoader' &&
      context != 'RoutesResolver' &&
      context != 'RouterExplorer'
    ) {
      await this.logsRepository.save(
        {
          event_time: new Date(),
          log_message: message,
          sec_user_id: c.sec_user_id,
          request_id: c.request_id,
          logger_name: c.logger_name,
          additional_info: context,
          solution_name: 'node-api',
          log_level: LogLevel.LOG,
        },
        { listeners: false },
      );
    }
  }
  async error(message: any, context: any) {
    await this.logsRepository.save(
      {
        event_time: new Date(),
        log_message: message,
        sec_user_id: context?.sec_user_id,
        request_id: context?.request_id,
        logger_name: context?.logger_name,
        solution_name: 'node-api',
        log_level: LogLevel.ERROR,
        additional_info: context,
      },
      { listeners: false },
    );
  }
  async warn(message: any, context: any) {
    await this.logsRepository.save(
      {
        event_time: new Date(),
        log_message: message,
        sec_user_id: context?.sec_user_id,
        request_id: context?.request_id,
        logger_name: context?.logger_name || 'node-api.general',
        solution_name: 'node-api',
        log_level: LogLevel.WARN,
      },
      { listeners: false },
    );
  }
  async debug?(message: any, context: any) {
    if (context === 'ClsModule') return;
    await this.logsRepository.save({
      event_time: new Date(),
      log_message: message,
      sec_user_id: context?.sec_user_id,
      request_id: context?.request_id,
      logger_name: context?.logger_name || 'node-api.general',
      solution_name: 'node-api',
      log_level: LogLevel.DEBUG,
    });
  }
  async verbose?(message: any, context: any) {
    await this.logsRepository.save({
      event_time: new Date(),
      log_message: message,
      sec_user_id: context?.sec_user_id,
      request_id: context?.request_id,
      logger_name: context?.logger_name || 'node-api.general',
      solution_name: 'node-api',
      log_level: LogLevel.VERBOSE,
    });
  }
}

export class NestLogger extends ConsoleLogger {
  log(message: string, context: string, ...args) {
    // Filter out the startup logs
    if (
      context !== 'NestFactory' &&
      context !== 'InstanceLoader' &&
      context != 'RoutesResolver' &&
      context != 'RouterExplorer'
    ) {
      super.log(message, context, ...args);
    }
  }

  error(message: any, ...args: any) {
    super.error(message, ...args);
  }

  warn(message: any, ...args: any) {
    super.warn(message, ...args);
  }
}
