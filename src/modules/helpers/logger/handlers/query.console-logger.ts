import { AbstractLogger, LogLevel, Log<PERSON>essage, QueryRunner } from 'typeorm';
import { nonParameterizedQuery } from '@/shared/utils/sql-utils';
import { ConsoleLogger } from '@nestjs/common';
import * as chalk from 'chalk';

export class QueryLogger extends AbstractLogger {
  private readonly logger = new ConsoleLogger('TypeORM');
  private readonly ignoredQueries = [
    'INSERT INTO "log_events"',
    'information_schema',
    'CREATE EXTENSION',
    'current_schema()',
    'COMMIT',
  ];

  protected writeLog(
    level: LogLevel,
    logMessage: LogMessage | LogMessage[],
    queryRunner?: QueryRunner,
  ) {
    const messages = this.prepareLogMessages(logMessage, {
      highlightSql: false,
    });

    for (const message of messages) {
      const logType = message.type ?? level;
      const formattedMessage = message.prefix
        ? `${message.prefix} ${message.message}`
        : message.message;

      switch (logType) {
        case 'log':
        case 'schema-build':
        case 'migration':
          this.logger.log(
            chalk.cyan(`[${logType.toUpperCase()}] `) + formattedMessage,
          );
          break;

        case 'info':
        case 'query':
          this.logger.log(
            chalk.blue(`[${logType.toUpperCase()}] `) + formattedMessage,
          );
          break;

        case 'warn':
        case 'query-slow':
          this.logger.warn(
            chalk.yellow(`[${logType.toUpperCase()}] `) + formattedMessage,
          );
          break;

        case 'error':
        case 'query-error':
          this.logger.error(
            chalk.red(`[${logType.toUpperCase()}] `) + formattedMessage,
          );
          break;
      }
    }
  }

  logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
    if (this.shouldIgnoreQuery(query)) {
      return;
    }

    const requestUrl = this.getRequestUrl(queryRunner);
    const formattedQuery = nonParameterizedQuery(query, parameters);

    this.logger.log(`${requestUrl}${chalk.white(formattedQuery)}`);
  }

  private shouldIgnoreQuery(query: string): boolean {
    return this.ignoredQueries.some(
      (ignored) => typeof query === 'string' && query.includes(ignored),
    );
  }

  private getRequestUrl(queryRunner?: QueryRunner): string {
    if (queryRunner?.data?.['request']?.url) {
      return chalk.yellow(`(${queryRunner.data['request'].url}) `);
    }
    return '';
  }
}
