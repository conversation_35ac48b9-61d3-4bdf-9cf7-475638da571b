import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { GetLogsQueryDto } from './dto/get-logs.dto';
import { CreateLogBodyDto } from './dto/post-logs.dto';
import { LoggerService } from './logger.service';
import { Auth } from '@/shared/decorators';

@ApiTags('Logs')
@Controller({
  path: 'log_message',
  version: '2',
})
@Auth()
export class LoggerController {
  constructor(private loggerService: LoggerService) {}

  @Get()
  getLogs(@Query() query: GetLogsQueryDto) {
    return this.loggerService.getLogs(query);
  }

  @Post()
  createLog(@Body() body: CreateLogBodyDto) {
    return this.loggerService.createLog(body);
  }
}
