import { Module, DynamicModule } from '@nestjs/common';
import { ModuleLogger } from './module-logger.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LogEventEntity, LogManagementEntity } from '@entities';
import { LoggerConfig } from '../LogOptions';

@Module({})
export class ModuleLoggerModule {
  static register(prefix: string, config?: LoggerConfig): DynamicModule {
    return {
      module: ModuleLoggerModule,
      imports: [
        TypeOrmModule.forFeature([LogEventEntity, LogManagementEntity]),
      ],
      providers: [
        {
          provide: 'MODULE_NAME',
          useValue: prefix,
        },
        {
          provide: 'LOGGER_CONFIG',
          useValue: config,
        },
        {
          provide: ModuleLogger,
          useClass: ModuleLogger,
        },
      ],
      exports: [ModuleLogger],
    };
  }
}
