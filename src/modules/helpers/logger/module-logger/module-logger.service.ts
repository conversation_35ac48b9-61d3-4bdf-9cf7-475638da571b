import { LogEventEntity, LogManagementEntity } from '@entities';
import { Inject, Injectable, ConsoleLogger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  LogDestination,
  LoggerConfig,
  LogLevel,
  logLevelMapper,
  LogLevels,
} from '../LogOptions';
import { v4 as uuid } from 'uuid';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import * as chalk from 'chalk';
const Queue = require('better-queue');

const DEFAULT_CONFIG: LoggerConfig = {
  destination: LogDestination.DB,
  queueConcurrency: 1,
  queueMaxRetries: 0,
  queueMaxTimeout: 60 * 1000,
};

@Injectable()
export class ModuleLogger {
  static modules: LogManagementEntity[];
  static loggerNames: string[] = [];
  static applicationPart: string = 'node-api';

  private queue: any;
  private config: LoggerConfig;
  private readonly prefix: string;
  private readonly consoleLogger: ConsoleLogger;

  constructor(
    private cls: ClsService,
    @Inject('MODULE_NAME') prefix: string,
    @InjectRepository(LogManagementEntity)
    private logMgmtRepo: Repository<LogManagementEntity>,
    @InjectRepository(LogEventEntity)
    private logRepo: Repository<LogEventEntity>,
    @Inject('LOGGER_CONFIG') loggerConfig?: LoggerConfig,
  ) {
    this.prefix = `${ModuleLogger.applicationPart}.${prefix}`;
    this.consoleLogger = new ConsoleLogger(this.prefix);
    ModuleLogger.loggerNames.push(this.prefix);

    this.config = {
      destination: loggerConfig?.destination || DEFAULT_CONFIG.destination,
      queueConcurrency:
        loggerConfig?.queueConcurrency || DEFAULT_CONFIG.queueConcurrency,
      queueMaxRetries:
        loggerConfig?.queueMaxRetries || DEFAULT_CONFIG.queueMaxRetries,
      queueMaxTimeout:
        loggerConfig?.queueMaxTimeout || DEFAULT_CONFIG.queueMaxTimeout,
    };
    this.initializeQueue();
  }

  private initializeQueue(): void {
    this.queue = new Queue(this.queueHandler.bind(this), {
      concurrent: this.config.queueConcurrency,
      maxRetries: this.config.queueMaxRetries,
      maxTimeout: this.config.queueMaxTimeout,
    });
  }

  private async queueHandler(
    { level, message, context, prefix }: any,
    cb: any,
  ) {
    try {
      const shouldLogToConsole =
        [LogDestination.CONSOLE, LogDestination.BOTH].includes(
          this.config.destination,
        ) || level === LogLevel.ERROR;

      const shouldLogToDb =
        [LogDestination.DB, LogDestination.BOTH].includes(
          this.config.destination,
        ) || level === LogLevel.ERROR;

      if (shouldLogToConsole) {
        this.logToConsole(level, message, context);
      }

      if (shouldLogToDb) {
        await this.logToDatabase(level, prefix, message, context);
      }

      cb(null);
    } catch (error) {
      console.error('Logger queue handler error:', error);
      cb(error);
    }
  }

  private logToConsole(level: LogLevel, message: string, context?: any): void {
    const contextStr = context ? JSON.stringify(context) : '';

    switch (level) {
      case LogLevel.LOG:
        this.consoleLogger.log(`${message} ${chalk.gray(contextStr)}`);
        break;
      case LogLevel.ERROR:
        this.consoleLogger.error(
          `${message}`,
          context?.stack || '',
          chalk.gray(contextStr),
        );
        break;
      case LogLevel.WARN:
        this.consoleLogger.warn(`${message} ${chalk.gray(contextStr)}`);
        break;
      case LogLevel.DEBUG:
        this.consoleLogger.debug(`${message} ${chalk.gray(contextStr)}`);
        break;
      case LogLevel.VERBOSE:
        this.consoleLogger.verbose(`${message} ${chalk.gray(contextStr)}`);
        break;
    }
  }

  private async logToDatabase(
    log_level: LogLevel,
    logger_name: string,
    message: string,
    context?: any,
  ): Promise<boolean> {
    if (!this.checkLogger(logger_name, logLevelMapper[log_level])) {
      return false;
    }

    const caller = ModuleLogger.getCaller();
    const authData = await this.cls.get<AuthorizedData>(ClsProperty.user);
    const user = authData?.user;
    const company = authData?.company;

    let additional_info: any = {
      caller_file: caller.file,
      caller_function: caller.function,
    };

    if (context && typeof context === 'object' && !Array.isArray(context)) {
      additional_info = { ...additional_info, ...context };
    } else {
      additional_info = { ...additional_info, context };
    }

    await this.logRepo.save(
      this.logRepo.create({
        event_time: new Date(),
        log_message: message,
        sec_user_id: user?.id,
        sec_company_id: company?.id,
        request_id: this.cls.getId(),
        logger_name,
        additional_info,
        solution_name: ModuleLogger.applicationPart,
        log_level,
      }),
    );

    return true;
  }

  log(message: string, context?: any): void {
    this.queue.push({
      level: LogLevel.LOG,
      message,
      context,
      prefix: this.prefix,
    });
  }

  error(message: string, context?: any): void {
    this.queue.push({
      level: LogLevel.ERROR,
      message,
      context,
      prefix: this.prefix,
    });
  }

  warn(message: string, context?: any): void {
    this.queue.push({
      level: LogLevel.WARN,
      message,
      context,
      prefix: this.prefix,
    });
  }

  debug(message: string, context?: any): void {
    this.queue.push({
      level: LogLevel.DEBUG,
      message,
      context,
      prefix: this.prefix,
    });
  }

  verbose(message: string, context?: any): void {
    this.queue.push({
      level: LogLevel.VERBOSE,
      message,
      context,
      prefix: this.prefix,
    });
  }

  private checkLogger(logger_name: string, log_level: LogLevels): boolean {
    if (!ModuleLogger.modules) {
      console.warn('WARNING! Logger modules are empty. Logs are not saving');
      return false;
    }

    let module = ModuleLogger.modules.find(
      (m) =>
        m.logger_name === logger_name &&
        m.application_part === ModuleLogger.applicationPart,
    );

    const isActive = module?.logger_active;
    const isSameLevel =
      !module?.log_level ||
      ModuleLogger.checkLogLevel(module.log_level, log_level);

    if (isActive && isSameLevel) {
      return true;
    } else if (!module) {
      console.error('WARNING! Log module is not found for', logger_name);
    }

    return false;
  }

  async createLogManagement(logger_name: string): Promise<void> {
    try {
      const item = await this.logMgmtRepo.save(
        this.logMgmtRepo.create({
          log_management_id: uuid(),
          logger_name,
          logger_active: 0,
          application_part: ModuleLogger.applicationPart,
        }),
      );

      ModuleLogger.modules.push(item);
    } catch (error) {
      console.error('Error creating log management:', error);
      throw error;
    }
  }

  setConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
    this.initializeQueue();
  }

  getConfig(): LoggerConfig {
    return { ...this.config };
  }

  static getCaller(): { file: string; function: string } {
    const stack = new Error().stack.split('\n');
    const caller = stack[4].trim().split(' ');
    const fn = caller[1].replace(/\(|@/g, '');
    const file = caller[2].replace(/\(|\/|\\|@/g, '/');
    return {
      file: file?.split('/')?.pop()?.split(':').shift(),
      function: fn,
    };
  }

  static checkLogLevel(selectedLevel: LogLevels, logLevel: LogLevels): boolean {
    if (selectedLevel === 'all') return true;
    const levels = ['debug', 'info', 'warning', 'error'];

    const selectedLevelIndex = levels.indexOf(selectedLevel);
    const logLevelIndex = levels.indexOf(logLevel);

    return logLevelIndex >= selectedLevelIndex;
  }
}
