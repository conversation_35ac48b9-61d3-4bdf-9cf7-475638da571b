import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import * as moment from 'moment';
import { v4 as uuidv4 } from 'uuid';

import {
  ModWccsImportMapEntity,
  ModWccsImportTemplateEntity,
  ModWccsMetaTablesEntity,
} from '@entities';

import {
  bshMappingFields,
  employeeFields,
} from '../../../config/contents/data-table.contents';

import { DBHelperService } from '../../core/db-helper/db-helper.service';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import {
  convertToDelete,
  convertToInsert,
  convertToUpdate,
  queryConvert,
} from '@/shared/utils';
@Injectable()
export class ImportService {
  constructor(
    private cls: ClsService,
    private dbHelper: DBHelperService,
    @InjectRepository(ModWccsImportTemplateEntity)
    private readonly importTemplateRepo: Repository<ModWccsImportTemplateEntity>,
  ) {}
  private convert(
    data: any[],
    importMap: ModWccsImportMapEntity[],
    importTemplate: Partial<ModWccsImportTemplateEntity>,
  ) {
    importMap.forEach((im) => {
      switch (im.metafield?.metafield_type) {
        case 'Date': {
          const date_format = importTemplate.template_date_format;
          const dateSeparator = importTemplate.template_date_separator;
          const timeSeparator = importTemplate.template_time_separator;
          let resultFormat = '';
          let current = date_format[0];
          for (let i = 0; i < date_format.length; ++i) {
            if (
              ['d', 'm', 'y'].find((e) => e === date_format[i].toLowerCase())
            ) {
              if (current != date_format[i]) resultFormat += dateSeparator;
              resultFormat += date_format[i].toUpperCase();
              current = date_format[i];
            }
          }
          resultFormat += ` H${timeSeparator}m`;
          data.forEach((row) => {
            // const dateString: string = row[im.map_source_field];
            row[im.map_source_field] = moment(
              row[im.map_source_field],
              resultFormat,
            ).toDate();
          });
        }
      }
    });
  }

  private setContextData(
    data: any[],
    user: AuthorizedData,
    maps: ModWccsImportMapEntity[],
  ) {
    maps.forEach((item) => {
      let df = item.metafield;
      // if (!df.metafield_is_system_field) return;
      return;
      switch (df.metafield_name) {
        case 'creator': {
          data.forEach((row) => {
            row[df.metafield_id] = null; //user.id
          });
          break;
        }

        case 'created_by': {
          data.forEach((row) => {
            row[df.metafield_name] = null; //  user.sec_user_id;
          });
          break;
        }

        case 'creation_date': {
          data.forEach((row) => {
            row[df.metafield_name] = new Date();
          });
          break;
        }

        case 'modification_date': {
          data.forEach((row) => {
            row[df.metafield_name] = new Date();
          });
          break;
        }

        case 'sec_company_id': {
          data.forEach((row) => {
            row[df.metafield_name] = null; //user.sec_company_id;
          });
          break;
        }
      }
    });

    return data;
  }

  public translateTablename(name: string) {
    switch (true) {
      case name === 'blacklist':
        return ['customer_blacklist'];
      case name === 'coupon':
        return ['surv_coupon'];
      case name === 'survey_contacts':
        return ['surv_imported_contacts'];
      case employeeFields.includes(name):
        return ['techgrp', name];
      case bshMappingFields.includes(name):
        return ['bsh_mapping', name];
      default:
        return [name, null];
    }
  }

  public async findTablePrimaryKey(tableName: string) {
    let item = await this.importTemplateRepo.query(
      `SELECT               
    pg_attribute.attname, 
    format_type(pg_attribute.atttypid, pg_attribute.atttypmod) 
  FROM pg_index, pg_class, pg_attribute, pg_namespace 
  WHERE 
    pg_class.oid = $1::regclass AND 
    indrelid = pg_class.oid AND 
    nspname = 'public' AND 
    pg_class.relnamespace = pg_namespace.oid AND 
    pg_attribute.attrelid = pg_class.oid AND 
    pg_attribute.attnum = any(pg_index.indkey)
   AND indisprimary`,
      [tableName],
    );
    return [item[0]?.attname, item[0]?.format_type];
  }

  public async save(
    fields: ModWccsImportMapEntity[],
    data: any[],
    template: Partial<ModWccsImportTemplateEntity>,
    metatable: ModWccsMetaTablesEntity,
    table?: string,
  ) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let [tableName, fieldName] = this.translateTablename(
      table || metatable.metatable_name,
    );
    const [primaryKey, primaryKeyType] = await this.findTablePrimaryKey(
      tableName,
    );
    let nextId = -1;
    if (primaryKeyType.includes('integer')) {
      nextId = await this.dbHelper.getNextId(tableName, primaryKey);
    }

    this.convert(data, fields, template);
    // this.setContextData(data, null, fields);
    // const map = fields.reduce((acc, f) => {
    //   acc[f.map_destination_field] = f;
    //   return acc;
    // }, {});

    let keyField = fields.find((i) => i.map_key == 1);
    let keyColumn = keyField?.metafield?.metafield_name;

    let existsRecords = [];
    if (keyField?.metafield) {
      existsRecords = await this.importTemplateRepo.query(
        `SELECT ${primaryKey}, ${keyColumn} from ${tableName} WHERE sec_company_id =$1 AND ${keyColumn} = ANY($2)`,
        [company.id, data.map((row) => row[keyField.map_source_field])],
      );
    }

    let tkey = tableName == 'techgrp' ? 'techgrp_type' : 'map_field';
    let list = data.map((row) => {
      let obj: any = {
        table: tableName,
        insert: !existsRecords.find(
          (item) => item[keyColumn] == row[keyField.map_source_field],
        ),
        keyField: keyColumn,
        data: {
          sec_company_id: company.id,
        },
      };
      if (nextId === -1) {
        obj.data[primaryKey] = uuidv4();
      }

      if (fieldName) {
        obj.data[tkey] = fieldName;
      }

      if (tableName === 'bsh_mapping') {
        if (typeof obj.data.isactive === 'undefined') obj.data.isactive = 1;
        // } else if (tableName === 'surv_survey_coupon') {
        //   if (obj.data.coupon_id) {
        //     obj.data.surv_coupon_id = obj.data.coupon_id;
        //     delete obj.data.coupon_id;
        //   }
      }

      for (let field of fields) {
        let key = field.metafield.metafield_name;
        let skey = field.map_source_field;
        let value = skey && row[skey] ? row[skey] : skey;

        if (tableName === 'surv_coupon' && key === 'start_date') {
          let isValid = row.start_date && moment(row.start_date).isValid();
          if (!isValid) value = new Date();
        }
        obj.data[key] = value;
      }

      if (tableName === 'surv_coupon' && !obj.data.brand_id) {
        obj.data.brand_id = row.brand_id;
      }
      return obj;
    });
    let insertData = await this.insertData(list.filter((obj) => obj.insert));
    let updateData = await this.updateData(
      list.filter((obj) => !obj.insert),
      true,
    );

    return {
      insertData,
      updateData,
    };
  }

  private async insertData(list: any[]) {
    if (list.length == 0) return false;
    let [sql, params] = convertToInsert(list);
    if (!sql) return false;

    return await this.importTemplateRepo.query(sql, params);
  }
  private async updateData(list: any[], markDelete: boolean = false) {
    if (list.length == 0) return false;
    if (markDelete == true) {
      await this.deleteData(list);
      return this.insertData(list);
    }
    let convertSql = list.map((obj) =>
      convertToUpdate(
        obj.table,
        [obj.keyField, 'sec_company_id'],
        obj.data,
        obj.keyField,
      ),
    );

    let cParams = convertSql.reduce((prev, item) => {
      prev = { ...prev, ...item[1] };
      return prev;
    }, {});

    let [sql, params] = queryConvert(
      convertSql.map((item) => item[0]).join(';'),
      cParams,
    );

    return await this.importTemplateRepo.query(sql, params);
  }

  private async deleteData(list: any[]) {
    if (list.length == 0) return false;
    let [sql, params] = convertToDelete(list, [
      list[0].keyField,
      'sec_company_id',
    ]);

    return await this.importTemplateRepo.query(sql, params);
  }
}
