import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';

export class UpsertImportTemplateQueryDto {
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Template ID',
  })
  id: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Table name',
  })
  table?: string;
}

export class UpsertImportTemplateItem {
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Template item ID',
  })
  id: string;

  @IsString()
  @IsOptional()
  @MaxLength(200)
  @ApiProperty({
    required: false,
    nullable: true,
    type: String,
    description: 'Display label',
    maxLength: 200,
  })
  label: string;

  @IsString()
  @IsOptional()
  @<PERSON>Length(5)
  @ApiProperty({
    required: false,
    nullable: true,
    type: String,
    description: 'Date format string',
    maxLength: 5,
  })
  date_format: string;
}

export class MappedFieldSourceDto {
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Source field ID',
  })
  id: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Source field name',
  })
  name: string;
}

export class MappedFieldTarget {
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Target field ID',
  })
  id: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Target field name',
  })
  name: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Target field title',
  })
  title: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Target field type',
  })
  type: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Number,
    description: 'Target field limit',
  })
  limit: number;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Whether field is unique',
  })
  unique: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Whether field is mandatory',
  })
  mandatory: boolean;
}

export class MappedFieldDto {
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Mapped field ID',
  })
  id: string;

  @Type(() => MappedFieldSourceDto)
  @ValidateNested()
  @IsObject()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: MappedFieldSourceDto,
    description: 'Source field information',
  })
  source: MappedFieldSourceDto;

  @Type(() => MappedFieldTarget)
  @ValidateNested()
  @IsObject()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: MappedFieldTarget,
    description: 'Target field information',
  })
  target: MappedFieldTarget;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Whether this is a key field',
  })
  isKeyField: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Whether field was auto-matched',
  })
  autoMatched: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Whether field has valid limit',
  })
  hasValidLimit: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Whether mapping needs confirmation',
  })
  confirmMapping: boolean;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Number,
    description: 'Maximum length found in data',
  })
  maxLengthFound: number;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Whether to ignore this mapping',
  })
  ignoreMapping: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Whether field is collapsed in UI',
  })
  collapsed: boolean;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: String,
    description: 'Mapped value',
  })
  mapped_value: string;
}

export class UpsertImportTemplateBodyDto {
  @Type(() => UpsertImportTemplateItem)
  @ValidateNested()
  @IsObject()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: UpsertImportTemplateItem,
    description: 'Template information',
  })
  template: UpsertImportTemplateItem;

  @Type(() => MappedFieldDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: MappedFieldDto,
    isArray: true,
    nullable: true,
    description: 'Field mappings',
  })
  mapped_fields: MappedFieldDto[];

  @IsArray()
  @ApiProperty({
    type: Object,
    isArray: true,
    description: 'Import data',
  })
  @IsOptional()
  data: any[];

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: true,
    nullable: true,
    type: String,
    description: 'Metatable ID',
  })
  metatable_id: string;
}
