import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, FindOptionsWhere, In, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { v4 as uuidv4 } from 'uuid';

import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import { UpsertImportTemplateBodyDto } from './dto/post-import-template.dto';

import { ImportService } from './import.service';
import { TranslateService } from '@libs/translate/translate.service';

import {
  ModWccsImportMapEntity,
  ModWccsImportTemplateEntity,
  ModWccsMetaFieldsEntity,
  ModWccsMetaTablesEntity,
} from '@entities';

import { stringCapitalize } from '@/shared/utils';
import { ClsProperty } from '@/config/contents';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';
import { I18nService } from 'nestjs-i18n';

export class ImportTemplateService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private translate: TranslateService,
    private readonly i18n: I18nService,
    @InjectRepository(ModWccsImportTemplateEntity)
    private readonly importTemplateRepo: Repository<ModWccsImportTemplateEntity>,
    @InjectRepository(ModWccsImportMapEntity)
    private readonly importMapsRepo: Repository<ModWccsImportMapEntity>,
    @InjectRepository(ModWccsMetaFieldsEntity)
    private readonly metaFieldsRepo: Repository<ModWccsMetaFieldsEntity>,
    @InjectRepository(ModWccsMetaTablesEntity)
    private readonly metaTableRepo: Repository<ModWccsMetaTablesEntity>,
    private importService: ImportService,
  ) {}

  async getTemplates(table: string) {
    let [tableName] = this.importService.translateTablename(table);
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`getTemplates for ${table}`, { company });

    let metatable = await this.getMetatable(
      {
        metatable_name: tableName,
        metatable_company_type: company.type,
      },
      ['metatable_id'],
    );
    if (!metatable)
      throw new NotFoundException(`props.import_template.metatable_not_found`);

    let templates = await this.importTemplateRepo.find({
      relations: ['metatable'],
      where: {
        sec_company_id: company.id,
        metatable: {
          metatable_name: tableName,
          metatable_company_type: company.type,
        },
      },
    });
    let import_fields = await this.metaFieldsRepo.find({
      relations: ['metatable'],
      where: {
        metatable: {
          metatable_name: tableName,
          metatable_id: metatable.metatable_id,
          ...(table !== 'blacklist'
            ? { metatable_company_type: company.type }
            : {}),
        },
        metafield_isfield: 1,
      },
    });

    let translateFields = import_fields.map((item) =>
      item.metafield_title.replace('i18n:', ''),
    );

    let translates = await this.translate.dbs(
      translateFields?.length ? translateFields : ['null'],
    );

    let importFields = import_fields.map((item) => {
      let title = item.metafield_title.replace('i18n:', '');
      let findTitle = translates.find((i) => i.key == title);
      title = findTitle?.value || title;
      let obj = {
        id: item.metafield_id,
        name: item.metafield_name,
        title: stringCapitalize(title),
        type: item.metafield_type,
        limit: item.metafield_limit,
        unique: !!item.metafield_isunique,
        mandatory: !!item.metafield_ismandatory,
      };

      if (
        !obj.limit &&
        (obj.name === 'start_date' || obj.name === 'end_date')
      ) {
        delete obj.limit;
      }

      return obj;
    });

    if (table === 'coupon') {
      importFields = importFields.filter((item) => item.name !== 'brand_id');
    }

    let result = {
      metatable_id: metatable.metatable_id,
      templates: templates.map((item) => {
        return {
          id: item.template_id,
          name: item.template_name,
          file_seperator: item.template_file_separator,
          file_type: item.template_file_type,
          date_format: item.template_date_format,
        };
      }),
      import_fields: importFields,
    };

    this.logger.debug(`getTemplates result for ${table}`, { result });

    return result;
  }
  async getTemplate(id: string) {
    return await this.importTemplateRepo.findOne({
      relations: ['metatable'],
      where: {
        template_id: id,
      },
    });
  }
  getMaps(template_id: string) {
    return this.importMapsRepo.find({
      relations: ['metafield'],
      where: {
        template_id: template_id,
      },
    });
  }

  getMetatable(
    where: string | FindOptionsWhere<ModWccsMetaTablesEntity>,
    select?: Array<keyof ModWccsMetaTablesEntity>,
  ) {
    return this.metaTableRepo.findOne({
      where: typeof where === 'string' ? { metatable_id: where } : where,
      select,
    });
  }

  async getTemplateWithFields(id: string) {
    let template = await this.importTemplateRepo.findOne({
      where: { template_id: id },
    });

    return {
      id: template.template_id,
      mapping_template_title: template.template_name,
      mapped_fields: template.mapped_fields,
    };
  }

  async upsertTemplate({
    template,
    mapped_fields,
    metatable_id,
  }: Omit<UpsertImportTemplateBodyDto, 'data'>) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    const { id, label, date_format } = template;

    let template_id = id;

    if (label) {
      // upsert template
      let payload: Partial<ModWccsImportTemplateEntity> = {
        template_id: id || uuidv4().toUpperCase(),
        sec_company_id: company.id,
        template_name: label,
        mapped_fields,
      };

      if (date_format?.length > 0) payload.template_date_format = date_format;
      if (metatable_id?.length > 0) payload.metatable_id = metatable_id;

      let result: any = await this.importTemplateRepo.save(payload);
      if (id) {
        await this.importMapsRepo.delete({ template_id });
      }

      this.logger.debug(`upsertTemplate templateData result ${id}`, { result });
    }

    mapped_fields = mapped_fields.filter((item) => !item.ignoreMapping);

    let maps: DeepPartial<ModWccsImportMapEntity>[] = mapped_fields
      .filter((field) => field.source && field.target)
      .map((item) => {
        return {
          map_id: item.id || uuidv4().toUpperCase(),
          template_id,
          metafield_id: item.target.id,
          map_source_field: item.source.id,
          map_key: +item.isKeyField || 0,
          map_ismandatory: +item.target.mandatory,
        };
      });
    if (template_id) {
      await this.importMapsRepo.save(maps);
    }
    let metafields = await this.metaFieldsRepo.find({
      where: {
        metafield_id: In(maps.map((i) => i.metafield_id)),
      },
    });

    maps = maps.map((item) => {
      let metafield = metafields.find(
        (i) => i.metafield_id == item.metafield_id,
      );
      return {
        ...item,
        metafield,
      };
    });
    return {
      maps,
      template: template_id && (await this.getTemplate(template_id)),
    };
  }

  async import(table: string, body: UpsertImportTemplateBodyDto) {
    let { data, metatable_id } = body;

    const { id, label } = body.template;

    this.logger.debug(`upsertTemplate id ${id || 'new'}`, { body, table });

    let { maps, template } = await this.upsertTemplate(body);
    let metatable = await this.getMetatable(metatable_id);
    if (maps.find((i) => !i.metafield))
      throw new NotFoundException(`props.import_template.metafield_not_found`);
    try {
      let { insertData, updateData } = await this.importService.save(
        maps as any,
        data,
        template,
        metatable,
        table,
      );
      let msg;
      if (id) msg = this.i18n.t('props.import_template.template_updated');
      else if (label)
        msg = this.i18n.t('props.import_template.template_created');
      else msg = this.i18n.t('props.import_template.data_imported');

      let result = {
        msg,
        status: id ? HttpStatus.ACCEPTED : HttpStatus.CREATED,
        new: insertData?.length || 0,
        updated: updateData?.length || 0,
        template,
        fields: maps,
      };
      this.logger.debug(`upsertTemplate result ${id}`, {
        result,
        maps,
        template,
      });
      return result;
    } catch (e) {
      this.logger.debug(`upsertTemplate error ${id}`, {
        error: e?.message || e,
      });

      throw new HttpException(e.message, 500);
    }
  }
}
