import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { GetImportTemplateQueryDto } from './dto/get-import-template.dto';
import {
  UpsertImportTemplateBodyDto,
  UpsertImportTemplateQueryDto,
} from './dto/post-import-template.dto';
import { ImportTemplateService } from './import-template.service';

import { Auth } from '@/shared/decorators';
@ApiTags('Import Template')
@Controller({
  path: 'import_template',
  version: '2',
})
@Auth()
export class ImportTemplateController {
  constructor(private importTemplateService: ImportTemplateService) {}

  @Get()
  getTemplates(@Query() { table, id }: GetImportTemplateQueryDto) {
    if (table?.length > 0)
      return this.importTemplateService.getTemplates(table);
    else return this.importTemplateService.getTemplateWithFields(id);
  }

  @Post()
  import(
    @Body() body: UpsertImportTemplateBodyDto,
    @Query() { id, table }: UpsertImportTemplateQueryDto,
  ) {
    if (id?.length > 0) {
      body.template.id = id;
    }
    return this.importTemplateService.import(table, body);
  }
}
