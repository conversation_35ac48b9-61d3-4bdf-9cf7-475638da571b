import { Module, DynamicModule } from '@nestjs/common';
import { DBQueueService } from './queue.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueEntity } from '@entities';

@Module({})
export class DBQueueModule {
  static register(prefix: string): DynamicModule {
    return {
      module: DBQueueModule,
      imports: [TypeOrmModule.forFeature([QueueEntity])],
      providers: [
        {
          provide: 'MODULE_NAME',
          useValue: prefix,
        },
        {
          provide: DBQueueService,
          useClass: DBQueueService,
        },
      ],
      exports: [DBQueueService],
    };
  }
}
