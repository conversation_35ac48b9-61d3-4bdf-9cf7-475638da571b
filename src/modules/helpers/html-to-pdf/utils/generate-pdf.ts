import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import hb from 'handlebars';

export async function generatePdf(browser: Browser, file: any, options: any) {
  const page = await browser.newPage();
  try {
    await page.setCacheEnabled(false);
    if (file.content) {
      let data = file.content;
      console.log('Compiling the template with handlebars');
      const template = hb.compile(data, { strict: true });
      const html = template(data);

      await page.setContent(html, {
        waitUntil: 'networkidle0',
      });
    } else {
      await page.goto(file.url, {
        waitUntil: ['load', 'networkidle0'],
      });
    }

    let response = await page.pdf(options);

    closePage(page);

    return Buffer.from(Object.values(response));
  } catch (err) {
    closePage(page);
    return null;
  }
}

async function closePage(page: Page) {
  try {
    const client = await page.target().createCDPSession();
    await client.send('Network.clearBrowserCache');
    page.on('close', () => console.log('PUPPETEER: page closed'));
    await page.close();
  } catch {
    await page.close();
  }
  page.off('request');
  page.off('close');
  page.off('pageerror');
}
