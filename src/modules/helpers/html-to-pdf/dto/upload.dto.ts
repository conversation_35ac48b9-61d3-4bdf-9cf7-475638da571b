import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';

export class UploadDto {
  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'File to upload',
  })
  file: any;

  @ApiProperty({
    required: false,
    default: true,
    type: Boolean,
    description: 'Whether to create one record per page',
  })
  @Transform(({ value }: TransformFnParams) => value === 'true')
  @IsBoolean()
  @IsOptional()
  recordPerPage: boolean = true;
}
