import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Req,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiConsumes,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { HTMLFileFilter } from './utils/file-filter';
import { UploadDto } from './dto/upload.dto';
import { HtmlToPdfService } from './html-to-pdf.service';
import { Request } from 'express';
import WebhookGuard from '@/modules/webhook/webhook.guard';

@Controller({
  path: 'converter',
  version: '2',
})
@ApiBasicAuth()
@UseGuards(WebhookGuard)
@ApiTags('Converter')
export class HtmlToPdfController {
  constructor(private readonly converterService: HtmlToPdfService) {}

  @Post('upload')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: HTMLFileFilter,
      limits: { files: 1 },
    }),
  )
  @ApiOperation({
    description:
      'This endpoint is used for converting uploaded HTML page to PDF',
  })
  async uploadHTML(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadDto,
    @Req() req: Request,
  ) {
    if (!file?.buffer)
      throw new HttpException('File is not provided', HttpStatus.BAD_REQUEST);

    let content = this.converterService.removeUnusedTags(file.buffer);

    if (uploadDto.recordPerPage == true) {
      content = this.converterService.appendPerPageStyle(content);
    }

    // writeFileSync('test.html', content);
    let result = await this.converterService.convertToPDF(content);
    let fileName = await this.converterService.createRandomFile(result);

    let url = `${req.protocol}://${req.get('Host')}/uploads/pdf/${fileName}`;

    return {
      success: true,
      message: 'Uploaded',
      url,
    };
  }
}
