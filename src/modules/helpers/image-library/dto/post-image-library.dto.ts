import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
} from 'class-validator';

export class CreateImageLibraryBodyDto {
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @ApiProperty({
    required: true,
    type: String,
    description: 'Library name',
    minLength: 1,
    maxLength: 100,
  })
  name: string;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
    type: Number,
    description: 'Parent folder ID',
    nullable: true,
  })
  parent_id?: number;
}

export class RenameImageLibraryBodyDto {
  @Type(() => Number)
  @IsNumber()
  @ApiProperty({
    required: true,
    type: Number,
    description: 'Folder ID to rename',
  })
  folder_id: number;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @ApiProperty({
    required: true,
    type: String,
    description: 'New folder name',
    minLength: 1,
    maxLength: 100,
  })
  name: string;
}

export class CreateImageBodyDto {
  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'Image file to upload',
  })
  image: any;
}
