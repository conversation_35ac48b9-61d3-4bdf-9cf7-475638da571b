import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImageLibraryFolderEntity } from '../../database/entities/image-library-folder.entity';
import { ImageLibraryEntity } from '../../database/entities/image-library.entity';
import { ImageLibraryController } from './image-library.controller';
import { ImageLibraryService } from './image-library.service';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ImageLibraryEntity, ImageLibraryFolderEntity]),
    ModuleLoggerModule.register('image-library'),
  ],
  controllers: [ImageLibraryController],
  providers: [ImageLibraryService],
})
export class ImageLibraryModule {}
