import {
  ConflictException,
  ForbiddenException,
  HttpException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents';
import { CompanyType } from '@/shared/enums';

import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';

import {
  CreateImageLibraryBodyDto,
  RenameImageLibraryBodyDto,
} from './dto/post-image-library.dto';

import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';

import { ImageLibraryEntity, ImageLibraryFolderEntity } from '@entities';
import { groupNested } from '@/shared/utils';
import { saveImage } from './utils/file.utils';
import { I18nService } from 'nestjs-i18n';

export class ImageLibraryService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private i18n: I18nService,
    @InjectRepository(ImageLibraryEntity)
    private readonly imageLibRepo: Repository<ImageLibraryEntity>,
    @InjectRepository(ImageLibraryFolderEntity)
    private readonly imageFolderRepo: Repository<ImageLibraryFolderEntity>,
  ) {}

  async getNestedContent() {
    this.logger.debug(`getNestedContent`);
    let list: any = await this.getFolderList();
    let images = await this.getFolderImages(list.map((item) => item.id));

    list = list.map((folder) => {
      return {
        ...folder,
        content: images
          .filter((image) => image.folder_id == folder.id)
          .map((image) => ({ ...image, folder_id: undefined })),
      };
    });
    let result = await this.getNestedFolderList(list);

    this.logger.debug('getNestedContent result', { result, list, images });
    return {
      global: result
        .filter((i) => i.global)
        .map((i) => ({ ...i, global: undefined })),
      local: result
        .filter((i) => !i.global)
        .map((i) => ({ ...i, global: undefined })),
    };
  }

  async getNestedFolderList(list?: any[]) {
    this.logger.debug(`getNestedFolderList`, { list });
    list = list || (await this.getFolderList());
    let nestedList = groupNested(list, 'id', 'parent_id', 'content', true);
    this.logger.debug(`getNestedFolderList result`, { result: nestedList });

    return nestedList;
  }

  async getFolderList() {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug(`getFolderList for company ${company.id}`);

    let global_cid = company.type == CompanyType.BSH ? 25 : 1;
    let list = await this.imageFolderRepo
      .createQueryBuilder('f')
      .select(['f.id', 'f.parent_id', 'f.name', 'f.sec_company_id'])
      .where(
        new Brackets((qb) => {
          qb.where('sec_company_id = :global_cid', { global_cid });
          qb.orWhere('sec_company_id =:cid', { cid: company.id });
          return qb;
        }),
      )
      .getMany();
    let result = list.map((item) => {
      if (item.sec_company_id == global_cid) item.global = true;
      delete item.sec_company_id;
      return item;
    });

    this.logger.debug(`getFolderList result for company ${company.id}`, {
      result,
    });

    return result;
  }

  async getFolderImages(ids: number[]) {
    this.logger.debug(`getFolderImages for ids ${ids.join(',')}`);
    ids = Array.isArray(ids) ? ids : [ids];
    if (ids.length == 0) return [];

    let images = await this.imageLibRepo
      .createQueryBuilder('i')
      .where('i.folder_id IN(:...ids)', { ids })
      .select(['i.id', 'i.folder_id', 'i.key', 'i.name'])
      .getMany();

    this.logger.debug(`getFolderImages result for ids ${ids.join(',')}`, {
      images,
    });

    return images;
  }
  getFolder(id: number) {
    return this.imageFolderRepo
      .createQueryBuilder()
      .where('id =:id', { id })
      .getOne();
  }

  async createFolder(body: CreateImageLibraryBodyDto) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug(`createFolder`, { body, company, user: user.id });

    let { name, parent_id } = body;
    if (parent_id === 0) parent_id = null;

    let isExists = await this.imageFolderRepo
      .createQueryBuilder()
      .where('name =:name', { name })
      .andWhere('parent_id =:parent_id', { parent_id })
      .andWhere('sec_company_id =:cid', {
        cid: company.id,
      })
      .andWhere('global = FALSE')
      .getOne();
    if (isExists)
      throw new HttpException('folder with this name is exists', 400);

    let result = await this.imageFolderRepo
      .createQueryBuilder()
      .insert()
      .values({
        name,
        parent_id,
        sec_company_id: company.id,
        user_id: user.id,
      })
      .returning(['id'])
      .execute();

    this.logger.debug(`createFolder result`, { result });

    return result.generatedMaps[0]?.id;
  }

  async renameFolder(body: RenameImageLibraryBodyDto) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug(`renameFolder`, { body, company });

    const { name, folder_id: id } = body;
    let folder = await this.imageFolderRepo
      .createQueryBuilder()
      .where('id =:id', { id })
      .getOne();
    if (folder?.sec_company_id != company.id) throw new ForbiddenException();

    let isExists = await this.imageFolderRepo
      .createQueryBuilder()
      .where('name =:name', { name })
      .andWhere('parent_id =:parent_id', { parent_id: folder.parent_id })
      .andWhere('sec_company_id =:cid', {
        cid: company.id,
      })
      .andWhere('id !=:id', { id })
      .andWhere('global = FALSE')
      .getOne();
    if (isExists)
      throw new ConflictException('props.image_library.folder_exists');

    let result = await this.imageFolderRepo
      .createQueryBuilder()
      .update({ name })
      .where('id =:id', { id })
      .execute();

    this.logger.debug(`renameFolder result`, { result, name, folder });

    return id;
  }

  async deleteFolder(id: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`deleteFolder id ${id}`, { company });

    let folder = await this.imageFolderRepo
      .createQueryBuilder()
      .where('id =:id', { id })
      .getOne();
    if (folder?.sec_company_id != company.id) throw new ForbiddenException();

    let result = await this.imageFolderRepo
      .createQueryBuilder()
      .delete()
      .where('id =:id', { id })
      .execute();
    this.logger.debug(`deleteFolder result`, { result });

    return id;
  }

  async createImage(folder_id: number, image?: Express.Multer.File) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug(`createImage`, {
      image: image?.originalname,
      folder_id,
      user: user.id,
      company,
    });

    let folder = await this.getFolder(folder_id);
    if (folder?.sec_company_id != company.id) new NotFoundException();

    if (!image?.buffer) throw new HttpException('you need to pick image', 400);

    let file_name = await saveImage(image);
    let result = await this.imageLibRepo
      .createQueryBuilder()
      .insert()
      .values({
        folder_id,
        key: file_name,
        name: image.originalname,
        sec_company_id: company.id,
        user_id: user.id,
      })
      .returning(['id'])
      .execute();

    this.logger.debug(`createImage result`, { file_name, result });

    return {
      message: this.i18n.t('props.image_library.image_uploaded'),
      image_id: result.generatedMaps[0]?.id,
      image_path: `/uploads/image-library/${file_name}`,
    };
  }

  async deleteImage(id: number) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    this.logger.debug(`delete id ${id}`, { company });

    let result = await this.imageLibRepo
      .createQueryBuilder()
      .where('sec_company_id =:cid', { cid: company.id })
      .delete()
      .andWhere('id =:id', { id })
      .execute();
    this.logger.debug(`delete result for id ${id}`, { result });

    if (result.affected != 1) throw new NotFoundException();
    return {
      message: this.i18n.t('props.image_library.image_deleted'),
      id,
    };
  }
}
