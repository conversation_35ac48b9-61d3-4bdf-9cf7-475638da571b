import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Post,
  Req,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { CompanyService } from './company.service';
import { LoginCompanyBodyDto } from './dto/post-company.dto';
import { ValidateUserService } from '../auth/validate-user.service';
@Controller({
  path: 'company',
  version: '2',
})
@ApiTags('Company')
@ApiBearerAuth()
export class CompanyController {
  constructor(
    private validateUserService: ValidateUserService,
    private companyService: CompanyService,
  ) {}

  @Post()
  async loginCompany(@Body() body: LoginCompanyBodyDto, @Req() req: Request) {
    let user = await this.validateUserService.validate(
      req.headers.authorization,
    );
    if (!user) throw new HttpException('UNAUTHORIZED', HttpStatus.UNAUTHORIZED);
    return this.companyService.loginCompany(user, body);
  }
}
