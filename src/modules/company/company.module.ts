import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthModule } from '../auth/auth.module';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import { CompanyController } from './company.controller';
import { CompanyService } from './company.service';

import {
  CompanyEntity,
  ReportLayoutEntity,
  SecProgramSettingsEntity,
  UserCompanyEntity,
  UserEntity,
  UserTokenEntity,
} from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      CompanyEntity,
      UserCompanyEntity,
      UserTokenEntity,
      SecProgramSettingsEntity,
      ReportLayoutEntity,
    ]),
    forwardRef(() => AuthModule),
    ModuleLoggerModule.register('company'),
    ProgramSettingsModule,
  ],
  providers: [CompanyService],
  controllers: [CompanyController],
  exports: [CompanyService],
})
export class CompanyModule {}
