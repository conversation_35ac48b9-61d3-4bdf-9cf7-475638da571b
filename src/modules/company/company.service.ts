import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { ProgramSettingsService } from '../common/program-settings/program-settings.service';
import { PrivilegeService } from '../auth/privilege.service';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { LoginCompanyBodyDto } from './dto/post-company.dto';
import {
  CompanyEntity,
  ReportLayoutEntity,
  UserCompanyEntity,
  UserEntity,
  UserTokenEntity,
} from '@entities';
import { UserCompany } from './interface/company.interface';
import { aliasPrefix } from '@/shared/utils';
import { AI_MODULES } from '@/shared/enums';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class CompanyService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private privilegeService: PrivilegeService,
    private progSettingsService: ProgramSettingsService,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(UserCompanyEntity)
    private readonly userCompanyRepo: Repository<UserCompanyEntity>,
    @InjectRepository(UserTokenEntity)
    private readonly userTokenRepo: Repository<UserTokenEntity>,
    @InjectRepository(ReportLayoutEntity)
    private readonly reportLayoutRepo: Repository<ReportLayoutEntity>,
  ) {}

  async loginCompany(
    { tokenId, user }: AuthorizedData,
    params: LoginCompanyBodyDto,
  ) {
    const { company: companyName } = params;
    this.logger.debug(`login company ${companyName} with user ${user.id}`, {
      params,
    });
    let userCompany = await this.getUserCompany(companyName, user.id);
    let sec_company_id = userCompany?.sec_company_id;
    if (!sec_company_id)
      throw new HttpException(
        { success: false, status: 2003, msg: 'Not allowed' },
        403,
      );

    await this.userTokenRepo
      .createQueryBuilder()
      .update({
        sec_company_id,
      })
      .where('sec_user_ws_token_id =:sec_user_ws_token_id', {
        sec_user_ws_token_id: tokenId,
      })
      .execute();

    let { sec_company: company } = userCompany;

    let settings = await this.progSettingsService.get(sec_company_id);
    const publish =
      settings?.company_properties?.storyWidget?.flag_story_widget === 1;

    const ai_modules_entries = Object.entries(AI_MODULES);
    const ai_active = settings.company_properties?.use_ai || 0;
    const ai_modules =
      settings.company_properties?.ai_modules
        ?.map(
          (item) => ai_modules_entries.find(([name, key]) => key === item)?.[0],
        )
        ?.filter((item) => item) || [];

    let result = {
      result: true,
      company_type: company.company_type,
      ai_active,
      ai_modules,
      neutral_sentiment_allowed: settings?.neutral_sentiment === 1,
      keys: await this.privilegeService.userKeys(user.id, sec_company_id),
      publish,
      is_parent_company: company.is_parent_company || false,
      sec_company_id: company.sec_company_id,
      backend_allowed: await this.checkBackendIsAllowed(userCompany.sec_group),
      message: `Logged in to company ${company.company_code}`,
      new_dashboards: await this.getDashboardCount(sec_company_id, user.id),
    };

    this.logger.debug(`loginCompany result`, { result });

    return result;
  }

  async getUserCompanies(id: number): Promise<UserCompany[]> {
    this.logger.debug(`getUserCompanies ${id}`);
    const select = aliasPrefix([
      'c.sec_company_id',
      'c.company_code',
      'c.company_type',
      'c.country_code',
      'c.default_language_key',
      'c.is_parent_company',
      'uc.sec_group',
    ]);
    let result = await this.userRepo
      .createQueryBuilder('u')
      .select(select)
      .leftJoin('u.company', 'uc')
      .leftJoin('uc.sec_company', 'c')
      .distinct(true)
      .where('u.sec_user_id =:id', { id })
      .getRawMany<UserCompany>();

    this.logger.debug(`getUserCompanies result`, { result });

    return result;
  }

  async getUserCompany(company_code: string, sec_user_id: number) {
    this.logger.debug(`getUserCompany ${company_code}, user: ${sec_user_id}`);
    let result = await this.userCompanyRepo
      .createQueryBuilder('uc')
      .select(['uc.sec_company_id', 'uc.sec_group'])
      .leftJoinAndSelect('uc.sec_company', 'sec_company')
      .where('sec_company.company_code =:company_code', { company_code })
      .andWhere('uc.sec_user_id =:sec_user_id', { sec_user_id })
      .getOne();

    this.logger.debug(`getUserCompany result`, {
      sec_user_id,
      company_code,
      result,
    });

    return result?.sec_company_id && result;
  }
  async getUserCompanyById(sec_company_id) {
    this.logger.debug(`getUserCompanyById companyId: ${sec_company_id}`);
    let result = await this.userCompanyRepo
      .createQueryBuilder('uc')
      .select(['uc.sec_company_id', 'uc.sec_group'])
      .leftJoinAndSelect('uc.sec_company', 'sec_company')
      .where('sec_company.sec_company_id =:sec_company_id', { sec_company_id })
      .getOne();

    this.logger.debug(
      `getUserCompanyById result for companyId: ${sec_company_id}`,
      { result },
    );

    return result?.sec_company_id && result;
  }

  async checkBackendIsAllowed(group_id: number) {
    let list = await this.companyRepo.query(
      `select distinct key_id from sec_user_right where group_id = $1`,
      [group_id],
    );
    return !!list.length;
  }
  async getDashboardCount(sec_company_id: number, sec_user_id: number) {
    this.logger.debug(`getDashboardCount companyId: ${sec_company_id}`);
    let result = await this.reportLayoutRepo
      .createQueryBuilder()
      .where('sec_company_id =:sec_company_id', { sec_company_id })
      .andWhere('created_by =:sec_user_id', { sec_user_id })
      .getCount();

    this.logger.debug(
      `getDashboardCount result for companyId: ${sec_company_id}`,
      { result },
    );

    return result;
  }
  async getKeys(sec_user_id: number, sec_company_id: number) {
    this.logger.debug(
      `getKeys company: ${sec_company_id}, user: ${sec_user_id}`,
    );

    let keys = await this.companyRepo.query(
      `
      SELECT DISTINCT sk.name FROM sec_key sk 
      LEFT JOIN sec_user_right sur ON sur.key_id = sk.key_id 
      LEFT JOIN sec_group sg ON sg.group_id = sur.group_id 
      LEFT JOIN sec_users su ON sg.group_id = su.sec_group 
      LEFT JOIN sec_user_company suc ON suc.sec_group = sg.group_id  AND suc.sec_company_id =  $1
      WHERE suc.sec_user_id = $2 AND sk.solution_type = 2
      `,
      [sec_company_id, sec_user_id],
    );
    this.logger.debug(
      `getKeys result for company: ${sec_company_id}, user: ${sec_user_id}`,
      { keys },
    );

    return keys.map((item) => item.name);
  }
}
