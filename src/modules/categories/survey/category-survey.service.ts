import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { SurvSurveyEntity } from '@entities';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';

@Injectable()
export class CategorySurveyService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(SurvSurveyEntity)
    private readonly survSurveyRespository: Repository<SurvSurveyEntity>,
  ) {}

  public async list<T = { id: number; name: string }>(params?: {
    selects?: (keyof SurvSurveyEntity)[];
    companyId?: number;
    order?: keyof SurvSurveyEntity;
    checkParent?: boolean;
  }): Promise<T[]> {
    let {
      selects = null,
      order = null,
      checkParent = false,
      companyId,
    } = params || {};

    const { company, userCompany } = this.cls.get<AuthorizedData>(
      ClsProperty.user,
    );

    companyId = +(companyId || company.id);

    this.logger.debug(`list for company ${companyId}`, { userCompany });

    const groupFilter = `
    AND (surv_survey_id NOT IN (
      SELECT surv_survey_id
      FROM surv_survey_groups)
    OR surv_survey_id IN (
      SELECT surv_survey_id
      FROM surv_survey_groups
      WHERE group_id = ${userCompany.sec_group}
  ))
    `;

    let result = await this.survSurveyRespository.query(`
      SELECT ${selects ? selects.join(',') : 'surv_survey_id, internal_name'}
      FROM surv_survey
      WHERE sec_company_id = ${companyId} 
      ${!checkParent || (checkParent && !company.isParent) ? groupFilter : ''}
      ${order ? `ORDER BY ${order}` : ''}
    `);

    this.logger.debug(`list result for company ${companyId}`, { result });

    return result.map((item) =>
      selects ? item : { id: item.surv_survey_id, name: item.internal_name },
    );
  }

  public async getSurveyDetails(
    survey_id: number,
    { brands = false, questions = false } = {},
  ) {
    this.logger.debug(`getSurveyDetails`, { include: { brands, questions } });
    const result = {
      survey_details: {
        brands: [],
        questions: [],
      },
    };
    if (brands)
      result.survey_details.brands = await this.survSurveyRespository.query(`
        SELECT et.email_template_id as id, initcap(et.companyname) as name
        FROM surv_surveytemplate st
        INNER JOIN email_template et ON st.email_template_id = et.email_template_id
        WHERE st.surv_survey_id = ${survey_id}
        ORDER BY LOWER(et.companyname)
      `);
    if (questions)
      result.survey_details.questions = await this.survSurveyRespository.query(`
        SELECT surv_surveyquestion_id as id, sp.title as name, sq.questiontype as type
        FROM surv_surveyquestion sq
        INNER JOIN surv_surveypart sp ON sp.surv_surveypart_id = sq.surv_surveypart_id
        WHERE sp.surv_survey_id = ${survey_id}
        ORDER BY sp.title
      `);
    this.logger.debug(`getSurveyDetails result`, { result });

    return result;
  }
}
