import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserCompanyEntity } from '../../database/entities/sec-user-company.entity';
import { SurvSurveyEntity } from '../../database/entities/surv-survey.entity';
import { CategorySurveyController } from './category-survey.controller';
import { CategorySurveyService } from './category-survey.service';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserCompanyEntity, SurvSurveyEntity]),
    ModuleLoggerModule.register('category-survey'),
  ],
  controllers: [CategorySurveyController],
  providers: [CategorySurveyService],
  exports: [CategorySurveyService],
})
export class CategorySurveyModule {}
