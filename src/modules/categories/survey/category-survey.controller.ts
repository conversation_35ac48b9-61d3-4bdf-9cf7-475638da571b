import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiOkResponse, ApiQuery, ApiTags } from '@nestjs/swagger';

import { GetSurveyDetailsResponse } from './dto/get-survey-details.dto';
import { GetSurveyListResponse } from './dto/get-survey-list.dto';

import { CategorySurveyService } from './category-survey.service';
import { Auth } from '@/shared/decorators';

@ApiTags('Categories')
@Controller({
  version: '2',
  path: 'categories/survey',
})
@Auth()
export class CategorySurveyController {
  constructor(private surveyService: CategorySurveyService) {}

  @ApiOkResponse({
    description: 'Survey list/Touchpoints list',
    type: GetSurveyListResponse,
  })
  @Get('/')
  getSurveyList() {
    return this.surveyService.list();
  }

  @ApiOkResponse({
    description: 'Survey details/Touchpoints list',
    type: GetSurveyDetailsResponse,
  })
  @ApiQuery({ name: 'brands', required: false })
  @ApiQuery({ name: 'questions', required: false })
  @Get('/:id')
  getSurveyDetails(
    @Param('id') id: number,
    @Query('brands') brands?: boolean,
    @Query('questions') questions?: boolean,
  ) {
    return this.surveyService.getSurveyDetails(id, {
      brands,
      questions,
    });
  }
}
