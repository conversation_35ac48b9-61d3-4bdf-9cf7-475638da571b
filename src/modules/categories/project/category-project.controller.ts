import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { CategoryProjectService } from './category-project.service';
import { Auth } from '@/shared/decorators';

@ApiTags('Categories')
@Controller({
  version: '2',
  path: 'categories',
})
@Auth()
export class CategoryProjectController {
  constructor(private projectService: CategoryProjectService) {}

  @Get('project')
  projects() {
    return this.projectService.list();
  }

  @Get('/club')
  clubs() {
    return this.projectService.list();
  }
}
