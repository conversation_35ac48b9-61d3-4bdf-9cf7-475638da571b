import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserCompanyEntity } from '../../database/entities/sec-user-company.entity';
import { CategoryProjectController } from './category-project.controller';
import { CategoryProjectService } from './category-project.service';
import { ModuleLoggerModule } from '@helpers/logger/module-logger/module-logger.module';
import { ProjectsEntity } from '../../database/entities/projects.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserCompanyEntity, ProjectsEntity]),
    ModuleLoggerModule.register('project'),
  ],
  controllers: [CategoryProjectController],
  providers: [CategoryProjectService],
  exports: [CategoryProjectService],
})
export class CategoryProjectModule {}
