import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { ProjectsEntity } from '@entities';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';

@Injectable()
export class CategoryProjectService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(ProjectsEntity)
    private readonly projectRepository: Repository<ProjectsEntity>,
  ) {}

  public async list({ companyId = 0 } = {}) {
    const { company, userCompany } = this.cls.get<AuthorizedData>(
      ClsProperty.user,
    );

    companyId = +(companyId || company.id);

    this.logger.debug(`list for company ${companyId}`, { userCompany });

    let result = await this.projectRepository.find({
      where: {
        sec_company_id: company.id,
      },
    });

    this.logger.debug(`list result for company ${companyId}`, { result });

    return result.map((item) => ({
      id: item.project_id,
      name: item.project_name,
    }));
  }

  public async listByGroup({ companyId = 0 } = {}) {
    const { company, userCompany } = this.cls.get<AuthorizedData>(
      ClsProperty.user,
    );
    companyId = +(companyId || company.id);

    this.logger.debug(`list for company ${companyId}`, { userCompany });

    const groupFilter = `
    AND (project_id NOT IN (
      SELECT project_id
      FROM project_groups)
        OR project_id IN (SELECT project_id
        FROM project_groups
        WHERE group_id = ${userCompany.sec_group}))
    `;

    let result = await this.projectRepository.query(`
      SELECT project_id as id
      FROM projects
      WHERE sec_company_id = ${companyId} 
          ${groupFilter}
    `);

    this.logger.debug(`list result for company ${companyId}`, { result });

    return result;
  }
}
