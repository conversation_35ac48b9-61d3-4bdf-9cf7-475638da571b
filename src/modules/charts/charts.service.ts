import {
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import * as moment from 'moment';

import {
  BrandsItem,
  ChartParams,
  ChartSqlOptions,
  ClassificationDataItem,
  ClassificationLabelAccumulatorItem,
  ClassificationLabelItem,
  FeedbackItem,
  FormatTrendsAccumulatorItem,
  GetNpsOptions,
  MultipleChoiseItem,
  NpsScaleItem,
  NpsSegmentsItem,
  NpsTimeItem,
  NpsTrendItem,
  PotentialAndLikeItem,
  RegionItem,
  ResponseBehaviorItem,
  SentEmailsItem,
  TouchpointsItem,
} from './interfaces/chart-result.interface';
import { CombinedFilters, Filters } from './interfaces/filters.interface';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import { AI_THRESHOLD_DEFAULT } from '../../config/ai.config';
import { CHART_COLORS, colors } from './constants/colors';

import {
  ChartType,
  CompanyType,
  DateField,
  ReportTemplate,
} from '@/shared/enums';
import {
  SurveyAnswerEntity,
  UserCompanyEntity,
  UserReportsEntity,
} from '@entities';

import { ProgramSettingsService } from '../common/program-settings/program-settings.service';
import { UserReportsService } from '../users/user-reports/user-reports.service';
import { ImportService } from '../lib/import-template/import.service';
import {
  DateUtilsService,
  groupBy,
  parseJSON,
  stringCapitalize,
} from '@/shared/utils';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class ChartsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private userReportsService: UserReportsService,
    @InjectRepository(SurveyAnswerEntity)
    private readonly surveyAnswerEntity: Repository<SurveyAnswerEntity>,
    @InjectRepository(UserReportsEntity)
    private readonly userReportsEntity: Repository<UserReportsEntity>,
    @InjectRepository(UserCompanyEntity)
    private readonly userCompanyEntity: Repository<UserCompanyEntity>,
    private dateUtilService: DateUtilsService,
    private importService: ImportService,
    private readonly programSettingsService: ProgramSettingsService,
  ) {}

  async getChart(
    chartId: number,
    chartType: ChartType,
    params: ChartParams,
    page?: number,
  ): Promise<{ name: string; filters: Partial<Filters>; chart: any }> {
    const { user, company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`getChart chartId: ${chartId} chartType: ${chartType}`);
    const userReport = await this.userReportsEntity.findOne({
      where: {
        user_report_id: chartId,
      },
    });

    let groupIds: any = await this.userCompanyEntity.find({
      where: { sec_user_id: user.id, sec_company_id: company.id },
      select: ['sec_group'],
    });

    groupIds = groupIds.map((item) => item.sec_group);

    if (!userReport) {
      this.logger.debug(`getChart error chart is not found`);

      throw new HttpException('User report not found', HttpStatus.BAD_REQUEST);
    }

    const {
      report_template: reportTemplate,
      filters: filtersString,
      report_name: reportName,
      report_properties,
    } = userReport;

    const filters = await this.getCombinedFilters(
      filtersString,
      groupIds,
      reportTemplate,
      chartType,
    );

    const chartSql = await this.getChartSql({
      reportTemplate,
      params,
      page,
      filters,
    });

    try {
      let result = await this.getAndFormatResult(
        reportTemplate,
        chartSql,
        filters.period || 'week',
        filters.filtersObject.chart_model,
        chartType,
        params,
        filters,
      );
      result = this.userReportsService.getExtraOptions(
        parseJSON(report_properties),
        result,
      );
      if (chartType == ChartType.TABLE) {
        result.rows = result.rows.filter((item: any) => item.name !== null);
      }

      this.logger.debug(`getChart results and query`, {
        result,
        reportName,
        reportTemplate,
        chartSql,
        period: filters.period || 'week',
        model: filters.filtersObject.chart_model,
        chartType,
        params,
        filters,
      });

      return {
        name: reportName,
        filters: filters.filtersObject,
        chart: result,
      };
    } catch (error) {
      this.logger.error('getChart error', {
        error: error.message || error,
        reportTemplate,
        filtersString,
        reportName,
        report_properties,
      });
      throw new InternalServerErrorException(error?.message);
    }
  }

  /**
   * Parse string filter from user reports datatable,
   * then combine it to object, sql query string + additional fields,
   * which are used for building chart queries
   * @param {string} filtersString filter string from user reports record
   * @param {CompanyType} companyType additional field for extra queries
   * @param {number} templateId template id, used for adding filter for several template ids
   * @returns {CombinedFilters} combined filters object
   */
  public async getCombinedFilters(
    filtersString: string,
    groupIds: any[],
    templateId?: ReportTemplate,
    chartType?: ChartType,
  ): Promise<CombinedFilters> {
    const { company } = this.cls.get<AuthorizedData>('user');

    const filtersObject = JSON.parse(filtersString) as Partial<Filters>;
    const sqlFiltersArray = [];
    const extraSqlFiltersArray = [];
    let fromDate: moment.Moment;
    let toDate: moment.Moment;
    if (filtersObject.survey?.length) {
      sqlFiltersArray.push(
        `a.surv_survey_id in (${filtersObject.survey
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (groupIds.length > 0) {
      let sgroup = await this.surveyAnswerEntity
        .query(`SELECT surv_survey_id as id
      FROM surv_survey
      WHERE sec_company_id =${company.id} AND (surv_survey_id NOT IN (
          SELECT surv_survey_id
          FROM surv_survey_groups)
            OR surv_survey_id IN (SELECT surv_survey_id
            FROM surv_survey_groups
            WHERE group_id IN (${groupIds})))
      `);
      if (sgroup.length > 0) {
        sqlFiltersArray.push(
          `a.surv_survey_id IN (${sgroup.map((i) => i.id)})`,
        );
      }
      if (filtersObject.survey?.length) {
        if (company.type != 1) {
          let pgroup = await this.surveyAnswerEntity
            .query(`SELECT project_id as id
        FROM projects
        WHERE sec_company_id = ${company.id} AND (project_id NOT IN (
            SELECT project_id
            FROM project_groups)
              OR project_id IN (SELECT project_id
              FROM project_groups
              WHERE group_id IN (${groupIds})))
        `);
          if (pgroup.length > 0) {
            sqlFiltersArray.push(
              `ec.xml_internal_external IN (${pgroup.map((i) => `'${i.id}'`)})`,
            );
          }
        }
      }
    }

    if (filtersObject.regions?.length) {
      if (company.id == 2) {
        sqlFiltersArray.push(
          `ec.xml_techgrp in (${filtersObject.regions
            .map((i) => `'${i.id}'`)
            .join(', ')})`,
        );
      } else {
        sqlFiltersArray.push(
          `ec.xml_region in (${filtersObject.regions
            .map((i) => `'${i.id}'`)
            .join(', ')})`,
        );
      }
    }
    filtersObject.brand = filtersObject.brand || filtersObject.brands;
    if (filtersObject.brand?.length) {
      sqlFiltersArray.push(
        `LOWER(ec.xml_brand) IN (${filtersObject.brand
          .map((i) => `'${i.id}'`.toLowerCase())
          .join(', ')})`,
      );
    }

    if (filtersObject.language?.length) {
      sqlFiltersArray.push(
        `ec.xml_language in (${filtersObject.language
          .map((i) => `'${i.id}'`)
          .join(', ')})`,
      );
    }

    if (filtersObject.medium?.length === 1) {
      const { id } = filtersObject.medium[0];
      sqlFiltersArray.push(`ec.sent_medium = '${id}'`);
    }

    if (filtersObject?.warranty?.[0]?.id) {
      let wid = filtersObject.warranty[0].id;
      if (wid == '1') sqlFiltersArray.push(` ec.xml_la IN ('01', '83', '84') `);
      else if (wid == '2')
        sqlFiltersArray.push(
          ` (ec.xml_la NOT IN ('01', '83', '84') OR ec.xml_la IS NULL)`,
        );
    }

    if (filtersObject.score?.length) {
      extraSqlFiltersArray.push(
        `a.nps_value in (${filtersObject.score
          .map((i) => {
            switch (i.id) {
              case 11: {
                return '9,10';
              }
              case 12: {
                return '7,8';
              }
              case 13: {
                return '0,1,2,3,4,5,6';
              }
              default: {
                return i.id;
              }
            }
          })
          .join(', ')})`,
      );
    }

    if (filtersObject.bsh_survey_categories?.length) {
      sqlFiltersArray.push(
        `s.bsh_category_id in (${filtersObject.bsh_survey_categories
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (filtersObject.productgroup?.length) {
      sqlFiltersArray.push(
        `ec.xml_prodarea in (${filtersObject.productgroup
          .map((i) => `'${i.id}'`)
          .join(', ')})`,
      );
    }
    if (filtersObject.product_division?.length) {
      sqlFiltersArray.push(
        `ec.xml_proddivision in (${filtersObject.product_division
          .map((i) => `'${i.id}'`)
          .join(', ')})`,
      );
    }

    if (filtersObject.cp?.length) {
      sqlFiltersArray.push(
        `ec.xml_cp in (${filtersObject.cp.map((i) => `'${i.id}'`).join(', ')})`,
      );
    }

    if (filtersObject.sv?.length) {
      sqlFiltersArray.push(
        `ec.xml_sv in (${filtersObject.sv.map((i) => `'${i.id}'`).join(', ')})`,
      );
    }

    if (filtersObject.project?.length) {
      let project_div = [];
      let project_nr = [];
      for (let project of filtersObject.project) {
        if (['BO-MDA', 'BO-SDA', 'SI-MDA', 'SI-SDA'].includes(project.id)) {
          project_div.push(`'${project.id}'`);
        } else {
          project_nr.push(`'${project.id}'`);
        }
      }

      if (project_div.length) {
        sqlFiltersArray.push(
          `ec.xml_projectdiv in (${project_div.join(', ')})`,
        );
      }
      if (project_nr.length) {
        sqlFiltersArray.push(`ec.xml_projectnr in (${project_nr.join(', ')})`);
      }
    }

    if (filtersObject.labels?.length) {
      sqlFiltersArray.push(
        `mr.item_id in (${filtersObject.labels.map((i) => i.id).join(', ')})`,
      );
    }

    if (filtersObject.from_date || filtersObject.to_date) {
      let formatted = this.dateUtilService.formatDates({
        fromDate: filtersObject.from_date,
        toDate: filtersObject.to_date,
      });
      fromDate = formatted.fromDate;
      toDate = formatted.toDate;
      if (templateId !== ReportTemplate.Sent_Emails) {
        sqlFiltersArray.push(
          `${
            filtersObject.date_field[0]?.id === 1
              ? 'a.creation_date'
              : 'ec.xml_readydate'
          } between '${fromDate.toISOString()}' AND '${toDate.toISOString()}'`,
        );
      }
    }

    if (filtersObject.label_type?.length && templateId === 15) {
      sqlFiltersArray.push(
        `mr.record_status in (${filtersObject.label_type
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (filtersObject.respondent_device?.length) {
      sqlFiltersArray.push(
        `a.device in (${filtersObject.respondent_device
          .map((i) => `'${i.id}'`)
          .join(', ')})`,
      );
    }

    if (filtersObject.respondent_os?.length) {
      sqlFiltersArray.push(
        `a.os in (${filtersObject.respondent_os
          .map((i) => `'${i.id}'`)
          .join(', ')})`,
      );
    }

    // Special joins required

    if (filtersObject.contact_center?.length) {
      sqlFiltersArray.push(
        `techDOB.techgrp_id in (${filtersObject.contact_center
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (filtersObject.dispatcher?.length) {
      sqlFiltersArray.push(
        `techDispatch.techgrp_id in (${filtersObject.dispatcher
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (filtersObject.preprepper?.length) {
      sqlFiltersArray.push(
        `techPreppep.techgrp_id in (${filtersObject.preprepper
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (filtersObject.service_partner?.length) {
      sqlFiltersArray.push(
        `techServicePartner.techgrp_id in (${filtersObject.service_partner
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (filtersObject.counselor?.length) {
      sqlFiltersArray.push(
        `techCounselor.techgrp_id in (${filtersObject.counselor
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (filtersObject.engineer?.length) {
      sqlFiltersArray.push(
        `tech.techgrp_id in (${filtersObject.engineer
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    if (filtersObject.dealer?.length) {
      sqlFiltersArray.push(
        `techDealer.techgrp_id in (${filtersObject.dealer
          .map((i) => i.id)
          .join(', ')})`,
      );
    }

    // China filters

    if (filtersObject.cp?.length) {
      sqlFiltersArray.push(
        `ec.xml_cp in (${filtersObject.cp.map((i) => `'${i.id}'`).join(', ')})`,
      );
    }

    if (filtersObject.sv?.length) {
      sqlFiltersArray.push(
        `ec.xml_sv in (${filtersObject.sv.map((i) => `'${i.id}'`).join(', ')})`,
      );
    }

    // Company type filters

    if (
      filtersObject.project_bouw?.length &&
      company.type === CompanyType.BOUW
    ) {
      sqlFiltersArray.push(
        `ec.xml_internal_external in (${filtersObject.project_bouw
          .map((i) => `'${i.id}'`)
          .join(', ')})`,
      );
    }

    if (filtersObject.club?.length && company.type === CompanyType.SPORT) {
      sqlFiltersArray.push(
        `ec.xml_internal_external in (${filtersObject.club
          .map((i) => `'${i.id}'`)
          .join(', ')})`,
      );
    }

    return {
      filtersObject,
      filtersSql: sqlFiltersArray.length
        ? `AND ${sqlFiltersArray.join(' AND ')}`
        : '',
      extraFiltersSql: extraSqlFiltersArray.length
        ? `AND ${extraSqlFiltersArray.join(' AND ')}`
        : '',
      dateField:
        filtersObject.date_field[0]?.id === 1
          ? 'a.creation_date'
          : 'ec.xml_readydate',
      dateType:
        filtersObject.date_field[0]?.id == 1
          ? DateField.ResponseDate
          : DateField.VisitDate,
      period: filtersObject.period?.[0]?.id || 'week',
      fromDate,
      toDate,
    };
  }

  /**
   * Build special joins SQL depends on filters input
   * @param {Partial<Filters>} filters filters object
   * @returns {{ specialJoinsSql: string }} special joins sql
   */
  public getSpecialJoins(filters: Partial<Filters>): {
    specialJoinsSql: string;
  } {
    const specialJoinsArray: string[] = [];
    if (filters.contact_center?.length) {
      specialJoinsArray.push(
        `LEFT JOIN techgrp techDOB ON ec.xml_acceptance_by = techDOB.techgrp_techn AND techDOB.techgrp_type = 'xml_acceptance_by'`,
      );
    }
    if (filters.dispatcher?.length) {
      specialJoinsArray.push(
        `LEFT JOIN techgrp techDispatch ON ec.xml_planning = techDispatch.techgrp_techn AND techDispatch.techgrp_type = 'xml_planning'`,
      );
    }
    if (filters.preprepper?.length) {
      specialJoinsArray.push(
        `LEFT JOIN techgrp techPreppep ON ec.xml_wvb = techPreppep.techgrp_techn AND techPreppep.techgrp_type = 'xml_wvb'`,
      );
    }
    if (filters.service_partner?.length) {
      specialJoinsArray.push(
        `LEFT JOIN techgrp techServicePartner ON ec.xml_custcreditnr = techServicePartner.techgrp_techn AND techServicePartner.techgrp_type = 'xml_custcreditnr'`,
      );
    }
    if (filters.counselor?.length) {
      specialJoinsArray.push(
        `LEFT JOIN techgrp techCounselor ON ec.xml_employee = techCounselor.techgrp_techn AND techCounselor.techgrp_type = 'xml_employee'`,
      );
    }
    if (filters.engineer?.length) {
      specialJoinsArray.push(
        `LEFT JOIN techgrp tech ON ec.xml_technician = tech.techgrp_techn AND tech.techgrp_type = 'xml_technician'`,
      );
    }
    if (filters.dealer?.length) {
      specialJoinsArray.push(
        `LEFT JOIN techgrp techDealer ON ec.xml_dealer = techDealer.techgrp_techn AND techDealer.techgrp_type = 'xml_dealer'`,
      );
    }

    return {
      specialJoinsSql: specialJoinsArray.join(' \r\n '),
    };
  }

  async getChartSql({
    reportTemplate,
    params,
    page = 1,
    filters,
  }: ChartSqlOptions): Promise<string> {
    const { user, company } = this.cls.get<AuthorizedData>('user');

    const { templateQuestionId, surveyQuestionId, groupByField } = params;
    const dates = {
      fromDate: filters.fromDate,
      toDate: filters.toDate,
    };
    let {
      filtersSql,
      extraFiltersSql,
      dateField,
      dateType,
      period = 'week',
    } = filters;

    let filtersSqlWithoutExtra = filtersSql;
    filtersSql += extraFiltersSql;
    const { specialJoinsSql } = this.getSpecialJoins(filters.filtersObject);
    let chartModel = filters.filtersObject.chart_model;
    let isLinear = this.isLinear(chartModel);

    let settings, ai_threshold;
    if (reportTemplate === ReportTemplate.Classification) {
      settings = await this.programSettingsService.get(company.id);
      let props = settings?.company_properties;
      ai_threshold = props?.ai_threshold || AI_THRESHOLD_DEFAULT;
    }

    switch (reportTemplate) {
      case ReportTemplate.CSAT_SCORE:
      case ReportTemplate.NPS_Score: {
        return `
          WITH data AS (
            SELECT a.nps_value, ${dateField} AS date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END AS promoter
            FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

            ${specialJoinsSql}

            WHERE ec.survey_done = 1
            AND a.nps_value IS NOT NULL

            AND ec.sec_company_id = ${company.id}
    
            ${filtersSql}
    
          ) SELECT sum(promoter) AS promoters, sum(passive) AS passives, sum(detractor) AS detractors, COUNT(nps_value) AS cnt
          FROM data;

          SELECT count(*) AS total
          FROM email_customer ec
          LEFT JOIN surv_surveyanswer a ON a.email_customer_id = ec.email_customer_id
          LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id

          ${specialJoinsSql}
          
          WHERE ec.send_date IS NOT NULL

          AND ec.sec_company_id = ${company.id}

          ${filtersSqlWithoutExtra.replace(/a\./g, 'ec.')};

          SELECT count(a.surv_surveyanswer_id) AS cntTxt
          FROM surv_surveyanswer a
          LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id
          LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id

          ${specialJoinsSql}

                    WHERE a.has_text = 1

          AND ec.sec_company_id = ${company.id}

          ${filtersSql};
    
        `;
      }
      case ReportTemplate.CSAT_TREND:
      case ReportTemplate.NPS_Trend: {
        return `
          WITH data AS (
            SELECT a.nps_value, ${dateField} AS date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END AS promoter
            FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

            ${specialJoinsSql}
            
            WHERE ec.survey_done = 1
            AND a.nps_value IS NOT NULL

            AND ec.sec_company_id = ${company.id}
      
            ${filtersSql}

          )
          SELECT date_trunc('${period}', date_field::date) as period,
          sum(promoter) AS promoters,
          sum(passive) AS passives,
          sum(detractor) AS detractors,
          COUNT(nps_value) AS cnt
          FROM data
          GROUP BY period
          ORDER BY period
        `;
      }
      case ReportTemplate.NPS_Segments: {
        return `
          WITH data AS (
            SELECT a.nps_value, ${dateField} AS date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END AS promoter
            FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

            ${specialJoinsSql}

            WHERE ec.survey_done = 1
            AND a.nps_value IS NOT NULL

            AND ec.sec_company_id = ${company.id}
    
            ${filtersSql}
          )
          SELECT sum(detractor) AS detractors, sum(passive) AS passives, sum(promoter) AS promoters
          FROM data
        `;
      }
      case ReportTemplate.NPS_Scale: {
        return `
          WITH data AS (
            SELECT a.nps_value,
            ${dateField} AS date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END AS promoter
            FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

            ${specialJoinsSql}

            WHERE ec.survey_done = 1
            AND a.nps_value IS NOT NULL

            AND a.sec_company_id = ${company.id}

            ${filtersSql}
          )
          SELECT nps_value, COUNT(nps_value) AS cnt  ${
            isLinear ? `, date_trunc('${period}', date_field) as dt` : ''
          }
          FROM data
          GROUP BY nps_value ${isLinear ? `, dt` : ''}
          ORDER BY nps_value
        `;
      }
      case ReportTemplate.Brands: {
        return `
          WITH data AS (
            SELECT lower(xml_brand) AS xml_brand,
            a.nps_value,
            ${dateField} AS date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END AS promoter
            FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

            ${specialJoinsSql}

            WHERE ec.survey_done = 1
            AND a.nps_value IS NOT NULL
            AND xml_brand IS NOT NULL

            AND ec.sec_company_id = ${company.id}
    
            ${filtersSql}
          )
          SELECT initcap(xml_brand) AS xml_brand,
          ${
            isLinear || chartModel == ChartType.TABLE
              ? `date_trunc('${period}', date_field::date) as period,`
              : ''
          }
          sum(promoter) AS promoters,
          sum(passive) AS passives,
          sum(detractor) AS detractors,
          COUNT(nps_value) AS cnt
          FROM data
          GROUP BY ${
            isLinear || chartModel == ChartType.TABLE ? `period ,` : ''
          }
          xml_brand 
          ORDER BY xml_brand
          ${isLinear || chartModel == ChartType.TABLE ? `, period` : ''}
        `;
      }
      case ReportTemplate.Touchpoints: {
        return `
          WITH data AS (
            SELECT lower(internal_name) AS internal_name,
            a.nps_value,
            ${dateField} AS date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END             AS promoter
            FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

            ${specialJoinsSql}

            WHERE ec.survey_done = 1
            AND internal_name IS NOT NULL
            AND a.nps_value IS NOT NULL

            AND ec.sec_company_id = ${company.id}
   
            ${filtersSql}
          )
          SELECT internal_name,
          ${
            !this.isBar(chartModel)
              ? `date_trunc('${period}', date_field::date) as period,`
              : ''
          }
          sum(promoter) AS promoters,
          sum(passive) AS passives,
          sum(detractor) AS detractors,
          COUNT(nps_value) AS cnt
          FROM data
          GROUP BY ${
            this.isBar(chartModel) ? `internal_name` : 'period, internal_name'
          }
          ORDER BY ${
            this.isBar(chartModel) ? `internal_name` : 'internal_name, period'
          }
        `;
      }
      case ReportTemplate.NPS_Time: {
        return `
          WITH data AS (
            SELECT a.nps_value,
            ${dateField} AS date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END AS promoter
            FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

            ${specialJoinsSql}

            WHERE ec.survey_done = 1
            AND a.nps_value IS NOT NULL

            AND ec.sec_company_id = ${company.id}
      
            ${filtersSql}
          )
          SELECT date_trunc('${period}', date_field::date) as period,
          sum(promoter) AS promoters,
          sum(passive) AS passives,
          sum(detractor) AS detractors,
          COUNT(nps_value) AS cnt
          FROM data
          GROUP BY period
          ORDER BY period
        `;
      }
      case ReportTemplate.Sent_Emails: {
        let fromDate = dates.fromDate;
        let toDate = dates.toDate;
        let periodArg = `1 ${period}`;
        // when period is quarter, we need to change the dates CTE
        if (period === 'quarter') {
          periodArg = '3 month';
        }
        return `
        WITH dates AS (
          SELECT 
            generate_series(
              date_trunc('${period}', '${fromDate}'::timestamp),
              date_trunc('${period}', '${toDate}'::timestamp),
              ('${periodArg}')::interval
            ) AS period),
            ec AS (
              SELECT CASE
                  WHEN send_date BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp THEN date_trunc('${period}', send_date) ELSE null END as sent,
                CASE
                  WHEN remainder_send_date BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp THEN date_trunc('${period}', remainder_send_date) ELSE null END as reminder,
                CASE
                  WHEN thankmail_senddate BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp THEN date_trunc('${period}', thankmail_senddate) ELSE null END as thank,
                CASE
                  WHEN
                    ${
                      dateType == DateField.ResponseDate
                        ? `a.creation_date BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp  THEN date_trunc('${period}', a.creation_date) ELSE null END as answered `
                        : ''
                    }
                    ${
                      dateType == DateField.VisitDate
                        ? `xml_readydate BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp and ec.survey_done = 1 THEN date_trunc('${period}', xml_readydate) ELSE null END as answered`
                        : ''
                    }
                  
              FROM email_customer ec
              left join surv_surveyanswer a on ec.email_customer_id = a.email_customer_id
              LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id

              ${specialJoinsSql}
  
              WHERE (
                send_date BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp
                OR remainder_send_date BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp
                OR thankmail_senddate BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp
                OR ${dateField} BETWEEN '${fromDate}'::timestamp AND '${toDate}'::timestamp
              )
              AND ec.sec_company_id = ${company.id}
              ${filtersSqlWithoutExtra.replace(/a\./g, 'ec.')}
              order by sent
            )
          SELECT 
            dt.period,
            SUM(CASE WHEN date_trunc('${period}', sent) = dt.period THEN 1 ELSE 0 END) AS sent,
            SUM(CASE WHEN date_trunc('${period}', reminder) = dt.period THEN 1 ELSE 0 END) AS reminder,
            SUM(CASE WHEN date_trunc('${period}', thank) = dt.period THEN 1 ELSE 0 END) AS thank,
            SUM(CASE WHEN date_trunc('${period}', answered) = dt.period THEN 1 ELSE 0 END) AS answered
          FROM dates dt
          LEFT JOIN ec ON
            dt.period = date_trunc('${period}', sent)
            OR dt.period = date_trunc('${period}', reminder)
            OR dt.period = date_trunc('${period}', thank)
            or dt.period = date_trunc('${period}', answered)
          WHERE sent IS NOT NULL
            or reminder IS NOT NULL
            or thank IS NOT NULL
            or answered is NOT NULL
          GROUP BY dt.period
          ORDER BY dt.period
        `;
      }
      case ReportTemplate.Region: {
        return `
          WITH data AS (
            SELECT lower(
              CASE
              WHEN map.map_description IS NULL OR map.map_description = '' THEN xml_region
              ELSE map.map_description END
            ) AS xml_region,
            a.nps_value,
            ${dateField} AS date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END AS promoter
            FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id
            LEFT JOIN bsh_mapping map ON ec.xml_region = map.map_key AND map.sec_company_id = ec.sec_company_id AND map.map_field = 'xml_region'
            ${specialJoinsSql}
            WHERE ec.survey_done = 1
            AND a.nps_value IS NOT NULL
            AND CASE WHEN map.map_description IS NULL OR map.map_description = '' THEN xml_region ELSE map.map_description END IS NOT NULL

            AND ec.sec_company_id = ${company.id}

            ${filtersSql}
          )
          SELECT initcap(xml_region) as xml_region,
          ${
            !this.isBar(chartModel)
              ? `
          date_trunc('${period}', date_field::date) as period,`
              : ''
          }
          sum(promoter) AS promoters,
          sum(passive) AS passives,
          sum(detractor) AS detractors,
          COUNT(nps_value) AS cnt
          FROM data
          GROUP BY ${
            this.isBar(chartModel) ? `xml_region` : 'period, xml_region'
          }
          ORDER BY ${
            this.isBar(chartModel) ? `xml_region` : 'xml_region, period'
          }
        `;
      }
      case ReportTemplate.Response_Behaviour: {
        return `
          WITH dt AS (
            SELECT 
            date_trunc('day', generate_series(current_date, current_date + interval '5 weeks', interval '1 day')::date - current_date::timestamp) AS day),
            data as (
              SELECT date_trunc('day', a.creation_date_system - ec.send_date) AS Periode,
              count(*) AS cnt,
              a.nps_value AS nps
              FROM surv_surveyanswer a
              LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id
              LEFT JOIN email_customer ec
              ON a.email_customer_id = ec.email_customer_id AND s.surv_survey_id = ec.surv_survey_id
              LEFT JOIN email_template et ON ec.email_template_id = et.email_template_id

              ${specialJoinsSql}
  
              WHERE ec.survey_done = 1

              AND ec.sec_company_id = ${company.id}

              ${filtersSql}

              GROUP BY a.creation_date_system, ec.send_date, a.nps_value
            )
            SELECT dt.day as period, sum(data.cnt), data.nps
            FROM dt
            LEFT JOIN data ON dt.day = data.Periode
            group by period, data.nps
            order by period
        `;
      }
      case ReportTemplate.Classification: {
        return `
          SELECT mr.item_id,
          a.surv_surveyanswer_id AS sa_id,
          mi.item_label,
          mil.item_label_locale,
          mi.parent_id AS pid,
          CASE WHEN mr.istype = 1 THEN 1 ELSE 0 END AS pos,
          CASE WHEN mr.istype = 2 THEN 1 ELSE 0 END AS neg
          FROM surv_surveyanswer a
          LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id
          LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id
          LEFT JOIN mclass_response mr ON a.surv_surveyanswer_id = mr.answer_id
          LEFT JOIN mclass_item mi ON mr.item_id = mi.mclass_item_id
          LEFT JOIN mclass_item_local mil ON mil.mclass_item_id = mi.mclass_item_id AND mil.sec_company_id = ${
            company.id
          }

          ${specialJoinsSql}

          WHERE mi.deleted_on IS NULL
          AND (mr.confidence >= ${ai_threshold} OR mr.confidence IS NULL)

          AND ec.sec_company_id = ${company.id}
    
          ${filtersSql}

          AND mr.item_id IS NOT NULL
          AND (mr.is_excluded = 0 OR mr.is_excluded IS NULL)
          AND mr.istype IN (1, 2)
          AND mi.sec_company_id IN (${company.id}, 25)
          ORDER BY mi.item_label;

          SELECT DISTINCT 
            mi.mclass_item_id,
            mi.item_label,
            mil.item_label_locale,
		        CASE WHEN mi.parent_id IS NULL THEN -1 ELSE mi.parent_id END as parent,
		        mi.order_number,
		        (CASE WHEN mi.sec_company_id = 25 THEN 1 ELSE 0 END) global_record
	        FROM mclass_item mi
	        LEFT JOIN mclass_item_local mil ON mil.mclass_item_id = mi.mclass_item_id AND mil.sec_company_id = ${
            company.id
          }

			    ${
            filters.filtersObject.survey?.length > 0
              ? `left join mclass_proces_item mpi ON mi.mclass_item_id = mpi.item_id AND mpi.survey_id IN (${filters.filtersObject.survey
                  .map((i) => i.id)
                  .join(',')})`
              : ''
          }

	        WHERE mi.deleted_by IS NULL
          AND mi.sec_company_id IN (${company.id}, 25)

          -- if there are surveys selected in the filters, include following filter
          ${
            filters.filtersObject.survey?.length > 0
              ? 'AND mpi.is_active = 1'
              : ''
          }

	        order by parent, global_record DESC,  mi.order_number;

          SELECT
          count(distinct a.surv_surveyanswer_id)
          FROM surv_surveyanswer a
            LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id
            LEFT JOIN mclass_response mr ON a.surv_surveyanswer_id = mr.answer_id

            ${specialJoinsSql}

          WHERE
          -- make sure to only select answers where comments have been entered.
          (a.comments_pos_length > 5 OR a.comments_neg_length > 5) 

          AND ec.sec_company_id = ${company.id}

          ${filtersSql}
        `;
      }

      case ReportTemplate.PotentialAndLike: {
        return `
          SELECT mr.item_id,
          a.surv_surveyanswer_id AS sa_id,
          mi.item_label,
          CASE WHEN mr.is_liked = 1 THEN 1 ELSE 0 END AS isLike,
          CASE WHEN mr.is_potential = 1 THEN 1 ELSE 0 END AS isPot
          FROM surv_surveyanswer a
          LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id
          LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id
          LEFT JOIN mclass_response mr ON a.surv_surveyanswer_id = mr.answer_id
          LEFT JOIN mclass_item mi ON mr.item_id = mi.mclass_item_id

          ${specialJoinsSql}

          WHERE 1 = 1
          AND mi.parent_id IS NULL AND mi.deleted_by IS NULL 
          AND ( mi.sec_company_id  = ${company.id}  OR mi.sec_company_id = 25)
          AND mr.item_id IS NOT NULL
          AND (mr.is_excluded = 0 OR mr.is_excluded IS NULL)
          AND (mr.is_potential > 0 or mr.is_liked > 0)

          AND ec.sec_company_id = ${company.id}
          
          ${filtersSql};

          SELECT count(distinct answer_id) FROM mclass_response mr 
					LEFT JOIN surv_surveyanswer a ON mr.answer_id = a.surv_surveyanswer_id 
					LEFT JOIN email_customer ec ON a.email_customer_id= ec.email_customer_id 
					LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id 
          ${specialJoinsSql}
					WHERE (mr.is_excluded = 0 OR mr.is_excluded IS NULL)
          AND mr.istype = 0
          ${filtersSql};
          SELECT 
            mi.mclass_item_id,  mi.item_label, mil.item_label_locale, 
            (CASE WHEN mi.sec_company_id = 25 THEN 1 ELSE 0 END) global_record 
          FROM 
            mclass_item mi 
            LEFT JOIN mclass_item_local mil ON mil.mclass_item_id = mi.mclass_item_id 
            AND mil.sec_company_id = ${company.id}
          WHERE 
            parent_id IS NULL 
            AND deleted_by IS NULL 
            AND (
              mi.sec_company_id = ${company.id}
              OR mi.sec_company_id = 25
            ) 
          ORDER BY 
            global_record DESC,
            order_number
        `;
      }
      case ReportTemplate.Multiple_Choice: {
        if (!templateQuestionId && !surveyQuestionId) {
          throw new HttpException(
            'Params not specified',
            HttpStatus.BAD_REQUEST,
          );
        }

        if (templateQuestionId && surveyQuestionId) {
          throw new HttpException(
            'Please, specify one param',
            HttpStatus.BAD_REQUEST,
          );
        }

        const getTotalQuery = () => `
          SELECT count(DISTINCT a.surv_surveyanswer_id)
          FROM surv_surveyanswer a
          LEFT JOIN surv_surveyansweritem ai ON a.surv_surveyanswer_id = ai.surv_surveyanswer_id
          LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

          ${specialJoinsSql}

          ${
            templateQuestionId
              ? `
                LEFT JOIN surv_surveyquestion sq ON sq.surv_surveyquestion_id = ai.surv_surveyquestion_id
                WHERE sq.surv_survquest_temp_id = ${templateQuestionId}
              `
              : ``
          }

          ${
            surveyQuestionId
              ? `WHERE ai.surv_surveyquestion_id = ${surveyQuestionId}`
              : ``
          }

          ${filtersSql}
        `;

        // Single datasets
        if (this.isBar(filters.filtersObject.chart_model)) {
          return `
            SELECT ai.return_value, count(ai.return_value)
            FROM surv_surveyanswer a
            LEFT JOIN surv_surveyansweritem ai ON a.surv_surveyanswer_id = ai.surv_surveyanswer_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

            ${specialJoinsSql}

            ${
              templateQuestionId
                ? `
                  LEFT JOIN surv_surveyquestion sq ON sq.surv_surveyquestion_id = ai.surv_surveyquestion_id
                  WHERE sq.surv_survquest_temp_id = ${templateQuestionId}
                `
                : ``
            }

            ${
              surveyQuestionId
                ? `WHERE ai.surv_surveyquestion_id = ${surveyQuestionId}`
                : ``
            }

            ${filtersSql}

            group by ai.return_value;

            ${getTotalQuery()};

            ${
              templateQuestionId
                ? `
                  SELECT sp.title FROM surv_surveyquestion_template sqt 
                  LEFT JOIN surv_surveyquestion sq on sq.surv_survquest_temp_id = sqt.surv_surveyquestion_template_id 
                  LEFT JOIN surv_surveypart sp ON sp.surv_surveypart_id = sq.surv_surveypart_id 
                  LEFT JOIN surv_survey s ON sp.surv_survey_id = s.surv_survey_id 
                  WHERE sqt.surv_surveyquestion_template_id = ${templateQuestionId}
                `
                : ``
            }

            ${
              surveyQuestionId
                ? `
                  SELECT sp.title FROM surv_surveyquestion sq 
                  LEFT JOIN surv_surveypart sp ON sq.surv_surveypart_id = sp.surv_surveypart_id 
                  LEFT JOIN surv_survey s ON sp.surv_survey_id = s.surv_survey_id 
                  WHERE surv_surveyquestion_id = ${surveyQuestionId}
                `
                : ``
            }
          `;
        }

        // Multiple datasets
        return `
          SELECT ai.return_value, count(ai.return_value), date_trunc('${period}', ${dateField}) period
          FROM surv_surveyanswer a
          LEFT JOIN surv_surveyansweritem ai ON a.surv_surveyanswer_id = ai.surv_surveyanswer_id
          LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id

          ${specialJoinsSql}

          ${
            templateQuestionId
              ? `
              LEFT JOIN surv_surveyquestion sq ON sq.surv_surveyquestion_id = ai.surv_surveyquestion_id
              WHERE sq.surv_survquest_temp_id = ${templateQuestionId}
            `
              : ``
          }

          ${
            surveyQuestionId
              ? `WHERE ai.surv_surveyquestion_id = ${surveyQuestionId}`
              : ``
          }

          ${filtersSql}

          group by ai.return_value, period
          order by ai.return_value, period;

          ${getTotalQuery()}
        `;
      }
      case ReportTemplate.Feedback: {
        return `
          select ${dateField} as dt,
          a.nps_value AS nps,
          pos.return_value AS strengths,
          neg.return_value AS improvements,
          s.internal_name AS survey,
          concat_ws(' ', ec.xml_initials, ec.xml_customername) as customername,
          ec.xml_phone2,
          ec.xml_email,
          a.surv_surveyanswer_id,
          ec.sec_company_id,
          cc.clf_call_id
          from surv_surveyanswer a
          left join surv_survey s ON a.surv_survey_id = s.surv_survey_id
          left join surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND s.clf_question2 = pos.surv_surveyquestion_id
          left join surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND s.clf_question1 = neg.surv_surveyquestion_id
          left join email_customer ec ON a.email_customer_id = ec.email_customer_id
          left join clf_call cc on a.email_customer_id = cc.email_customer_id

          ${specialJoinsSql}
          
          where 1 = 1

          AND ec.sec_company_id = ${company.id}

          ${filtersSql}

          ORDER BY dt DESC
          
          LIMIT 20
          ${page ? `OFFSET ${20 * (page - 1)}` : ''} 
        `;
      }
      case ReportTemplate.Custom: {
        let field = groupByField;
        let sql = '';
        let [table] = this.importService.translateTablename(field);

        if (table == 'techgrp') {
          sql = `CASE WHEN map.techgrp_name IS NULL OR map.techgrp_name = '' THEN ${field} ELSE map.techgrp_name END`;
        } else if (table == 'bsh_mapping') {
          sql = `CASE WHEN map.map_description IS NULL OR map.map_description = '' THEN ${field} ELSE map.map_description END`;
        }

        return `
        WITH data AS
          (SELECT
            lower(${sql}) AS ${field},
            a.nps_value,  ${dateField} as date_field,
            CASE WHEN a.nps_value BETWEEN 0 AND 6 THEN 1 ELSE 0 END AS detractor,
            CASE WHEN a.nps_value BETWEEN 7 AND 8 THEN 1 ELSE 0 END AS passive,
            CASE WHEN a.nps_value > 8 THEN 1 ELSE 0 END AS promoter
          FROM surv_surveyanswer a
            LEFT JOIN surv_survey s on a.surv_survey_id = s.surv_survey_id
            LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id
            ${
              table == 'techgrp'
                ? `
            LEFT JOIN techgrp map
            ON
              lower(ec.${field}) = lower(map.techgrp_techn)
              AND map.sec_company_id = ec.sec_company_id
              AND map.techgrp_type = '${field}'
            `
                : ''
            }

            ${
              table == 'bsh_mapping'
                ? `
            LEFT JOIN bsh_mapping map ON ec.${field} = map.map_key AND 
						map.sec_company_id = ec.sec_company_id AND map.map_field = '${field}'
            `
                : ''
            }
            ${specialJoinsSql}
          WHERE
            ec.survey_done = 1
            AND a.sec_company_id  = ${company.id}
            AND a.nps_value IS NOT NULL
            AND CASE WHEN map.techgrp_name IS NULL OR map.techgrp_name = '' THEN ${field} ELSE map.techgrp_name END IS NOT NULL
            ${filtersSql}
          );
       SELECT
       ${field},
       date_trunc('week', date_field::date) as period,
       sum(promoter) AS promoters,sum(passive) AS passives, sum(detractor) AS detractors,
       COUNT(nps_value) AS cnt
     FROM data
     GROUP BY period ,${field}
     ORDER BY ${field}, period;

     SELECT  date_trunc('${period}', date_field::date) as period 	FROM data  GROUP BY period order by period
        `;
      }
    }
  }

  private async getAndFormatResult(
    reportTemplate: ReportTemplate,
    sqlQuery: string,
    period: 'day' | 'week' | 'month' | 'year' | 'quarter',
    chartType: ChartType,
    queryChartType: ChartType,
    chartParams: ChartParams,
    filters: CombinedFilters,
  ) {
    const {
      surveyQuestionId = 0,
      templateQuestionId = 0,
      groupByField = null,
    } = chartParams;
    const { dateType } = filters;
    const sortPeriods = (a: string | Date, b: string | Date) => {
      try {
        return moment(a).diff(moment(b), 'hour');
      } catch {
        return null;
      }
    };

    let labelsLength = 0;

    const formatPeriod = (date: string | Date) => {
      switch (period) {
        case 'week': {
          return `Week ${moment(date).isoWeek()}`;
        }
        case 'year': {
          return `${moment(date).get(period)}`;
        }
        case 'day': {
          return `${moment(date).format('DD')}-${moment(date).format('MM')}`;
        }
        case 'month': {
          if (labelsLength > 12) {
            return moment(date).format('MMM YY');
          } else {
            return moment(date).format('MMMM');
          }
        }
        case 'quarter': {
          return moment(date).format('Y [Q]Q');
        }
      }
    };

    const formatPeriodKey = (date: string | Date) => {
      let result = null;
      switch (period) {
        case 'week': {
          // result = `wk_${moment(date).format('YYYY_W')}`;
          result = `wk${moment(date).isoWeek()}-${moment(date).get('year')}`;
          break;
        }
        case 'year': {
          result = `${moment(date).format('YYYY')}`;
          break;
        }
        case 'day': {
          result = `${moment(date).format('YYYY_MM_DD')}`;
          break;
        }
        case 'month': {
          result = moment(date).format('YYYY_MMM');
          break;
        }
        case 'quarter': {
          result = moment(date).format('[q]Q_Y');
          break;
        }
      }
      return result?.toLowerCase();
    };

    const getColorByChartType = (type: ChartType) => {
      switch (type) {
        case ChartType.BAR:
          return {
            backgroundColor: 'rgba(64,177,217,0.7)',
            borderColor: 'rgba(64,177,217,0.9)',
          };
        // return {
        //   backgroundColor: 'rgba(108,181,73,0.7)',
        //   borderColor: 'rgba(108,181,73,0.9)',
        // };
        case ChartType.HORIZONTAL_BAR:
          return {
            backgroundColor: 'rgba(64,177,217,0.9)',
            borderColor: 'rgba(64,177,217,0.9)',
          };
        case ChartType.LINE:
        case ChartType.SPLINE:
        case ChartType.SPLINE_AREA:
        case ChartType.LINE_AREA:
          return {
            backgroundColor: 'rgba(13,142,37,0.7)',
            borderColor: 'rgba(13,142,37,0.9)',
          };
        default:
          return {
            backgroundColor: 'rgba(64,177,217,0.9)',
            borderColor: 'rgba(64,177,217,0.9)',
          };
      }
    };

    const formatToWkYear = (date: string | Date) =>
      `wk${moment(date).isoWeek()}-${moment(date).get('year')}`;

    const getNps = ({ cnt, detractors, promoters }: GetNpsOptions) =>
      Math.round((100 * (+promoters - +detractors)) / +cnt) || 0;

    const getCSAT = ({ cnt, promoters }: Omit<GetNpsOptions, 'decorators'>) =>
      Math.round((+promoters / +cnt) * 100) || 0;

    const getColorByIndex = (
      index = 0,
    ): {
      backgroundColor: string;
      borderColor: string;
    } => {
      if (index > colors.length - 1) {
        return getColorByIndex(index - colors.length);
      }
      return colors[index];
    };

    switch (reportTemplate) {
      case ReportTemplate.CSAT_SCORE:
      case ReportTemplate.NPS_Score: {
        let [dataQuery, totalQuery, cnttxtQuery] = sqlQuery.split(';');

        let result = await this.surveyAnswerEntity.query(dataQuery);
        const [{ cnt, detractors, promoters }] = result;

        const [{ total: sentCount }] = await this.surveyAnswerEntity.query(
          totalQuery,
        );

        let metric = 'NPS';
        let value = null;

        const [{ cnttxt }] = await this.surveyAnswerEntity.query(cnttxtQuery);

        if (reportTemplate == ReportTemplate.CSAT_SCORE) {
          metric = 'CSAT';
          value = Math.round((promoters / sentCount) * 100);
        } else {
          value = getNps({ cnt, detractors, promoters });
        }

        const feedbackRate = (100 * cnttxt) / cnt;
        let responseRate = (cnt / sentCount) * 100;

        if (dateType == DateField.ResponseDate) {
          responseRate = 100;
        }

        return {
          value,
          metric,
          allowed_charttypes: ChartType.GAUGE,
          total: Math.round(Number(cnt)) || 0,
          feedback_rate: Math.round(feedbackRate) || 0,
          response_rate: Math.round(responseRate) || 0,
        };
      }
      case ReportTemplate.CSAT_TREND:
      case ReportTemplate.NPS_Trend: {
        const result = (
          (await this.surveyAnswerEntity.query(sqlQuery)) as NpsTrendItem[]
        ).map<NpsTrendItem<number>>((i) => ({
          cnt: Number(i.cnt),
          detractors: Number(i.detractors),
          passives: Number(i.passives),
          promoters: Number(i.promoters),
          period: i.period,
        }));

        let metric =
          reportTemplate == ReportTemplate.CSAT_TREND ? 'CSAT' : 'NPS';

        // const formatWeekNames = (data: NpsTrendItem[]) =>
        //   data.map(({ period }) => formatToWkYear(period));

        const formatTrends = (data: NpsTrendItem<number>[]) =>
          data
            .reduce<FormatTrendsAccumulatorItem[]>(
              (accumulator, item, index) => {
                const { promoters, detractors, cnt } = item;
                if (index === 0) {
                  let value = null;
                  if (metric == 'CSAT') {
                    value = getCSAT(item);
                  } else {
                    value = getNps(item);
                  }

                  accumulator.push({
                    value,
                    prevDetractors: detractors,
                    prevPromoters: promoters,
                    prevCnt: cnt,
                  });
                } else {
                  const { prevPromoters, prevDetractors, prevCnt } =
                    accumulator[index - 1];

                  let value = null;
                  let params = {
                    cnt: cnt + prevCnt,
                    detractors: detractors + prevDetractors,
                    promoters: promoters + prevPromoters,
                  };
                  if (metric == 'CSAT') {
                    value = getCSAT(params);
                  } else {
                    value = getNps(params);
                  }

                  accumulator.push({
                    value,
                    prevDetractors: detractors + prevDetractors,
                    prevPromoters: promoters + prevPromoters,
                    prevCnt: cnt + prevCnt,
                  });
                }
                return accumulator;
              },
              [],
            )
            .map((i) => Math.round(i.value));

        return {
          allowed_charttypes: ChartType.MIXED_LINE_BAR,
          dataSets: [
            {
              border: 'none',
              backgroundColor: 'rgba(64,177,217,0.7)',
              borderColor: 'rgba(64,177,217,0.9)',
              data: metric == 'CSAT' ? result.map(getCSAT) : result.map(getNps),
              filled: true,
              label: metric,
              type: 'bar',
            },
            {
              border: 'solid',
              backgroundColor: 'rgba(108,181,73,0.25)',
              borderColor: 'rgba(108,181,73,1)',
              data: formatTrends(result),
              filled: false,
              label: 'Trend',
              type: 'line',
            },
          ],
          type: chartType,
          labels: result.map((item) =>
            this.dateUtilService.getPeriodLabel(item.period, period),
          ),
        };
      }
      case ReportTemplate.NPS_Segments: {
        const [{ detractors, passives, promoters }] =
          (await this.surveyAnswerEntity.query(sqlQuery)) as NpsSegmentsItem[];

        const total = Number(detractors) + Number(passives) + Number(promoters);

        const detractorsPercent =
          Math.round((Number(detractors) * 100) / total) || 0;
        const passivesPercent =
          Math.round((Number(passives) * 100) / total) || 0;
        const promotersPercent =
          Math.round((Number(promoters) * 100) / total) || 0;

        return {
          allowed_charttypes: ChartType.BAR_DIFF_COLORS,
          dataSets: [
            {
              backgroundColor: [
                'rgba(227,35,26,1)',
                'rgba(255,155,34,1)',
                'rgba(108,181,73,1)',
              ],
              borderColor: [
                'rgba(227,35,26,0.9)',
                'rgba(255,155,34,0.9)',
                'rgba(108,181,73,0.9)',
              ],
              data: [detractorsPercent, passivesPercent, promotersPercent],
              label: ['Detractors', 'Passives', 'Promoters'],
              values: [Number(detractors), Number(passives), Number(promoters)],
            },
          ],
          type: chartType,
          labels: ['Detractors', 'Passives', 'Promoters'],
        };
      }
      case ReportTemplate.NPS_Scale: {
        let isLinear = this.isLinear(chartType);
        let isFilled = this.isFilled(chartType);
        const allowed_charttypes =
          'horizontalBar,line,spline,bar,line_area,spline_area,table';
        const result = (await this.surveyAnswerEntity.query(
          sqlQuery,
        )) as NpsScaleItem[];

        const { backgroundColor, borderColor } = getColorByChartType(chartType);
        if (queryChartType == ChartType.TABLE) {
          return {
            allowed_charttypes,
            type: ChartType.TABLE,
            rows: result.map((i) => {
              return {
                name: i.nps_value,
                value: +i.cnt,
              };
            }),
            cols: [
              {
                name: '',
                sticky: true,
                id: 'name',
              },
              {
                name: 'count',
                sticky: false,
                id: 'value',
              },
            ],
          };
        }
        let labels: any = result.map((i) => i.nps_value);
        let dataSets: any = [];

        if (isLinear) {
          labels = this.dateUtilService
            .getPeriods(filters.fromDate, filters.toDate, filters.period)
            .sort(sortPeriods);
          let labelKeys = labels.map(formatPeriodKey);

          let nps_values = [...new Set(result.map((item) => item.nps_value))];

          dataSets = Object.entries(nps_values).map(([index, nps_value]) => {
            let values: any = result
              .filter((i) => i.nps_value == nps_value)
              .map((i) => ({ ...i, period: formatPeriodKey(i.dt) }));
            values = labelKeys.map((label) => {
              let value = values.find((i) => i.period === label);
              if (!value) return 0;
              else return value.cnt;
            });
            return this.createDataset(
              nps_value,
              this.getChartColor(index),
              values,
              isFilled,
            );
          });

          labelsLength = labels.length;

          return {
            allowed_charttypes,
            dataSets,
            type: chartType,
            labels: labels.map((label) =>
              this.dateUtilService.getPeriodLabel(label, period, labels.length),
            ),
          };
        } else {
          let values = [];
          for (let item of result) {
            values.push(+item.cnt);
          }

          if (result.length > 0) {
            // Add empty values
            for (let i = 0; i <= 10; i++) {
              if (labels[i] !== i) {
                labels.splice(i, 0, i);
                values.splice(i, 0, 0);
              }
            }
          }
          return {
            allowed_charttypes,
            dataSets: [
              this.createDataset(
                'NPS Numbers',
                'rgba(108,181,73,0.7)',
                values,
                false,
              ),
            ],
            type: chartType,
            labels,
          };
        }
      }
      case ReportTemplate.Brands: {
        const allowed_charttypes =
          'horizontalBar,line,spline,bar,line_area,spline_area,table';
        let data = await this.surveyAnswerEntity.query(sqlQuery);
        labelsLength = data.length;
        const result = (data as BrandsItem[]).map((i) => ({
          brand: i.xml_brand,
          nps: getNps(i),
          period: moment(i.period).format('YYYY-MM-DD'),
          periodKey: formatPeriodKey(i.period),
          periodName: formatPeriod(i.period),
        }));

        const periods = [...new Set(result.map((i) => i.period))].sort(
          sortPeriods,
        );
        const formattedPeriods = [
          ...new Set(result.map((i) => i.periodName)),
        ].sort(sortPeriods);

        if (queryChartType == ChartType.TABLE) {
          return {
            allowed_charttypes,
            type: ChartType.TABLE,
            rows: Object.values(
              result.reduce((prev, item) => {
                const { brand } = item;
                if (!prev[brand]) {
                  prev[brand] = {
                    name: brand,
                    ...periods.reduce((prev, period) => {
                      let key = formatPeriodKey(period);
                      let nps = result.find(
                        (i) => i.periodKey == key && i.brand == brand,
                      )?.nps;
                      prev[key] = nps !== undefined ? nps : '-';
                      return prev;
                    }, {}),
                  };
                }

                return prev;
              }, {}),
            ),
            cols: result.reduce((prev, item) => {
              if (prev.length == 0)
                prev.push({ name: '', id: 'name', sticky: false });

              const { periodKey, periodName } = item;
              const isExists = prev.find((i) => i.id == periodKey);
              if (!isExists)
                prev.push({ id: periodKey, name: periodName, sticky: false });
              return prev;
            }, []),
          };
        }

        const { backgroundColor, borderColor } = getColorByChartType(chartType);
        let dataSets: any = [
          {
            border: 'none',
            backgroundColor: borderColor,
            borderColor,
            data: result.map((i) => i.nps),
            color: '#dfdfdf',
            label: 'NPS',
          },
        ];
        let labels: any;
        if (this.isLinear(chartType)) {
          dataSets = Object.values(
            result.reduce((prev: any, item) => {
              if (!prev[item.brand])
                prev[item.brand] = { item, list: [...periods] };
              let index = prev[item.brand].list.indexOf(item.period);
              if (index >= 0) prev[item.brand].list[index] = item;
              return prev;
            }, {}),
          ).map((item: any, index: any) => {
            let color = this.getChartColor(index);
            let values = item.list.map((i) =>
              i?.nps !== undefined ? i.nps : null,
            );
            return this.createDataset(
              item.item.brand,
              color,
              values,
              this.isFilled(chartType),
            );
          });
        }
        labelsLength = periods.length;
        return {
          allowed_charttypes,
          dataSets,
          type: chartType,
          labels: this.isLinear(chartType)
            ? periods.map(formatPeriod)
            : result.map((i) => i.brand),
        };
      }
      case ReportTemplate.Touchpoints: {
        const allowed_charttypes =
          'horizontalBar,line,spline,bar,line_area,spline_area,table';
        const result = (
          (await this.surveyAnswerEntity.query(sqlQuery)) as TouchpointsItem[]
        ).map((i) => ({
          label: i.internal_name,
          nps: getNps(i),
          period: moment(i.period).format('YYYY-MM-DD'),
          periodKey: formatPeriodKey(i.period),
          periodName: formatPeriod(i.period),
        }));

        const periods = [...new Set(result.map((i) => i.period))].sort(
          sortPeriods,
        );
        const labels = [...new Set(result.map((i) => i.label))];

        if (queryChartType == ChartType.TABLE) {
          return {
            allowed_charttypes,
            type: ChartType.TABLE,
            rows: Object.values(
              result.reduce((prev, item) => {
                const { label } = item;
                if (!prev[label]) {
                  prev[label] = {
                    name: label,
                    ...periods.reduce((prev, period) => {
                      let key = formatPeriodKey(period);
                      let nps = result.find(
                        (i) => i.periodKey == key && i.label == label,
                      )?.nps;
                      prev[key] = nps !== undefined ? nps : '-';
                      return prev;
                    }, {}),
                  };
                }

                return prev;
              }, {}),
            ),
            cols: periods.reduce((prev, item) => {
              if (prev.length == 0)
                prev.push({ name: '', id: 'name', sticky: false });

              let periodKey = formatPeriodKey(item);
              let periodName = formatPeriod(item);
              const isExists = prev.find((i) => i.id == periodKey);
              if (!isExists)
                prev.push({ id: periodKey, name: periodName, sticky: false });
              return prev;
            }, []),
          };
        }

        if (this.isLinear(chartType)) {
          const labelsWithNps = periods.reduce((accumulator, periodOfTime) => {
            labels.forEach((label) => {
              if (!accumulator[label]) {
                accumulator[label] = [];
              }
              const value = result.find(
                (i) => i.label === label && i.period === periodOfTime,
              );
              accumulator[label].push(
                value?.nps !== undefined ? value.nps : null,
              );
            });

            return accumulator;
          }, {});

          labelsLength = periods.length;

          return {
            allowed_charttypes,
            dataSets: Object.keys(labelsWithNps).map((label, index) => {
              return this.createDataset(
                label,
                this.getChartColor(index),
                labelsWithNps[label],
                this.isFilled(chartType),
              );
            }),
            type: chartType,
            labels: periods.map(formatPeriod),
          };
        } else {
          return {
            allowed_charttypes,
            dataSets: [
              {
                label: 'NPS',
                data: result.map((i) => i.nps),
                backgroundColor: 'rgba(64,177,217, 0.9)',
                borderColor: 'rgba(64,177,217, 0.9)',
                color: '#dfdfdf',
                border: 'none', // none / solid / dotted / dashed
              },
            ],
            type: chartType,
            labels: labels.map(this.formatLabel),
          };
        }
      }
      case ReportTemplate.NPS_Time: {
        const result = (await this.surveyAnswerEntity.query(
          sqlQuery,
        )) as NpsTimeItem[];

        const { backgroundColor, borderColor } = getColorByChartType(chartType);
        let values = result.map(getNps);
        let dataSets = [];
        dataSets.push(
          this.createDataset(
            'NPS',
            backgroundColor,
            values,
            this.isFilled(chartType),
          ),
        );
        labelsLength = result.length;
        return {
          allowed_charttypes: 'line,spline,line_area,spline_area',
          dataSets,
          type: chartType,
          labels: result.map((i) => formatPeriod(i.period)),
        };
      }
      case ReportTemplate.Sent_Emails: {
        const allowed_charttypes =
          'stacked_bar,group_bar,line_area,spline_area,table';
        let list = (await this.surveyAnswerEntity.query(
          sqlQuery,
        )) as SentEmailsItem[];
        const result = list.map((i) => ({
          answered: Number(i.answered),
          reminder: Number(i.reminder),
          sent: Number(i.sent),
          thank: Number(i.thank),
          date: moment(i.period).format('YYYY-MM-DD'),
          period: formatPeriod(i.period),
          periodKey: formatPeriodKey(i.period),
        }));

        const periods = [...new Set(result.map((i) => i.date))]
          .sort(sortPeriods)
          .map((i) => formatPeriodKey(i));

        if (queryChartType == ChartType.TABLE) {
          let rows = [
            { key: 'sent', name: 'First Messages' },
            { key: 'reminder', name: 'Reminder Messages' },
            { key: 'thank', name: 'Thank Messages' },
            { key: 'answered', name: 'Answered Messages' },
          ];
          return {
            allowed_charttypes,
            type: ChartType.TABLE,
            rows: Object.values(
              rows.reduce((prev, item) => {
                const { key, name } = item;
                if (!prev[key]) {
                  prev[key] = {
                    name,
                    ...periods.reduce((prev, period) => {
                      prev[period] =
                        result.find((i) => i.periodKey == period)?.[key] || '-';
                      return prev;
                    }, {}),
                  };
                }

                return prev;
              }, {}),
            ),
            cols: result.reduce((prev, item) => {
              if (prev.length == 0)
                prev.push({ name: '', id: 'name', sticky: false });

              const { periodKey } = item;
              const isExists = prev.find((i) => i.id == periodKey);
              if (!isExists)
                prev.push({ id: periodKey, name: periodKey, sticky: false });
              return prev;
            }, []),
          };
        }

        const firstColor = getColorByIndex(0);
        const reminderColor = getColorByIndex(1);
        const thankColor = getColorByIndex(2);
        const answeredColor = getColorByIndex(3);

        let labels = result.map((i) =>
          this.dateUtilService.getPeriodLabel(i.date, period, result.length),
        );
        return {
          allowed_charttypes,
          dataSets: [
            {
              border: 'none',
              backgroundColor: firstColor.backgroundColor,
              borderColor: firstColor.borderColor,
              data: result.map((i) => i.sent),
              filled: false,
              label: 'First messages',
              spanGaps: true,
            },
            {
              border: 'none',
              backgroundColor: reminderColor.backgroundColor,
              borderColor: reminderColor.borderColor,
              data: result.map((i) => i.reminder),
              filled: false,
              label: 'Reminder messages',
              spanGaps: true,
            },
            {
              border: 'none',
              backgroundColor: thankColor.backgroundColor,
              borderColor: thankColor.borderColor,
              data: result.map((i) => i.thank),
              filled: false,
              label: 'Thank messages',
              spanGaps: true,
            },
            {
              border: 'none',
              backgroundColor: answeredColor.backgroundColor,
              borderColor: answeredColor.borderColor,
              data: result.map((i) => i.answered),
              filled: false,
              label: 'Answered messages',
              spanGaps: true,
            },
          ],
          type: chartType,
          labels,
        };
      }
      case ReportTemplate.Region: {
        const allowed_charttypes =
          'horizontalBar,line,spline,bar,line_area,spline_area,table';
        const result = (
          (await this.surveyAnswerEntity.query(sqlQuery)) as RegionItem[]
        ).map((i) => ({
          region: i.xml_region,
          nps: getNps(i),
          period: moment(i.period).format('YYYY-MM-DD'),
          periodKey: formatPeriodKey(i.period),
          periodName: formatPeriod(i.period),
        }));

        const periods = [...new Set(result.map((i) => i.period))].sort(
          sortPeriods,
        );
        const regions = [...new Set(result.map((i) => i.region))];
        labelsLength = periods.length;
        if (queryChartType == ChartType.TABLE) {
          let cols = result
            .reduce((prev, item) => {
              if (prev.length == 0)
                prev.push({ name: '', id: 'name', sticky: false });

              const { periodKey, periodName, period } = item;
              const isExists = prev.find((i) => i.id == periodKey);
              if (!isExists)
                prev.push({
                  id: periodKey,
                  name: periodName,
                  period,
                  sticky: false,
                });
              return prev;
            }, [])
            .sort((a, b) => sortPeriods(a.period, b.period));
          cols = [
            ...cols.filter((a) => !a.period),
            ...cols.filter((a) => a.period),
          ];

          labelsLength = cols.length;
          cols = cols.map((col) => {
            if (!col.period) return col;
            return { ...col, name: formatPeriod(col.period) };
          });
          let rows = result.reduce((prev, item) => {
            const { region } = item;
            if (!prev[region]) {
              prev[region] = {
                name: region,
                ...periods.reduce((prev, period) => {
                  let key = formatPeriodKey(period);
                  let nps = result.find(
                    (i) => i.periodKey == key && i.region == region,
                  )?.nps;
                  prev[key] = nps !== undefined ? nps : '-';
                  return prev;
                }, {}),
              };
            }

            return prev;
          }, {});
          return {
            allowed_charttypes,
            type: ChartType.TABLE,
            rows: Object.values(rows),
            cols,
          };
        } else if (this.isLinear(chartType)) {
          let dataSets = Object.values(
            result.reduce((prev: any, item) => {
              if (!prev[item.region])
                prev[item.region] = { item, list: [...periods] };
              let index = prev[item.region].list.indexOf(item.period);
              if (index >= 0) prev[item.region].list[index] = item;
              return prev;
            }, {}),
          ).map((item: any, index: any) => {
            let values = item.list.map((i) =>
              i?.nps !== undefined ? i.nps : null,
            );
            return this.createDataset(
              item.item.region,
              this.getChartColor(index),
              values,
              this.isFilled(chartType),
            );
          });
          labelsLength = periods.length;
          return {
            allowed_charttypes,
            dataSets,
            type: chartType,
            labels: periods.map(formatPeriod),
          };
        } else if (this.isBar(chartType)) {
          let dataSets = [
            {
              label: 'NPS',
              data: regions.map(
                (region) =>
                  result.find((item) => item.region == region)?.nps || 0,
              ),
              backgroundColor: 'rgba(64,177,217, 0.9)',
              borderColor: 'rgba(64,177,217, 0.9)',
              color: '#dfdfdf',
              border: 'none', // none / solid / dotted / dashed
            },
          ];

          return {
            allowed_charttypes,
            dataSets,
            type: chartType,
            labels: regions.map(this.formatLabel),
          };
        } else {
          const regionsWithNps = periods.reduce<{
            [key: string]: number[];
          }>((accumulator, periodOfTime) => {
            regions.forEach((region) => {
              if (!accumulator[region]) {
                accumulator[region] = [];
              }
              const value = result.find(
                (i) => i.region === region && i.period === periodOfTime,
              );
              accumulator[region].push(value?.nps || 0);
            });

            return accumulator;
          }, {});

          let dataSets =
            result.length == 0
              ? []
              : Object.keys(regionsWithNps).map((region, index) => {
                  const { backgroundColor, borderColor } =
                    getColorByIndex(index);
                  return {
                    border: 'none',
                    backgroundColor,
                    borderColor,
                    data: regionsWithNps[region],
                    filled: false,
                    label: region,
                  };
                });

          return {
            allowed_charttypes,
            dataSets,
            type: chartType,
            labels: Object.keys(regionsWithNps).map(this.formatLabel),
          };
        }
      }
      case ReportTemplate.Response_Behaviour: {
        const result = (
          (await this.surveyAnswerEntity.query(
            sqlQuery,
          )) as ResponseBehaviorItem[]
        )
          .map((i) => ({
            nps: i.nps,
            periodNumber: Number(
              (i.period.toPostgres() as string).split(' ')[0],
            ),
            sum: Number(i.sum) | 0,
          }))
          .sort((a, b) => a.periodNumber - b.periodNumber);

        const labels = [
          'On the same day',
          'After one day',
          'After two days',
          'After three days',
          'After four days',
          'After five days',
          'After six days',
          'After seven days',
          'After two weeks',
          'After three weeks',
          'After four weeks',
          'Longer',
        ];

        const totalRespondents = result.reduce((accumulator, { sum }) => {
          return (accumulator += sum);
        }, 0);

        const getNpsByPeriod = (
          array: {
            nps: number;
            periodNumber: number;
            sum: number;
          }[],
          periodName: string,
        ) => {
          const getFilteredData = (
            array: {
              nps: number;
              periodNumber: number;
              sum: number;
            }[],
            periodName: string,
          ) => {
            switch (periodName) {
              case 'On the same day': {
                return array.filter(({ periodNumber }) => periodNumber === 0);
              }
              case 'After one day': {
                return array.filter(({ periodNumber }) => periodNumber === 1);
              }
              case 'After two days': {
                return array.filter(({ periodNumber }) => periodNumber === 2);
              }
              case 'After three days': {
                return array.filter(({ periodNumber }) => periodNumber === 3);
              }
              case 'After four days': {
                return array.filter(({ periodNumber }) => periodNumber === 4);
              }
              case 'After five days': {
                return array.filter(({ periodNumber }) => periodNumber === 5);
              }
              case 'After six days': {
                return array.filter(({ periodNumber }) => periodNumber === 6);
              }
              case 'After seven days': {
                return array.filter(({ periodNumber }) => periodNumber === 7);
              }
              case 'After two weeks': {
                return array.filter(
                  ({ periodNumber }) => periodNumber > 7 && periodNumber < 15,
                );
              }
              case 'After three weeks': {
                return array.filter(
                  ({ periodNumber }) => periodNumber > 14 && periodNumber < 22,
                );
              }
              case 'After four weeks': {
                return array.filter(
                  ({ periodNumber }) => periodNumber > 21 && periodNumber < 29,
                );
              }
              case 'Longer': {
                return array.filter(({ periodNumber }) => periodNumber > 28);
              }
            }
          };

          const data = getFilteredData(array, periodName).reduce(
            (accumulator, { sum, nps }) => {
              if (nps > 8) {
                accumulator.npsPro += sum;
              } else if (nps < 7) {
                accumulator.npsDet += sum;
              }
              accumulator.periodCount += sum;
              return accumulator;
            },
            {
              npsPro: 0,
              npsDet: 0,
              periodCount: 0,
            },
          );

          return {
            npsScore: data.periodCount
              ? ((data.npsPro - data.npsDet) / data.periodCount) * 100
              : 0,
            periodCount: data.periodCount,
            periodName,
          };
        };

        const npsByPeriods = labels.map((label) =>
          getNpsByPeriod(result, label),
        );

        const {
          backgroundColor: barBackgroundColor,
          borderColor: barborderColor,
        } = getColorByChartType(ChartType.BAR);

        return {
          type: chartType,
          dataSets: [
            {
              type: ChartType.BAR,
              label: 'Response percentage',
              backgroundColor: barBackgroundColor,
              borderColor: barborderColor,
              filled: true,
              border: 'none',
              data: npsByPeriods.map(({ periodCount }) =>
                Math.round((100 * periodCount) / totalRespondents),
              ),
              yAxis: {
                position: 'left',
              },
            },
            {
              type: ChartType.LINE,
              label: 'NPS',
              backgroundColor: 'rgba(108,181,73,0.25)',
              borderColor: 'rgba(108,181,73,1)',
              border: 'solid',
              data: npsByPeriods.map(({ npsScore }) => Math.round(npsScore)),
              yAxis: {
                position: 'right',
              },
            },
          ],
          labels,
        };
      }
      case ReportTemplate.Classification: {
        const [dataQuery, labelQuery, countQuery] = sqlQuery.split(';');

        const dataArray = (await this.surveyAnswerEntity.query(
          dataQuery,
        )) as ClassificationDataItem[];

        const labelArray = (await this.surveyAnswerEntity.query(
          labelQuery,
        )) as ClassificationLabelItem[];

        let vData = [];

        const countAnswers = (await this.surveyAnswerEntity.query(
          countQuery,
        )) as { count: number }[];
        const totalNumberOfClassifiedAnswers = countAnswers[0].count;

        const findLabelObject = (item_id: number): any => {
          const parent = vData.find((i) =>
            i.children.some((child) => child.id === item_id),
          );

          if (parent) {
            const child = parent.children.find((child) => child.id === item_id);
            return child;
          }
          return null;
        };

        if (dataArray.length > 0) {
          vData = labelArray.reduce<ClassificationLabelAccumulatorItem[]>(
            (accumulator, item) => {
              if (item.parent === -1) {
                accumulator.push({
                  id: item.mclass_item_id,
                  cnt: 0,
                  neg: 0,
                  pos: 0,
                  has_values: false,
                  label: item.item_label_locale || item.item_label,
                  children: item.parent === -1 ? [] : undefined,
                });
              } else {
                const parent = accumulator.find((i) => i.id === item.parent);
                if (parent) {
                  parent.children.push({
                    id: item.mclass_item_id,
                    neg: 0,
                    pos: 0,
                    label: item.item_label_locale || item.item_label,
                    aUniqueAnswers: { pos: [], neg: [] },
                  });
                }
              }
              return accumulator;
            },
            [],
          );

          const answers: number[] = [];

          dataArray.forEach((item) => {
            answers.push(item.sa_id);
            const object = findLabelObject(item.item_id);
            if (!object) {
              return;
            }

            if (
              item.neg === 1 &&
              object.aUniqueAnswers.neg.indexOf(item.sa_id) < 0
            ) {
              object.aUniqueAnswers.neg.push(item.sa_id);
            } else if (
              item.pos === 1 &&
              object.aUniqueAnswers.pos.indexOf(item.sa_id) < 0
            ) {
              object.aUniqueAnswers.pos.push(item.sa_id);
            }
            return object;
          });

          const numberOfClassifiedAnswers = [...new Set(answers)].length;

          vData.forEach((item) => {
            const parentAnswers: {
              pos: number[];
              neg: number[];
            } = {
              pos: [],
              neg: [],
            };

            item.children?.forEach((child) => {
              if (child.aUniqueAnswers) {
                parentAnswers.neg.push(...child.aUniqueAnswers.neg);
                parentAnswers.pos.push(...child.aUniqueAnswers.pos);
              } else {
                child.neg = 0;
                child.pos = 0;
                child.aUniqueAnswers = {
                  pos: [],
                  neg: [],
                };
              }

              parentAnswers.neg = [...new Set(parentAnswers.neg)];
              parentAnswers.pos = [...new Set(parentAnswers.pos)];

              item.cnt = [
                ...new Set([...parentAnswers.neg, ...parentAnswers.pos]),
              ].length;

              item.has_values = item.cnt > 0;

              item.neg =
                numberOfClassifiedAnswers === 0
                  ? 0
                  : Math.round(
                      (parentAnswers.neg.length / numberOfClassifiedAnswers) *
                        100,
                    );

              item.pos =
                numberOfClassifiedAnswers === 0
                  ? 0
                  : Math.round(
                      (parentAnswers.pos.length / numberOfClassifiedAnswers) *
                        100,
                    );

              item.children.forEach((childItem) => {
                childItem.neg =
                  item.cnt === 0
                    ? 0
                    : Math.round(
                        (childItem.aUniqueAnswers.neg.length / item.cnt) * 100,
                      );

                childItem.pos =
                  item.cnt === 0
                    ? 0
                    : Math.round(
                        (childItem.aUniqueAnswers.pos.length / item.cnt) * 100,
                      );
              });
            });
          });
        }

        return {
          allowed_charttypes: ChartType.CPL_GROUP,
          type: chartType,
          color_pos: '#6cb549',
          color_neg: '#e2231a',
          data: vData,
          unique_answers: totalNumberOfClassifiedAnswers,
        };
      }
      case ReportTemplate.PotentialAndLike: {
        const [resultSql, countSql, parentSql] = sqlQuery.split(';');
        const result: PotentialAndLikeItem[] =
          await this.surveyAnswerEntity.query(resultSql);
        const parents = await this.surveyAnswerEntity.query(parentSql);

        let total = +(await this.surveyAnswerEntity.query(countSql))?.[0]
          ?.count;
        let chartData = [];
        let group = [];
        for (let parent of parents) {
          let id = parent.mclass_item_id;
          group.push({
            id,
            parent,
            list: result.filter((i) => i.item_id == id),
          });
        }

        for (let item of group) {
          const { list, parent, id } = item;
          let label = '';
          if (
            parent.item_label_locale !== null &&
            parent.item_label_locale !== ''
          ) {
            label = parent.item_label_locale;
          } else {
            label = parent.item_label;
          }
          let like = list.reduce((p, i) => p + i.islike, 0);
          let potential = list.reduce((p, i) => p + i.ispot, 0);
          chartData.push({
            id,
            label,
            like: Math.round(100 * (like / total)),
            potential: Math.round(100 * (potential / total)),
          });
        }

        return {
          allowed_charttypes: ChartType.CPL_GROUP,
          data: chartData,
          color_like: '#0080ff',
          type: chartType,
          color_potential: '#ff8000',
          title: `${Object.keys(chartData).length} potential/likes in ${
            result.length
          } classified responses`,
          unique_answers: total,
        };
      }
      case ReportTemplate.Multiple_Choice: {
        const [resultSql, totalSql, labelSql] = sqlQuery.split(';');
        const result = (
          (await this.surveyAnswerEntity.query(
            resultSql,
          )) as MultipleChoiseItem[]
        ).map((i) => ({
          count: Number(i.count),
          return_value: i.return_value,
          label: i.return_value,
          period: moment(i.period).format('YYYY-MM-DD'),
          periodKey: formatPeriodKey(i.period),
          periodName: formatPeriod(i.period),
        }));

        const periods = [...new Set(result.map((i) => i.period))].sort(
          sortPeriods,
        );

        let periodCount = periods.map((period) => {
          return {
            period,
            count: result
              .filter((r) => period == r.period)
              .reduce((p, i) => p + i.count, 0),
          };
        });
        const formattedPeriods = [...new Set(result.map((i) => i.period))].sort(
          sortPeriods,
        );

        const total = Number(
          (await this.surveyAnswerEntity.query(totalSql))[0].count,
        );

        let list = await this.getMCList([
          { q_id: surveyQuestionId, q_temp_id: templateQuestionId },
        ]);

        let labels = [];
        let answers = [];

        for (let item of Object.values(list[0].values)) {
          labels.push(item as string);
          answers[item as string] = formattedPeriods.map(() => 0);
        }

        if (this.isBar(chartType)) {
          const label = (await this.surveyAnswerEntity.query(labelSql))[0]
            .title;

          let values = [];
          let avgValues = [];
          for (let label of labels) {
            let count = result.find((i) => i.return_value == label)?.count;

            values.push(count !== undefined ? count : 0);
            avgValues.push(
              count !== undefined
                ? Number(((count * 100) / total).toFixed(2))
                : null,
            );
          }

          let { backgroundColor, borderColor } = getColorByChartType(
            ChartType.BAR,
          );

          return {
            allowed_charttypes:
              'horizontalBar,bar,line,spline,line_area,spline_area',
            dataSets: [
              {
                border: 'none',
                backgroundColor,
                borderColor,
                data: avgValues,
                values: values,
                label,
              },
            ],
            type: chartType,
            labels,
            totals: total,
          };
        }

        for (let item of result) {
          let v = [];
          if (!item.return_value) v[0] = '<empty>';
          else if (item.return_value[0] === '[')
            v = parseJSON(item.return_value);
          else v[0] = item.return_value;

          for (var j = 0; j < v.length; j++) {
            let key = list[0].values[v[j]];
            let periodKey = formattedPeriods.indexOf(item.period);
            if (!answers[key]) answers[key] = formattedPeriods.map(() => 0);
            answers[key][periodKey] = item.count;
          }
        }
        let dataSets = [];
        for (let item of labels) {
          dataSets.push(
            this.createDataset(
              item,
              this.getChartColor(dataSets.length),
              this.valuesToPercentage(answers[item], periodCount),
              this.isFilled(chartType),
              answers[item],
            ),
          );
        }

        labelsLength = formattedPeriods.length;

        return {
          allowed_charttypes:
            'horizontalBar,bar,line,spline,line_area,spline_area',
          labels: formattedPeriods.map((i) => formatPeriod(i)),
          dataSets,
          type: chartType,
        };
      }
      case ReportTemplate.Feedback: {
        const result = (
          (await this.surveyAnswerEntity.query(sqlQuery)) as FeedbackItem[]
        ).map((i) => ({
          nps: i.nps,
          phone: i.xml_phone2 || '',
          strengths: i.strengths || '',
          answer_date: moment(i.dt).format('DD-MM-YYYY HH:mm'),
          title: i.survey || '',
          customername: i.customername || '',
          answer_id: i.surv_surveyanswer_id,
          email: i.xml_email || '',
          clf_call_id: i.clf_call_id,
          improvements: i.improvements || '',
        }));
        return {
          allowed_charttypes: 'feedback',
          feedback: result,
        };
      }
      case ReportTemplate.Custom: {
        const allowed_charttypes =
          'horizontalBar,line,spline,bar,line_area,spline_area,table';
        const [mainQuery, helperQuery, helperPeriodQuery] = sqlQuery.split(';');
        let field = groupByField;
        if (!field)
          throw new HttpException('you need to pass group_by_field', 400);

        if (queryChartType == ChartType.TABLE) {
          let result = await this.surveyAnswerEntity.query(
            mainQuery + ' ' + helperQuery,
          );
          result = result.map((item) => {
            return {
              ...item,
              nps: getNps(item),
              date: moment(item.period).format('YYYY-MM-DD'),
              period: formatPeriod(item.period),
              periodKey: formatPeriodKey(item.period),
            };
          });

          const periods = [...new Set(result.map((i) => i.date))].sort(
            sortPeriods,
          );

          let cols = result
            .reduce((prev, item) => {
              if (prev.length == 0)
                prev.push({ name: '', id: 'name', sticky: false });
              const { periodKey, period, date } = item;
              let id = periodKey;
              const isExists = prev.find((i) => i.id == id);
              if (!isExists)
                prev.push({
                  id,
                  name: period,
                  date,
                  sticky: false,
                });
              return prev;
            }, [])
            .sort((a, b) => sortPeriods(a.date, b.date));
          cols = [
            ...cols.filter((a) => !a.date),
            ...cols.filter((a) => a.date),
          ];

          let rows = result.reduce((prev, item) => {
            const id = item[field];
            if (!prev[id]) {
              let data: any = periods.reduce((prev, period: string) => {
                let key = formatPeriodKey(period);
                let nps = result.find(
                  (i) => i.periodKey == key && i[field] == id,
                )?.nps;
                prev[key] = nps !== undefined ? nps : '-';
                return prev;
              }, {});
              prev[id] = {
                name: id,
                ...data,
              };
            }

            return prev;
          }, {});
          return {
            allowed_charttypes,
            type: ChartType.TABLE,
            rows: Object.values(rows),
            cols,
          };
        }

        if (this.isLinear(chartType)) {
          let result = await this.surveyAnswerEntity.query(`${mainQuery} 
          SELECT initcap(${field}) AS ${field},  date_trunc('${period}', date_field::date) period, sum(promoter) AS promoters, sum(passive) AS passives, sum(detractor) AS detractors, COUNT(nps_value) AS cnt 
                FROM data GROUP BY period, ${field} ORDER BY ${field}, period
            `);
          let labels = await this.surveyAnswerEntity.query(
            mainQuery + helperPeriodQuery,
          );
          labels = labels.map((item) =>
            this.dateUtilService.getPeriodLabel(
              item.period,
              period,
              labels.length,
            ),
          );

          result = result.map((i) => ({
            ...i,
            period: this.dateUtilService.getPeriodLabel(
              i.period,
              period,
              labels.length,
            ),
          }));

          let dataSets: any = [];
          let groups = groupBy(result, field);
          let index = -1;
          for (let [key, list] of Object.entries(groups) as any) {
            index++;
            let values = [];

            for (let label of labels) {
              let item = list.find((i) => i.period == label);
              if (item) {
                let promoters = item.promoters;
                let detractors = item.detractors;
                let total = item.cnt;
                values.push(
                  Math.round(((promoters - detractors) / total) * 100),
                );
              } else {
                values.push(null);
              }
            }
            dataSets.push(
              this.createDataset(
                key,
                this.getChartColor(index),
                values,
                this.isFilled(chartType),
              ),
            );
          }
          return {
            allowed_charttypes,
            type: chartType,
            dataSets,
            labels,
          };
        } else {
          let result = await this.surveyAnswerEntity.query(`${mainQuery} 
  SELECT initcap(${field}) AS ${field}, sum(promoter) AS promoters, sum(passive) AS passives, sum(detractor) AS detractors, COUNT(nps_value) AS cnt 
        FROM data GROUP BY ${field} ORDER BY ${field}
    `);
          let labels = [];
          let values = [];
          // loop through dataset and fill labels and values arrays
          for (let item of result) {
            labels.push(item[field]);
            let promoters = item.promoters;
            let detractors = item.detractors;
            let total = item.cnt;
            values.push(Math.round(((promoters - detractors) / total) * 100));
          }

          let dataSets = [
            {
              label: 'NPS',
              data: values,
              backgroundColor: 'rgba(64,177,217, 0.9)',
              borderColor: 'rgba(64,177,217, 0.9)',
              color: '#dfdfdf',
              border: 'none',
            },
          ];
          return {
            allowed_charttypes,
            type: chartType,
            dataSets,
            labels,
          };
        }
      }
    }
  }
  private async getMCList(list) {
    let result = [];
    for (let item of list) {
      let arg = item.q_id || item.q_temp_id;
      let sql = `SELECT qr.question_shortlabel, question_returnvalue FROM surv_surveyquestionrow qr WHERE surv_surveyquestion_id = $1 ORDER BY qr.ordernumber`;
      if (!item.q_id && item.q_temp_id)
        sql = `SELECT qr.question_shortlabel, qr.question_returnvalue FROM surv_surveyquestionrow_template qr WHERE surv_surveyquestion_template_id = $1 ORDER BY qr.ordernumber`;

      let values = {};
      let data = await this.userReportsEntity.query(sql, [arg]);
      for (let item of data) {
        values[item.question_returnvalue] = item.question_shortlabel;
      }
      result.push({
        ...item,
        values,
      });
    }
    return result;
  }

  private valuesToPercentage(list, periodKeys) {
    return list.map((item, index) => {
      let total = periodKeys[index]?.count || 0;
      let value = 0;
      let d = total > 0 ? total : 1;
      value = item * (100 / d);
      return value.toFixed(2);
    });
  }

  private isLinear(type: ChartType) {
    return [
      ChartType.LINE,
      ChartType.LINE_AREA,
      ChartType.SPLINE,
      ChartType.SPLINE_AREA,
    ].includes(type);
  }

  private isBar(type: ChartType) {
    return [ChartType.BAR, ChartType.HORIZONTAL_BAR].includes(type);
  }
  private isFilled(type: ChartType) {
    return [ChartType.LINE_AREA, ChartType.SPLINE_AREA].includes(type);
  }

  private formatLabel(label: string) {
    return label[0] === label[0].toUpperCase()
      ? label
      : stringCapitalize(label);
  }

  private createDataset(
    datasetName,
    color,
    avgValues,
    filled: boolean = false,
    values?: Number[],
  ) {
    datasetName = datasetName?.toString();
    if (!datasetName?.length) datasetName = 'No Name';
    var returnObject = {
      label: this.formatLabel(datasetName),
      backgroundColor: color,
      borderColor: color?.replace('0.7', '0.9'),
      filled: filled,
      border: 'none',
      data: avgValues,
      spanGaps: true,
    };
    if (values && Array.isArray(values)) {
      returnObject['values'] = values;
    }
    return returnObject;
  }

  private getChartColor(index) {
    var res;
    var cnt = CHART_COLORS.length;
    if (index >= cnt) {
      res = CHART_COLORS[index % cnt];
    } else {
      res = CHART_COLORS[index];
    }
    return res;
  }
}
