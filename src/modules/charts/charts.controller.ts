import {
  Body,
  Controller,
  Get,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiTags, ApiBearerAuth, ApiQ<PERSON>y, ApiParam } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { ChartsService } from './charts.service';
import { ChartType } from '../../shared/enums/chart-types.enum';

@ApiTags('report data')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'report_data',
})
export class ChartsController {
  constructor(private chartsService: ChartsService) {}

  @UseGuards(AuthGuard)
  @ApiQuery({
    name: 'chart_type',
    required: false,
    description: 'for table view use table',
  })
  @ApiQuery({ name: 'templatequestion_id', required: false, type: Number })
  @ApiQuery({ name: 'surveyquestion_id', required: false, type: Number })
  @ApiQuery({ name: 'group_by_field', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiParam({ name: 'chart_id', type: Number })
  @Get(':chart_id')
  getChart(
    @Param('chart_id') chart_id: number,
    @Query('chart_type') chart_type: ChartType,
    @Query('templatequestion_id') templatequestion_id,
    @Query('surveyquestion_id') surveyquestion_id,
    @Query('group_by_field') groupByField,
    @Query('page') page = 1,
  ) {
    return this.chartsService.getChart(
      chart_id,
      chart_type,
      {
        templateQuestionId: Number(templatequestion_id),
        surveyQuestionId: Number(surveyquestion_id),
        groupByField,
      },
      Number(page),
    );
  }
}
