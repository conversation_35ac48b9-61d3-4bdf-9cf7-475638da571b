import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImportTemplateModule } from '../lib/import-template/import-template.module';
import { UserReportsModule } from '../users/user-reports/user-reports.module';
import { DateUtilsService } from '../../shared/utils/date-utils.service';
import { ChartsController } from './charts.controller';
import { ChartsService } from './charts.service';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import {
  SurveyAnswerEntity,
  UserCompanyEntity,
  UserReportsEntity,
  UserTokenEntity,
} from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserTokenEntity,
      SurveyAnswerEntity,
      UserReportsEntity,
      UserCompanyEntity,
    ]),
    ModuleLoggerModule.register('charts'),
    ProgramSettingsModule,
    UserReportsModule,
    ImportTemplateModule,
  ],
  controllers: [ChartsController],
  providers: [ChartsService, DateUtilsService],
  exports: [ChartsService],
})
export class ChartsModule {}
