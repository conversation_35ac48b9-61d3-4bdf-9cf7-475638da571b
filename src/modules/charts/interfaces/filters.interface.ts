import { ChartType } from '../../../shared/enums/chart-types.enum';
import { DateField } from '../../../shared/enums/datefield.enum';

interface FilterItem<T = string> {
  name: string;
  id: T;
}

interface Survey<T> extends FilterItem<T> {
  questionId: number;
  type: string;
}

export interface Filters {
  chart_model: ChartType;
  period: FilterItem<'day' | 'week' | 'month' | 'year'>[];
  date_field: FilterItem<number>[];
  from_date: string;
  to_date: string;
  regions: FilterItem[];
  brand: FilterItem[];
  brands: FilterItem[];
  survey: Survey<number>[];
  language: FilterItem[];
  labels: FilterItem[];
  medium: FilterItem<number>[];
  productgroup: FilterItem[];
  product_division: FilterItem[];
  cp: FilterItem[];
  sv: FilterItem[];
  project: FilterItem[];
  score: FilterItem<number>[];
  bsh_survey_categories: FilterItem[];
  engineer: FilterItem[];
  label_type: FilterItem[];
  contact_center: FilterItem[];
  dispatcher: FilterItem[];
  preprepper: FilterItem[];
  service_partner: FilterItem[];
  counselor: FilterItem[];
  dealer: FilterItem[];
  project_bouw: FilterItem[];
  club: FilterItem[];
  warranty: FilterItem[];
  respondent_device: FilterItem[];
  respondent_os: FilterItem[];
}

export interface CombinedFilters {
  filtersObject: Partial<Filters>;
  filtersSql: string;
  extraFiltersSql: string;
  dateField: string;
  dateType: DateField;
  period: 'year' | 'month' | 'week' | 'day' | 'quarter';
  fromDate: moment.Moment;
  toDate: moment.Moment;
}
