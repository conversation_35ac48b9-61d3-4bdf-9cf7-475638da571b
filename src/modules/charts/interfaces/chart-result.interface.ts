import { DateField } from '../../../shared/enums/datefield.enum';
import { ReportTemplate } from '../../../shared/enums/report-template.enum';
import { CombinedFilters } from './filters.interface';

export interface ChartSqlOptions {
  reportTemplate: ReportTemplate;
  params?: ChartParams;
  page?: number;
  filters: CombinedFilters;
}
export interface ChartParams {
  templateQuestionId?: number;
  surveyQuestionId?: number;
  groupByField?: string;
}
export interface GetNpsOptions {
  cnt: number | string;
  detractors: number | string;
  promoters: number | string;
}

export interface NpsScoreDataItem {
  cnt: string;
  detractors: string;
  promoters: string;
}

export interface NpsTrendItem<T = string | number> {
  cnt: T;
  detractors: T;
  passives: T;
  period: Date;
  promoters: T;
}

export interface FormatTrendsAccumulatorItem {
  value: number;
  prevDetractors: number;
  prevPromoters: number;
  prevCnt: number;
}

export interface NpsSegmentsItem {
  detractors: string;
  passives: string;
  promoters: string;
}

export interface NpsScaleItem {
  nps_value: number;
  cnt: string;
  dt?: Date;
}

export interface BrandsItem {
  cnt: string;
  detractors: string;
  passives: string;
  promoters: string;
  period: Date;
  xml_brand: string;
}

export interface TouchpointsItem {
  cnt: string;
  detractors: string;
  internal_name: string;
  passives: string;
  period: Date;
  promoters: string;
}

export interface NpsTimeItem {
  cnt: string;
  detractors: string;
  passives: string;
  period: Date;
  promoters: string;
}

export interface SentEmailsItem {
  answered: string;
  reminder: string;
  sent: string;
  thank: string;
  period: Date;
}

export interface RegionItem {
  cnt: string;
  detractors: string;
  passives: string;
  period: Date;
  promoters: string;
  xml_region: string;
}

export interface ResponseBehaviorItem {
  nps: number;
  period: any; // PostgresInterval
  sum: string;
}

export class ClassificationDataItem {
  item_id: number;
  item_label: string;
  item_label_locale: string;
  neg: number;
  pid: number;
  pos: number;
  sa_id: number;
}

export interface ClassificationLabelItem {
  global_record: number;
  item_label: string;
  item_label_locale: string;
  mclass_item_id: number;
  order_number: number;
  parent: number;
}

export interface ClassificationLabelAccumulatorItem {
  id: number;
  label: string;
  cnt: number;
  neg: number;
  pos: number;
  has_values: boolean;
  children: {
    id: number;
    label: string;
    pos: number;
    neg: number;
    aUniqueAnswers: {
      neg: number[];
      pos: number[];
    };
  }[];
}

export interface PotentialAndLikeItem {
  islike: number;
  ispot: number;
  item_id: number;
  item_label: string;
  item_label_locale: string;
  sa_id: number;
}

export interface MultipleChoiseItem {
  count: string;
  period: Date;
  return_value: string;
}

export interface FeedbackItem {
  clf_call_id: number;
  customername: string;
  dt: Date;
  improvements: string;
  nps: number;
  sec_company_id: number;
  strengths: string;
  surv_surveyanswer_id: number;
  survey: string;
  xml_email: string;
  xml_phone2: string;
}
