import { Type } from 'class-transformer';
import { Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class NewCronJobDto {
  @Type()
  @ApiProperty({
    type: 'string',
    required: false,
  })
  name: string;

  @Type()
  @ApiProperty({ required: false, default: false })
  callOnStart: boolean = false;

  @Type()
  @ApiProperty({ required: false, default: [], description: 'array of any' })
  parameters: any[] = [];

  @Type()
  @ApiProperty({ required: true })
  @Matches(
    /(@(annually|yearly|monthly|weekly|daily|hourly|reboot))|(@every (\d+(ns|us|µs|ms|s|m|h))+)|((((\d+,)+\d+|(\d+(\/|-)\d+)|\d+|\*) ?){5,7})/,
    { context: '* * * * *' },
  )
  timer: string;
}

export class CallJobImmediatelyDto {
  @Type()
  @ApiProperty({
    type: 'string',
    required: false,
  })
  name: string;

  @Type()
  @ApiProperty({ required: false, default: [], description: 'array of any' })
  parameters: any[] = [];
}
