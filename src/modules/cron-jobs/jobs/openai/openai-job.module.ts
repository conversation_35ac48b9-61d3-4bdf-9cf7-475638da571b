import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OpenAIJobService } from './openai-job.service';
import { SurvSurveyEntity } from '../../../database/entities/surv-survey.entity';
import { HttpModule } from '@nestjs/axios';
import { EmailCustomerEntity } from '../../../database/entities/email-customer-ec.entity';
import { CronJobsEntity } from '../../../database/entities/cron-jobs.entity';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { QueueEntity } from '@entities';
import { OpenAIModule } from '@/modules/openai/openai.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([QueueEntity, CronJobsEntity]),
    ModuleLoggerModule.register('openai-job'),
    OpenAIModule,
  ],
  providers: [OpenAIJobService],
  exports: [OpenAIJobService],
})
export class OpenAIJobModule {}
