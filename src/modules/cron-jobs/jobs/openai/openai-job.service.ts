import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThan, MoreThanOrEqual, Repository } from 'typeorm';

import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { QueueEntity } from '@entities';
import { QueueStatus } from '@/shared/enums/queue.enum';
import { OPENAI_MAX_RETRIES_PER_ROW } from '@/config/ai.config';
import { OpenAIClassificationService } from '@/modules/openai/services/classification/openai-classification.service';
import * as moment from 'moment';

@Injectable()
export class OpenAIJobService {
  constructor(
    private logger: ModuleLogger,
    @InjectRepository(QueueEntity)
    private queueRepo: Repository<QueueEntity>,
    private openAIClassificationService: OpenAIClassificationService,
  ) {}

  public async classificationQueue(cron: any) {
    let where;

    const threeDaysAgo = moment().subtract(3, 'days').toDate();
    if (cron?.executedFrom === 'startUp') {
      where = {
        module: 'openai.classification',
        status: QueueStatus.ERROR,
        creation_date: MoreThan(threeDaysAgo),
      };
    } else {
      where = {
        module: 'openai.classification',
        status: QueueStatus.ERROR,
        tryCount: MoreThanOrEqual(OPENAI_MAX_RETRIES_PER_ROW),
        creation_date: MoreThan(threeDaysAgo),
      };
    }
    let items = await this.queueRepo.find({
      where,
    });
    for (let item of items) {
      this.openAIClassificationService.startClassification(+item.value);
    }
  }
}
