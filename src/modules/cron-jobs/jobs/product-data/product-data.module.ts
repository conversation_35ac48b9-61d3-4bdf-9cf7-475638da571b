import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductDataService } from './product-data.service';
import { SurvSurveyEntity } from '../../../database/entities/surv-survey.entity';
import { HttpModule } from '@nestjs/axios';
import { EmailCustomerEntity } from '../../../database/entities/email-customer-ec.entity';
import { CronJobsEntity } from '../../../database/entities/cron-jobs.entity';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SurvSurveyEntity,
      EmailCustomerEntity,
      CronJobsEntity,
    ]),
    ModuleLoggerModule.register('product-data'),
    HttpModule,
  ],
  providers: [ProductDataService],
  exports: [ProductDataService],
})
export class ProductDataModule {}
