import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SurvSurveyEntity } from '../../../database/entities/surv-survey.entity';
import { Repository } from 'typeorm';
import { CronJobsEntity } from '../../../database/entities/cron-jobs.entity';
import * as moment from 'moment';
import { chunkArray } from '../../../../shared/utils/array-utils';
import { IProductDataItem } from './product-data.interface';
import { HttpService } from '@nestjs/axios';
import { env } from '../../../../app.env';
import { v4 } from 'uuid';
import { lastValueFrom } from 'rxjs';
import { EmailCustomerEntity } from '../../../database/entities/email-customer-ec.entity';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class ProductDataService {
  constructor(
    private logger: ModuleLogger,
    private httpService: HttpService,
    @InjectRepository(SurvSurveyEntity)
    private surveyRepo: Repository<SurvSurveyEntity>,
    @InjectRepository(EmailCustomerEntity)
    private ecRepo: Repository<EmailCustomerEntity>,

    @InjectRepository(CronJobsEntity)
    private cronRepo: Repository<CronJobsEntity>,
  ) {}

  public async updateProductData(_, chunkSize = 50) {
    let cron = await this.cronRepo.findOne({
      where: { name: 'productDataService.updateProductData' },
    });

    let lastCall =
      cron && moment(cron?.lastCall).isValid()
        ? moment(cron.lastCall)
        : moment().add(-1, 'day');

    this.logger.debug(
      `Getting list of creation_date > ${lastCall.toLocaleString()}`,
    );

    let list = await this.surveyRepo.query(
      `
      SELECT ec.email_customer_id, ec.xml_enumber, ec.xml_prodarea, ec.xml_proddivision
      FROM
      email_customer ec
      LEFT JOIN surv_survey s ON ec.xml_department = s.department
      LEFT JOIN bsh_categories child ON s.bsh_category_id = child.bsh_category_id
      LEFT JOIN bsh_categories parent ON child.parent_bsh_category_id = parent.bsh_category_id
      WHERE parent.bsh_category_uuid in ('210', '220') AND ec.creation_date > $1
      `,
      [lastCall],
    );

    let chunkList = chunkArray<IProductDataItem>(list, chunkSize);

    let url = new URL('/v1/products', env.PRODUCT_DATA_URL).href;

    let chunkResult = [];
    let notFoundList = [];
    try {
      for (let items of chunkList) {
        let updateList = [];
        const params = {
          product_ids: items
            .map((i) => i.xml_enumber)
            .filter((i) => i)
            .join(','),
          locale: 'en-WW',
        };

        this.logger.debug(`Sending request for chunk`, { params });
        let request = this.httpService.get(url, {
          params,
          headers: {
            'User-Agent': 'Focusfeedback',
            'X-Flow-ID': 'GKY7oDhpSiKY_gAAAABZ_B',
            'X-API-Key': env.PRODUCT_DATA_API_KEY,
          },
        });
        let result = await lastValueFrom(request);
        for (let item of items) {
          let product = result.data.products.find(
            (p) => p.productId === item.xml_enumber,
          );
          if (!product) {
            notFoundList.push({
              email_customer_id: item.email_customer_id,
              enumber: item.xml_enumber,
            });
            continue;
          }
          const groupedProperties = [].concat(
            ...product.groupedProperties.map((product) => product.piProperties),
          );
          if (Array.isArray(groupedProperties)) {
            let field =
              groupedProperties?.find((prop) => prop.name === 'BSHM_GFELD') ||
              '';
            let prodDivision = field?.value;
            let prodArea = prodDivision?.[0] || null;
            updateList.push(
              this.ecRepo.update(
                { email_customer_id: item.email_customer_id },
                { xml_proddivision: prodDivision, xml_prodarea: prodArea },
              ),
            );
          }
        }

        chunkResult.push(await Promise.all(updateList));
      }
    } catch (e) {
      this.logger.error(`Error during request`, { error: e?.message || e });
    }

    return {
      // list: list.map((item) => ({
      //   email_customer_id: item.email_customer_id,
      //   enumber: item.xml_enumber,
      // })),
      updateCount: []
        .concat(...chunkResult)
        .reduce((prev, item) => prev + item.affected, 0),
      notFoundList: notFoundList.map((item) => item.enumber),
    };
  }
}
