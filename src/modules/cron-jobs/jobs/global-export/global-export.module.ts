import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecProgramSettingsEntity } from '../../../database/entities/sec-programsettings.entity';
import { GlobalExportService } from './global-export.service';
import { ProgramSettingsModule } from '../../../common/program-settings/program-settings.module';
import { WebhookSftpModule } from '../../../webhook/sftp/sftp.module';
import { WebhookFtpModule } from '../../../webhook/ftp/ftp.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SecProgramSettingsEntity]),
    ModuleLoggerModule.register('global-export'),
    ProgramSettingsModule,
    WebhookSftpModule,
    WebhookFtpModule,
  ],
  providers: [GlobalExportService],
  exports: [GlobalExportService],
})
export class GlobalExportModule {}
