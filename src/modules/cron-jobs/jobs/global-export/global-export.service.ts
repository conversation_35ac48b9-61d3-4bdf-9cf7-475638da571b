import { HttpException, Injectable, Module } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SecProgramSettingsEntity } from '../../../database/entities/sec-programsettings.entity';
import { Repository } from 'typeorm';
import * as moment from 'moment';
import { WebhookSftpService } from '../../../webhook/sftp/sftp.service';
import { WebhookFtpService } from '../../../webhook/ftp/ftp.service';
import * as fs from 'fs';
import * as csv from 'fast-csv';
import { join } from 'path';
import { v4 as uuid } from 'uuid';
import { waitListener } from '../../../../shared/utils/helper-functions';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { CronJobsEntity } from '@entities';

export const EXPORT_TYPE = {
  RESPONSES: 0,
  BLACKLIST: 1,
  GLOBAL: 2,
  CLASSIFICATIONS: 3,
};

const SURVEY_CATEGORY_ID = 16;

@Injectable()
export class GlobalExportService {
  constructor(
    @InjectRepository(SecProgramSettingsEntity)
    private psRepo: Repository<SecProgramSettingsEntity>,
    private sftpService: WebhookSftpService,
    private ftpService: WebhookFtpService,
    private logger: ModuleLogger,
  ) {}

  async export(cron: CronJobsEntity, from?: any, to?: any) {
    let startDate = from ? moment(from) : 'yesterday';
    let endDate = to ? moment(to) : 'today';

    let clist = await this.psRepo
      .createQueryBuilder('s')
      .select(['s.sec_company_id', 's.ftp_usesftp'])
      .where(`s.company_properties ILIKE '%\"inGlobalExport\":1%'`)
      .orderBy('s.sec_programsetting_id', 'DESC')
      .getMany();
    let companies = clist.map((s) => s.sec_company_id);
    if (companies.length == 0) return;

    this.logger.debug(
      `Export running between ${startDate.toLocaleString()} AND ${endDate.toLocaleString()} \ncompany ids: ${companies.join()}`,
    );

    let data = await this.psRepo.query(
      `
    select a.surv_surveyanswer_id as id, to_char(e.xml_readydate, 'YYYYMMDD') as date, e.xml_rasnumber as risnr, s.name as survey, a.nps_value as nps, c.country_code as country, e.xml_region as region, e.xml_technician as technician, 
    regexp_replace(neg.return_value, '[\n\r]', ' ', 'g' ) as improvements, regexp_replace(pos.return_value, '[\n\r]', ' ', 'g' ) as strenghts, cb.return_value as call_back, to_char(a.creation_date, 'YYYYMMDD HH24:MI:SS') as response_date 
    from surv_surveyanswer a 
    LEFT JOIN sec_company c ON a.sec_company_id = c.sec_company_id 
    LEFT JOIN email_customer e on a.email_customer_id=e.email_customer_id 
    LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id 
    LEFT JOIN surv_survey_categories sc ON sc.surv_survey_id = s.surv_survey_id 
    LEFT JOIN surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND s.clf_question1 = neg.surv_surveyquestion_id 
    LEFT JOIN surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND s.clf_question2 = pos.surv_surveyquestion_id 
    LEFT JOIN surv_surveyansweritem cb ON a.surv_surveyanswer_id = cb.surv_surveyanswer_id AND s.callback_question = cb.surv_surveyquestion_id 
    WHERE a.sec_company_id IN (${companies.join(',')})
    AND sc.category_id = ${SURVEY_CATEGORY_ID} 
    AND a.creation_date_system BETWEEN $1 AND $2 ORDER BY a.surv_surveyanswer_id DESC
    `,
      [startDate, endDate],
    );

    companies[0] = 4;
    const fileDate = moment().format('YYYYMMDD.HHmm');
    const name = `NETWEL.GERB1P.KB.1.${fileDate}.csv`;

    try {
      const directoryPath = process.env.SFTP_TEMP_FOLDER + '/csv';
      const localFile = join(directoryPath, name);
      if (!fs.existsSync(directoryPath)) {
        fs.mkdirSync(directoryPath, { recursive: true });
      }
      const stream = fs.createWriteStream(localFile);
      let pipe = csv.write(data, { headers: true }).pipe(stream);

      await waitListener(pipe, 'finish');

      let result = {};
      if (clist[0].ftp_usesftp === 1) {
        result = await this.sftpService.uploadFile(
          localFile,
          '/BSHIN/' + name,
          companies[0],
        );
      } else {
        result = await this.ftpService.uploadFile(
          localFile,
          '/BSHIN/' + name,
          companies[0],
        );
      }
      this.psRepo.query(
        `INSERT INTO public.export_log (export_log_id, export_date, export_type, exported_lines, export_filename) VALUES ($1, $2, $3, $4, $5)`,
        [uuid(), new Date(), EXPORT_TYPE.GLOBAL, data.length, name],
      );

      return result;
    } catch (e) {
      this.logger.error('Error during export', { error: e?.message || e });
      throw new HttpException('Error during export: ' + e?.message, 400);
    }
  }
}
