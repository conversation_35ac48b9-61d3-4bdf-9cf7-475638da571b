import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CronJobsEntity } from '../database/entities/cron-jobs.entity';
import { CronJobsService } from './cron-jobs.service';
import { CronJobsController } from './cron-jobs.controller';
import { GlobalExportModule } from './jobs/global-export/global-export.module';
import { ProductDataModule } from './jobs/product-data/product-data.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { OpenAIJobModule } from './jobs/openai/openai-job.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CronJobsEntity]),
    ModuleLoggerModule.register('cron-jobs'),
    GlobalExportModule,
    ProductDataModule,
    OpenAIJobModule,
  ],
  controllers: [CronJobsController],
  providers: [CronJobsService],
})
export class CronJobsModule {}
