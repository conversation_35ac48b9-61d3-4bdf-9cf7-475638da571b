import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { CronJobsService } from './cron-jobs.service';
import {
  CallJobImmediatelyDto,
  NewCronJobDto,
} from './dto/cron-job.request.dto';
import { CronJobsEntity } from '../database/entities/cron-jobs.entity';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { IsAdmin } from '@/shared/decorators/is-admin.decorator';

@Controller({
  path: 'cron-jobs',
  version: '2',
})
@ApiTags('Cron Jobs')
@ApiBearerAuth()
export class CronJobsController {
  constructor(private readonly cronJobService: CronJobsService) {}

  @Get()
  @UseGuards(AuthGuard)
  @IsAdmin()
  async getAllCronJobs(): Promise<CronJobsEntity[]> {
    return await this.cronJobService.getList();
  }

  @Get('/available-methods')
  @UseGuards(AuthGuard)
  @IsAdmin()
  getAvailableMethods() {
    return this.cronJobService.getAvailableMethods();
  }

  @Post()
  @ApiOperation({
    description:
      'name should be serviceName.methodName.<br> parameters should be array of strings.<br> timer should be in * * * * * format',
  })
  @UseGuards(AuthGuard)
  @IsAdmin()
  async addCronJob(
    @Body() newCronJobDto: NewCronJobDto,
  ): Promise<CronJobsEntity> {
    return this.cronJobService.addJob(newCronJobDto);
  }

  @Post('/call-job-manually')
  @ApiOperation({
    description:
      'name should be serviceName.methodName. parameters should be array of strings',
  })
  @UseGuards(AuthGuard)
  @IsAdmin()
  async callService(@Body() body: CallJobImmediatelyDto) {
    return this.cronJobService.callJob(body.name, body.parameters);
  }

  @Delete('/:id')
  @UseGuards(AuthGuard)
  @IsAdmin()
  async deleteCronJob(@Param('id') id: string) {
    return this.cronJobService.removeJob(id);
  }
}
