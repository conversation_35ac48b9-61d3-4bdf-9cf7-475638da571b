import { HttpException, OnApplicationBootstrap } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { CronJob } from 'cron';
import { CronJobsEntity } from '../database/entities/cron-jobs.entity';
import { Repository } from 'typeorm';
import { NewCronJobDto } from './dto/cron-job.request.dto';
import { GlobalExportService } from './jobs/global-export/global-export.service';
import { JOB_SCHEMA } from './jobs/schema';
import { ProductDataService } from './jobs/product-data/product-data.service';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { OpenAIJobService } from './jobs/openai/openai-job.service';

export class CronJobsService implements OnApplicationBootstrap {
  constructor(
    private schedulerRegistry: SchedulerRegistry,
    @InjectRepository(CronJobsEntity)
    private cronJobRepo: Repository<CronJobsEntity>,
    private logger: ModuleLogger,

    // don't remove theese services, even linter says they're unused services
    private globalExportService: GlobalExportService,
    private productDataService: ProductDataService,
    private openAIJobService: OpenAIJobService,
  ) {}

  async onApplicationBootstrap() {
    const crons = await this.getList();
    this.scheduleCronJobs(crons);
  }

  getList(): Promise<CronJobsEntity[]> {
    return this.cronJobRepo.find();
  }

  getRegistryList() {
    return this.schedulerRegistry.getCronJobs();
  }

  getAvailableMethods() {
    return JOB_SCHEMA;
  }

  async addJob(params: NewCronJobDto) {
    const { name, timer, callOnStart, parameters } = params;
    const [serviceName, methodName] = name.split('.');
    if (!this[serviceName]?.[methodName])
      throw new HttpException(
        `Service or function called ${name} is not found`,
        400,
      );
    let cronJob = await this.cronJobRepo.save(
      this.cronJobRepo.create({
        name,
        timer,
        callOnStart,
        parameters,
      }),
    );

    this.scheduleCronJob(cronJob);
    return cronJob;
  }

  async removeJob(id: string) {
    try {
      let result = await this.cronJobRepo.delete({ id: +id });
      this.schedulerRegistry.deleteCronJob(id);
      return result;
    } catch {
      throw new HttpException('job with given id is not found', 404);
    }
  }

  scheduleCronJobs(crons: CronJobsEntity[]) {
    crons.forEach(this.scheduleCronJob.bind(this));
  }

  async scheduleCronJob(cron: CronJobsEntity) {
    const { id, name, parameters, timer } = cron;
    try {
      console.log('CRON JOB - scheduled new job', name, timer);
      let job = new CronJob(timer, () => {
        this.callJob(name, parameters, cron).catch((e) =>
          console.error('CRON JOB {' + id + '} ERROR', e?.message || e),
        );
      });
      this.schedulerRegistry.addCronJob(id.toString(), job);
      job.start();
      if (cron.callOnStart) {
        this.callJob(name, parameters, {
          ...cron,
          executedFrom: 'startUp',
        }).catch((e) =>
          console.error('CRON JOB {' + id + '} ERROR', e?.message || e),
        );
      }
      return cron;
    } catch (e) {
      console.error(e);
      return null;
    }
  }

  async callJob(
    name: string,
    parameters: any[],
    cron?: CronJobsEntity & { executedFrom?: 'startUp' | 'timer' | 'api' },
  ) {
    try {
      const [serviceName, methodName] = name.split('.');
      let result = await this[serviceName]?.[methodName]?.(
        cron,
        ...(parameters || []),
      );
      await this.cronJobRepo.update({ name }, { lastCall: new Date() });

      if (cron?.logging) {
        this.logger.debug(result, { serviceName, methodName });
      }

      return result;
    } catch (e) {
      this.logger.error('error during cron-job process', {
        name,
        error: e.message ? { message: e?.message, stack: e?.stack } : e,
      });
      throw new HttpException('Error: ' + e, 400);
    }
  }
}
