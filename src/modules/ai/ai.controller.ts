import {
  Controller,
  Get,
  Param,
  Post,
  Query,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AIService } from './ai.service';
import { FileFieldsInterceptor } from '@nestjs/platform-express';

@Controller({
  path: '/ai',
  version: '2',
})
@ApiTags('AI')
export class AIController {
  constructor(private aiService: AIService) {}

  @Get('/call-classification/models')
  @ApiOperation({ description: 'Get List of classification models' })
  async getClassificationAIModels() {
    return ['gpt-4'];
  }

  @Get('/call-classification')
  @ApiOperation({ description: 'Call classification service manually' })
  async callClassification(@Query('model') model: string) {
    return await this.aiService.startClassification(model);
  }

  @Get('/call-classification/:answerId')
  @ApiOperation({ description: 'Call classification service manually' })
  async callClassificationById(
    @Param('answerId') answerId: number,
    @Query('model') model: string,
  ) {
    return await this.aiService.startClassification(model, answerId);
  }

  @Get('/call-classification-csv')
  @ApiOperation({ description: 'Call classification with csv files' })
  async classificationByCSV(@Query('model') model: string) {
    return await this.aiService.startClassificationCSV(null, null, model);
  }

  @Post('/call-classification-csv') // Uploads category labels and data to be classified
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'labels', maxCount: 1 },
      { name: 'data', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        labels: { type: 'string', format: 'binary' },
        data: { type: 'string', format: 'binary' },
      },
    },
  })
  async classificationByCSVUpload(
    @UploadedFiles() files,
    @Query('model') model: string,
  ) {
    const labelsFile = files.labels[0];
    const dataFile = files.data[0];
    return await this.aiService.startClassificationCSV(
      labelsFile.buffer,
      dataFile.buffer,
      model,
    );
  }
}
