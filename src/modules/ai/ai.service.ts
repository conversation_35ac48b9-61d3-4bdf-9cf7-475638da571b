import { Injectable } from '@nestjs/common';
import { OpenAIClassificationService } from '../openai/services/classification/openai-classification.service';
import { OpenAIClassificationByCsvService } from '../openai/services/classification-by-csv/openai-classification-by-csv.service';

@Injectable()
export class AIService {
  constructor(
    private classificationService: OpenAIClassificationService,
    private classificationByCsvService: OpenAIClassificationByCsvService,
  ) {}
  startClassification(model: string, answerId?: number) {
    if (model.includes('gpt-4')) {
      return this.classificationService.startClassification(answerId, model);
    }
  }
  startClassificationCSV(
    classificationLabels?: any,
    data?: any,
    model?: string,
  ) {
    if (model.includes('gpt-4')) {
      return this.classificationByCsvService.startClassification(
        classificationLabels,
        data,
        model,
      );
    }
  }
}
