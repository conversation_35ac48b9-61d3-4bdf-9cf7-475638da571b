import { ActionRecommenderService } from '@/modules/openai/services/action-recommender/action-recommender.service';
import { EvaluationService } from '@/modules/openai/services/classification-by-csv/evaluation.service';
import { OpenaiClassificationByCsvProcessingService } from '@/modules/openai/services/classification-by-csv/openai-classification-by-csv-processing.service';
import { FeedbackReportService } from '@/modules/openai/services/feedback-report/feedback-report.service';
import { LabelTreeBuilderService } from '@/modules/openai/services/label-tree-builder/label-tree-builder';
import { WeaviateClassificationService } from '@/modules/openai/services/weaviate/weaviate-classification.service';
import { WeaviateService } from '@/modules/openai/services/weaviate/weaviate.service';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import * as fs from 'fs';
import * as path from 'path';
import { OpenAIClassificationService } from './services/classification/openai-classification.service';
import { OpenAIClassificationByCsvService } from './services/classification-by-csv/openai-classification-by-csv.service';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { EmailGeneratorService } from './services/email-generator/email-generator';
import { FeedbackChatService } from './services/feedback-chat/feedback-chat.service';
import { TranslateService } from './services/translate/translate.service';
import { Response } from 'express';
import weaviate, { ApiKey, WeaviateClient } from 'weaviate-ts-client';
import {
  StartClassificationBulkDto,
  StartClassificationByDateDto,
} from './dto/start-classification-bulk.dto';
import { GenerateTicketSummaryService } from './services/generate-ticket-summary/generate-ticket-summary.service';
export interface EmailTone {
  toneName: string;
  toneDescription: string;
}

export interface ExtraGuide {
  value: string;
  description: string;
  displayName: string;
}
function groupByAnswerId(data) {
  return data.reduce((acc, item) => {
    const key = item.answer_id;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);
    return acc;
  }, {});
}
function combineEvaluations(evaluations: any) {
  // Implement logic to combine individual evaluations into a single result
  // This can include averaging metrics, aggregating results, etc.
  // Example:
  const combined = {
    exactMatchRatio: 0,
    hammingLoss: 0,
    precision: 0,
    recall: 0,
    f1Score: 0,
    count: evaluations.length,
  };

  evaluations.forEach((evaluation) => {
    combined.exactMatchRatio += evaluation.metrics.exactMatchRatio;
    combined.hammingLoss += evaluation.metrics.hammingLoss;
    combined.precision += evaluation.metrics.precision;
    combined.recall += evaluation.metrics.recall;
    combined.f1Score += evaluation.metrics.f1Score;
  });

  combined.exactMatchRatio /= combined.count;
  combined.hammingLoss /= combined.count;
  combined.precision /= combined.count;
  combined.recall /= combined.count;
  combined.f1Score /= combined.count;

  return combined;
}

function mapByAnswerId(data: any) {
  return data.reduce((acc, item) => {
    acc[item.answer_id] = item;
    return acc;
  }, {});
}
@Controller({
  path: '/openai',
  version: '2',
})
@ApiTags('OpenAI')
export class OpenAIController {
  constructor(
    private classificationService: OpenAIClassificationService,
    private classificationByCsvService: OpenAIClassificationByCsvService,
    private feedbackChatService: FeedbackChatService,
    private translateService: TranslateService,
    private emailGeneratorService: EmailGeneratorService,
    private labelTreeBuilderService: LabelTreeBuilderService,
    private actionRecommenderService: ActionRecommenderService,
    private feedbackReportService: FeedbackReportService,
    private weaviateService: WeaviateService,
    private weaviateClassificationService: WeaviateClassificationService,
    private evaluationService: EvaluationService,
    private queueProcessingService: OpenaiClassificationByCsvProcessingService,
    private generateTicketSummaryService: GenerateTicketSummaryService,
  ) {}

  @Get('/call-classification')
  @ApiOperation({ description: 'OpenAI call classification service manually' })
  async startClassification() {
    return await this.classificationService.startClassification();
  }
  @Get('/call-classification/date')
  @ApiOperation({ description: 'OpenAI call classification service manually' })
  async startClassificationByDate(@Body() body: StartClassificationByDateDto) {
    return await this.classificationService.startClassificationByDate(body);
  }
  @Post('/call-classification/bulk')
  @ApiOperation({ description: 'OpenAI call classification service manually' })
  async startClassificationBulk(@Body() body: StartClassificationBulkDto) {
    return await this.classificationService.startClassificationBulk(body);
  }

  @Get('/weaviate')
  @ApiOperation({ description: 'Test weaviate local' })
  async weaviate() {
    const client: WeaviateClient = weaviate.client({
      scheme: 'http',
      host: 'localhost:8080',
      headers: {
        'X-Openai-Api-Key': process.env.OPENAI_API_KEY,
      },
    });
    // const classWithVectorizer = {
    //     class: 'Article',
    //     properties: [
    //         {
    //             name: 'title',
    //             dataType: ['text'],
    //         },
    //     ],
    //     vectorizer: 'text2vec-openai', // this could be any vectorizer
    // };
    // const result = await client.schema.classCreator().withClass(classWithVectorizer).do();

    // const result2 = await client.data
    //     .creator()
    //     .withClassName('Article')
    //     .withProperties({
    //         title: 'Een test article',
    //     })
    //     .do();
    const result = await client.graphql
      .get()
      .withClassName('Article')
      .withNearText({
        concepts: ['article'],
      })
      .withLimit(2)
      .withFields('title')
      .do();

    console.log(JSON.stringify(result, null, 2));

    // console.log(JSON.stringify(result, null, 2));  // the returned value is the object
    // console.log(JSON.stringify(result, null, 2));  // the returned value is the object
    // console.log('result', result)
  }

  @Get('/call-classification/:answerId')
  @ApiOperation({ description: 'OpenAI call classification service manually' })
  async singleRecordClassification(@Param('answerId') answerId: number) {
    return await this.classificationService.startClassification(answerId);
  }

  @Get('/call-classification-csv')
  @ApiOperation({ description: 'OpenAI call classification with csv files' })
  async classificationByCSV(@Query('iterations') iterations: number = 2) {
    console.log('Starting classification task with iterations:', iterations);

    // try {
    //     const allResults = [];
    //
    //     console.log('Starting classification task with iterations:', iterations);
    //     for (let i = 0; i < iterations; i++) {
    //         console.log(`Starting iteration ${i + 1}`);
    //         this.queueProcessingService.resultArray = []; // Clear the result array before starting the new iteration
    //
    //         const result = await this.classificationByCsvService.startClassification(null, null, '', i);
    //         console.log(`Iteration ${i + 1} result:`, result);
    //         allResults.push(result);
    //     }
    //
    //     console.log('All results collected:', allResults);
    //
    // } catch (error) {
    //     console.error('Error during classification by CSV:', error);
    //     throw error;
    // }
    try {
      const allResults = [];

      for (let i = 0; i < iterations; i++) {
        console.log(`Starting iteration ${i + 1}`);

        // Clear the result array before starting the new iteration
        // this.queueProcessingService.clearResultArray();

        const result =
          await this.classificationByCsvService.startClassification();
        // console.log(`Iteration ${i + 1} result:`, result);
        // const results = this.queueProcessingService.resultArray.map(res => ({...res, iterationIndex:i}))
        // console.log('results', results)
        // if(i === (iterations - 1) ) {
        //     allResults.push(results);
        // }
      }

      // console.log('All results collected:', allResults);
      //
      // // Save all results to a single JSON file
      // const dir = `${process.env.NODE_FOLDER}data`;
      // if (!fs.existsSync(dir)) {
      //     fs.mkdirSync(dir, { recursive: true });
      // }
      // const resultFile = `${dir}/classification-results.json`;
      // fs.writeFileSync(resultFile, JSON.stringify(allResults, null, 2));
      //
      return 'Finished';
    } catch (error) {
      console.error('Error during classification by CSV:', error);
      throw error;
    }
  }

  @Get('/call-classification-eval')
  @ApiOperation({ description: 'OpenAI call classification eval' })
  async classificationEval() {
    try {
      let file = `${process.env.NODE_FOLDER}data/classification-results.json`;
      let evalFile = `${process.env.NODE_FOLDER}data/classification-manually.json`;

      // Read and parse the manually labeled data
      const evalData = JSON.parse(fs.readFileSync(evalFile, 'utf-8'));

      // Read and parse the newly generated classification results
      const newData = JSON.parse(fs.readFileSync(file, 'utf-8'));

      console.log('Evaluation data length:', evalData.length);
      console.log('New classification data length:', newData.length);

      // Group newData by answer_id
      const groupedData = this.groupByAnswerId(newData);

      // Ensure evalData and groupedData have the same keys
      const evalDataMap = this.mapByAnswerId(evalData);
      const evalAnswerIds = Object.keys(evalDataMap);
      const newAnswerIds = Object.keys(groupedData);

      console.log('Evaluation answer IDs:', evalAnswerIds);
      console.log('New classification answer IDs:', newAnswerIds);

      // Filter newAnswerIds to ensure both datasets have the same keys
      const commonAnswerIds = evalAnswerIds.filter((id) =>
        newAnswerIds.includes(id),
      );

      console.log('Common answer IDs:', commonAnswerIds);

      if (commonAnswerIds.length !== evalAnswerIds.length) {
        throw new Error('Datasets must have the same length for evaluation.');
      }

      const evaluations = commonAnswerIds.map((id) => {
        const evalItem = evalDataMap[id];
        const newItems = groupedData[id];

        // console.log(`Evaluation data for answer_id ${id}:`, evalItem);
        // console.log(`New classification data for answer_id ${id}:`, newItems);
        //
        // if (newItems.length !== 2) {
        //     throw new Error(`Mismatched length for answer_id ${id}`);
        // }

        const evalResults = newItems.map((newItem) =>
          this.evaluationService.evaluate([evalItem], [newItem]),
        );

        console.log(`Evaluations for answer_id ${id}:`, evalResults);

        return evalResults;
      });

      // Flatten the evaluations array
      const flattenedEvaluations = evaluations.flat();

      console.log('Evaluations:', flattenedEvaluations);

      // Combine evaluations into a single result
      const combinedEvaluation = this.combineEvaluations(flattenedEvaluations);
      console.log('Combined Evaluation:', combinedEvaluation);
    } catch (error) {
      console.error('Error parsing JSON file:', error);
    }
  }

  groupByAnswerId(data) {
    return data.reduce((acc, item) => {
      const key = item.answer_id;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {});
  }

  mapByAnswerId(data) {
    return data.reduce((acc, item) => {
      acc[item.answer_id] = item;
      return acc;
    }, {});
  }

  combineEvaluations(evaluations) {
    const combined = {
      exactMatchRatio: 0,
      hammingLoss: 0,
      precision: 0,
      recall: 0,
      f1Score: 0,
      count: evaluations.length,
    };

    evaluations.forEach((evaluation) => {
      combined.exactMatchRatio += evaluation.metrics.averageExactMatchRatio;
      combined.hammingLoss += evaluation.metrics.averageHammingLoss;
      combined.precision += evaluation.metrics.averagePrecision;
      combined.recall += evaluation.metrics.averageRecall;
      combined.f1Score += evaluation.metrics.averageF1Score;
    });

    combined.exactMatchRatio /= combined.count;
    combined.hammingLoss /= combined.count;
    combined.precision /= combined.count;
    combined.recall /= combined.count;
    combined.f1Score /= combined.count;

    return combined;
  }

  @Get('/feedback-report')
  @ApiOperation({ description: 'OpenAI create report about feedback' })
  async feedbackReport() {
    const data = [
      {
        p: 'Goede apparaten, geen gedoe met zelf instellen',
        n: '',
      },
      {
        p: 'Ik kom al een hele poos sporten bij Physical met een 25x rittenkaart dus ik ben hier al bekend.\nNu heb ik een abonnement genomen.\nIk vind de sfeer prettig en vriendelijk en er wordt hulp geboden als het wordt gevraagd.\nIk voel me op mn gemak om mn eigen trainingsroute te doen, zoals de mensen om me heen dat ook doen, erg ontspannen.\nOok de mix van leeftijden, jong en oud, geeft het gevoel dat er ruimte en mogelijkheden zijn voor iedereen!',
        n: '',
      },
      {
        p: 'Je krijgt meteen het welkom gevoel  er heerst een vriendelijke gezellige sfeer',
        n: '',
      },
      {
        p: 'Professioneel, efficiënt, goede uitleg',
        n: 'Niet van toepassing',
      },
      {
        p: 'Persoonlijk zonder een oordeel te vellen.',
        n: '-',
      },
      {
        p: 'Relaxte sfeer',
        n: 'Op dit moment zou ik het nog niet weten',
      },
      {
        p: 'Kleinschalig, daardoor goede begeleiding',
        n: 'Nog geen idee',
      },
      {
        p: 'Kleinschalig; efficient circuit.',
        n: 'Afspraak is afspraak qua tijd: de ene pers trainer was druk, de ander was ruim te laat. Ik moest dus best even wachten. Ik werd niet aangespoord op activeren core voor begin aan het circuit.',
      },
      {
        p: 'mooie sportruimte, goed verzorgd, netjes',
        n: 'Geen idee',
      },
      {
        p: 'Moet ik nog gaan ervaren.\nHeb nog  geen echt sterk punt ontdekt wat eruit schiet.',
        n: 'Op vrijdagavond en zondag openen.\nWerk zelf in een winkel en de avonden vind ik te beperkt voor mij. Dus de vrijdag en / of zondag zou ik heel prettig vinden.',
      },
      {
        p: 'iig in Vlaardingen kleinschalig, directe begeleiding als het nodig is en goed verzorgd .',
        n: 'Na 1 keer op apparaten kan ik hier nog niets van zeggen',
      },
      {
        p: 'Kleinschalig, gastvrij , uitstekende begeleiding',
        n: 'Nvt',
      },
      {
        p: 'Kwalitatieve /sympathieke begeleiding\n\nprima machinerie',
        n: 'Verkleedruimte zou geen luxe zijn',
      },
      {
        p: 'Kleinschalig en vriendelijk en veel leeftijdsgenoten\nGeen krachtpatsers en andere indrukmaken willende mensen',
        n: 'Begeleiding bij beginnende mensen kan beter',
      },
      {
        p: 'overzichtelijk, niet te druk, goede oefeningen om te doen.',
        n: 'Geen bijzonderheden wel vind ik de contributie behoorlijk prijzig.',
      },
      {
        p: 'Goede begeleiding?',
        n: '?',
      },
      {
        p: 'Fijne , gezellige sportschool\nIedereen vriendelijk\nOntvangen met altijd een lach\nSchoon',
        n: 'Zorgen dat de spinning fietsen helemaal oké zijn ( bandjes om de voeten ) en dat Bluetooth goed werkt op de oortjes',
      },
      {
        p: 'De begeleiding',
        n: 'Voorlopig weet ik dat nog niet.',
      },
      {
        p: 'Goede begeleiding',
        n: 'Kan niets bedenken',
      },
      {
        p: 'Kleinschalig, geavanceerde toestellen,',
        n: 'Geen idee',
      },
      {
        p: 'Vriendelijk, persoonlijk, mooi gevarieerd aanbod, goede apparatuur, handige app.',
        n: 'De kosten voor lidmaatschap etc zijn niet op de site te vinden. Er komen bij de eerste inschrijving best wat kosten bij, het lijkt me goed om daar transparant over te zijn.',
      },
      {
        p: 'Omdat  ze  hijgen uitloop hebben',
        n: 'Niks',
      },
      {
        p: 'Zeer kundig, allert op de sporter  en ze geven jou het gevoel dat je meetelt',
        n: 'Zo doorgaan',
      },
      {
        p: 'Kleinschalig, geen wachttijd, pers begeleiding',
        n: 'Minder frequent mailen',
      },
      {
        p: 'Gezellig en ontspannen bezig zijn.',
        n: 'Xxxxxx',
      },
      {
        p: 'Goede uitleg over de apparaten, goed eerste gesprek, gewoon lekker relax, blijven je goed adviseren hie de apparatuur te gebruiken, ook bij volgende afspraken.',
        n: 'Dat weet ik nog niet, ben pas 2x geweest.',
      },
      {
        p: 'Heel professioneel, warm welkom ! Je voelt je er direct thuis! Hele fijne begeleiding',
        n: 'Het is fijn en goed zo',
      },
      {
        p: 'Gezellig, sfeervol, niet te groot, goed advies',
        n: 'Het was mij niet duidelijk of ik tijdens de kennismaking ook al zou sporten. Dat had ik op zich wel fijn gevonden, omdat je met een proefles nog beter weet of het klikt of niet.',
      },
      {
        p: 'schoon, top apparaten ,zeer goede vriendelijke begeleiding. gezellige  balie. dames  en heerlijke koffie!!!',
        n: 'Vooral Doorgaan!',
      },
      {
        p: 'Vrolijke medewerkers die hulpzaam zijn',
        n: 'Shakes of proteïne drankjes en of proteïne koekjes aanbieden',
      },
      {
        p: 'Na 2 weken 7 lessen verder ga ik er met veel plezier naar toe.\nGoede begeleiding,  ongedwongen sfeer, persoonlijke benadering.',
        n: 'Mensen de wetenschap geven dat trainingen gecombineerd kunnen worden.',
      },
      {
        p: 'Nog  geen',
        n: 'Is te vroeg  voor mij daar een oordeel over te geven',
      },
      {
        p: 'Begeleiding en sfeer.',
        n: 'Iets strenger  naar de klanten toe die er een potje van maken.',
      },
      {
        p: 'Toegankelijk, laagdrempelig',
        n: 'De app. Het bijhouden van activiteit is top, maar op deze manier met ‘moves’ niet heel duidelijk. Ik vind het leuk en motiverend om data bij te houden maar op deze manier zit er voor mij geen meerwaarde aan het installeren van de app.',
      },
      {
        p: 'Saamhorigheid',
        n: 'Dat er soms, om de 6 weken bv, individuele begeleiding is bij de 6 apparaten',
      },
      {
        p: 'Gezellig en goede begeleiding.',
        n: 'Voor mij niets want doe lekker mijn ding.',
      },
      {
        p: 'Gemakkelijk om in te stromen op je eigen tempo\nGoede persoonlijke aandacht\nPositieve en gezellige sfeer',
        n: 'Op dit moment geen suggesties',
      },
      {
        p: 'Dat alles geregeld is',
        n: 'Ik zou het op het ogenblik niet weten',
      },
      {
        p: 'Minimaal kans op blessures, prima planning en toezicht',
        n: 'Adviezen extra oefeningen',
      },
      {
        p: 'Vriendelijke, behulpzaam, netjes, schoon , enz enz enz      PRIMA',
        n: 'Zal niet weten alles zijn PRIMA',
      },
      {
        p: 'Persoonlijke aandacht',
        n: 'Nvt',
      },
      {
        p: 'Super leuk en fijne mensen die daar lesgeven',
        n: 'Geen idee',
      },
      {
        p: 'Kleine en open club. Waar je snel contact kunt maken. Goede persoonlijke benadering. Goede tips tijdens de oefeningen.',
        n: '?? Nog niet bekend.',
      },
      {
        p: '',
        n: 'Ik ben binnenkort weer terug',
      },
      {
        p: 'Altijd bereid om een oplossing te vinden',
        n: 'Parkeer mogelijkheden (ivm veiligheid),\nentree ruimte naast toilet',
      },
      {
        p: '',
        n: 'n.v.t.',
      },
      {
        p: 'Het is niet te groot. je komt in eerste instantie om te trainen, maar sfeer is goed.',
        n: 'Fysical Hillegersberg kan er misschien niet zo veel aan doen, maar gebeurt geregeld dat mensen op hun telefoon zitten waardoor pauzes tussen oefeningen langer duren en apparaat langer bezet blijft. Ook heb ik het een aantal keer meegemaakt dat mensen de hele tijd al trainend luid aan het (beeld)ellen zijn. Dit vind ik storend en niet netjes.',
      },
      {
        p: '',
        n: 'Niks hoor, ik moet het zelf doen 😁',
      },
      {
        p: 'Kleinschalig en gezellig.',
        n: 'Niets! Blijven zoals het is. 👍🏼',
      },
      {
        p: 'Wij waren voor bijna een maand weg en dan verzoek ik mijn abonnement op te schorten,  voor een korte vakantie uiteraard niet.',
        n: 'Kom vanzelf wer',
      },
      {
        p: 'Laagdrempelig en no-nonsense',
        n: 'Luchtcondities in de zaal laten te wensen over en daarnaast teveel overheersende muziek die ik als lawaai ervaar.',
      },
      {
        p: 'Medische kleine ingreep en voel me wat down met wachten op de uitslag. Probeer gauw weer te komen.',
        n: 'Ja niet zoveel. Ik denk als de uitslag binnen is en weet of me zwangerschap kan doorzetten dat ik gauw kom. Zit even psychische in de knoop nu momenteel.',
      },
      {
        p: 'Ben ik inmiddels alweer geweest',
        n: 'Niets',
      },
      {
        p: 'Vriendelijk personeel\nSchoon\nInnovatief\nBehulpzaam\nKlantenkring is divers',
        n: 'Vergeleken met vroeger, vond ik begeleiding in de sportschool prettig. Niet op afspraak maar wel iemand die af en toe even meekijkt met wat je doet, even het gesprek aangaat. Tips geeft etc.',
      },
      {
        p: "Het intieme karakter, de afwezigheid van typisch 'sportschoolvolk' en de voortreffelijke apparatuur.",
        n: 'Graag de deodorant terug in de mannenkleedkamer.',
      },
      {
        p: 'Kleinschalig, gezellig, persoonlijke aandacht. Uitermate geschikt voor de iets ouderen voor het verbeteren en het op peil houden van conditie ( cardio ) en spieren.',
        n: 'Zou het niet weten. Ik vind het prima zo.',
      },
      {
        p: 'Veel te duur',
        n: 'Niks',
      },
      {
        p: 'Wegens leeftijd en diverse blessures ben ik gestopt',
        n: 'Niets leeftijd en ongemakken',
      },
      {
        p: '',
        n: 'Ik ben gewoon weer doorgegaan met sporten na een vakantie van 3 weken dus no worries !',
      },
      {
        p: 'Zie boven',
        n: 'Doe ik volop, buiten en bij Wellness Terbregge in de winter met sauna.\n\nHopelijk niet tot snel, want dan heb ik een klacht. Maar als ik dan toch geblesseerd ben, dan het liefst bij jullie  voor herstel.',
      },
      {
        p: 'Combi met fysiotherapie\nPersoonlijke benadering van Peter en Patricia\nFijne begeleiding Patricia tijdens yogales\nGeheel vernieuwde state of the art Technogym apparatuur',
        n: 'Benodigde yogamaterialen op orde brengen, zoals voldoende yogablokken\nMeer in gesprek gaan over trainingsdoelstellingen\nBv regulier elk halfjaar doelstellingen opnieuw bespreken\nSpinning optie als groepsles',
      },
      {
        p: 'Schoon, kleinschalig, persoonlijk, motiverend',
        n: 'Op dit moment geen punten',
      },
      {
        p: 'Ernstige astma aanval gehad. Gaat nu voorzichtig de goede kant op.',
        n: 'Wanneer mijn gezondheid het toelaat kom ik weer.',
      },
      {
        p: 'Altijd vriendelijk personeel, een nette kleedkamer en prima douche. Na afloop een heerlijk kopje koffie met fruit om van te genieten.',
        n: 'Soms zijn de regels que instroom of doorstroom niet voor iedereen duidelijk....',
      },
      {
        p: 'Huiselijke sfeer\nKlantvriendelijkheid',
        n: '',
      },
      {
        p: 'Zwaar telerurgesteld dat ik na een rug operatie waarin ik een aantal maanden niet kan sporten, plus dat ik een aantal weken langer achtereen niet in Nederland het einden van mijn abonnement nioet naar voren geplaatst kan worden en ik toch nog 4 (of zelfs 5?) maanden door moet betalen. Ik heb  nu een beetje een rotgevoel voor deze maanden betalen en niet langs (kunnen) komen.\n\nVoor de 6 keer dat ik actief gebruik heb gemaakt van de apparatuur vind ik dit toch zwaar belast',
        n: 'GoHealth niet meer denk. Als mijn situatie zou veranderen en er zou vanuit GoHealth iets meer coulance zijn geweest was ik graag weer terug gekomen. De locatie, de sfeer en de mensen zijn verder super en vriendelijk',
      },
      {
        p: 'vriendelijke medewerkers , zowel instructeurs als aan de balie.\nmoderne apparatuur en fijne ruimte\nfijne rustige sfeer',
        n: '',
      },
      {
        p: 'Even aanvullen. De begeleiding is goed, de mensen zijn leuk, maar ik merkte dat ik meer afwisseling zocht. Dat heb ik gevonden, maar ik zal Gohealth zeker bij anderen aanvevelen',
        n: '.',
      },
      {
        p: '',
        n: 'niets, ligt helemaal aan mijzelf.',
      },
      {
        p: 'Leuk  goede en gezellige  begeleiding\nMaar ook de kleine groepjes',
        n: '',
      },
      {
        p: 'De buitenlessen worden aangeboden, maar dat is met een man in de continu en een baby moeilijk te combineren.',
        n: 'Nvt ik blijf lekker sporten.',
      },
      {
        p: 'Ik ben ongeveer 3wkn geweest na deelname aan een triathlon en ook de operatie van mijn vrouw. Ben 25sept weer begonnen met trainen. De 28e een inbody gedaan bij Jeremy en daarna getraind.',
        n: '',
      },
      {
        p: 'De aandacht die je krijgt',
        n: '',
      },
      {
        p: 'Luisteren  naar je en goed begeleiding',
        n: '',
      },
      {
        p: 'Deskundigheid van de fysiotherapeuten.\n\nCombinatie behandelingen en apparaten van de sportschool.\n\nEfficiëntie  van de organisatie.\n\nRustige en prettige sfeer. Open contact.',
        n: 'goed overleg over een persoonlijk trainingsschema  in de sportschool',
      },
      {
        p: 'Ik ben gewoon 2x per week op de sportschool geweest dus geen idee waar bovenstaande vandaan komt!',
        n: '',
      },
      {
        p: "Ik ben zeer tevreden over het resultaat dat ik heb bereikt via GoHealth. Ik wil nu via een andere aanpak verder doorpakken.\nIk vind het 'pakket' te statisch en relatief duur in vergelijkjng met andere sportscholen en te weinig onderscheidend gezien de maandelijkse contributie",
        n: '',
      },
      {
        p: 'Goede persoonlijke begeleiding\nAltijd bereikbaar voor vragen\nVriendelijk en gezellig',
        n: '',
      },
      {
        p: 'Uitstekende apparaten, goede intake en begeleiding',
        n: '',
      },
      {
        p: 'Schoon, gezellige mensen, goede begeleiding',
        n: '',
      },
      {
        p: 'Gezellige sfeer, goede begeleiding p',
        n: '',
      },
      {
        p: 'Kleinschalig, persoonlijk, informeel',
        n: '',
      },
      {
        p: 'Vriendelijk en open.',
        n: '',
      },
      {
        p: 'Goed trainingsprogramma',
        n: '',
      },
      {
        p: 'Goed programma, fijne sfeer, heel vriendelijke medewerkers',
        n: '',
      },
      {
        p: 'Fijne kleine sportschool  met goede begeleiding',
        n: '',
      },
      {
        p: 'Good.training',
        n: '',
      },
      {
        p: 'Vriendelijk en enthousiast\nDe les van Jeremy  heel goed',
        n: '',
      },
      {
        p: 'Ben door de dokters en ziekenhuis molens gegaan veel pijn aan mijn knieën en hart klachten\nDat ik totaal geen zin iets te ondernemen zodra ik weer ergens zin in heb kom ik weer sporten.',
        n: 'Geen idee.',
      },
      {
        p: 'Wat ik super vindt is dat alle apparaten automatische  ingesteld worden voor wat jij op dat moment aan kan\nEn dat er trainers rond lopen die je bij kunnen staan indien nodig. Er is persoonlijke aandacht',
        n: '',
      },
      {
        p: 'Leuke ontvangt, Leuke jongens en de elektronische apparaten, ga zo door.',
        n: '',
      },
      {
        p: 'Persoonlijk en gelukkig kleinschalig',
        n: '',
      },
      {
        p: 'Goede begeleiding, apparatuur afgestemd op eigen fysiek',
        n: '',
      },
      {
        p: 'Persoonlijk. Goede begeleiding.\nGoede schone trainingsruimte.',
        n: '',
      },
      {
        p: 'Gedreven begeleiders en die je serieus nemen',
        n: '',
      },
    ];
    console.log('RUNNING REPORT');
    return await this.feedbackReportService.createReport(data);
  }

  @Post('/call-classification-csv') // Uploads category labels and data to be classified
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'labels', maxCount: 1 },
      { name: 'data', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        labels: { type: 'string', format: 'binary' },
        data: { type: 'string', format: 'binary' },
      },
    },
  })
  async classificationByCSVUpload(@UploadedFiles() files) {
    const labelsFile = files.labels[0];
    const dataFile = files.data[0];
    return await this.classificationByCsvService.startClassification(
      labelsFile.buffer,
      dataFile.buffer,
    );
  }

  @Post('/answer-from-albert')
  @ApiOperation({ description: 'OpenAI answer from Albert' })
  async answerFromAlbert(
    @Body()
    body: {
      question: string;
      history: any;
      clientId: string;
      data: any;
    },
  ) {
    return await this.feedbackChatService.answerFromAlbert(body);
  }

  @Post('/translate')
  @ApiOperation({ description: 'OpenAI translate' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        strengths: { type: 'string' },
        improvements: { type: 'string' },
        language: { type: 'string' },
      },
      required: ['language'],
    },
  })
  async translate(
    @Body() body: { strengths: string; improvements: string; language: string },
  ) {
    return await this.translateService.translate(
      body.strengths,
      body.improvements,
      body.language,
    );
  }

  @Post('/generate-ticket-summary')
  @ApiOperation({ description: 'OpenAI generate ticket summary' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        strengths: { type: 'string' },
        improvements: { type: 'string' }
      },
      required: ['strengths', 'improvements'],
    },
  })
  async generateTicketSummary(
    @Body() body: { strengths: string; improvements: string },
  ) {
    return await this.generateTicketSummaryService.run(
      body.strengths,
      body.improvements,
    );
  }


  @Post('/generate-email')
  @ApiOperation({
    description: 'Generate an Email based on the customer feedback',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        strengths: { type: 'string' },
        improvements: { type: 'string' },
        extraGuides: { type: 'array' },
      },
    },
  })
  async generateEmail(
    @Body()
    body: {
      strengths: string;
      improvements: string;
      extraGuides: ExtraGuide[];
    },
  ) {
    return await this.emailGeneratorService.generate(
      body.strengths,
      body.improvements,
      body.extraGuides,
    );
  }

  @Post('/suggest-email-tone')
  @ApiOperation({
    description: 'Suggest Email tones based on the customer feedback',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        strengths: { type: 'string' },
        improvements: { type: 'string' },
        tone: { type: 'array' },
      },
    },
  })
  async suggestEmailTones(
    @Body()
    body: {
      strengths: string;
      improvements: string;
      tone: EmailTone[];
    },
  ) {
    return await this.emailGeneratorService.suggestEmailTone(
      body.strengths,
      body.improvements,
      body.tone,
    );
  }

  @Get('/label-tree-builder')
  @ApiOperation({
    description: 'OpenAI Create a label tree for classification',
  })
  async labelTreeBuilder() {
    return await this.labelTreeBuilderService.createLabelTree();
  }

  /**
   * WEAVIATE REPORT OVER LARGE AMOUNT OF FEEDBACK
   */
  @Get('/weaviate-feedback')
  @ApiOperation({ description: 'Weaviate get feedback' })
  async getFeedback() {
    return await this.weaviateService.getFeedback();
  }

  @Get('/weaviate-feedback-date-filter')
  @ApiOperation({ description: 'Weaviate get feedback' })
  async getTopTenTopics() {
    return await this.weaviateService.getTopTenTopics();
  }

  @Get('/weaviate-all-topics')
  @ApiOperation({ description: 'Weaviate get all topics for report' })
  async getAllTopics() {
    return await this.weaviateService.getAllTopics();
  }

  @Get('/weaviate-report-generator')
  @ApiOperation({ description: 'Weaviate report generator' })
  async reportGenerator() {
    return await this.weaviateService.reportGenerator();
  }

  @Post('/weaviate-feedback')
  @ApiOperation({ description: 'Weaviate get feedback' })
  async getFeedbackSearch(
    @Body()
    body: {
      search: string;
      searchDistance?: number;
      groupForce?: number;
    },
  ) {
    return await this.weaviateService.getFeedback(
      body.search,
      body.searchDistance,
      body.groupForce,
    );
  }

  @Post('/weaviate-ask-question-about-feedback')
  @ApiOperation({ description: 'Weaviate ask question about feedback' })
  async askQuestionAboutFeedback(
    @Body() body: { question: string; feedback: any[] },
  ) {
    return await this.weaviateService.askQuestionAboutFeedback(
      body.question,
      body.feedback,
    );
  }

  @Get('/weaviate-create-feedback-class')
  @ApiOperation({ description: 'Weaviate create feedback class' })
  async createFeedbackClass() {
    //FeedbackPhrases
    await this.weaviateService.createFeedbackClass('FeedbackPhrases');
    await this.weaviateService.createFeedbackClass('Feedback');
    const references = await this.weaviateService.createCrossReference();
    return { result: 'Classes created', references: references };
  }

  @Post('/weaviate-create-feedback')
  @ApiOperation({ description: 'Weaviate create feedback' })
  async createFeedback(@Body() body: { topicSimilarityDistance: number }) {
    return await this.weaviateService.createFeedback(
      body.topicSimilarityDistance,
    );
  }

  @Get('/weaviate-feedback-chuck-report')
  @ApiOperation({ description: 'Weaviate chunk feedback report' })
  async feedbackChunkReport() {
    return await this.weaviateService.feedbackReport();
  }

  @Get('/weaviate-delete-classes')
  @ApiOperation({ description: 'Weaviate delete classes' })
  async deleteClasses() {
    return await this.weaviateService.deleteClasses();
  }

  @Get('/weaviate-create-report')
  @ApiOperation({ description: 'Create a report of the phrases' })
  async createPhrasesReport() {
    const data = [
      { f: 'goed eerste gesprek', p: 1, t: 0, n: 0 },
      { f: 'Voorlopig weet ik dat nog niet', p: 0, t: 1, n: 0 },
      { f: 'kosten niet op de site', p: 0, t: 0, n: 1 },
      { f: "afwezigheid van typisch 'sportschoolvolk'", p: 1, t: 0, n: 0 },
      { f: 'goede begeleiding', p: 3, t: 0, n: 0 },
      { f: 'kleine en open club', p: 1, t: 0, n: 0 },
      { f: 'goed begeleiding', p: 1, t: 0, n: 0 },
      { f: 'inbody gedaan bij Jeremy', p: 0, t: 1, n: 0 },
      { f: 'regels niet duidelijk', p: 0, t: 0, n: 1 },
      { f: 'voortreffelijke apparatuur', p: 1, t: 0, n: 0 },
      { f: 'doe lekker mijn ding', p: 0, t: 1, n: 0 },
      { f: 'Deskundigheid van de fysiotherapeuten', p: 1, t: 0, n: 0 },
      { f: 'spinning fietsen helemaal oké', p: 0, t: 0, n: 1 },
      { f: 'De begeleiding', p: 1, t: 0, n: 0 },
      { f: 'geen specifieke feedback', p: 0, t: 1, n: 0 },
      {
        f: 'Na 1 keer op apparaten kan ik hier nog niets van zeggen',
        p: 0,
        t: 1,
        n: 0,
      },
      { f: 'goed programma', p: 1, t: 0, n: 0 },
      { f: 'vertrouwen in de diensten voor herstel', p: 1, t: 0, n: 0 },
      { f: 'goed verzorgd', p: 1, t: 0, n: 0 },
      { f: 'geen verbeterpunten', p: 0, t: 1, n: 0 },
      { f: 'goede tips tijdens de oefeningen', p: 2, t: 0, n: 0 },
      { f: 'directe begeleiding als het nodig is', p: 1, t: 0, n: 0 },
      { f: 'persoonlijke begeleiding', p: 1, t: 0, n: 0 },
      {
        f: 'gestopt vanwege leeftijd en diverse blessures',
        p: 1,
        t: 1,
        n: 0,
      },
      { f: 'goede uitleg over de apparaten', p: 1, t: 0, n: 0 },
      { f: 'Iets strenger naar de klanten toe', p: 1, t: 1, n: 1 },
      { f: 'kleine groepjes', p: 1, t: 0, n: 0 },
      { f: 'vriendelijke medewerkers', p: 1, t: 0, n: 0 },
      { f: 'intieme karakter', p: 1, t: 0, n: 0 },
      {
        f: 'begeleiding bij beginnende mensen kan beter',
        p: 0,
        t: 0,
        n: 1,
      },
      { f: 'Goede begeleiding', p: 1, t: 0, n: 0 },
      { f: 'Op dit moment zou ik het nog niet weten', p: 0, t: 1, n: 0 },
      { f: 'Professioneel, efficiënt, goede uitleg', p: 2, t: 0, n: 0 },
      {
        f: 'niets kan doen vanwege leeftijd en ongemakken',
        p: 1,
        t: 1,
        n: 2,
      },
      { f: 'binnenkort weer terug', p: 1, t: 0, n: 0 },
      { f: 'Begeleiding en sfeer', p: 1, t: 0, n: 0 },
      { f: 'Combinatie behandelingen en apparaten', p: 2, t: 1, n: 0 },
      { f: 'niet meer terugkeren naar GoHealth', p: 0, t: 0, n: 1 },
      { f: 'kleinschalig', p: 3, t: 0, n: 0 },
      { f: 'kleinschalig', p: 1, t: 0, n: 0 },
      { f: 'Spinning optie als groepsles', p: 0, t: 0, n: 1 },
      { f: 'aandacht die je krijgt', p: 1, t: 0, n: 0 },
      { f: 'ik moet het zelf doen', p: 1, t: 0, n: 0 },
      { f: 'serieus nemen', p: 1, t: 0, n: 0 },
      { f: 'vriendelijk personeel', p: 2, t: 0, n: 0 },
      { f: 'begonnen met trainen', p: 2, t: 1, n: 0 },
      { f: 'te vroeg voor oordeel', p: 0, t: 2, n: 0 },
      { f: 'snel contact kunnen maken', p: 1, t: 0, n: 1 },
      { f: 'Geen idee', p: 0, t: 2, n: 0 },
      { f: 'Rustige en prettige sfeer', p: 1, t: 0, n: 0 },
      { f: 'proefles', p: 0, t: 1, n: 0 },
      { f: 'Persoonlijk zonder een oordeel te vellen', p: 1, t: 0, n: 0 },
      { f: 'hijgen uitloop', p: 1, t: 0, n: 0 },
      { f: 'prima machinerie', p: 1, t: 0, n: 0 },
      { f: 'geen sterk punt ontdekt', p: 0, t: 1, n: 0 },
      { f: 'Verkleedruimte zou geen luxe zijn', p: 0, t: 0, n: 1 },
      { f: 'Ernstige astma aanval gehad', p: 0, t: 1, n: 0 },
      { f: 'goede persoonlijke benadering', p: 1, t: 0, n: 0 },
      { f: 'goede apparatuur', p: 2, t: 0, n: 1 },
      { f: 'Bluetooth goed werkt', p: 0, t: 0, n: 1 },
      { f: 'prima douche', p: 1, t: 0, n: 0 },
      { f: 'Goede persoonlijke begeleiding', p: 8, t: 1, n: 1 },
      { f: 'niet sporten vanwege een rugoperatie', p: 0, t: 1, n: 0 },
      { f: 'luisteren naar je', p: 2, t: 0, n: 0 },
      {
        f: 'Geheel vernieuwde state of the art Technogym apparatuur',
        p: 1,
        t: 0,
        n: 0,
      },
      { f: 'rustige sfeer', p: 1, t: 0, n: 0 },
      { f: 'geschikt voor ouderen', p: 1, t: 0, n: 0 },
      { f: 'Persoonlijk en gelukkig kleinschalig', p: 1, t: 0, n: 0 },
      { f: 'Vriendelijk en enthousiast personeel', p: 1, t: 0, n: 0 },
      { f: 'op peil houden van spieren', p: 1, t: 0, n: 0 },
      { f: 'welkom gevoel', p: 1, t: 0, n: 0 },
      { f: 'ligt helemaal aan mijzelf', p: 0, t: 1, n: 0 },
      { f: 'na een vakantie van 3 weken', p: 0, t: 1, n: 1 },
      { f: 'vriendelijke medewerkers', p: 2, t: 0, n: 0 },
      {
        f: 'locatie, sfeer en mensen zijn super en vriendelijk',
        p: 1,
        t: 0,
        n: 0,
      },
      { f: 'Toegankelijk, laagdrempelig', p: 1, t: 0, n: 0 },
      { f: 'Kleinschalig en gezellig', p: 1, t: 0, n: 0 },
      { f: 'Niks', p: 0, t: 1, n: 0 },
      { f: 'veel leeftijdsgenoten', p: 1, t: 0, n: 0 },
      { f: 'deodorant terug in de mannenkleedkamer', p: 0, t: 0, n: 1 },
      { f: 'pas 2x geweest', p: 0, t: 1, n: 0 },
      { f: 'geavanceerde toestellen', p: 1, t: 0, n: 0 },
      { f: 'meer coulance', p: 0, t: 0, n: 1 },
      { f: 'zeer kundig', p: 1, t: 0, n: 0 },
      { f: 'geen wachttijd', p: 1, t: 0, n: 0 },
      { f: 'Niets', p: 1, t: 0, n: 0 },
      { f: 'kleinschalig en vriendelijk', p: 1, t: 0, n: 0 },
      { f: 'verbeteren van conditie', p: 1, t: 0, n: 1 },
      { f: 'voorzichtig de goede kant op', p: 1, t: 0, n: 0 },
      { f: 'Kwalitatieve /sympathieke begeleiding', p: 1, t: 0, n: 0 },
      { f: 'Geen idee', p: 0, t: 1, n: 0 },
      { f: 'zeer goede vriendelijke begeleiding', p: 2, t: 0, n: 0 },
      { f: 'geen gedoe met zelf instellen', p: 1, t: 0, n: 0 },
      { f: 'Goede apparaten', p: 3, t: 0, n: 0 },
      { f: 'gezellige sportschool', p: 4, t: 3, n: 0 },
      { f: 'Persoonlijk', p: 1, t: 0, n: 0 },
      { f: 'altijd een lach', p: 2, t: 0, n: 0 },
      { f: 'Positieve en gezellige sfeer', p: 4, t: 0, n: 0 },
      { f: 'Goede begeleiding?', p: 2, t: 1, n: 0 },
      { f: 'ongedwongen sfeer', p: 1, t: 0, n: 0 },
    ];
    return await this.feedbackReportService.createReport(data);
  }

  /**
   * WEAVIATE CLASSIFICATION
   */

  @Get('/x-weaviate-create-classification-labels-class')
  @ApiOperation({ description: 'Create classification labels class' })
  async createClassificationLabelsClass() {
    return await this.weaviateClassificationService.createClassificationLabelsClass(
      'ClassificationLabels',
    );
  }

  @Get('/x-weaviate-delete-classification-labels-class')
  @ApiOperation({ description: 'Delete classification labels class' })
  async deleteClassificationLabelsClass() {
    return await this.weaviateClassificationService.deleteClasses(
      'ClassificationLabels',
    );
  }

  @Get('/x-weaviate-add-classification-labels')
  @ApiOperation({ description: 'Add classification labels ' })
  async addClassificationLabels() {
    return await this.weaviateClassificationService.addClassificationLabels(
      'ClassificationLabels',
    );
  }

  @Post('/x-weaviate-find-classification-labels')
  @ApiOperation({ description: 'Find classification labels ' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        textToClassify: {
          type: 'string',
          description: 'Text to classify',
        },
        question: {
          type: 'string',
          description: 'The question asked to the consumer',
        },
      },
      required: ['textToClassify'],
    },
  })
  async findClassificationLabels(
    @Body() body: { textToClassify: string; question: string },
  ) {
    return await this.weaviateClassificationService.findClassificationLabels(
      'ClassificationLabels',
      body.textToClassify,
      body.question,
    );
  }
}
