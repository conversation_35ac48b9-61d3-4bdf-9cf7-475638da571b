import { HttpService } from '@nestjs/axios';
import { HttpException, Injectable } from '@nestjs/common';
import { AxiosResponse } from 'axios';
import OpenAI from 'openai';
import { lastValueFrom, map, tap } from 'rxjs';
import { ModuleLogger } from "@lib/logger/module-logger/module-logger.service";

@Injectable()
export class OpenAIService {
  openai: OpenAI;
  constructor(private logger: ModuleLogger, private httpService: HttpService) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async createChatCompletion(body: any, options?: { apiKey: string }) {
    if (process.env.OPENAI_FORWARD?.startsWith('http')) {
      try {
        this.logger.debug(`createChatCompletion FORWARD ${process.env.OPENAI_FORWARD} `, {body});
        return lastValueFrom(
          this.httpService
            .post(process.env.OPENAI_FORWARD, body, {
              headers: {
                'x-webhook-key': process.env.API_WEBHOOK_SECRET,
                'x-api-key': process.env.OPENAI_API_KEY,
              },
            })
            .pipe(
              tap((response) =>
                console.log('OPENAI FORWARD', 'response', response),
              ),
              map((response) => response.data),
            ),
        ) as Promise<AxiosResponse<any, any>>;
      } catch (e) {
        console.log('OPENAI FORWARD', 'ERROR', e);
        throw new HttpException(e?.response?.error || 'error', 400);
      }
    } else {
      let openai = this.openai;
      if (options?.apiKey) {
        openai = new OpenAI({
          apiKey: options?.apiKey,
        });
      }

      let result = await openai.chat.completions.create(body);
      // console.log('OPENAI', 'createChatCompletion = res', JSON.stringify(result?.choices[0].message, null, 2));
      // console.log(
      //   'OPENAI',
      //   'response status',
      //   result.status,
      //   result.statusText,
      //   result?.usage,
      // );

      return result;
    }
  }
}
