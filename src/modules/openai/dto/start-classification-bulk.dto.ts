import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsNumber } from 'class-validator';

export class StartClassificationBulkDto {
  @Type()
  @ApiProperty({ default: [] })
  @IsNumber({}, { each: true })
  answerIds: number[];
}
export class StartClassificationByDateDto {
  @Type()
  @IsDate()
  @ApiProperty({ default: new Date() })
  start: Date;

  @Type()
  @IsDate()
  @ApiProperty({ default: new Date() })
  end: Date;
}
