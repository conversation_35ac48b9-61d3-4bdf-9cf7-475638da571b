export interface OpenAIResponse {
  completion: any;
  usage: OpenAIUsage;
}

export interface OpenAIUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}


export interface WeaviateAddObject {
  parent_category: string,
  parent_category_description: string,
  sub_category: string,
  sub_category_description: string
  rules?: string
}


export interface Category {
  parent_category: string;
  parent_category_description?: string;
  sub_categories: SubCategory[];
}

export interface SubCategory {
  name: string;
  sub_category_description?: string;
}
