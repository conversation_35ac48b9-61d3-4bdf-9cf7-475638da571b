import { Injectable } from '@nestjs/common';
import { Subject } from 'rxjs';
import * as fs from 'fs';
import * as moment from 'moment';
/**
 * A service for processing rows of data in a queue and
 * formatting the results into a CSV file.
 * @class
 */
@Injectable()
export class QueueProcessingBaseService {
  queueFinished = new Subject<any>();
  resultArray = [];
  queue: any;

  /**
   * Creates an instance of QueueProcessingService.
   * @param {OpenaiClassificationService} openAIClassificationService - The service for getting classification from OpenAI API.
   */
  constructor() {}

  /**
   * Processes a queue of rows of data, formats the resuls and writes them to a CSV file.
   * @param surveyData
   */
  public async processQueue(list: any[]) {
    console.log('Record count: ', list.length); // Log the total data count

    list.forEach((row, index) => {
      // Add all the rows to the queue
      const task = this.queue.push(row); // Push the row to the queue
      task.id = index + 1; // Assign a task ID for easier tracking
      task.on('progress', function (progress) {
        console.log(
          `TASK PROGRESS - eta:${progress.eta} completed: ${progress.complete} total: ${progress.total}`,
        ); // Log the progress
      });
    });
  }

  /**
   * Registers event handlers for a given queue.
   * @param {any} q - The queue to register event handlers for.
   */
  protected registerEventHandlers(q) {
    // Event handler for when all tasks are finished
    q.on('drain', () => {
      // console.log(
      //   'ALL TASKS FINISHED',
      //   JSON.stringify(this.resultArray, null, 4),
      // );
        console.log('ALL TASKS FINISHED');
      if (process.env.NODE_FOLDER) {
        let file = `${process.env.NODE_FOLDER}data/classification-results.json`;
        // console.log('resultArray', this.resultArray)
        fs.writeFileSync(file, JSON.stringify(this.resultArray));
      }
      this.queueFinished.next({ data: 'QUEUE FINISHED' });
    });

    // Event handler for when a single task is finished
    q.on('task_finish', (taskId, result) => {
      const stats = q.getStats();
      const processTime = moment
        .utc(moment.duration(3693 * 1000, 'millisecond').asMilliseconds())
        .format('HH:mm:ss');

      console.log('      ');
      console.log('\x1b[32m%s\x1b[0m', `Total tasks processed: ${stats.total}`);
      console.log('\x1b[32m%s\x1b[0m', `Average process time: ${processTime}`);
      console.log(
        '\x1b[32m%s\x1b[0m',
        `Success rate: ${stats.successRate * 100} %`,
      );
      console.log(
        '\x1b[32m%s\x1b[0m',
        `Most tasks queued at any given point in time: ${stats.peak}`,
      );
      // console.log(
      //   '\x1b[32m%s\x1b[0m',
      //   `Task ${taskId} completed with result:`,
      //   result,
      // );
    });
    // Event handler for when a single task fails
    q.on('task_failed', (taskId, error) =>
      console.log(`Task ${taskId} failed with error:`, error),
    );
    // Event handler for when a single task is started
    q.on('task_started', (taskId) => console.log(`Task ${taskId} started`));
    // Event handler for progress updates
    q.on('task_progress', (taskId) => console.log(`Task ${taskId} started`));
  }
}
