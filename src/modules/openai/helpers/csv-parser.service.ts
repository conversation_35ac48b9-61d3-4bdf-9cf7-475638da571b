import { Injectable } from '@nestjs/common';
import * as fs from "fs";
import * as <PERSON> from 'papaparse';

@Injectable()
export class CsvParserService {
    async getColumnFromCsv(
      filename: string,
      strengths: string,
      improvements,
    ): Promise<any> {
        // Read the file
        const data = fs.readFileSync(filename, { encoding: 'utf-8' });

        // Parse the data into an array of objects, using the first row as the keys
        const rows = Papa.parse(data, { header: true }).data;
        // Remove duplicates
        // Create a Set to store the unique rows
        let uniqueRows = new Set();

        // Remove duplicates
        const cleanRows = rows.filter((row) => {
            // Convert the row to a string
            const rowString = row[improvements] + row[strengths];

            // Check if the row is unique
            if (uniqueRows.has(rowString) || rowString === '') {
                return false; // Duplicate, filter it out
            } else {
                uniqueRows.add(rowString); // Unique, keep it
                return true;
            }
        });

        // Extract the values from the specified column
        const reviews = cleanRows.map((row) => {
            return { review: row[improvements] + row[strengths] };
            // const cleanedReviews = reviews.filter(row => row.review )
            // return cleanedReviews;
        });
        return reviews;
    }
    async getCSV(filename: string) {
        // Read the file
        const data = fs.readFileSync(filename, { encoding: 'utf-8' });

        // Parse the data into an array of objects, using the first row as the keys
        const rows = Papa.parse(data, { header: true, skipEmptyLines: true }).data;
        // Remove duplicates
        // Create a Set to store the unique rows
        return rows;
    }

    async parseCSV(buffer: Buffer) {
        // Convert the buffer to a string
        const data = buffer.toString('utf-8');

        // Parse the data into an array of objects, using the first row as the keys
        const rows = Papa.parse(data, { header: true, skipEmptyLines: true }).data;

        // Return the parsed data
        return rows;
    }
}
