import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import weaviate, { ApiKey, WeaviateClient } from 'weaviate-ts-client';
import { OpenAIService } from '../../openai.service';
import * as Queue from 'better-queue';
import { env } from '@/app.env';
const { CohereClient } = require('cohere-ai');
// Instantiate the client with the auth config
const client: WeaviateClient = weaviate.client({
  scheme: 'https',
  host: 'classification-lnh9rddu.weaviate.network',
  apiKey: new ApiKey('GyoS91mocDt5esG7CaPlE7hQy2lgJNvGMWjx'),
  headers: {
    'X-Openai-Api-Key': process.env.OPENAI_API_KEY,
    'X-Cohere-Api-Key': 'DarZfIeabpuU4rdmDeo0nhvV6HRQwewTMJZ0PsN9',
  },
});
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});
const cohere = new CohereClient({
  token: 'DarZfIeabpuU4rdmDeo0nhvV6HRQwewTMJZ0PsN9',
});

interface ClassificationLabels {
  parent: string;
  label: string;
}
const labels: ClassificationLabels[] = [
  { parent: 'Groepslessen', label: 'Drukte' },
  { parent: 'Medewerkers', label: 'Beschikbaarheid' },
  { parent: 'Groepslessen', label: 'Kwaliteit van groepslessen' },
  { parent: 'Groepslessen', label: 'Breedte van aanbod' },
  { parent: 'Overige', label: 'Correspondentie' },
  { parent: 'Algemeen', label: 'Temperatuur' },
  { parent: 'Overige', label: 'Diefstal' },
  { parent: 'Algemeen', label: 'Drukte' },
  { parent: 'Overige', label: 'Parkeren' },
  { parent: 'Algemeen', label: 'Openingstijden' },
  { parent: 'App en website', label: 'Reserveringen' },
  { parent: 'Algemeen', label: 'Grootte' },
  { parent: 'App en website', label: 'Gebruiksvriendelijkheid' },
  { parent: 'Algemeen', label: 'Hygiëne' },
  { parent: 'Abonnementen', label: 'Flexibiliteit' },
  { parent: 'Begeleiding', label: 'Kwaliteit' },
  { parent: 'Abonnementen', label: 'Inhoud abonnement' },
  { parent: 'Begeleiding', label: 'Persoonlijk' },
  { parent: 'Abonnementen', label: 'Prijs' },
  { parent: 'Begeleiding', label: 'Motiverend' },
  { parent: 'Overige', label: 'Buiten trainen' },
  { parent: 'Faciliteiten', label: 'Kluisjes' },
  { parent: 'Begeleiding', label: 'Instructies' },
  { parent: 'Faciliteiten', label: 'Douches' },
  { parent: 'Begeleiding', label: 'Voedingsschema' },
  { parent: 'Faciliteiten', label: 'Kleedkamers' },
  { parent: 'Faciliteiten', label: 'Toiletten' },
  { parent: 'Begeleiding', label: 'Trainingsschema' },
  { parent: 'Intake', label: 'Kwaliteit' },
  { parent: 'Faciliteiten', label: 'Zonnebank' },
  { parent: 'Intake', label: 'Persoonlijk' },
  { parent: 'Algemeen', label: 'Muziek' },
  { parent: 'Faciliteiten', label: 'Sauna' },
  { parent: 'Intake', label: 'Tijdsduur' },
  { parent: 'Faciliteiten', label: 'Gebruiksvriendelijkheid apparatuur' },
  { parent: 'Intake', label: 'Aandacht' },
  { parent: 'Faciliteiten', label: 'Hoeveelheid apparatuur' },
  { parent: 'Medewerkers', label: 'Vriendelijkheid' },
  { parent: 'Faciliteiten', label: 'Staat van apparatuur' },
  { parent: 'Medewerkers', label: 'Vakkundigheid' },
  { parent: 'Algemeen', label: 'Sfeer' },
];
@Injectable()
export class WeaviateClassificationService {
  constructor(private openAIService: OpenAIService) {}

  async createCrossReference() {
    try {
      await client.schema
        .propertyCreator()
        .withClassName('FeedbackPhrases')
        .withProperty({
          name: 'hasFeedback',
          dataType: ['Feedback'],
        })
        .do();
      // console.log(JSON.stringify(result, null, 2));
    } catch (e) {
      console.error(e);
    }
  }

  async createClassificationLabelsClass(className: string) {
    const classWithProps = {
      class: className,

      vectorizer: 'text2vec-openai', // this could be any vectorizer
      // vectorizer: "text2vec-cohere",
      moduleConfig: {
        'text2vec-openai': {
          vectorizeClassName: true,
          model: 'text-embedding-3-large',
          dimensions: 3072, // Parameter only applicable for `v3` model family and newer
          type: 'text',
        },
        'generative-openai': {
          model: 'gpt-3.5-turbo', // Optional - Defaults to `gpt-3.5-turbo`
          temperatureProperty: 0, // Optional, applicable to both OpenAI and Azure OpenAI
          vectorizeClassName: true,
        },
        'reranker-cohere': {
          model: 'rerank-multilingual-v2.0',
        },
      },
      properties: [
        { name: 'parent', description: 'The main label', dataType: ['string'] },
        { name: 'label', description: 'The sub label', dataType: ['string'] },
        {
          name: 'fullLabel',
          description: 'The main label and sub label',
          dataType: ['string'],
        },
        {
          name: 'label_explanation',
          description: 'An explanation of when to use this label',
          dataType: ['string'],
        },
        {
          name: 'label_keywords',
          description:
            'A comma separated list of keywords related to the label',
          dataType: ['string'],
        },
      ],
    };
    try {
      // Add the class to the schema
      const result = await client.schema
        .classCreator()
        .withClass(classWithProps)
        .do();

      // The returned value is the full class definition, showing all defaults
      console.log(JSON.stringify(result, null, 2));
      return result;
    } catch (e) {
      console.log(e);
    }
  }

  async deleteClasses(className: string) {
    const deleteResult = await client.schema
      .classDeleter()
      .withClassName(className)
      .do();
    console.log('' + className + ' class deleted');
    return deleteResult;
  }

  async addClassificationLabels(className: string) {
    let dataObjs = labels.map((label) => {
      return { ...label, fullLabel: `${label.parent} - ${label.label}` };
    });
    let answers = [];
    const processTask = async (task, callback) => {
      try {
        const labelsCompletion = await openai.chat.completions.create({
          model: env.OPENAI_NEW_MODEL,
          response_format: { type: 'json_object' },
          messages: [
            {
              role: 'system',
              content:
                "You are a helpful assistant that adds a good description to the labels. When you've done that you need to output JSON.",
            },
            {
              role: 'user',
              content: `Here are the labels: """${JSON.stringify(task)}"""`,
            },
            {
              role: 'user',
              content: `For each label, add a good description about the label so that it is very clear where the label should be used for. 
                  The description should be written in a way so that we can do a similarity search on the description and the label which we use for classification.
                  Because these labels are meant for a classification system, I also want you to add a comma separated list of keywords that are relevant to the label.
                  The keywords should be things like synonyms, highly related words, or topics that are very similar to the label.
                  Here is how you should return the results:
                        {
                            "labels": [
                                {
                                "parent": "the original paren label", 
                                "label": "the original sub label", 
                                "fullLabel": "the original full label", 
                                "label_explanation": "The description of the label in the same language as the parent and the label",
                                "label_keywords": "comma separated list of keywords that are relevant to the label"
                                }
                            ]
                        }
                    `,
            },
          ],
        });

        console.log('Labels usage: ', labelsCompletion.usage);
        const labelSets = JSON.parse(
          labelsCompletion.choices[0].message.content,
        )?.labels;
        console.log(
          'labelSets completion: ',
          JSON.stringify(labelSets, null, 2),
        );
        callback(null, labelSets);
      } catch (e) {
        console.log(e);
      }
    };

    // Instantiate the queue with the processing function
    const q = new Queue(processTask, { concurrent: 30, batchDelay: 100 });

    // Add tasks to the queue
    // for(const chunk of sorted) {
    //     // for (const feedback of chunk) {
    //     const topicList = []
    //     chunk.forEach(feedback => {
    //     })
    // }
    dataObjs.forEach((feedback) => {
      // console.log('Processing feedback: ', feedback)
      q.push(feedback);
    });

    // Handle task completion
    q.on('task_finish', (taskId, result, stats) => {
      console.log(`Task ${taskId} completed with result: ${result}`);
      const answer = result;
      answer.forEach((data) => {
        answers.push(data);
      });
    });

    // Handle task failure
    q.on('task_failed', (taskId, errorMessage, stats) => {
      console.error(`Task ${taskId} failed with error: ${errorMessage}`);
    });

    q.on('drain', async () => {
      console.log('Finished: ', answers);
      console.log('Finished total: ', answers.length);
      let batcher5 = client.batch.objectsBatcher();
      for (const dataObj of answers)
        batcher5 = batcher5.withObject({
          class: className,
          properties: dataObj,
          // tenant: 'tenantA'  // If multi-tenancy is enabled, specify the tenant to which the object will be added.
        });

      // Flush
      await batcher5.do();
    });

    // for (let i = 1; i <= 5; i++)
    //     dataObjs.push({ title: `Object ${i}` });  // Replace with your actual objects
  }

  async findClassificationLabels(
    className: string,
    textToClassify: string,
    question: string,
  ) {
    const text = 'Verkleedruimte zou geen luxe zijn';
    try {
      const topicsCompletion = await openai.chat.completions.create({
        model: env.OPENAI_NEW_MODEL,
        response_format: { type: 'json_object' },
        messages: [
          {
            role: 'system',
            content:
              "You are a helpful assistant that extracts a list of topics from a given text. When you've done that you need to output JSON.",
          },
          {
            role: 'user',
            content: `Here is the text: """${textToClassify}"""`,
          },
          {
            role: 'user',
            content: `Here is an example of how you should return the topics:
                        {
                            "topics": [
                                "the topic you found"
                            ]
                        }
                    `,
          },
          {
            role: 'user',
            content: `Make sure to return the topics in the same language as the text was written in. 
                    Also make sure the topics are clear and represent what the text is about.
                    If needed the topic may be a sentence or a few words that represent the text. It is important that the topics have enough information and context to be able to find similar texts with them.`,
          },
        ],
      });
      console.log(
        'Topics completion: ',
        topicsCompletion.choices[0].message.content,
      );
      console.log('Topics usage: ', topicsCompletion.usage);
      const topics = JSON.parse(
        topicsCompletion.choices[0].message.content,
      )?.topics;
      const initialQuery = `De sportschool is te druk en de apparaten zijn vaak kapot.`;
      let searchContext = new SearchContext(topics, question, textToClassify);
      await searchContext.process();

      console.log('Final Answer:', searchContext.finalAnswer);
      console.log('Final Answer:', searchContext.results);

      //     // OLD WAY
      //     const labelList = []
      //     for (const topic of topics){
      //         const result = await client.graphql
      //             .get()
      //             .withClassName(className)
      //             .withNearText({ concepts: [topic], distance: 0.6})
      //             .withFields('parent label fullLabel label_explanation label_keywords _additional { distance }')
      //             .withAutocut(1)
      //             .withLimit(2)
      //             .do();
      //         result.data.Get[className].forEach((label) => {
      //             labelList.push(label)
      //         })
      //     }
      //
      //     if(labelList.length === 0){
      //         for (const topic of topics){
      //             const result = await client.graphql
      //                 .get()
      //                 .withClassName(className)
      //                 .withNearText({ concepts: [topic], distance: 0.7})
      //                 .withFields('parent label fullLabel label_explanation label_keywords _additional { distance }')
      //                 .withAutocut(1)
      //                 .withLimit(2)
      //                 .do();
      //             result.data.Get[className].forEach((label) => {
      //                 labelList.push(label)
      //             })
      //         }
      //     }
      //     console.log("labelList: ", JSON.stringify(labelList, null, 2));
      //     console.log("labelList count: ", labelList.length);
      //
      //     const labelsForAI = labelList.map((label) => { return {parent: label.parent, label: label.label}})
      //     const completion = await openai.chat.completions.create({
      //         model: 'gpt-4-0125-preview',
      //         response_format: { type: "json_object" },
      //         messages: [
      //             {
      //                 role: "system",
      //                 content: "You are a helpful assistant that classifies text to one or more labels. When you've done that you need to output JSON.",
      //             },
      //             { role: "user", content: `Rules for classification:
      // - To get the complete picture, you need to look at the question and the answer together. Note: the question is to give you some extra context.
      //         Note: Question-1 is always about what the customer likes about the product or service and can be seen as a more positive interpretation. Answers that have 'no clear sentiment' are always neutral or positive.
      //         Note: Question-2 is always about what the customer thinks can be improved or is not good and can be seen as a more negative interpretation.  Answers that have 'no clear sentiment' are always neutral or negative.
      //         So make sure to fully understand the context because sometimes it looks like sarcasm and the sentiment is therefore the opposite.
      //
      // - Do not hallucinate and do not make up things that are not in the customer feedback. Only use the customer feedback to classify the customer feedback.
      // - You can classify the customer feedback into multiple categories and subcategories.
      // - Look very carefully when choosing a category and subcategory and make sure the combination of category and subcategory makes sense and are both highly relevant to the feedback the customer has given.
      // - Ignore customer feedback with only abbreviation, very short feedback or vague feedback. If the other question has good feedback that makes sense and is clear, then you can classify that one of course.
      //   If you have ignored the feedback then mention the reason in the justification_details field.
      // - I might also be the case that on of the questions and answer is empty. In that case, you can ignore that question and answer and only classify the other one.
      // - Important! It should be very clear that the feedback belongs to both main and subcategory. If 1 of the 2 don't match or in not clearly mentioned then do not use it!
      //   In other words, you need to be absolutely sure that the topics in the customer feedback are highly relevant to the main category and subcategory that you choose.
      //   Here is an example when not to choose a category and subcategory:
      //     the feedback: 'Persoonlijke aandacht'
      //     and you have a parent_category: 'Intake'
      //     with a sub_category: 'Aandacht'
      //     then it is no match and you should not choose it.
      //   This is because it is not clear from the feedback that it is about the 'Intake' although the sub_category could match, the parent_category 'Intake' is not clear . Use the answer object to classify the customer feedback.
      //   It is important that you look at the question and the answer together to get the complete picture. So if the question is about improvement and in the answer the sentiment is not clear, it is most likely negative.
      //   ` },
      //             {
      //                 role: "user",
      //                 content: `Here is the question we asked our customer: """${question}"""`,
      //             },
      //             {
      //                 role: "user",
      //                 content: `Here is the feedback to classify: """${textToClassify}"""`,
      //             },
      //             {
      //                 role: "user",
      //                 content: `Here are the labels: """${JSON.stringify(labelsForAI)}"""`,
      //             },
      //             {
      //                 role: "user",
      //                 content: `Please classify the feedback to the labels above. Here is how you should return the results:
      //
      //                 {
      //                     "answer": [
      //                         {
      //                             "parent": "The parent label",
      //                             "label": "The sub label",
      //                             "sentiment": "Positive or Neutral or Negative",
      //                         }
      //                     ]
      //                 }`,
      //
      //             },
      //         ],
      //     })
      //     console.log("Completion: ", completion.choices[0].message.content)
      //     console.log("Completion: ", completion.usage)
      //     return completion.choices[0].message.content;
    } catch (e) {
      console.log(e);
    }
  }
}

// State Interface
interface State {
  handle(context: SearchContext): Promise<void>;
}

// Concrete States
class RetrievalState implements State {
  async handle(context: SearchContext): Promise<void> {
    console.log('Performing retrieval from vector database...', context.query);
    const labelList = [];
    // Create an array of promises for each topic
    const promises = context.topics.map(async (topic) => {
      const result = await client.graphql
        .get()
        .withClassName('ClassificationLabels')
        .withNearText({ concepts: [topic], distance: 0.7 })
        .withFields(`parent label  _additional{distance}`)
        .withLimit(2)
        .do();
      return result.data.Get.ClassificationLabels;
    });

    // Wait for all promises to resolve
    const allResults = await Promise.all(promises);

    // Push all results to labelList
    allResults.forEach((result) => {
      result.forEach((label) => {
        labelList.push(label);
      });
    });

    // Logic for similarity search in a vector database
    context.results = labelList; // Replace with actual search results
    // console.log('context.results', context.results)
    // console.log(JSON.stringify(result, null, 2));
    context.finalAnswer = 'Final answer';
    context.setState(new RelevanceCheckState());
  }
}

class RelevanceCheckState implements State {
  async handle(context: SearchContext): Promise<void> {
    console.log('Checking relevance of results...');
    const relevantLabels = [];

    // Create an array of promises for each topic
    const promises = context.topics.map(async (topic) => {
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo-0125',
        messages: [
          {
            role: 'system',
            content: 'Check if the found data is relevant to the user input',
          },
          {
            role: 'user',
            content: `You need to check if the found data is relevant to the user input. 
            [data-found]: ${JSON.stringify(context.results)}[/data-found]
            [user-input]: ${context.query}[/user-input]
            [topic]: ${topic}
            Return as JSON like this:{
            labels:[{
                parent: "The parent label",
                label: "The sub label",
                relevance: boolean,
                topic: "The relevant topic"
                }]
            }`,
          },
        ],
        response_format: { type: 'json_object' },
      });
      const responseMessage = completion.choices[0].message.content;
      console.log('responseMessage', responseMessage);
      const parsedResponse = JSON.parse(responseMessage);
      // Logic to check relevance with LLM

      parsedResponse.labels.forEach((label) => {
        if (label.relevance) {
          relevantLabels.push(label);
        }
      });
    });

    // Wait for all promises to resolve
    await Promise.all(promises);

    // If all topics have a relevant label, proceed to the next state
    if (relevantLabels.length === context.topics.length) {
      console.log(
        'All topics have a relevant label',
        JSON.stringify(relevantLabels, null, 2),
      );
      context.setState(new DecisionState());
    } else {
      console.log(
        'Not All topics have a relevant label!!',
        JSON.stringify(relevantLabels, null, 2),
      );
      context.setState(new ModifyQueryState());
    }
  }
}

class DecisionState implements State {
  async handle(context: SearchContext): Promise<void> {
    console.log('Deciding next step...');

    // Logic for decision making
    const generateAnswer = true; // Replace with decision making logic

    context.setState(
      generateAnswer ? new GenerationState() : new ModifyQueryState(),
    );
  }
}

class ModifyQueryState implements State {
  async handle(context: SearchContext): Promise<void> {
    console.log('Modifying query and restarting search...');
    // Logic for modifying the query
    context.query = 'Modified query'; // Replace with actual query modification logic
    context.setState(new RetrievalState());
  }
}

class GenerationState implements State {
  async handle(context: SearchContext): Promise<void> {
    console.log('Generating answer...');
    const completion = await openai.chat.completions.create({
      model: env.OPENAI_NEW_MODEL,
      messages: [
        {
          role: 'system',
          content:
            "You are a helpful assistant that classifies text to one or more labels. When you've done that you need to output JSON.",
        },
        {
          role: 'user',
          content: `Rules for classification:
                

                - Do not hallucinate and do not make up things that are not in the customer feedback. Only use the customer feedback to classify the customer feedback.
                - You can classify the customer feedback into multiple categories and subcategories.
                - Look very carefully when choosing a category and subcategory and make sure the combination of category and subcategory makes sense and are both highly relevant to the feedback the customer has given.
                - Ignore customer feedback with only abbreviation, very short feedback or vague feedback. If the other question has good feedback that makes sense and is clear, then you can classify that one of course.
                  If you have ignored the feedback then mention the reason in the justification_details field.
                - I might also be the case that on of the questions and answer is empty. In that case, you can ignore that question and answer and only classify the other one.
                - Important! It should be very clear that the feedback belongs to both main and subcategory. If 1 of the 2 don't match or in not clearly mentioned then do not use it!
                  In other words, you need to be absolutely sure that the topics in the customer feedback are highly relevant to the main category and subcategory that you choose.
                  Here is an example when not to choose a category and subcategory:
                    the feedback: 'Persoonlijke aandacht'
                    and you have a parent_category: 'Intake'
                    with a sub_category: 'Aandacht'
                    then it is no match and you should not choose it.
                  This is because it is not clear from the feedback that it is about the 'Intake' although the sub_category could match, the parent_category 'Intake' is not clear . Use the answer object to classify the customer feedback.
                  It is important that you look at the question and the answer together to get the complete picture. So if the question is about improvement and in the answer the sentiment is not clear, it is most likely negative.
                  Make sure to cover each topic in the feedback when you classify it. So each topic should have a label with a sentiment.
                  `,
        },
        {
          role: 'user',
          content: `Here is the question we asked our customer: """${context.question}"""`,
        },
        {
          role: 'user',
          content: `Here is the feedback to classify: """${context.customerFeedback}"""`,
        },
        {
          role: 'user',
          content: `Here are the labels: """${JSON.stringify(
            context.results,
          )}"""`,
        },
        {
          role: 'user',
          content: `Please classify the feedback to the labels above. Here is how you should return the results:

                                {
                                    "answer": [
                                        {
                                            "parent": "The parent label",
                                            "label": "The sub label",
                                            "sentiment": "Positive or Neutral or Negative",
                                        }
                                    ]
                                }`,
        },
      ],
      //     messages: [{
      //         role: "system",
      //         content: "Classify the user feedback into one of the following categories: 'Positive', 'Negative', or 'Neutral'.",
      //     }, {
      //         role: "user",
      //         content: `Classify the feedback into one or more categories with a sentiment.
      // [data-found]: ${JSON.stringify(context.results)}[/data-found]
      // [user-input]: ${context.query}[/user-input]
      // Return as JSON like this: {
      //   answer:[
      //   {
      //     parent: "The parent label",
      //     label: "The label",
      //     sentiment: "Positive, Negative, Neutral",
      //   }
      //   ]
      // }`,
      //     }],
      response_format: { type: 'json_object' },
    });
    // Logic for generating the answer
    context.finalAnswer = JSON.parse(completion.choices[0].message.content);
    context.setState(null); // End of process
  }
}

// Context
class SearchContext {
  private state: State;
  public query: string;
  public topics: string[];
  public results: any[];
  public question: string;
  public customerFeedback: string;
  public finalAnswer: string;

  constructor(
    topics: string[],
    question: string,
    customerFeedback: string,
    query?: string,
  ) {
    this.query = query;
    this.topics = topics;
    this.question = question;
    this.customerFeedback = customerFeedback;
    this.state = new RetrievalState();
  }

  setState(state: State): void {
    this.state = state;
  }

  async process(): Promise<void> {
    while (this.state != null) {
      await this.state.handle(this);
    }
  }
}
