import {CategoriesPrompt} from "@/modules/openai/services/prompt-builder/default-prompts/categories.prompt";
import {ClassificationRulesPrompt} from "@/modules/openai/services/prompt-builder/default-prompts/classification-rules.prompt";
import {CustomLabelsPrompt} from "@/modules/openai/services/prompt-builder/default-prompts/custom-labels.prompt";
import {CustomPrompt} from "@/modules/openai/services/prompt-builder/default-prompts/custom.prompt";
import {CustomerFeedback} from "@/modules/openai/services/prompt-builder/default-prompts/customer-feedback.prompt";
import {ExtraInfoActionRecommenderPrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-action-recommender.prompt";
import {ExtraInfoBasePrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-base.prompt";
import {ExtraInfoContactCustomerReasonPrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-contact-customer-reason.prompt";
import {ExtraInfoContactCustomerWriteEmailPrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-contact-customer-write-email.prompt";
import {ExtraInfoContactCustomerPrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-contact-customer.prompt";
import {ExtraInfoJustificationDetailsPrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-justification-details.prompt";
import {ExtraInfoKeywordsPrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-keywords.prompt";
import {ExtraInfoOverallConfidencePrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-overall-confidence.prompt";
import {JustificationPrompt} from "@/modules/openai/services/prompt-builder/default-prompts/justification.prompt";
import {LastGuidePrompt} from "@/modules/openai/services/prompt-builder/default-prompts/last-guide.prompt";
import {SentimentMarkupPrompt} from "@/modules/openai/services/prompt-builder/default-prompts/sentiment-markup.prompt";
import {SystemPrompt} from "@/modules/openai/services/prompt-builder/default-prompts/system.prompt";
import {ExtraInfoPhrasesPrompt} from "@/modules/openai/services/prompt-builder/extra-info-prompts/extra-info-phrases.prompt";

const promptTypeMap = {
    'SystemPrompt': SystemPrompt,
    'ClassificationRulesPrompt': ClassificationRulesPrompt,
    'CustomLabelRulesPrompt': CustomLabelsPrompt,
    'SentimentMarkupPrompt': SentimentMarkupPrompt,
    'JustificationPrompt': JustificationPrompt,
    'LastGuidePrompt': LastGuidePrompt,
    'CustomerFeedback': CustomerFeedback,
    'CategoriesPrompt': CategoriesPrompt,
    'CustomPrompt': CustomPrompt,
    'ExtraInfoBasePrompt': ExtraInfoBasePrompt,
    'ExtraInfoJustificationDetailsPrompt': ExtraInfoJustificationDetailsPrompt,
    'ExtraInfoOverallConfidencePrompt': ExtraInfoOverallConfidencePrompt,
    'ExtraInfoKeywordsPrompt': ExtraInfoKeywordsPrompt,
    'ExtraInfoPhrasesPrompt': ExtraInfoPhrasesPrompt,
    'ExtraInfoContactCustomerPrompt': ExtraInfoContactCustomerPrompt,
    'ExtraInfoContactCustomerReasonPrompt': ExtraInfoContactCustomerReasonPrompt,
    'ExtraInfoContactCustomerWriteEmailPrompt': ExtraInfoContactCustomerWriteEmailPrompt,
    'ExtraInfoActionRecommenderPrompt': ExtraInfoActionRecommenderPrompt
};
export type PromptType = keyof typeof promptTypeMap;

export class PromptFactory {
    createPrompt(type: PromptType, content?: string, params?: any) {
        const PromptClass = promptTypeMap[type];
        if (!PromptClass) {
            throw new Error(`Invalid prompt type: ${type}`);
        }
        return new PromptClass().get(content, params);
    }
}
