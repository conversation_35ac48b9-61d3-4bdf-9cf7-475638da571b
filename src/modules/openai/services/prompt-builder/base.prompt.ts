export type IRole = 'user' | 'assistant' | 'system'
export interface IPrompt{
    role: IRole
    content: string
}

export interface IPromptBuilder{
    prompt: string
    get(content?: string, params?: any): IPrompt
}
// Base class for all prompts
export class BasePrompt {
    private role: IRole;
    private content: string;

    constructor(role: IRole, content: string) {
        this.role = role;
        this.content = content;
    }

    getPrompt() {
        return { role: this.role, content: this.content };
    }
}
