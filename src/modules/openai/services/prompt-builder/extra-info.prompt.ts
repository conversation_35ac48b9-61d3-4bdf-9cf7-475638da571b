import {IPrompt, IPromptBuilder} from "./base.prompt";

export class ExtraInfoPrompt implements IPromptBuilder {
    // Define unique symbols for each method
    static addJustificationDetails  = Symbol('addJustificationDetails');
    static addOverallConfidence     = Symbol('addOverallConfidence');
    static addKeywords              = Symbol('addKeywords');
    static addContactCustomer       = Symbol('addContactCustomer');
    static addContactCustomerReason = Symbol('addContactCustomerReason');
    static addContactCustomerWriteEmail = Symbol('addContactCustomerWriteEmail');

    prompt = `Collect the following in the extra_info object which you can find in the function.
    This information is not used for the classification, but is used to help the customer service agent to understand the customer feedback better.
    Extra information rules: \n\n`

    addJustificationDetails(){
        return (extraInfoPrompt: ExtraInfoPrompt) => {
            extraInfoPrompt.prompt += '- Write an overall justification about the whole classification task. Add that to the extra_info -> justification_details \n';
            return {role: 'user', content: extraInfoPrompt.prompt} as IPrompt
        }
    }

    addOverallConfidence(){
        return (extraInfoPrompt: ExtraInfoPrompt) => {
            extraInfoPrompt.prompt += '- Write the overall confidence you have about the whole classification task. Add that to the extra_info -> overall_confidence \n'
            return {role: 'user', content: extraInfoPrompt.prompt} as IPrompt
        }
    }

    addKeywords(){
        return (extraInfoPrompt: ExtraInfoPrompt) => {
            extraInfoPrompt.prompt += '- Add a comma separated list of short keywords that are relevant to the customer feedback. Add that to the extra_info -> keywords \n'
            return {role: 'user', content: extraInfoPrompt.prompt} as IPrompt
        }
    }

    addContactCustomer(wordList: string[]){
        return (extraInfoPrompt: ExtraInfoPrompt) => {
            // console.log('wordList', wordList)
            extraInfoPrompt.prompt += `- If the customer feedback is in any way related to a word or word phrase in the Wordlist then set this to true. Wordlist: ${JSON.stringify(wordList)} \n\nAdd that to the extra_info -> contact_customer \n`
            return {role: 'user', content: extraInfoPrompt.prompt} as IPrompt
        }
    }

    addContactCustomerReason(){
        return (extraInfoPrompt: ExtraInfoPrompt) => {
            extraInfoPrompt.prompt += '- Explain, why we need to contact the customer about this feedback. Important: First detect the language the customer is using. Next, write the explanation is the same language as the customer feedback. Add that to the extra_info -> contact_customer_reason \n'
            return {role: 'user', content: extraInfoPrompt.prompt} as IPrompt
        }
    }

    addContactCustomerWriteEmail(){
        return (extraInfoPrompt: ExtraInfoPrompt) => {
            // console.log('wordList', wordList)
            extraInfoPrompt.prompt += `- contact_customer is true, the write an email that we could send to our customer \n\nAdd that to the extra_info -> contact_customer_email \n`
            return {role: 'user', content: extraInfoPrompt.prompt} as IPrompt
        }
    }

    get = (content?: string, params?: any): IPrompt => {
        return {role: 'user', content: this.prompt}
    }


}
