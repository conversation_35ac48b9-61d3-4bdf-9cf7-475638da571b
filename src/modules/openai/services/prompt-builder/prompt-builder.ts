import {PromptFactory, PromptType} from "@/modules/openai/services/prompt-builder/prompt-factory";
import {AIModuleType} from "@/shared/enums";
import {IPrompt} from "./base.prompt";

enum availableAIModules {
    ExtraInfoKeywordsPrompt= 'Keywords',
    ExtraInfoContactCustomerPrompt= 'ContactRequest',
    ExtraInfoContactCustomerReasonPrompt= 'ContactRequest',
    CustomLabelRulesPrompt= 'CustomCategories',
}
export class PromptBuilder {
    private prompts: IPrompt[] = [];
    private promptFactory = new PromptFactory();

    addPrompt(type: PromptType, content?: string, params?: any) {
        this.prompts.push(this.promptFactory.createPrompt(type, content, params));
        return this; // allow for method chaining
    }

    addActiveAIModulesPrompt(type: PromptType, activeAIModules: number[], params?: any) {
        const activeAIModule: string = availableAIModules[type];
        // Check if the module is active
        if (activeAIModules.includes(AIModuleType[activeAIModule])) {
            // If active, add the prompt
            this.prompts.push(this.promptFactory.createPrompt(type, '', params));
        }
        return this;
    }

    getPrompts() {
        return this.prompts;
    }

    reset() {
        this.prompts = [];
        return this;
    }
}
