import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Add this class before adding any other extra info prompts.
 * @Prompt Collect the following in the extra_info object which you can find in the function.
 *     This information is not used for the classification, but is used to help the customer service agent to understand the customer feedback better.
 *     Extra information rules:
 */
export class ExtraInfoBasePrompt implements IPromptBuilder{
    prompt = `Collect the following in the extra_info object which you can find in the function.
    This information is not used for the classification, but is used to help the customer service agent to understand the customer feedback better.
    Extra information rules:`

    append(content: string) {
        this.prompt += content;
    }

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
