import {OpenAIGetClassificationParamsDto} from "@/modules/openai/services/classification/openai-classification.dto";
import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Determine if we need to contact the customer based on a wordlist.
 * @Prompt - If the customer feedback is in any way related to a word or word phrase in the Wordlist then set this to true. Wordlist: ${JSON.stringify(params)} \n\nAdd that to the extra_info -> contact_customer \n
 */
export class ExtraInfoContactCustomerPrompt implements IPromptBuilder{
    prompt = ``

    generatePrompt(params: any) {
        return `- Analyse the Customer-answer-1 and Customer-answer-2 and find out if any form of contact is wanted. \n\nAdd that to the extra_info -> contact_customer`;
    }
    get(content?: string, params?: any): IPrompt {
        if (!content && params) {
            content = this.generatePrompt(params);
        }
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
