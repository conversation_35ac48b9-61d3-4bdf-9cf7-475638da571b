import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Write the overall confidence you have about the whole classification task.
 * @Prompt - Write the overall confidence you have about the whole classification task. Add that to the extra_info -> overall_confidence
 */
export class ExtraInfoActionRecommenderPrompt implements IPromptBuilder{
    prompt = `Recommend actions based on feedback
    What actions do you recommend,we should take to improve the NPS?
    Make sure to read all the feedback in the strengths and improvements and then recommend actions based on the feedback.
    If there is a need for a action then add it to the use the extra_info -> action to add the action.
    `

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
