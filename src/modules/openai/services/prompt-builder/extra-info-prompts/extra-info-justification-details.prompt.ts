import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Write overall justification about the whole classification task.
 * @Prompt - Write an overall justification about the whole classification task. Add that to the extra_info -> justification_details
 */
export class ExtraInfoJustificationDetailsPrompt implements IPromptBuilder{
    prompt = `- Write an overall justification about the whole classification task. Add that to the extra_info -> justification_details`

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
