import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Add a comma separated list of short keywords that are relevant to the customer feedback.
 * @Prompt - Add a comma separated list of short keywords that are relevant to the customer feedback. Add that to the extra_info -> keywords
 */
export class ExtraInfoKeywordsPrompt implements IPromptBuilder{
    prompt = `- Add a list of short keywords that are relevant to the customer feedback. Add that to the extra_info -> keywords`

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
