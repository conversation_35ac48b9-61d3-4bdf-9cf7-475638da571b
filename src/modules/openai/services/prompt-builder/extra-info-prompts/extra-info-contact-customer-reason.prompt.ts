import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Why should we need to contact the customer about this feedback.
 * @Prompt - Explain, why we need to contact the customer about this feedback. Important: First detect the language the customer is using. Next, write the explanation is the same language as the customer feedback. Add that to the extra_info -> contact_customer_reason
 */
export class ExtraInfoContactCustomerReasonPrompt implements IPromptBuilder{
    prompt = `- Explain, why we need to contact the customer about this feedback. Important: First detect the language the customer is using. Next, write the explanation is the same language as the customer feedback. Add that to the extra_info -> contact_customer_reason`

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
