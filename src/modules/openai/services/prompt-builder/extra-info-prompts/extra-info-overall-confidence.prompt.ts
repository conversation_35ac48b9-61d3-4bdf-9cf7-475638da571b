import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Write the overall confidence you have about the whole classification task.
 * @Prompt - Write the overall confidence you have about the whole classification task. Add that to the extra_info -> overall_confidence
 */
export class ExtraInfoOverallConfidencePrompt implements IPromptBuilder{
    prompt = `- Write the overall confidence you have about the whole classification task. Add that to the extra_info -> overall_confidence`

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
