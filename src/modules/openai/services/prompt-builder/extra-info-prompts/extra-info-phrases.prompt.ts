import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Add a comma separated list of short keywords that are relevant to the customer feedback.
 * @Prompt - Add a comma separated list of short keywords that are relevant to the customer feedback. Add that to the extra_info -> keywords
 */
export class ExtraInfoPhrasesPrompt implements IPromptBuilder{
    prompt = `- Extract the topic or topics from the customer feedback then also check the sentiment. Add that to the extra_info -> phrases`

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
