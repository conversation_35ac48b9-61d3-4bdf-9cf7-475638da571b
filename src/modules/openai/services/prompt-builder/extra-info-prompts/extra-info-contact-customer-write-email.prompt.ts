import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * If contact_customer is true, then write an email that we could send to our customer.
 * @Prompt - contact_customer is true, the write an email that we could send to our customer \n\nAdd that to the extra_info -> contact_customer_email \n
 */
export class ExtraInfoContactCustomerWriteEmailPrompt implements IPromptBuilder{
    prompt = `- contact_customer is true, the write an email that we could send to our customer \n\nAdd that to the extra_info -> contact_customer_email`

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
