import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Base rules for classification.
 * @Prompt Rules for classification:
 *     - To get the complete picture, you need to look at the question and the answer together. Note: the question is to give you some extra context.
 *             Note: Question-1 is always about what the customer likes about the product or service and can be seen as a more positive interpretation. Answers that have 'no clear sentiment' are always neutral or positive.
 *             Note: Question-2 is always about what the customer thinks can be improved or is not good and can be seen as a more negative interpretation.  Answers that have 'no clear sentiment' are always neutral or negative.
 *             So make sure to fully understand the context because sometimes it looks like sarcasm and the sentiment is therefore the opposite.
 *
 *     - Do not hallucinate and do not make up things that are not in the customer feedback. Only use the customer feedback to classify the customer feedback.
 *     - You can classify the customer feedback into multiple categories and subcategories.
 *     - Look very carefully when choosing a category and subcategory and make sure the combination of category and subcategory makes sense and are both highly relevant to the feedback the customer has given.
 *     - Ignore customer feedback with only abbreviation, very short feedback or vague feedback. If the other question has good feedback that makes sense and is clear, then you can classify that one of course.
 *       If you have ignored the feedback then mention the reason in the justification_details field.
 *     - I might also be the case that on of the questions and answer is empty. In that case, you can ignore that question and answer and only classify the other one.
 *     - Important! It should be very clear that the feedback belongs to both main and subcategory. If 1 of the 2 don't match or in not clearly mentioned then do not use it!
 *       In other words, you need to be absolutely sure that the topics in the customer feedback are highly relevant to the main category and subcategory that you choose.
 *       Here is an example when not to choose a category and subcategory:
 *         the feedback: 'Persoonlijke aandacht'
 *         and you have a parent_category: 'Intake'
 *         with a sub_category: 'Aandacht'
 *         then it is no match and you should not choose it.
 *       This is because it is not clear from the feedback that it is about the 'Intake' although the sub_category could match, the parent_category 'Intake' is not clear .
 */
export class ClassificationRulesPrompt implements IPromptBuilder{
    prompt = `Rules for classification:
    - To get the complete picture, you need to look at the question and the answer together. Note: the question is to give you some extra context. 
            Note: Question-1 is always about what the customer likes about the product or service and can be seen as a more positive interpretation. Answers that have 'no clear sentiment' are always neutral or positive. 
            Note: Question-2 is always about what the customer thinks can be improved or is not good and can be seen as a more negative interpretation.  Answers that have 'no clear sentiment' are always neutral or negative. 
            So make sure to fully understand the context because sometimes it looks like sarcasm and the sentiment is therefore the opposite.
            
    - Do not hallucinate and do not make up things that are not in the customer feedback. Only use the customer feedback to classify the customer feedback.
    - Always populate the 'cite' field with relevant text snippets that support the chosen category and sentiment. This is crucial for interpretability and debugging the output.
    - You can classify the customer feedback into multiple categories and subcategories.
    - The sentiment field may only hold 1 sentiment. So if you need to classify a category on 2 sentiments then create a new record for each of them in the answer array.
    - Look very carefully when choosing a category and subcategory and make sure the combination of category and subcategory makes sense and are both highly relevant to the feedback the customer has given.
    - When a category-subcategory combination applies to multiple parts of the text with different sentiments, create separate records in the 'answer' array for each sentiment. Do not combine sentiments into a single record.
    - Important: stick to the provided categories and subcategories when you do the classification in the answer property.
    - When you have identified a certain category and sub category then always set the sentiment and confidence for that.
    - Make sure the array of answer is unique in the following way: parent_category -> sub_category -> sentiment. 
        This means that you can only add a new record if that combination is not already present in the array.
    - Do NOT output duplicate category-sentiment pairs. If a category with subcategory has already been output in the answer property with a given sentiment, 
        do not output it again, even if it applies to multiple parts of the text.
    - Ignore customer feedback with only abbreviation, very short feedback or vague feedback. If the other question has good feedback that makes sense and is clear, then you can classify that one of course.
    - Always assign the same categories to identical feedback. Avoid changing your interpretation of the same text across multiple runs.
    - If you cannot find any suitable subcategory then make sure to mention the reason why you could not add any in the justification_details. 
    - I might also be the case that on of the questions and answer is empty. In that case, you can ignore that question and answer and only classify the other one.
    - Important! It should be very clear that the feedback belongs to both main and subcategory. If 1 of the 2 don't match or in not clearly mentioned then do not use it!
      In other words, you need to be absolutely sure that the topics in the customer feedback are highly relevant to the main category and subcategory that you choose.
      Here is an example when not to choose a category and subcategory:
        the feedback: 'Persoonlijke aandacht'
        and you have a parent_category: 'Intake'
        with a sub_category: 'Aandacht'
        then it is no match and you should not choose it.
      This is because it is not clear from the feedback that it is about the 'Intake' although the sub_category could match, the parent_category 'Intake' is not clear . Use the answer object to classify the customer feedback.`

    /**
     * OLD PROMPT
     * - If the customer feedback does not provide specific details about any of the predefined categories or subcategories, then create a new category it to the custom_categories -> category_name if possible.
     *     - If you can not find a category and/or subcategory that fits the customer feedback for a topic, you can create a new category by adding it to the custom_categories -> category_name.
     *     - Make sure that each topic has a parent category and a subcategory. So if a customer is talking about climate and we don't have any category for it, then create one in the custom_categories for it
     *     - It might also be possible that the customer feedback is not about any of the categories and subcategories. In that case, do not use the 'answer' object but the 'custom_categories' in the functions.
     *       You can classify the customer feedback as "Other" if the feedback is vague or to short to have any meaning in the custom_categories -> category_name field. Or make up you own categories there, if applicable
     *     - If you use the custom_categories, then make sure to also add the sentiment and confidence for the custom_categories. The custom_categories is always an array.
     *
     */

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
