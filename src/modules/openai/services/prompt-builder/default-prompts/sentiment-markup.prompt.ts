import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Write html around the text that has triggered you to choose a certain parent_category and sub_category.
 * @Prompt Create markup:
 *      Once you have classified the feedback you will need to add some markup around the text that has triggered you to choose a certain parent_category and sub_category to the sentiment_markup
 *      Here is how it should look like:
 *      <span class="positive" title="parent_category -> sub_category">[positive text segment]</span>
 *
 *      Of course change the positive with negative or neutral and do this for all categories you've found. Write the complete customer feedback in the sentiment_markup -> customer_feedback_1 and/or sentiment_markup -> customer_feedback_2 with this markup added to the parts that are relevant
 *
 */
export class SentimentMarkupPrompt implements IPromptBuilder{
    prompt = `Create markup:
     Once you have classified the feedback you will need to add some markup around the text that has triggered you to choose a certain parent_category and sub_category in the answer to the sentiment_markup
     Here is how it should look like:
     <span class="positive" title="parent_category -> sub_category">[positive text segment]</span>
     Of course change the positive with negative or neutral and do this for all categories you've found. Write the complete customer feedback in the sentiment_markup -> customer_feedback_1 and/or sentiment_markup -> customer_feedback_2 with this markup added to the parts that are relevant`


    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
