import {BasePrompt, IPrompt, IPromptBuilder} from "../base.prompt";
import {OpenAIGetClassificationParamsDto} from "../../classification/openai-classification.dto";
import {undefined} from "zod";

/**
 * Provide the categories to the AI.
 * @Prompt Categories: """${JSON.stringify(params.categories)}"""
 */
export class CategoriesPrompt implements IPromptBuilder{
    prompt = ``

    generatePrompt(params: OpenAIGetClassificationParamsDto) {
        // console.log('CategoriesPrompt', params)
        return `Categories: """${JSON.stringify(params.categories)}"""`;
    }

    get(content?: string, params?: any): IPrompt {
        if (!content && params) {
            content = this.generatePrompt(params);
        }
        return {
            role: 'user',
            content: content || this.prompt
        }
    }


}
