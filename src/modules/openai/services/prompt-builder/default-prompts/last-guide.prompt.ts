import {IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * A guide to help the AI and giving it time to think.
 * @Prompt As a last guide, ask yourself questions like:
 *     - Does this customer feedback make sense in relation to the questions? If not then it will probably be hard to classify and you might choose not to classify it at all or make a custom category.
 *     - Is the customer feedback clear and specific? If not then it will probably be hard to classify.
 *     - Is the customer feedback very short or does it only contain an abbreviation? If so, then it will probably be hard to classify.
 */
export class LastGuidePrompt implements IPromptBuilder{
    prompt = `As a last guide, ask yourself questions like:
    - Does this customer feedback make sense in relation to the questions? If not then it will probably be hard to classify and you might choose not to classify it at all or make a custom category.
    - Is the customer feedback clear and specific? If not then it will probably be hard to classify.
    - Is the customer feedback very short or does it only contain an abbreviation? If so, then it will probably be hard to classify.`

    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }

}
