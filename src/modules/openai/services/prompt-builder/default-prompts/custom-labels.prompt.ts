import {IPrompt, IPrompt<PERSON>uilder} from "../base.prompt";

/**
 * Let the AI create its own custom labels.
 * @Prompt Rules for custom labels:
 *     - Ignore any category that has been provided in the predefined categories. You should write it yourself in the custom_categories -> category_name field.
 *     - Create highly related categories for the customer feedback.
 *     - It is important to know the context of the feedback. That is why you should look at the question and the answer together.
 *     - There can be more the 1 category for the feedback.
 *     - Make sure to add the sentiment and confidence for the custom_categories.
 *     - The custom_categories is always an array.
 *     - Always write the custom category in the custom_categories -> category_name field. And always write at least one custom category.
 */
export class CustomLabelsPrompt implements IPromptBuilder{
    prompt = `Rules for custom labels:
    - Ignore any category that has been provided in the predefined categories. You should write it yourself in the custom_categories -> category_name field.
    - Create highly related categories for the customer feedback. 
    - It is important to know the context of the feedback. That is why you should look at the question and the answer together.
    - There can be more the 1 category for the feedback. 
    - Make sure to add the sentiment and confidence for the custom_categories. 
    - The custom_categories is always an array.
    - Always write the custom category in the custom_categories -> category_name field. And always write at least one custom category.
    - Custom categories should be meaningful, non-overlapping, and capture key aspects not covered by the predefined set. Aim to create categories that add unique insights.`


    get(content?: string): IPrompt {
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
