import {IPrompt, IPromptBuilder} from "../base.prompt";
import {OpenAIGetClassificationParamsDto} from "../../classification/openai-classification.dto";

/**
 * Provide the customer feedback to the AI.
 * @Prompt Customer feedback:
 *             - Question-1: """${params.strengths_question}"""
 *             - Customer-answer-1: """${params.strengths_answer}"""
 *             - Question-2:"""${params.improvements_question}"""
 *             - Customer-answer-2: """${params.improvements_answer}"""
 */
export class CustomerFeedback implements IPromptBuilder{
    prompt = ``
    generatePrompt(params: OpenAIGetClassificationParamsDto) {
        return `Customer feedback: - Question-1: """${params.strengths_question}""" - Customer-answer-1: """${params.strengths_answer}""" - Question-2:"""${params.improvements_question}""" - Customer-answer-2: """${params.improvements_answer}"""`;
    }



    get(content?: string, params?: OpenAIGetClassificationParamsDto): IPrompt {
        if (!content && params) {
            content = this.generatePrompt(params);
        }
        return {
            role: 'user',
            content: content || this.prompt
        }
    }
}
