import {OpenAIGetClassificationParamsDto} from "@/modules/openai/services/classification/openai-classification.dto";
import {BasePrompt, IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * Write the justification about the classification task.
 * @Prompt Justification rules:
 *     - When you write the justification, make sure to explain how you arrived at the classification and explain what specific parts of the customer feedback were most important in the decision.
 *     - Always mention the exact category and subcategory, as provided in this prompt, that you are talking about in the justification.
 *     - If you could not find any appropriate category and subcategory, then explain the reason or why you could not find any and what you did instead.
 *     - So, always write the justification in the justification_details field so that we can understand your thought process.
 *     - Use the same language you've found in the Customer-answer-1 or Customer-answer-2 when writing the justification.
 */
export class JustificationPrompt implements IPromptBuilder{
    // prompt = `Justification rules:
    // - When you write the justification, make sure to explain how you arrived at the classification and explain what specific parts of the customer feedback were most important in the decision.
    // - Always mention the exact category and subcategory, as provided in this prompt, that you are talking about in the justification.
    // - If you could not find any appropriate category and subcategory, then explain the reason or why you could not find any and what you did instead.
    // - So, always write the justification in the justification_details field so that we can understand your thought process.
    // - Comment on the overall quality and specificity of the customer feedback. Is it clear and detailed enough for confident classification, or is it too vague or short? Mention this in the 'justification_details'.
    // - Use the following format, and stick to this format and do not add anything else, to write the justification. Write the whole justification in the same language as found in the customer feedback.
    // (note: all the tags are just for structure purposes, never output tag like these <some-tag></some-tag> and never mention the term "Question-1" or "Question-2" in the justification):
    //
    //     <short-explaination>Write a short intro about the topics the consumer is talking about or if you could not find any categories then write the reason for that here.</short-explaination>
    //     /n/n
    //     <list-subcategories>
    //         - <name>Write the category name that you have added in the sub_category inside the answer property</name> : <short-explanation>Write a short explanation about why you have chosen this subcategory</short-explanation>
    //     </list-subcategories>
    //     /n/n
    //     Actions:
    //     <actions><action>Write an action that could be taken to improve.</action></actions>
    // `
    // prompt = `Justification rules:
    // - When you write the justification, make sure to explain how you arrived at the classification and explain what specific parts of the customer feedback were most important in the decision.
    // - Always mention the exact category and subcategory, as provided in this prompt, that you are talking about in the justification.
    // - If you could not find any appropriate category and subcategory, then explain the reason or why you could not find any and what you did instead.
    // - So, always write the justification in the justification_details field so that we can understand your thought process.
    // - Comment on the overall quality and specificity of the customer feedback. Is it clear and detailed enough for confident classification, or is it too vague or short? Mention this in the 'justification_details'.
    // - Use the following format, and stick to this format and do not add anything else, to write the justification. Write the whole justification in the same language as found in the customer feedback or the question.
    // Important: Never mention the term "Question-1" or "Question-2" in the justification.
    // Important: NEVER output any tags like these <short-explaination>, <list-subcategories>, <name>, <actions> or <action>. All the tags are just to give you a structure on how to write it.
    // Important: Make sure you always write a justification and use the following format:
    //     <short-explaination>Write a short intro about the topics the consumer is talking about or if you could not find any categories then write the reason for that here.</short-explaination>
    //     /n/n
    //     <list-subcategories>
    //         - <name>Write the category name that you have added in the sub_category inside the answer property</name> : <short-explanation>Write a short explanation about why you have chosen this subcategory</short-explanation>
    //     </list-subcategories>
    //     /n/n
    //     Actions:
    //     <actions><action>Write an action that could be taken to improve.</action></actions>
    // `
    // - Language requirement:
// • The entire justification must be in exactly one language.
// • If the first question is in Dutch and the feedback is in another language, use Dutch for everything.
// • If the first question is in another language but the feedback is in Dutch, use that other language for everything.
// • If question and feedback are in the same language, use that language.
// • If you cannot detect the question’s language, default to the feedback’s language.
// • Never mix languages or provide a bilingual response.
    prompt = ''


    generatePrompt(params: OpenAIGetClassificationParamsDto) {
        return ` 
      Here are the rules I need you to follow when writing a justification for the classification task. 
      Let's take this step by step and think this over.
      
        
    Justification rules:
    Language requirement:
      - Before providing your justification, we need to know in which language we need to write it. 
        Now detect the language that is used in this question: 
            """${params.strengths_question}""" 
        If the above question is empty, NULL or undefined use the following question:    
            """${params.improvements_question}"""
            
        So now that we know the language we should make sure that everything you write and every word in the justification is according to that language. 
      
    Your justification must explain:
      - How you arrived at the classification (if any).
      - Which parts of the feedback (from either or both answers) influenced your decision.
      - Whether the feedback is clear/detailed or vague/brief.

    Other rules:
    - You must always provide a justification, even if no categories/subcategories apply.
        (If none apply, explicitly say “No subcategories found” but still follow the format below.)
        
    - You must mention the exact category and subcategory (if applicable) in the justification.

    - Output your justification in the "justification_details" field, so we can see your reasoning.

    - Consider both user questions and both answers as a whole when classifying. 
      Never mention “Question-1” or “Question-2” in the final output; just treat it as combined feedback.

    - **Do not** include any of the following in your final justification:
      • “Keywords”
      • “Phrases”
      • “Contact_customer”
      • “Contact_customer_reason”
      or any mention of them.

    - Format requirement:
      • Do NOT output bracketed tags (<template> </template>) in your final answer as these are only meant to show the structure.
      • You must always provide three sections in your final output.
      Here is a template which you should follow. Make sure to fill in this template and use the same language as the detected language
        <template>
        Short explaination
           - A concise summary of the feedback or a statement that no categories apply. 
     
       Categories
           - List the category and subcategory. For example:
           - Category Name: Explanation for why you chose it
           - If none apply, write something like “No subcategories found”.
        
        Actions
           - Propose one or more actions/improvements, or state “No action needed” if truly irrelevant. 
             Still present this section even if no action applies.
       </template>
      
      • These three sections must always appear in your final answer. 
      • Failing to provide all three means the output is incomplete.
      • The three sections must als be written in the language that is detected.
      • The terms used in the template should also be translated in the detected language 
        
    - Important:
      • Never mention “Question-1” or “Question-2” in your justification.
      • Never output any bracketed tags (<...>) in your final answer.
      • Never output or mention “Keywords,” “Phrases,” “Contact_customer,” or “Contact_customer_reason” in your final answer.
      • Always provide a justification in the specified format no matter what.


End of justification rules.

Now make sure the whole final output is written in the same language as the language in the detected language. After you have written the final output, check if you did not miss any rule and if the language you have used is correct.
 `;
    }
    get(content?: string, params?: OpenAIGetClassificationParamsDto): IPrompt {
        return {
            role: 'user',
            content: this.generatePrompt(params)
        }
    }
}
