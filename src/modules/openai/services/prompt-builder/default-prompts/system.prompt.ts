// Specific prompt classes
import {<PERSON><PERSON>rom<PERSON>, IPrompt, IPromptBuilder} from "../base.prompt";

/**
 * System prompt to explain the main task.
 * @Prompt Act as an expert on Aspect-based sentiment analysis on customer feedback.
 *      Along with the customer feedback you also get the question that we have asked the customer.
 *      Use the answer array in the classification function to classify the customer feedback into categories and subcategories.
 *      In the following prompts you will receive instructions on how to classify the customer feedback. Please follow them step-by-step.
 *      It is important to know that we have asked the customer 2 questions. At the end you will find the following structure:
 *         Question-1: """Here you will find the first question that we have asked the customer"""
 *         Customer-answer-1: """Here you will find the first answer from the customer"""
 *         Question-2:"""Here you will find the second question that we have asked the customer"""
 *         Customer-answer-2: """Here you will find the second answer from the customer"""
 */
export class SystemPrompt implements IPromptBuilder {
    prompt = `Act as an expert on Aspect-based sentiment analysis on customer feedback.
     Along with the customer feedback you also get the question that we have asked the customer.
     Use the answer array in the classification function to classify the customer feedback into categories and subcategories.
     In the following prompts you will receive instructions on how to classify the customer feedback. Please follow them step-by-step.
     It is important to know that we have asked the customer 2 questions. At the end you will find the following structure:
        Question-1: """Here you will find the first question that we have asked the customer"""
        Customer-answer-1: """Here you will find the first answer from the customer"""
        Question-2:"""Here you will find the second question that we have asked the customer"""
        Customer-answer-2: """Here you will find the second answer from the customer"""`
    constructor() {

    }

    // static create(content: string = this.prompt ) {
    //     return new SystemPrompt(content);
    // }

    get(content?: string): IPrompt{
        return {
            role: 'system',
            content: content || this.prompt
        }
    }
}
