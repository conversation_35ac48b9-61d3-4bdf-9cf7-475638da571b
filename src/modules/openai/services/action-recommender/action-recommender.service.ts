import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import OpenAI from 'openai';
import { parseJSON } from '@/shared/utils';
import { OpenAIService } from '../../openai.service';
import ChatCompletion = OpenAI.ChatCompletion;
import { env } from '@/app.env';

@Injectable()
export class ActionRecommenderService {
  constructor(private openAIService: OpenAIService) {}
  async recommend(feedback: any[], language: string) {
    const customerFeedback = feedback.map((feedback, index) => {
      return {
        strengths: feedback.strengths,
        improvements: feedback.improvements,
        answer_id: feedback.answer_id,
      };
    });

    //        const openai = new OpenAI({apiKey: process.env.OPENAI_API_KEY});
    //
    //        // Generate a unique temporary filename
    //        const tempFilePath = `${process.env.NODE_FOLDER}data/customerFeedback.json`;
    //         console.log('tempFilePath', tempFilePath)
    //
    // // Convert the customerFeedback object to a JSON string
    //        const customerFeedbackJson = JSON.stringify(customerFeedback);
    //
    // // Write the JSON string to the temporary file
    //        fs.writeFile(tempFilePath, customerFeedbackJson, (err) => {
    //            if (err) throw err;
    //            console.log('The file has been saved!');
    //        });
    //        // Upload a file with an "assistants" purpose
    //        const file = await openai.files.create({
    //            file: fs.createReadStream(`${process.env.NODE_FOLDER}data/customerFeedback.json`),
    //            purpose: "assistants",
    //        });
    //        console.log('file', JSON.stringify(file, null, 2))
    // return
    const completion = (await this.openAIService.createChatCompletion({
      model: env.OPENAI_NEW_MODEL,
      temperature: 0.0,
      messages: [
        { role: 'system', content: `Recommend actions based on feedback` },
        {
          role: 'user',
          content: `What actions you recommend to improve the NPS?`,
        },
        {
          role: 'user',
          content: `Here is the feedback: ${JSON.stringify(customerFeedback)}`,
        },
        {
          role: 'user',
          content: `Make sure to read all the feedback in the strengths and improvements and then recommend actions based on the feedback.`,
        },
        {
          role: 'user',
          content: `In general when you find a lot of feedback around some topic, the priority should be higher. `,
        },
        {
          role: 'user',
          content: `Return a JSON object like this and use this language:${language}:
                                        {
                                        actions:[{
                                        action: the action you recommend,
                                        priority: Critical,High Priority,Medium Priority,Low Priority,Optional,Future Consideration
                                        difficulty: very easy, easy, moderate, hard, very hard,
                                        based_on_feedback_count: count of the feedback where this action is related to
                                        based_on_answer_ids: [the answer_id of the feedback where this action is related to]
                                        }]
                                        }
                                        `,
        },
      ],
      response_format: { type: 'json_object' },
    })) as unknown as ChatCompletion;
    console.log('TOTAL TOKENS: ', completion.usage.total_tokens);

    // // Add the file to the assistant
    // const assistant = await openai.beta.assistants.create({
    //     instructions: "Recommend actions based on feedback",
    //
    //     model: "gpt-4-turbo-preview",
    //     tools: [{"type": "retrieval"}],
    //     file_ids: ['file-HHtbCVk7xHqr0Cf9KiUBOr91\n']
    // });
    return parseJSON(completion.choices[0].message.content);
  }
}
