import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { parseJ<PERSON><PERSON> } from '../../../../shared/utils/parseJSON';
import { OpenAIService } from '../../openai.service';
import ChatCompletion = OpenAI.ChatCompletion;
import { env } from '@/app.env';

/*
 NOTE: There seems to be an issue with functions and encoding utf-8 for gtp Gpt-4-1106-preview. This currently resolves in, in some cases, to special characters not being outputted correctly. You would see something like this: �
 See: https://community.openai.com/t/gpt-4-1106-preview-is-not-generating-utf-8/482839
 */
@Injectable()
export class TranslateService {
  constructor(private openAIService: OpenAIService) {}
  private cleanText(text: string): string {
    // Remove special characters and newline characters
    return text.replace(/[^a-zA-Z0-9 ]/g, '').replace(/\n/g, ' ');
  }
  async translate(strengths: string, improvements: string, language: string) {
    const translate = (await this.openAIService.createChatCompletion({
      model: 'gpt-4o-mini',
      temperature: 0.0,
      messages: [
        { role: 'system', content: `You are a professional translator.` },
        {
          role: 'user',
          content: `Make sure to translate the complete Strenghts text and Improvements text.`,
        },
        {
          role: 'user',
          content: `Leave the structure in tact. This means that if the orginal text has tabs or new lines or any other punctuation or writing style, they should be preserved.`,
        },
        {
          role: 'user',
          content: `[strength-text]${strengths}[/strength-text]`,
        },
        {
          role: 'user',
          content: `[improvement-text]${improvements}[/improvement-text]`,
        },
        { role: 'user', content: `Translate texts to ${language}` },
        {
          role: 'user',
          content: `Always return the following JSON format:{translated_strengths: "the translated strengths", translated_improvements: "the translated improvements" }`,
        },
        {
          role: 'user',
          content: `Never return the [strength-text] or [improvement-text]. These are meant to show you where the text starts and ends.`,
        },
        {
          role: 'user',
          content: `Make sure to translate the full text and do not skip any parts. Do not change the formatting of the text. So make sure the translated text is in the same format as the original is. `,
        },
      ],
      response_format: { type: 'json_object' },
    })) as unknown as ChatCompletion;
    return parseJSON(translate.choices[0].message.content);
  }
}
