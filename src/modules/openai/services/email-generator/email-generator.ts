import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { parseJ<PERSON><PERSON> } from '../../../../shared/utils/parseJSON';
import { EmailTone, ExtraGuide } from '../../openai.controller';
import { OpenAIService } from '../../openai.service';
import ChatCompletion = OpenAI.ChatCompletion;
import { env } from '@/app.env';

/*
 NOTE: There seems to be an issue with functions and encoding utf-8 for gtp Gpt-4-1106-preview. This currently resolves in, in some cases, to special characters not being outputted correctly. You would see something like this: �
 See: https://community.openai.com/t/gpt-4-1106-preview-is-not-generating-utf-8/482839
 */
@Injectable()
export class EmailGeneratorService {
  constructor(private openAIService: OpenAIService) {}
  async generate(
    strengths: string,
    improvements: string,
    extraGuides: ExtraGuide[],
  ) {
    const translate = (await this.openAIService.createChatCompletion({
      model: env.OPENAI_NEW_MODEL,
      temperature: 0.0,
      messages: [
        {
          role: 'system',
          content: `Write an email as a response on the feedback received from a customer.`,
        },
        {
          role: 'user',
          content: `Guidelines: There are extra guidelines that you should apply when writing the email. You will find the following structure: [{value: key field, description: explanation on how to apply, displayName: display name}] \n`,
        },
        {
          role: 'user',
          content: `Write the email and follow the guidelines and make sure to apply each one of them inside this JSON array: ${JSON.stringify(
            extraGuides,
          )}\n`,
        },
        {
          role: 'user',
          content: `Here is the feedback from the customer: \n\n ${strengths} \n\n ${improvements}`,
        },
        {
          role: 'user',
          content: `Return in the following JSON format and in the same language as the feedback from the customer:{title: "Title for the email that should include the main topic the customer is talking about", body: "Content of the email in HTML format", analysis: "Analysis of the email, does this help or satisfy the customer? If not, why not?", nps_improvement: "By what percentage could this email improve the NPS score? Use value between 0 and 100", rating: "How would you rate this email reaction? Use value between 0 and 10"}  `,
        },
      ],
      response_format: { type: 'json_object' },
    })) as unknown as ChatCompletion;
    return parseJSON(translate.choices[0].message.content);
  }

  async suggestEmailTone(
    strengths: string,
    improvements: string,
    tone: EmailTone[],
  ) {
    const translate = (await this.openAIService.createChatCompletion({
      model: env.OPENAI_NEW_MODEL,
      temperature: 0.0,
      messages: [
        {
          role: 'system',
          content: `Choose one or more tones from the given tones that would probably give the best result when we send an email.`,
        },
        {
          role: 'user',
          content: `Here are the tones: ${JSON.stringify(tone)}\n`,
        },
        {
          role: 'user',
          content: `Here is the feedback from the customer: \n\n ${strengths} \n\n ${improvements}`,
        },
        {
          role: 'user',
          content: `Return in the following JSON format:{best_tones: [{toneName: "Name of the tone", toneDescription: "Description of the tone"}}], sentiment: "overall sentiment of the feedback: Positive,Negative or Neutral", justification: "Explain why the chosen tones are the best in the same language as the feedback from the customer."  }`,
        },
      ],
      response_format: { type: 'json_object' },
    })) as unknown as ChatCompletion;
    return parseJSON(translate.choices[0].message.content);
  }
}
