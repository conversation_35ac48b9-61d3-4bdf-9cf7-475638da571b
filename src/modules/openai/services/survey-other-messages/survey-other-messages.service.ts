import { SurveyRulePageSchema } from './schemas/survey-rule-page.schema';
import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { zodResponseFormat } from "openai/helpers/zod";
import { z } from "zod";

const openai = new OpenAI({
    apiKey: '********************************************************',
});
@Injectable()
export class SurveyOtherMessagesService {
  constructor() {}
  async generateMessage(page: string) {
      try {
          console.log('PAGE: ', page)
          const UI = z.lazy(() =>
              z.object({
                  page_text: z.string(),
                  text_style: z.string({description:'In what style is the page text written? Engaging, Formal and so on.'}),
              })
          );
          const completion = await openai.chat.completions.parse({
              model: "gpt-4o-2024-08-06",
              messages: [
                  {
                      role: "system",
                      content: "Create a good and compelling text for survey page. The page will be given by the user",
                  },
                  {
                      role: "user",
                      content: `Write a good text for our page called: '''${page}'''. Make sure it is in Dutch language and use html. 
                      Only use p tags and add a style tag with the following:
                        margin-bottom:15px
                        font-family:var(--ff-general-font-family)
                        font-size: var(--ff-general-size)
                        color: var(--ff-font-color)
                      to make the text easier to read.`,
                  },
              ],
              response_format: zodResponseFormat(UI, "ui"),
          });
          console.log('completion.choices[0].message.parsed', completion)
          const ui = completion.choices[0].message.parsed;

          return ui;
      } catch (e) {
          console.log('SOmething went wrong: ', e)
      }
  }

  async generateSurveyPages() {
      // Create a "tool" (function) that the model can call to produce a survey page
      try {

          const messages: any[] = [
              {role: "system", content: "You are a survey builder. The user wants a survey page."},
              {
                  role: "user",
                  content: "Create a survey page named 'Page 1' with one required NPS question that asks about recommending our product 'page1'."
              },
          ];

          const openai = new OpenAI();

          const response = await openai.chat.completions.parse({
              model: "gpt-4o", // or the latest available model that supports function calling
              messages,
              response_format: zodResponseFormat(SurveyRulePageSchema, "SurveyRulePageSchema"),
          });

          console.log(response.choices[0].message.parsed);
          return response.choices[0].message.parsed;
      } catch (e) {
          console.log('Something went wrong: ', e)
      }
  }
}
