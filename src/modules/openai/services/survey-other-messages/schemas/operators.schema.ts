import { QuestionSchema } from './questions/question.schema';
import { z } from 'zod';

export const operatorSchema = z
  .object({
    label: z.string().optional(),
    id: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (!data.id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Select a operator',
        path: [], // Set the desired error path
      });
    }
  });

export const operatorsSchema = z.array(operatorSchema).default([
  { label: 'is', id: '=' },
  { label: 'less than', id: '<' },
  { label: 'greater than', id: '>' },
  { label: 'is not', id: '!=' },
  { label: 'contains', id: 'contains' },
  { label: 'does not contain', id: 'does not contain' },
  { label: 'is empty', id: 'is empty' },
  { label: 'is not empty', id: 'is not empty' },
]);

const pageToGoto = z.object({ id: z.string(), label: z.string() });

export const RulesSchema = z.object({
  uuid: z.string(),
  operator: operatorSchema,
  ruleValue: z.number().or(z.string()),
  question: QuestionSchema,
  questionId: z.number(),
  pageToGoto: pageToGoto,
  ruleAsTextShort: z.string().optional(),
  ruleAsTextLong: z.string().optional(),
});

export type Rule = z.infer<typeof RulesSchema>;
export type Operator = z.infer<typeof operatorSchema>;
export type Operators = z.infer<typeof operatorsSchema>;
