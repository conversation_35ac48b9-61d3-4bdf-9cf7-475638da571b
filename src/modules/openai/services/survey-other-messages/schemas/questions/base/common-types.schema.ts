import {z} from "zod";

export const LayoutSchema = z.enum(['list_view', 'card_view']);
export type Layout = z.infer<typeof LayoutSchema>;

export const SelectionTypeSchema = z.enum(['single', 'multiple']);
export type SelectionType = z.infer<typeof SelectionTypeSchema>;


export const QuestionTypesSchema = z.enum([
  'NPS_QUESTION',
  'CES_QUESTION',
  'CSAT_QUESTION',
  'OPEN_QUESTION',
  'SHORT_QUESTION',
  'SELECT_QUESTION',
  'NUMBER_SELECT_QUESTION',
  'ANSWER_TABLE_QUESTION',
  'MATRIX_QUESTION',
  'PARAGRAPH',
]);

export type QuestionTypes = z.infer<typeof QuestionTypesSchema>;
