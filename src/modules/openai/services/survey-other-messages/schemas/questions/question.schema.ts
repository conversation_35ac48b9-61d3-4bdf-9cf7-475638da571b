import { BaseQuestionSchema } from './base/base-question.schema';
import { z } from 'zod';
import { NPSQuestionPropertiesSchema } from './question-types/nps-question.schema';
import { CESQuestionPropertiesSchema } from './question-types/ces-question.schema';
import { CSATQuestionPropertiesSchema } from './question-types/csat-question.schema';
import { OpenQuestionPropertiesSchema } from './question-types/open-question.schema';
import { ShortQuestionPropertiesSchema } from './question-types/short-question.schema';
import { ParagraphQuestionPropertiesSchema } from './question-types/paragraph-question.schema';
import { MatrixQuestionPropertiesSchema } from './question-types/matrix-question.schema';
import { SelectQuestionPropertiesSchema } from './question-types/select-question.schema';
import { NumberSelectQuestionPropertiesSchema } from './question-types/number-select-question.schema';

export const QuestionSchema = z.discriminatedUnion('type', [
  // NPS_QUESTION
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('NPS_QUESTION'),
    canHaveRules: z.boolean(),
    properties: NPSQuestionPropertiesSchema,
  }),
  // CES_QUESTION
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('CES_QUESTION'),
    canHaveRules: z.boolean(),
    properties: CESQuestionPropertiesSchema,
  }),
  // CSAT_QUESTION
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('CSAT_QUESTION'),
    canHaveRules: z.boolean(),
    properties: CSATQuestionPropertiesSchema,
  }),
  // OPEN_QUESTION
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('OPEN_QUESTION'),
    canHaveRules: z.boolean(),
    properties: OpenQuestionPropertiesSchema,
  }),
  // SHORT_QUESTION
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('SHORT_QUESTION'),
    canHaveRules: z.boolean(),
    properties: ShortQuestionPropertiesSchema,
  }),
  // PARAGRAPH
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('PARAGRAPH'),
    canHaveRules: z.boolean(),
    properties: ParagraphQuestionPropertiesSchema,
  }),
  // MATRIX_QUESTION
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('MATRIX_QUESTION'),
    canHaveRules: z.boolean(),
    properties: MatrixQuestionPropertiesSchema,
  }),
  // SELECT_QUESTION
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('SELECT_QUESTION'),
    canHaveRules: z.boolean(),
    properties: SelectQuestionPropertiesSchema,
  }),
  // NUMBER_SELECT_QUESTION
  z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('NUMBER_SELECT_QUESTION'),
    canHaveRules: z.boolean(),
    properties: NumberSelectQuestionPropertiesSchema,
  }),
  // Add other question types as needed
]);

export type Question = z.infer<typeof QuestionSchema>;

export const QuestionListSchema = z.array(QuestionSchema);
export type QuestionList = z.infer<typeof QuestionListSchema>;
