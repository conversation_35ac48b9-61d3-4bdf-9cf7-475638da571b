import { OptionsSchema } from '../base/options.schema';
import { z } from 'zod';
import { ScaleLabelsSchema, ScaleSchema } from '../base/scale.schema';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const NumberSelectQuestionLanguageSchema = BaseLanguageSchema.extend({
  scale_labels: ScaleLabelsSchema,
});
export type NumberSelectQuestionLanguage = z.infer<
  typeof NumberSelectQuestionLanguageSchema
>;

export const NumberSelectQuestionPropertiesSchema = z.object({
  question: z.string().default(''),
  required: z.boolean(),
  scale: ScaleSchema,
  scale_labels: ScaleLabelsSchema,
  options: z.array(OptionsSchema),
  // optionsEnd: z.array(OptionsSchema),
  languages: z.array(NumberSelectQuestionLanguageSchema),
});

export type NumberSelectQuestionProperties = z.infer<
  typeof NumberSelectQuestionPropertiesSchema
>;

export const NumberSelectQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('NUMBER_SELECT_QUESTION').default('NUMBER_SELECT_QUESTION'),
  properties: NumberSelectQuestionPropertiesSchema.optional(),
});

export type NumberSelectQuestion = z.infer<typeof NumberSelectQuestionSchema>;
