import { z } from 'zod';
import { ScaleLabelsSchema } from '../base/scale.schema';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const NPSQuestionLanguageSchema = BaseLanguageSchema.extend({
  scale_labels: ScaleLabelsSchema,
});
export type NPSQuestionLanguage = z.infer<typeof NPSQuestionLanguageSchema>;

export const NPSQuestionPropertiesSchema = z.object({
  question: z.string(),
  scale: z.object({
    start: z.number(),
    end: z.number(),
  }),

  make_csi_question: z.boolean(),
  leading_nps_question: z.boolean(),
  required: z.boolean(),
  languages: z.array(NPSQuestionLanguageSchema),
});

export type NPSQuestionProperties = z.infer<typeof NPSQuestionPropertiesSchema>;

export const NPSQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('NPS_QUESTION').default('NPS_QUESTION'),
  properties: NPSQuestionPropertiesSchema,
  selectedLanguage: z.string().optional(),
});

export type NPSQuestion = z.infer<typeof NPSQuestionSchema>;
