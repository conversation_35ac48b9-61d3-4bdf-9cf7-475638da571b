import { z } from 'zod';
import { SelectionTypeSchema } from '../base/common-types.schema';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const HeaderLabelSchema = z.object({
  id: z.number(),
  label: z.string(),
});

export type HeaderLabel = z.infer<typeof HeaderLabelSchema>;

export const MatrixOptionSchema = z.object({
  id: z.number(),
  option: z.string(),
  selected: z.array(HeaderLabelSchema), // Array of selected header labels
});

export type MatrixOption = z.infer<typeof MatrixOptionSchema>;

export const MatrixQuestionLanguageSchema = BaseLanguageSchema.extend({
  options: z.array(MatrixOptionSchema),
  headerLabels: z.array(HeaderLabelSchema),
});
export type MatrixQuestionLanguage = z.infer<
  typeof MatrixQuestionLanguageSchema
>;

export const MatrixQuestionPropertiesSchema = z.object({
  question: z.string().default(''),
  required: z.boolean().optional(),
  selection_type: SelectionTypeSchema,
  languages: z.array(MatrixQuestionLanguageSchema),
});

export type MatrixQuestionProperties = z.infer<
  typeof MatrixQuestionPropertiesSchema
>;

export const MatrixQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('MATRIX_QUESTION').default('MATRIX_QUESTION'),
  properties: MatrixQuestionPropertiesSchema.optional(),
  selectedLanguage: z.string().optional(),
});

export type MatrixQuestion = z.infer<typeof MatrixQuestionSchema>;
