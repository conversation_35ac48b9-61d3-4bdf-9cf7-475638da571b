import { z } from 'zod';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const OpenQuestionLanguageSchema = BaseLanguageSchema.extend({});
export type OpenQuestionLanguage = z.infer<typeof OpenQuestionLanguageSchema>;

export const OpenQuestionPropertiesSchema = z.object({
  question: z.string().default(''),
  required: z.boolean(),
  strengths: z
    .boolean({
      description:
        'Is this a strengths question (true) or a Improvements question (false)',
    })
    .default(true),
  placeholder: z.string(),
  languages: z.array(OpenQuestionLanguageSchema),
});

export type OpenQuestionProperties = z.infer<
  typeof OpenQuestionPropertiesSchema
>;

export const OpenQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('OPEN_QUESTION').default('OPEN_QUESTION'),
  properties: OpenQuestionPropertiesSchema.optional(),
  selectedLanguage: z.string().optional(),
});

export type OpenQuestion = z.infer<typeof OpenQuestionSchema>;
