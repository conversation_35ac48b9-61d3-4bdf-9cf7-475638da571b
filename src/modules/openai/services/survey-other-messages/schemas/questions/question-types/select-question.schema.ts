import { z } from 'zod';
import { LayoutSchema, SelectionTypeSchema } from '../base/common-types.schema';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const SelectQuestionLanguageSchema = BaseLanguageSchema.extend({
  answers: z.array(
    z.object({
      label: z.string(),
      value: z.union([z.string(), z.number()]),
    }),
  ),
});
export type SelectQuestionLanguage = z.infer<
  typeof SelectQuestionLanguageSchema
>;

export const SelectQuestionPropertiesSchema = z.object({
  question: z.string().default(''),
  layout: LayoutSchema,
  required: z.boolean(),
  selection_type: SelectionTypeSchema,
  languages: z.array(SelectQuestionLanguageSchema),
});

export type SelectQuestionProperties = z.infer<
  typeof SelectQuestionPropertiesSchema
>;

export const SelectQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('SELECT_QUESTION').default('SELECT_QUESTION'),
  properties: SelectQuestionPropertiesSchema.optional(),
  selectedLanguage: z.string().optional(),
});

export type SelectQuestion = z.infer<typeof SelectQuestionSchema>;
