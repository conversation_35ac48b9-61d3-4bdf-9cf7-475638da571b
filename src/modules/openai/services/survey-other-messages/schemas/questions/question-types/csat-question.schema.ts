import { z } from 'zod';
import { ScaleLabelsSchema } from '../base/scale.schema';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const CSATQuestionLanguageSchema = BaseLanguageSchema.extend({
  scale_labels: ScaleLabelsSchema,
});
export type CSATQuestionLanguage = z.infer<typeof CSATQuestionLanguageSchema>;

export const CSATQuestionPropertiesSchema = z.object({
  question: z.string().default(''),
  required: z.boolean(),
  scale: z.object({
    start: z.number().min(1).default(1),
    end: z.number().max(5).default(5),
  }),

  make_csi_question: z.boolean().optional(),
  leading_nps_question: z.boolean().optional(),
  languages: z.array(CSATQuestionLanguageSchema),
});

export type CSATQuestionProperties = z.infer<
  typeof CSATQuestionPropertiesSchema
>;

export const CSATQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('CSAT_QUESTION').default('CSAT_QUESTION'),
  properties: CSATQuestionPropertiesSchema.optional(),
});

export type CSATQuestion = z.infer<typeof CSATQuestionSchema>;
