import { z } from 'zod';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const ParagraphQuestionLanguageSchema = BaseLanguageSchema.extend({});
export type ParagraphQuestionLanguage = z.infer<
  typeof ParagraphQuestionLanguageSchema
>;

export const ParagraphQuestionPropertiesSchema = z.object({
  content: z.string().default(''),
  languages: z.array(ParagraphQuestionLanguageSchema),
  required: z.number().default(0),
});

export type ParagraphQuestionProperties = z.infer<
  typeof ParagraphQuestionPropertiesSchema
>;

export const ParagraphQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('PARAGRAPH').default('PARAGRAPH'),
  properties: ParagraphQuestionPropertiesSchema.optional(),
});

export type ParagraphQuestion = z.infer<typeof ParagraphQuestionSchema>;
