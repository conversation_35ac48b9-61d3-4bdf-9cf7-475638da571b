import { z } from 'zod';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const ShortQuestionLanguageSchema = BaseLanguageSchema.extend({});
export type ShortQuestionLanguage = z.infer<typeof ShortQuestionLanguageSchema>;

export const ShortQuestionPropertiesSchema = z.object({
  question: z.string().default(''),
  required: z.boolean(),
  placeholder: z.string(),
  languages: z.array(ShortQuestionLanguageSchema),
});

export type ShortQuestionProperties = z.infer<
  typeof ShortQuestionPropertiesSchema
>;

export const ShortQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('SHORT_QUESTION').default('SHORT_QUESTION'),
  properties: ShortQuestionPropertiesSchema.optional(),
});

export type ShortQuestion = z.infer<typeof ShortQuestionSchema>;
