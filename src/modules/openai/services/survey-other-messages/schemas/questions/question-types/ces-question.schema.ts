import { z } from 'zod';
import { ScaleLabelsSchema } from '../base/scale.schema';
import { BaseQuestionSchema } from '../base/base-question.schema';
import { BaseLanguageSchema } from '../base/languages.schema';

export const CESQuestionLanguageSchema = BaseLanguageSchema.extend({
  scale_labels: ScaleLabelsSchema,
});
export type CESQuestionLanguage = z.infer<typeof CESQuestionLanguageSchema>;

export const CESQuestionPropertiesSchema = z.object({
  question: z.string().default(''),
  required: z.boolean(),
  scale: z.object({
    start: z.number().min(1).default(1),
    end: z.number().max(7).default(7),
  }),
  make_csi_question: z.boolean().optional(),
  leading_nps_question: z.boolean().optional(),
  languages: z.array(CESQuestionLanguageSchema),
});

export type CESQuestionProperties = z.infer<typeof CESQuestionPropertiesSchema>;

export const CESQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal('CES_QUESTION').default('CES_QUESTION'),
  properties: CESQuestionPropertiesSchema.optional(),
  selectedLanguage: z.string().optional(),
});

export type CESQuestion = z.infer<typeof CESQuestionSchema>;
