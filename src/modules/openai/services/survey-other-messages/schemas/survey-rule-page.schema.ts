import { BaseQuestionSchema } from './questions/base/base-question.schema';
import { NPSQuestionPropertiesSchema } from './questions/question-types/nps-question.schema';
import { z } from 'zod';
import { UUIDSchema } from './common.schema';

export const SurveyRulePageSchema = z.object({
  uuid: UUIDSchema,
  pageName: z.string({ description: 'The name of the page like Page 1' }),
  pageNumber: z.number(),
  questions: z.object({
    ...BaseQuestionSchema.shape,
    type: z.literal('NPS_QUESTION'),
    canHaveRules: z.boolean(),
    properties: NPSQuestionPropertiesSchema,
  }),
  // questionIds: z.array(z.number()),
  // gridArea: z.string(),
  // rules: z.array(RulesSchema),
  id: z.string(),
});

export type SurveyRulePage = z.infer<typeof SurveyRulePageSchema>;
