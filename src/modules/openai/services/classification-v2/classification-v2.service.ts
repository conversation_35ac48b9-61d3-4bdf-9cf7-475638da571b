import { CsvParserService } from '@/modules/openai/helpers/csv-parser.service';
import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import OpenAI from 'openai';
import { parseJSON } from '@/shared/utils';
import { OpenAIService } from '../../openai.service';
type ChatCompletion = OpenAI.Chat.ChatCompletion;
import { env } from '@/app.env';
import { z } from 'zod';
import { zodResponseFormat } from 'openai/helpers/zod';

const openai = new OpenAI({
  apiKey: '********************************************************',
});
@Injectable()
export class ClassificationV2Service {
  constructor(private csvParserService: CsvParserService) {}
  async classify() {
    const params = await this.getClassificationLabels();
    try {
      // console.log('PAGE: ', page)
      const Labels = z.object({
        parent: z.string({
          description:
            'The exact parent that is provided and relevant to the feedback',
        }),
        label: z.string({
          description:
            'The exact label that is provided and relevant to the feedback',
        }),
        label_id: z.number({
          description:
            'label id, if present that belongs to the label provided in the Categories array',
        }),
        sentiment: z.string({
          description: `Sentiment (choose 1 of these options: positive, neutral, negative).`,
        }),
        confidence: z.number({
          description:
            'Confidence level (0-1, use 2 decimal places), how confident are you that the customer feedback is about this category and subcategory. Analyze the cited attributions and estimate a confidence score based on the relevance and specificity to the label. Think in terms of a real classifier that uses thing like: accuracy, precision, recall, F1-score and AU-ROC ',
        }),
      });
      const Classification = z.lazy(() =>
        z.object({
          classification_labels: z.array(Labels),
          justification: z.string({
            description:
              'Justify why you chose the categories and subcategories. What in the feedback made you choose these categories and subcategories?',
          }),
        }),
      );
      const completion = await openai.chat.completions.parse({
        model: 'o1-mini',
        messages: [
          {
            role: 'user',
            content:
              'You are a specialist in analyzing customer feedback. You have received the following feedback from a customer. Please classify the feedback into the correct parent_category and sub_category.',
          },
          {
            role: 'user',
            content: `Choose the correct categories and subcategories that best fits the feedback. You will get 2 questions that has been asked with the answers the consumer gave`,
          },
          {
            role: 'user',
            content: `Question 1: '''${params[41].ques_pos}''' Answer: '''${params[41].pos}'''`,
          },
          {
            role: 'user',
            content: `Question 2: '''${params[41].ques_neg}''' Answer: '''${params[41].neg}'''`,
          },
          {
            role: 'user',
            content: `Here are the categories you need to use. Important: do not change these and keep them in the sam language: '''${params[41].labels}'''`,
          },
          {
            role: 'user',
            content: `Now classify the feedback and think this step by step. First, classify the feedback into the parent category and then into the subcategory. Make sure to read the feedback and all available categories and then classify it. Als write a justification why you chose the categories and subcategories and use the same language as the labels. Use Dutch if the labels are in Dutch and so on.`,
          },
          {
            role: 'user',
            content: `Return the following JSON format:
                        {
                            classification_labels: [
                                {
                                    parent: 'parent category provided',
                                    label: 'label provided',
                                    label_id: 'number of the label id',
                                    sentiment: 'positive, neutral or negative',
                                    confidence: 'number between 0 and 1 with 2 decimal places'
                                }
                            ],
                            justification: 'Justify why you chose the categories and subcategories. What in the feedback made you choose these categories and subcategories?'
                        }
                      `,
          },
        ],
        // response_format: zodResponseFormat(Classification, "classification"),
      });
      // console.log('completion.choices[0].message.parsed', completion)
      console.log(`Pos: ${params[41].pos}`);
      console.log(`Pos: ${params[41].neg}`);
      console.log(completion.choices[0].message.content);

      // const classification_result = completion.choices[0].message.parsed;
      const classification_result = completion.choices[0].message.content;

      return classification_result;
    } catch (e) {
      console.log('SOmething went wrong: ', e);
    }
  }

  async getClassificationLabels() {
    // console.log('Feedback4Sports_v3')
    return await this.csvParserService.getCSV('data/gohealth.csv');
  }
}
