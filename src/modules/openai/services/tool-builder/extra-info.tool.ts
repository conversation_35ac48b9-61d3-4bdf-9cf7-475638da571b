export interface ExtraInfoProperties {
    [key: string]: any
}

export class ExtraInfoTool {
    // Define unique symbols for each method
    static addJustificationDetails  = Symbol('addJustificationDetails');
    static addOverallConfidence     = Symbol('addOverallConfidence');
    static addKeywords              = Symbol('addKeywords');
    static addContactCustomer       = Symbol('addContactCustomer');
    static addContactCustomerReason = Symbol('addContactCustomerReason');
    static addContactCustomerEmail = Symbol('addContactCustomerEmail');

    addJustificationDetails() {
        return (extraInfoTool: ExtraInfoTool) => {
          return {
              justification_details: {
                  type: 'string',
                  description: 'Always add this! Step-by-step explanation on how you arrived at the classification and explain what specific parts of the customer feedback were most important in the decision',
              }
          }
        }
    }

    addOverallConfidence() {
        return (extraInfoTool: ExtraInfoTool) => {
            return {
                overall_confidence: {
                    type: 'number',
                    description: 'Overall confidence level about the whole classification (0-1, use 2 decimal places)',
                }
            }
        }
    }

    addKeywords() {
        return (extraInfoTool: ExtraInfoTool) => {
            return {
                    keywords: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                keyword: {
                                    type: 'string',
                                    description: 'Keyword that is relevant to the customer feedback. Use single a word if possible',
                                },
                                sentiment: {
                                    enum: ["Positive", "Negative", "Neutral"],
                                    description: `Sentiment (options: "Positive", "Negative", "Neutral")`,
                                },
                            },
                            required: ['keyword', 'sentiment'],
                        }
                    }

            }
        }
    }

    addPhrases() {
        return (extraInfoTool: ExtraInfoTool) => {
            return {
                phrases: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            phrase: {
                                type: 'string',
                                description: 'Extract important topics from customer feedback.',
                            },
                            sentiment: {
                                enum: ["Positive", "Negative", "Neutral"],
                                description: `Sentiment of the topic from the customer feedback (options: "Positive", "Negative", "Neutral")`,
                            },
                        },
                        required: ['phrase', 'sentiment'],
                    }
                }

            }
        }
    }
    addContactCustomer() {
        return (extraInfoTool: ExtraInfoTool) => {
            return {
                contact_customer: {
                    type: 'boolean',
                    // description: 'Is the customer requesting in any way to be contacted? If yes, set this to true. If not or if it is not clear, set this to false.',
                    description: 'Set to true if any form of contact is mentioned or requested in Customer-answer-1 or Customer-answer-2',
                }
            }
        }
    }

    addContactCustomerReason() {
        return (extraInfoTool: ExtraInfoTool) => {
            return {
                contact_customer_reason: {
                    type: 'string',
                    description: 'Explain, why we need to contact the customer about this feedback. Important: First detect the language the customer is using. Next, write the explanation is the same language as the customer feedback.',
                }
            }
        }
    }

    addContactCustomerEmail() {
        return (extraInfoTool: ExtraInfoTool) => {
            return {
                contact_customer_email: {
                    type: 'string',
                    description: 'If the contact_customer is true, then write an email that we can send to our customer here in the same language as the customer',
                }
            }
        }
    }

    addActionRecommender() {
        return (extraInfoTool: ExtraInfoTool) => {
            return {
                    action: {
                        type: 'string',
                        description: 'Use this to write recommendations or actions that we could take to do better,make the customer happier and do better in the future. Take a good look at the feedback so you understand the customer pain points or suggestions.',
                    },
                    action_priority: {
                        enum: ['Critical','High Priority','Medium Priority','Low Priority','Optional','Future Consideration'],
                        description:'What priority should we give to this action?'
                    },
                    action_difficulty: {
                        enum: ['very easy','easy','moderate','hard','very hard'],
                        description: 'How difficult is it to implement this action?'
                    },
                    action_category: {
                        type: 'string',
                        description: 'In which category does this action fall? For example: Product, Service, Marketing, Sales, etc. If you are not sure, then leave it empty. Do not make this to specific, keep it general.',
                    },
                }

        }
    }
}
