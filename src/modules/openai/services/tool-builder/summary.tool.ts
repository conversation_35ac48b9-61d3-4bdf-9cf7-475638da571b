import {BaseTool} from "./base.tool";
import {OpenAIGetClassificationParamsDto} from "../classification/openai-classification.dto";

export class SummaryTool extends BaseTool {
    parameters: any;

    constructor(params: OpenAIGetClassificationParamsDto) {
        const parameters = {
                type: 'object',
                properties: {
                    summary: {
                        type: 'string',
                        description: `Write phrases of the customer feedback that are the key points.Make this as short as possible. Don't use stopwords. Return just a string do not make an object`
                    }
                },
                required: ['summary'],
        }

        super(parameters);
        this.parameters = parameters;

    }

    static create(params: any) {
        return new SummaryTool(params);
    }
}
