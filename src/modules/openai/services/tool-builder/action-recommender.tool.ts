import {BaseTool} from "./base.tool";
import {OpenAIGetClassificationParamsDto} from "../classification/openai-classification.dto";

export class ActionRecommenderTool extends BaseTool {
    parameters: any;

    constructor(params: OpenAIGetClassificationParamsDto) {
        const parameters = {
                type: 'object',
                properties: {
                    action: {
                        type: 'string',
                        description: 'The action you recommend we should take to do better',
                    },
                    priority: {
                        enum: ['Critical','High Priority','Medium Priority','Low Priority','Optional','Future Consideration'],
                        description:'What priority should we give to this action?'
                    },
                    difficulty: {
                        enum: ['very easy','easy','moderate','hard','very hard'],
                        description: 'How difficult is it to implement this action?'
                    },
                },
                required: ['action', 'priority', 'difficulty'],
        }

        super(parameters);
        this.parameters = parameters;

    }

    static create(params: any) {
        return new ActionRecommenderTool(params);
    }
}
