import {ActionRecommenderTool} from "@/modules/openai/services/tool-builder/action-recommender.tool";
import {SummaryTool} from "@/modules/openai/services/tool-builder/summary.tool";
import {ClassificationAnswerTool} from "./classification-answer.tool";
import {ExtraInfoProperties, ExtraInfoTool} from "./extra-info.tool";
import {CustomCategoriesTool} from "./custom-categories.tool";
import {ExtraInfoPrompt} from "../prompt-builder/extra-info.prompt";
import {SentimentMarkupTool} from "./sentiment-markup.tool";

export class ToolBuilder {
    private tool: any;

    constructor() {
        this.tool = {
            name: '',
            description: '',
            parameters: {
                type: 'object',
                properties: {
                    extra_info: {
                        type: 'object',
                        properties: {},
                    }
                },
                required: []
            }
        };
    }

    setName(name?: string) {
        this.tool.name = name || 'classification';
        return this; // allow for method chaining
    }

    setDescription(description?: string) {
        this.tool.description = description || `Aspect-based sentiment analysis on customer feedback. Important, detect the language of the Customer-answer-1 or Customer-answer-2.
        If it is not possible to detect the language from the customer answer then use the question language. 
        Then translate your answers to the same language as the customer and fill in the fields below with the translated version.
        Use the answer property to classify based upon the provided Categories and only use these to classify. 
        Use the custom_categories to add categories that are not in the provided Categories but make sense to add.`;
        return this; // allow for method chaining
    }

    addClassificationAnswerTool(params: any) {
        const classificationAnswerFunction = new ClassificationAnswerTool(params);
        this.tool.parameters.properties.answer = classificationAnswerFunction.getTool().parameters;
        return this; // allow for method chaining
    }

    addSentimentMarkupTool(params: any) {
        const sentimentMarkupTool = new SentimentMarkupTool(params);
        this.tool.parameters.properties.sentiment_markup = sentimentMarkupTool.getTool().parameters;
        return this; // allow for method chaining
    }

    addCustomCategoriesTool(params: any) {
        const customCategoriesFunction = new CustomCategoriesTool(params);
        this.tool.parameters.properties.custom_categories = customCategoriesFunction.getTool().parameters;
        return this; // allow for method chaining
    }
    addActionRecommenderTool(params: any) {
        const customFunction = new ActionRecommenderTool(params);
        this.tool.parameters.properties.actions = customFunction.getTool().parameters;
        return this; // allow for method chaining
    }

    addSummaryTool(params: any) {
        const customFunction = new SummaryTool(params);
        this.tool.parameters.properties.summary = customFunction.getTool().parameters;
        return this; // allow for method chaining
    }

    // addExtraInfoFunction(params: any) {
    //     const extraInfoFunction = new ExtraInfoFunction(params);
    //     this.functionObject.parameters.properties.extra_info = extraInfoFunction.getFunction();
    //     return this; // allow for method chaining
    // }

    addExtraInfoTool(params: any, extraInfoTools: ((extraInfoTool: ExtraInfoTool) => ExtraInfoProperties)[], required: string[]) {
        const extraInfoTool = new ExtraInfoTool();
        extraInfoTools.forEach(func => {
            const newProperties = func(extraInfoTool);
            if (typeof newProperties === 'object' && newProperties !== null) {
                this.tool.parameters.properties.extra_info.properties = {
                    ...this.tool.parameters.properties.extra_info.properties,
                    ...newProperties
                };
            }
        });
        this.tool.parameters.properties.extra_info.required = required;
        return this; // allow for method chaining
    }

    setRequiredProperties(required: any[]){
        this.tool.parameters.required = required
        return this
    }
    getTool() {
        return this.tool;
    }
}
