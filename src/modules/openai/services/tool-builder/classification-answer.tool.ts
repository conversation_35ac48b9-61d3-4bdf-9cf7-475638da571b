import {BaseTool} from "./base.tool";
import {OpenAIGetClassificationParamsDto} from "../classification/openai-classification.dto";

export class ClassificationAnswerTool extends BaseTool {
    parameters: any;

    constructor(params: OpenAIGetClassificationParamsDto) {
        const parameters = {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    parent_category: {
                        type: 'string',
                        description:
                            'The parent category is highly relevant to the customer feedback. Do not skip this unless very short feedback or vague feedback. Make sure the combination as mentioned earlier does not yet exist',
                    },
                    sub_category: {
                        type: 'string',
                        description:
                            'The sub category that is highly relevant to the customer feedback. Do not skip this unless very short feedback or vague feedback. Make sure the combination as mentioned earlier does not yet exist',
                    },
                    sub_category_id: {
                        type: 'number',
                        description:
                            'Sub category id that belongs to the sub_category provided in the Categories array',
                    },
                    sentiment: {
                        type: 'string',
                        description: `Sentiment (choose 1 of these options: ${params.sentiment}).`,
                    },

                    confidence: {
                        type: 'number',
                        description:
                            'Confidence level (0-1, use 2 decimal places), how confident are you that the customer feedback is about this category and subcategory. Analyze the cited attributions and estimate a confidence score based on the relevance and specificity to the label. Think in terms of a real classifier that uses thing like: accuracy, precision, recall, F1-score and AU-ROC ',
                    },
                },
                required: ['parent_category', 'sub_category', 'sub_category_id', 'sentiment', 'confidence'],
            },
        }

        super(parameters);
        this.parameters = parameters;

    }

    static create(params: any) {
        return new ClassificationAnswerTool(params);
    }
}
