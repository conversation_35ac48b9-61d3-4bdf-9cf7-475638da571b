import {BaseTool} from "./base.tool";
import {OpenAIGetClassificationParamsDto} from "../classification/openai-classification.dto";

export class SentimentMarkupTool extends BaseTool {
    parameters: any;

    constructor(params: OpenAIGetClassificationParamsDto) {
        const parameters = {
                type: 'object',
                properties: {
                    customer_feedback_1: {
                        type: 'string',
                        description:
                            `Write the complete answer from the customer and add the following markup when a certain phrase/topic has a category assigned: 
                               <span class="positive or negative" title="parent_category -> sub_category">[positive/negative text segment]</span>)
                        `,
                    },
                    customer_feedback_2: {
                        type: 'string',
                        description:
                            `Write the complete answer from the customer and add the following markup when a certain phrase/topic has a category assigned: 
                               <span class="positive or negative" title="parent_category -> sub_category">[positive/negative text segment]</span>)
                        `,
                    }
                },
                required: ['customer_feedback_1', 'customer_feedback_2'],
        }

        super(parameters);
        this.parameters = parameters;

    }

    static create(params: any) {
        return new SentimentMarkupTool(params);
    }
}
