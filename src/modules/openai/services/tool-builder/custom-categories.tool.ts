import {BaseTool} from "./base.tool";
import {OpenAIGetClassificationParamsDto} from "../classification/openai-classification.dto";

export class CustomCategoriesTool extends BaseTool {
    parameters: any;

    constructor(params: OpenAIGetClassificationParamsDto) {
        const parameters =  {
            type: 'array',
                items: {
                type: 'object',
                    properties: {
                    category_name: {
                        type: 'string',
                        description: 'Write your custom category related to the feedback based on the question and answer. Always write a category here that is highly related to the feedback. Do not leave this empty!',
                    },
                    sentiment: {
                        type: 'string',
                        enum: [`${params.sentiment}`],
                        description: `Sentiment (options: ${params.sentiment})`,
                    },
                    confidence: {
                        type: 'number',
                        description: 'Confidence level (0-1, use 2 decimal places), how confident are you that the customer feedback is about this category and subcategory',
                    },
                },
                required: ['category_name', 'sentiment', 'confidence'],
            },
        }

        super(parameters);
        this.parameters = parameters;

    }

    static create(params: any) {
        return new CustomCategoriesTool(params);
    }
}
