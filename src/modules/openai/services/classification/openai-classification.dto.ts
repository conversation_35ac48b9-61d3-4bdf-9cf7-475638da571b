export class OpenAIGetClassificationParamsDto {
  activeAIModules: number[];
  categories: any;
  strengths_question: string;
  strengths_answer: string;
  improvements_question: string;
  improvements_answer: string;
  sentiment: string;
  sentiment_enum: any;
  model?: string;
}

export class OpenAIClassificationMetaDataUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

export class OpenAIClassificationCustomCategories {
  category_name: string;
  sentiment: string;
  confidence: number;
}
export class OpenAIClassificationKeywords {
  keyword: string;
  sentiment: string;
}

export class OpenAIClassificationMetaData {
  usage: OpenAIClassificationMetaDataUsage;
  justification: string;
  overall_confidence: number;
  custom_categories: OpenAIClassificationCustomCategories[];
  keywords: OpenAIClassificationKeywords[];
  contact_customer: boolean;
  contact_customer_reason: string;
  contact_customer_email: string;
}

export class OpenAIClassificationItem {
  category: string;
  category_id: number;
  sentiment: string;
  confidence: number;
}

export class OpenAIClassificationData {
  answer_id: number;
  classification: OpenAIClassificationItem[];
  meta_data: OpenAIClassificationMetaData;
  model?: string;
}
