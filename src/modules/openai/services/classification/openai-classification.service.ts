import { Injectable } from '@nestjs/common';
import { OpenAIResponse } from '../../openai.interface';
import { SurvSurveyEntity } from '../../../database/entities/surv-survey.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { PromptBuilder } from '../prompt-builder/prompt-builder';
import { ExtraInfoTool } from '../tool-builder/extra-info.tool';
import { ToolBuilder } from '../tool-builder/tool.builder';
import { OpenAISurveyAnswer } from './openai-classification.interface';
import { OpenAIGetClassificationParamsDto } from './openai-classification.dto';
import { parseJSON } from '../../../../shared/utils/parseJSON';
import { OpenAIClassificationProcessingService } from './openai-classification-processing.service';
import { OpenAIService } from '../../openai.service';
import { CompanyType } from '../../../../shared/enums/company-type.enum';
import {
  OPENAI_CLASSIFICATION_MIN_LETTER,
  OPENAI_DEFAULT_MODEL,
} from '../../../../config/ai.config';
import OpenAI from 'openai';
import ChatCompletion = OpenAI.ChatCompletion;
import { AIModuleType } from '@/shared/enums';
import { QueueService } from '@lib/queue/queue.service';
import {
  StartClassificationBulkDto,
  StartClassificationByDateDto,
} from '../../dto/start-classification-bulk.dto';
import { SurveyAnswerEntity } from '@entities';

// class PromptBuilder {
//   private prompt = [];
//
//   addPromptMessage(role: 'user' | 'assistant' | 'system', content: string) {
//     this.prompt.push({
//       role,
//       content,
//     });
//   }
//
//   getPrompt() {
//     return this.prompt;
//   }
// }

@Injectable()
export class OpenAIClassificationService {
  constructor(
    private queueService: QueueService,
    private openAIService: OpenAIService,
    private queueProcessingService: OpenAIClassificationProcessingService,
    @InjectRepository(SurvSurveyEntity)
    private readonly sRepo: Repository<SurvSurveyEntity>,
    @InjectRepository(SurveyAnswerEntity)
    private readonly saRepo: Repository<SurveyAnswerEntity>,
  ) {}

  async getClassification(
    params: OpenAIGetClassificationParamsDto,
  ): Promise<OpenAIResponse | any> {
    const { activeAIModules, model = OPENAI_DEFAULT_MODEL } = params;

    const wordList: string[] = [
      'Schok',
      'Elektriciteit',
      'Letsel',
      'ambulance',
      'beschadiging',
      'blauwe plek',
      'brand',
      'dokter',
      'explosie',
      'gewond',
      'ontploffing',
      'ontploft',
      'rook',
      'schade',
      'vuur',
      'waterschade',
      'ziekenhuis',
    ];

    const promptBuilder = new PromptBuilder();
    promptBuilder
      .addPrompt('SystemPrompt')
      .addPrompt('CustomerFeedback', '', params);
    if (activeAIModules.includes(AIModuleType.Classification)) {
      promptBuilder
        .addPrompt('ClassificationRulesPrompt')
        .addPrompt('JustificationPrompt', '', params)
        .addPrompt('ExtraInfoJustificationDetailsPrompt')
        .addPrompt('ExtraInfoOverallConfidencePrompt')

        .addPrompt('CategoriesPrompt', '', params);
    }
    if (activeAIModules.includes(AIModuleType.Keywords)) {
      promptBuilder.addActiveAIModulesPrompt(
        'ExtraInfoKeywordsPrompt',
        activeAIModules,
      );
    }
    if (activeAIModules.includes(AIModuleType.ContactRequest)) {
      promptBuilder
        .addActiveAIModulesPrompt(
          'ExtraInfoContactCustomerPrompt',
          activeAIModules,
          wordList,
        )
        .addActiveAIModulesPrompt(
          'ExtraInfoContactCustomerReasonPrompt',
          activeAIModules,
        );
    }
    if (activeAIModules.includes(AIModuleType.CustomCategories)) {
      promptBuilder.addActiveAIModulesPrompt(
        'CustomLabelRulesPrompt',
        activeAIModules,
        params,
      );
    }

    // promptBuilder.addPrompt('SentimentMarkupPrompt')
    promptBuilder.addPrompt('ExtraInfoBasePrompt').addPrompt('LastGuidePrompt');

    const toolBuilder = new ToolBuilder();
    const extraInfoTool = new ExtraInfoTool();
    toolBuilder.setName().setDescription();

    if (activeAIModules.includes(AIModuleType.Classification)) {
      toolBuilder.addClassificationAnswerTool(params);
    }

    if (activeAIModules.includes(AIModuleType.CustomCategories)) {
      toolBuilder.addCustomCategoriesTool(params);
    }
    let addExtraInfoTools = [
      extraInfoTool.addJustificationDetails(),
      extraInfoTool.addOverallConfidence(),
      // extraInfoTool.addPhrases(),
      extraInfoTool.addContactCustomerEmail(),
      extraInfoTool.addActionRecommender(),
    ];
    this.pushConditionally(
      addExtraInfoTools,
      activeAIModules.includes(AIModuleType.Keywords),
      extraInfoTool.addKeywords,
    );
    this.pushConditionally(
      addExtraInfoTools,
      activeAIModules.includes(AIModuleType.ContactRequest),
      extraInfoTool.addContactCustomer,
    );
    // this.pushConditionally(addExtraInfoTools, activeAIModules.includes(AIModuleType.ContactRequest), extraInfoTool.addContactCustomerReason);

    let requiredItems = ['justification_details'];
    this.pushRequiredConditionally(
      requiredItems,
      activeAIModules.includes(AIModuleType.Keywords),
      'keywords',
    );
    this.pushRequiredConditionally(
      requiredItems,
      activeAIModules.includes(AIModuleType.ContactRequest),
      'contact_customer',
    );
    // this.pushRequiredConditionally(requiredItems, activeAIModules.includes(AIModuleType.ContactRequest), 'contact_customer_reason');
    toolBuilder.addExtraInfoTool(params, addExtraInfoTools, requiredItems);
    let requiredProperties = ['answer', 'extra_info'];
    this.pushRequiredConditionally(
      requiredProperties,
      activeAIModules.includes(AIModuleType.CustomCategories),
      'custom_categories',
    );
    toolBuilder.setRequiredProperties(requiredProperties);

    const classifier = (await this.openAIService.createChatCompletion({
      model,
      temperature: 0.0,
      messages: promptBuilder.getPrompts(),
      functions: [toolBuilder.getTool()],
      function_call: { name: 'classification' },
    })) as unknown as ChatCompletion;

    const classified = parseJSON(
      classifier.choices[0].message.function_call.arguments,
    );
    return {
      completion: classified,
      usage: classifier.usage,
    };
  }

  private pushConditionally(array: any[], condition: boolean, func: Function) {
    if (condition) {
      array.push(func());
    }
  }
  private pushRequiredConditionally(
    array: any[],
    condition: boolean,
    requiredItem: string,
  ) {
    if (condition) {
      array.push(requiredItem);
    }
  }

  async startClassificationBulk(body: StartClassificationBulkDto) {
    let result = [];
    for (let answerId of body.answerIds) {
      result.push({
        id: answerId,
        ...(await this.startClassification(answerId)),
      });
    }
    return result;
  }

  async startClassificationByDate(body: StartClassificationByDateDto) {
    let result = [];
    const list = await this.saRepo.find({
      where: { creation_date: Between(body.start, body.end) },
    });
    for (let answer of list) {
      result.push({
        id: answer.surv_surveyanswer_id,
        ...(await this.startClassification(answer.surv_surveyanswer_id)),
      });
    }
    return result;
  }

  async startClassification(answerId?: number, model?: string) {
    console.log('OPENAI START CLASSIFICATION IS CALLING', answerId);

    let result = await this.getClassificationList(answerId); // Perform query

    console.log('OPENAI', 'GOT query');
    for (let row of result) {
      await this.queueService
        .method('classification')
        .save(row.surv_surveyanswer_id);
    }
    const processedQuery = await this.processQuery(result); // Pre-Process query
    console.log('OPENAI', 'processedQuery', processedQuery);
    const list: OpenAISurveyAnswer[] = processedQuery.map((item) => {
      // Map query to SurveyAnswer
      item.labels = this.processCategories(item.labels);
      item.model = model;
      return item;
    });

    await this.queueProcessingService.processQueue(list); // Start Process queue
    return {
      success: true,
      message: `${list.length} items added to queue for processing`,
    };
  }

  async processQuery(result: any[]) {
    if (result.length > 0) {
      return result?.map((item) => {
        // console.log('item', item)
        try {
          const labels = parseJSON(item.labels);
          return {
            ...item,
            labels: labels,
          };
        } catch (error) {
          console.log('ERROR', error);
        }
      });
    }
    return [];
  }

  async getClassificationList(answerId?: number) {
    let sql = `
    WITH labels AS ( SELECT mpi.survey_id, '[' || string_agg('{"parent":"' || coalesce(NULLIF(mipl.item_label_locale, ''), NULLIF(mip.item_label_locale, ''), NULLIF(mip.item_label, ''), '') || '", "label":"' || coalesce(NULLIF(mil.item_label_locale, ''), NULLIF(mi.item_label_locale, ''), NULLIF(mi.item_label, ''), '') || '", "id":' || mi.mclass_item_id || '}', ',') || ']' AS labels
FROM mclass_proces_item mpi
INNER JOIN surv_survey s ON mpi.survey_id = s.surv_survey_id
LEFT JOIN mclass_proces_item mpip ON mpi.parent_id = mpip.item_id AND mpi.survey_id = mpip.survey_id
LEFT JOIN mclass_item mip ON mpi.parent_id = mip.mclass_item_id AND (mip.sec_company_id = s.sec_company_id OR mip.sec_company_id=25)
LEFT JOIN mclass_item_local mipl ON mpi.parent_id = mipl.mclass_item_id AND (mipl.sec_company_id = s.sec_company_id OR mipl.sec_company_id=25)
LEFT JOIN mclass_item mi ON mpi.item_id = mi.mclass_item_id AND mi.parent_id IS NOT NULL AND (mi.sec_company_id = s.sec_company_id OR mi.sec_company_id=25)
LEFT JOIN mclass_item_local mil ON mi.mclass_item_id = mil.mclass_item_id AND (mil.sec_company_id = s.sec_company_id OR mil.sec_company_id=25)
WHERE mi.deleted_on IS NULL AND mpi.is_active=1 AND mpip.is_active=1 AND mi.mclass_item_id IS NOT NULL GROUP BY mpi.survey_id ) 
    SELECT a.surv_surveyanswer_id,
           CASE WHEN c.company_type = ${CompanyType.BOUW} OR c.company_type = ${CompanyType.SPORT} THEN
                    replace(i_pos.message_value, '[project]', p.project_name)
                ELSE
                    i_pos.message_value
               END as ques_pos,
           coalesce(r_pos.return_value, pos.return_value) AS pos,
           CASE WHEN c.company_type = ${CompanyType.BOUW} OR c.company_type = ${CompanyType.SPORT} THEN
                    replace(i_neg.message_value, '[project]', p.project_name)
                ELSE
                    i_neg.message_value
               END as ques_neg,
        coalesce(r_neg.return_value, neg.return_value) AS neg, 
        sp.neutral_sentiment AS neutral, 
        l.labels 
    FROM surv_surveyanswer a 
    INNER JOIN sec_company c ON a.sec_company_id = c.sec_company_id 
    INNER JOIN surv_survey ss ON a.surv_survey_id = ss.surv_survey_id 
    INNER JOIN sec_programsettings sp ON a.sec_company_id = sp.sec_company_id 
    LEFT JOIN surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND ss.clf_question2 = pos.surv_surveyquestion_id
    LEFT JOIN redacted_answers r_pos ON pos.surv_surveyansweritem_id = r_pos.surv_surveyansweritem_id
    LEFT JOIN surv_surveyquestion q_pos ON pos.surv_surveyquestion_id = q_pos.surv_surveyquestion_id 
    LEFT JOIN i18n i_pos ON i_pos.message_key = 'survey.question.' || q_pos.surv_surveypart_id || '.question' AND i_pos.message_language = c.default_language_key 
    LEFT JOIN surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND ss.clf_question1 = neg.surv_surveyquestion_id
    LEFT JOIN redacted_answers r_neg ON neg.surv_surveyansweritem_id = r_neg.surv_surveyansweritem_id
    LEFT JOIN surv_surveyquestion q_neg ON neg.surv_surveyquestion_id = q_neg.surv_surveyquestion_id 
    LEFT JOIN i18n i_neg ON i_neg.message_key = 'survey.question.' || q_neg.surv_surveypart_id || '.question' AND i_neg.message_language = c.default_language_key 
    LEFT JOIN labels l ON a.surv_survey_id = l.survey_id
    LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id
    LEFT JOIN projects p ON ec.xml_internal_external = p.project_id
    WHERE
    (a.comments_neg_length > ${OPENAI_CLASSIFICATION_MIN_LETTER} OR a.comments_pos_length > ${OPENAI_CLASSIFICATION_MIN_LETTER})
    AND l.labels IS NOT NULL
    `;
    if (answerId) {
      sql += `AND a.surv_surveyanswer_id = ${answerId}`;
    } else {
      sql += `
        AND (a.ai_processed_state = 0 OR a.ai_processed_state IS NULL)
        AND a.sec_company_id = 2
        AND a.surv_survey_id = 5
        LIMIT 10`;
    }
    return this.sRepo.query(sql);
  }

  /**
   * Processes categories and groups them by parent category
   * @private
   * @param {Array} categories - Array of category objects
   * @returns {Array} - Array of grouped categories
   */
  private processCategories(categories: any[]) {
    // console.log('categories', categories)
    const categoriesMap = new Map(); // Map of parent category to array of categories
    categories.forEach((row) => {
      // Loop through categories
      if (categoriesMap.has(row.parent)) {
        // If parent category exists, add category to array
        const group = categoriesMap.get(row.parent); // Get parent category
        group.subCategories.push({ name: row.label, id: row.id }); // Add category to array
      } else {
        // If parent category doesn't exist, create it
        categoriesMap.set(row.parent, {
          parentCategory: row.parent,
          subCategories: [{ name: row.label, id: row.id }],
        }); // Create parent category
      }
    });
    return [...categoriesMap.values()]; // Return array of grouped categories
  }
}
