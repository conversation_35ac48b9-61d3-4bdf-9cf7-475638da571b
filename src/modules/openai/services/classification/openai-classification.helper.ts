import {OpenAIGetClassificationParamsDto} from './openai-classification.dto';
import {categories2,} from '../classification-by-csv/categories';

export const convertSentimentToNum = (sentiment: string) => {
  return {
    Positive: 1,
    Negative: 2,
    Neutral: 3,
  }[sentiment];
};

export const generateOpenAIMainCategory = (
  {
    categories,
    strengths_question,
    strengths_answer,
    improvements_question,
    improvements_answer,
    sentiment,
  }: any,
  opinionUnitsResult,
  sentencesParsed,
) => {
  // console.log('categories', categories2)
  const parentCategory = categories2.map((category) => ({
    parent_category: category.parent_category,
    parent_category_description: category.parent_category_description,
  }));
  // console.log('parentCategory', parentCategory)
  return [
    {
      role: 'system',
      content: `You are a helpful assistant that is extremely good at aspect-based sentiment analysis on customer feedback. Your responses should be consistent when given the same input. `,
    },

    {
      role: 'user',
      content: `Let's take this step by step:
                    - Translate everything into English. That means all categories, the questions, the sentences and so on.\n
                    - First,look at the sentences\n
                    - Second, look at the question that was asked for each sentence. This should give you a better context when you classify it.\n
                    - Third, find the best fitting category for each sentence and make sure to also look at the parent_category_description so that you have a very good understanding about the purpose of that parent category.\n
                    - Fourth, before you classify also look at the sub_categories > name and sub_categories > sub_category_description so that you have a good understanding of what the parent_category contains.\n
                    - Last, look at the combination of the parent and sub category and see if that makes sense. if you can not find a good category or if it is not clear what the topic is about then skip it.\n
                    
                Self reflect and double check if you have all the correct parent categories. Ask yourself if the category is really related to the text and double check that before moving on.\n
                The categories you chose should make sense and be as accurate as possible. If it is not clear then skip it.\n
                It is extremely important to stick to the provided categories and do not make up your own.\n
                Do not infer or assume any information that is not explicitly stated in the sentences or opinion_units. And don't classify an empty sentence!\n
        `,
    },

    {
      role: 'user',
      content: `categories:\n\n${JSON.stringify(categories2)}`,
    },
    {
      role: 'user',
      content: `
            Here are the sentences: \n
            strengths: ${JSON.stringify(sentencesParsed.strengths)}\n
            improvements: ${JSON.stringify(sentencesParsed.improvements)}\n\n
    `,
    },
    {
      role: 'user',
      content: `Return an array of parent categories you have classified based upon the sentences in JSON format.\n IMPORTANT!: Only return the name of the provided parent_category (not the translated version).\n 
        If you already have a parent_category then do not return the same parent_category again. So make sure the list has unique parent categories.\n
        ['parent_category', 'parent_category', 'parent_category']
           `,
    },
  ];
};
export const generateOpenAIOpinionUnits = ({
  categories,
  strengths_question,
  strengths_answer,
  improvements_question,
  improvements_answer,
  sentiment,
}: OpenAIGetClassificationParamsDto) => {
  console.log('strengths_question', strengths_question);
  return [
    // {
    //     "role": "system",
    //     "content": `You are a helpful assistant that is extremely good at generating opinion units based on customer feedback. Your responses should be consistent when given the same input. `
    // },
    // {
    //     "role": "user",
    //     "content": `
    //         Translate the customer feedback into English. If the sentence is already in English, then do not translate it.
    //         Start by decomposing the feedback into individual sentences.Sometimes the feedback is written in fragments and not closed with punctuation marks, so you should identify where a new sentence starts. If it is a different topic it is most likely a new sentence.
    //         Make sure you have every piece of text and don't leave any text behind.
    //         First look at the strengths and then the improvements.
    //
    //         Now, analyze each sentence and further break it down into the smallest meaningful segments with 2 to 3 words, referred to as 'opinion units'.
    //         These 'opinion units' should be concise and directly encapsulate the main point of each feedback fragment, similar to keywords or key topics.
    //         For example, an 'opinion unit' might look like 'excellent service', 'late delivery', 'friendly staff', etc. Ensure that these units are brief and precise and as correct as possible.
    //         Double check that you capture every topic,opinion or complaint as 'opinion unit'.
    //         `
    // },
    {
      role: 'user',
      content: `
                You are a helpful assistant that is extremely good at analyzing customer feedback. This involves creating sentences from the feedback and extracting the most important topics and keywords. \n\n
                Customer feedback:\n\nStrengths:\nQuestion: ${strengths_question}\nAnswer: ${strengths_answer}\n\nImprovements:\nQuestion: ${improvements_question}\nAnswer: ${improvements_answer}\n\n
                
                Now, analyze each sentence and further break it down into the smallest meaningful segments with 2 to 3 words, referred to as 'opinion units'.
                 These 'opinion units' should be concise and directly encapsulate the main point of each feedback fragment, similar to keywords or key topics.
                 For example, an 'opinion unit' might look like 'excellent service', 'late delivery', 'friendly staff', etc. Ensure that these units are brief and precise and as correct as possible.
                 Double check that you capture every topic,opinion or complaint as 'opinion unit' and that the maximum length is 3 words.\n\n
                 Create the opinion units for the strengths and improvements and make sure they are maximal 2 to 3 words. \n\n
                
                `,
    },
    // {
    //     "role": "user",
    //     "content": `
    //         Only return the result and use the following JSON format:\n\n
    //         {
    //              strengths: {
    //                 question: Enter exactly this: ${strengths_question}
    //                 sentences: ['sentence', 'sentence', 'sentence'],
    //              },
    //              improvements: {
    //                 question: Enter exactly this: ${improvements_question}
    //                 sentences: ['sentence', 'sentence', 'sentence'],
    //              },
    //              opinion_units_strengths: {
    //                 question: Enter exactly this: ${strengths_question},
    //                 opinion_units: ['opinion_unit', 'opinion_unit', 'opinion_unit']
    //              },
    //              opinion_units_improvements: {
    //                 question: Enter exactly this: ${strengths_question},
    //                 opinion_units: ['opinion_unit', 'opinion_unit', 'opinion_unit']
    //              }
    //         }
    //         `
    // },
  ];
};

export const generateOpenAICustomCategories = ({
  categories,
  strengths_question,
  strengths_answer,
  improvements_question,
  improvements_answer,
  sentiment,
}: OpenAIGetClassificationParamsDto) => {
  console.log('strengths_question', strengths_question);
  return [
    {
      role: 'system',
      content: `You are a helpful assistant that is extremely good at generating categories based on customer feedback. Your responses should be consistent when given the same input. `,
    },
    {
      role: 'user',
      content: `
                Translate the customer feedback into English. If the sentence is already in English, then do not translate it.
                Start by decomposing the feedback into individual sentences.Sometimes the feedback is written in fragments and not closed with punctuation marks, so you should identify where a new sentence starts. If it is a different topic it is most likely a new sentence.
                Make sure you have every piece of text and don't leave any text behind.
                First look at the strengths and then the improvements.

                Now, analyze each sentence and further break it down into the smallest meaningful segments with 2 to 3 words, referred to as 'opinion units'. 
                These 'opinion units' should be concise and directly encapsulate the main point of each feedback fragment, similar to keywords or key topics. 
                For example, an 'opinion unit' might look like 'excellent service', 'late delivery', 'friendly staff', etc. Ensure that these units are brief and precise and as correct as possible.
                Double check that you capture every topic,opinion or complaint as 'opinion unit'.
                `,
    },
    {
      role: 'user',
      content: `
                Her is the customer feedback:\n\nStrengths:\nQuestion: ${strengths_question}\nAnswer: ${strengths_answer}\n\nImprovements:\nQuestion: ${improvements_question}\nAnswer: ${improvements_answer}\n\n
                `,
    },
    {
      role: 'user',
      content: `Now, after you have analyzed each sentence and identified the 'opinion units', identify key themes or patterns in the feedback. Then, list these themes or patterns as new categories in the 'your_categories' field. Each new category should encapsulate a unique aspect of the feedback. 

              For example, if a common opinion unit was 'late delivery', you could create a new category like 'Delivery > Timing'. If another unit was 'friendly staff', a category like 'Service > Staff Friendliness' could be made. 
            
              Try to cover all main points of the feedback with your categories. Your categories should be structured as 'Parent Category > Sub Category'. Each 'opinion unit' should have a matching category. 
            
              Finally, use these 'sentences' to create a new category. For example, if the feedback was 'The staff was very friendly and helpful', you could create a new category like 'Service > Staff Friendliness'. In this example Service is the Parent and Staff Friendliness is the Sub Category.`,
    },
    {
      role: 'user',
      content: `Provide your analysis in this format and classify each sentence:\n\n
            {
              "answer": [
                {
                  "category": "Enter your Parent Category > Sub Category here (format: Parent Category > Sub Category) dont't use Strengths or Improvements as Parent Category",
                  "reason": "Explain why you chose this category (format: This is the reason why I chose this category)",
                  "sentiment": "Sentiment (options: ${sentiment})",
                  "confidence": "Confidence level (0-1,2 decimal places)", 
                },
                ...
              ],
              "opinion_units": "Enter your opinion units here. Make sure that it is short and concise and keyword/topic like. (format: opinion_unit1\n opinion_unit2, ...)",
            }`,
    },
  ];
};
export const generateOpenAIClassificationMessages = (
  {
    categories,
    strengths_question,
    strengths_answer,
    improvements_question,
    improvements_answer,
    sentiment,
  }: OpenAIGetClassificationParamsDto,
  parentCategories?: any,
  opinionUnitsResult?: any,
  sentencesParsed?: any,
) => {

  let list: any[] = [
    {
      role: 'system',
      content: `You are a helpful assistant that is extremely good at aspect-based sentiment analysis on customer feedback. Your responses should be consistent when given the same input. The sentiments you can use are: ${sentiment}.`,
    },
    {
      role: 'user',
      content: `The categories are organized into groups, with each group having a parent_category and one or more sub_categories.`,
    },

    {
      role: 'user',
      content: `
                Let's take this step by step:
                    - Translate everything into English. That means all categories, the questions, the sentences and so on.\n
                    - First,look at the  sentences\n
                    - Second, look at the question that was asked for each sentence. This should give you a better context when you classify it.\n
                    - Third, find the best fitting subcategory for each sentence and make sure to also look at the sub_category_description so that you have a very good understanding about the purpose of that sub category.\n
                    - Fourth, combine the question that we have asked with the sentence. This gives you a better context so that you can better pick the right sub category and use the sub_category_description to have a full context.\n
                    - Last, look at the combination of the parent and sub category name and descriptions to see if it would relate to a sentence and question we've asked. if you can not find a good category or if it is not clear what the topic is about then skip it.\n\n
                
                
                `,
    },
    {
      role: 'user',
      content: `categories:\n\n${JSON.stringify(parentCategories)}`,
    },
    {
      role: 'user',
      content: `
                Here are the sentences: \n
                strengths: ${JSON.stringify(sentencesParsed.strengths)}\n
                improvements: ${JSON.stringify(
                  sentencesParsed.improvements,
                )}\n\n
            `,
    },
    {
      role: 'user',
      content: `
                Provide your analysis in this format and classify each sentence. 
                If there is no category that fits the sentence then skip it and move on to the next one.
                It is extremely important that you double check the sub categories you have classified and don't come up with double sub categories as explained in the steps:\n\n
            {
              "answer": [
                {
                  "category": "Enter your Classified category from the provided categories and make sure to apply the rules I have described above. Return the exact name of the categories I have provided (not the translated version).Use this format: (parent_category > sub_category)",
                  "reason": "Explain why you chose this category (format: This is the reason why I chose this category)",
                  "sentence": "What sentence from this: ${JSON.stringify(
                    sentencesParsed.strengths,
                  )} ${JSON.stringify(
        sentencesParsed.improvements,
      )} triggered you to choose this category? (format: This is the sentence that triggered me to choose this category)",
                  "category_id": "id of the category",
                  "sentiment": "Sentiment (options: ${sentiment})",
                  "confidence": "Confidence level (0-1,2 decimal places)",
                  "uncertainty": "Measure of uncertainty (0-1, 2 decimal places)"
                },
                ...
              ],
              "opinion_units": "Enter all sentences here. format: sentence\n sentence, ...)",
              
              "text_relation_category": "What sentence, from the sentences > strengths and sentences > improvements is related to the category you have chosen. For each category you have chosen, in this object answer > category, show me the sentence that it was based upon. Example: This is a sentence that is related to the category. --- parent_category > sub_category",
              
            }`,
    },
  ];

  return list;
};
