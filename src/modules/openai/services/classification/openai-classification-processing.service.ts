import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { OpenAISurveyAnswer } from './openai-classification.interface';
import {
  OpenAIClassificationCustomCategories,
  OpenAIClassificationData,
  OpenAIClassificationKeywords,
  OpenAIClassificationMetaData,
} from './openai-classification.dto';
import { OpenAIClassificationService } from './openai-classification.service';
import { QueueProcessingBaseService } from '../../helpers/queue-processing-base.service';
import { convertSentimentToNum } from './openai-classification.helper';
import {
  OPENAI_CLASSIFICATION_CONCURRENT_JOB,
  OPENAI_DEFAULT_MODEL,
  OPENAI_MAX_RETRIES_PER_ROW,
  OPENAI_RETRY_DELAY_MULTIPLIER,
  OPENAI_SLEEP_TIME,
} from '../../../../config/ai.config';
import {
  ClfCallEntity,
  KeywordsEntity,
  MClassCustomItem,
  MClassResponse,

  SurveyAnswerAIEntity,
  SurveyAnswerEntity,
} from '@entities';
import { sleep } from '@/shared/utils';
import { MclassRecordStatus } from '@/shared/enums';
import { ProgramSettingsService } from '@/modules/common/program-settings/program-settings.service';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { QueueStatus } from '@/shared/enums/queue.enum';
import { QueueService } from '@lib/queue/queue.service';
const Queue = require('better-queue');

const queueConfig = {
  concurrent: OPENAI_CLASSIFICATION_CONCURRENT_JOB,
  maxRetries: 0,
  maxTimeout: 3 * (60 * 1000),
  afterProcessDelay: 500,
};

/**
 * A service for processing rows of data in a queue and
 * formatting the results into a CSV file.
 * @class
 */
@Injectable()
export class OpenAIClassificationProcessingService extends QueueProcessingBaseService {
  constructor(
    private queueService: QueueService,
    private logger: ModuleLogger,

    private settingsService: ProgramSettingsService,

    @Inject(forwardRef(() => OpenAIClassificationService))
    private openAIClassificationService: OpenAIClassificationService,
    @InjectRepository(SurveyAnswerEntity)
    private saRepo: Repository<SurveyAnswerEntity>,
    @InjectRepository(SurveyAnswerAIEntity)
    private saAIRepo: Repository<SurveyAnswerAIEntity>,
    @InjectRepository(MClassResponse)
    private mcrRepo: Repository<MClassResponse>,
    @InjectRepository(MClassCustomItem)
    private customCategoryRepo: Repository<MClassCustomItem>,
    @InjectRepository(KeywordsEntity)
    private keywordRepo: Repository<KeywordsEntity>,
    @InjectRepository(ClfCallEntity)
    private clfCallRepo: Repository<ClfCallEntity>,

  ) {
    super();
    this.queue = new Queue(this.handleQueueRow.bind(this), queueConfig);
    this.registerEventHandlers(this.queue);
  }

  private async handleQueueRow(row: OpenAISurveyAnswer, cb) {
    const queue = this.queueService.method('classification');
    const id = `${row.surv_surveyanswer_id}`;

    this.logger.debug(`Process started: ${id}`);
    await queue.update(id, { status: QueueStatus.INPROGRESS });
    let result = await this.processRow(row, row.labels).catch((e) => {
      this.processError(row, e, cb);
      return false;
    });
    if (!result) return;

    this.logger.debug(`Process finished: ${id}`, result);
    cb(null, result);
    queue.remove(id);
  }

  /**
   * Processes a row of data, including getting its classification
   * and writing the result to the CSV stream.
   * @param {any} row - The row of data to be processed.
   * @param {any[]} categories - The categories to be included in the classification.
   * @param {string} sentiment - The sentiment to be included in the classification.
   * @param {any} csvStream - The CSV stream to write the result to.
   * @return {Promise<any>} A promise resolved with the processed row's ID.
   */
  private async processRow(
    row: OpenAISurveyAnswer,
    categories: any[],
  ): Promise<any> {

    const strengths_question = row.question_pos || '';
    const strengths_answer = row.pos || '';
    const improvements_question = row.question_neg || '';
    const improvements_answer = row.neg || '';
    const id = row.surv_surveyanswer_id;
    const sentiment_enum = ['Positive', 'Neutral', 'Negative'];
    if (row.neutral !== 1) sentiment_enum.splice(1, 1); // Removing 'Neutral' from sentiment_enum

    const sentiment = sentiment_enum.join(', ');
    const surveyAnswer = await this.saRepo.findOne({
      where: { surv_surveyanswer_id: +id },

      relations: {
        customCategories: true,
        keywords: true,
      },
    });

    const companyId = surveyAnswer?.sec_company_id || 0;
    const activeAIModules = await this.settingsService.AIModules(companyId);

    const model = row.model || OPENAI_DEFAULT_MODEL;

    this.logger.debug(`processRow activeAIModules: ${id}`, {
      model,
      activeAIModules,
    });

    const { completion, usage } =
      await this.openAIClassificationService.getClassification({
        activeAIModules,
        categories,

        strengths_question,
        strengths_answer,
        improvements_question,
        improvements_answer,
        sentiment,
        sentiment_enum,
        model,
      });

    this.logger.debug(`parseResult: ${id}`, { model, row, completion });
    if (!completion) {
      throw new Error(`No result for row: ${id}`);
    }
    const answer = completion?.answer?.map((item) => ({
      ...item,
      category_id: item.sub_category_id || 0,
    }));
    const filtered = this.filterDuplicates(answer);
    const info = completion?.extra_info;
    const data: OpenAIClassificationData = {
      answer_id: id,
      classification: filtered,
      model,

      meta_data: {
        usage: {
          prompt_tokens: usage.prompt_tokens,
          completion_tokens: usage.completion_tokens,
          total_tokens: usage.total_tokens,
        },

        justification: info?.justification_details,
        overall_confidence: info?.overall_confidence,
        custom_categories: completion?.custom_categories,
        keywords: info?.keywords,
        contact_customer: info?.contact_customer,
        contact_customer_reason: info?.contact_customer_reason,
        contact_customer_email: info?.contact_customer_email,

      },
    };

    const { meta_data } = data;
    const custom_categories = meta_data.custom_categories || [];
    const keywords = meta_data.keywords || [];
    if (meta_data.contact_customer) {
      this.addContactCustomer(surveyAnswer, {
        contact_customer: meta_data.contact_customer,
        contact_customer_reason: meta_data.contact_customer_reason,
      });
    }
    await this.addCustomCategories(surveyAnswer, custom_categories);
    await this.addKeywords(surveyAnswer, keywords);
    await this.saRepo.save(surveyAnswer);

    await this.addMclassResponse(data);
    return row.surv_surveyanswer_id;
  }

  private async processError(row: OpenAISurveyAnswer, error, cb) {
    const id = `${row.surv_surveyanswer_id}`;
    this.logger.debug(`Process error: ${id}`, {

      row,
      error: error?.message,
      stack: error?.stack,
    });
    await sleep(2);
    cb(null);

    this.queueService.method('classification').update(id, {
      result: row as any,
      tryCount: row.tryCount,
      errors: error,
      status: QueueStatus.ERROR,
    });
    if (row.tryCount >= OPENAI_MAX_RETRIES_PER_ROW) {
      this.logger.error(
        `Classification error on row processing and skipped due max retries. Check additional_info column for details: ${id}`,

        { row, error },
      );
    } else {
      const tryCount = (row.tryCount || 0) + 1;
      let delay =
        OPENAI_SLEEP_TIME * (tryCount * OPENAI_RETRY_DELAY_MULTIPLIER);

      this.logger.debug(`sleeping ${delay} sec: ${id}`);
      await sleep(delay);

      row.tryCount = tryCount;
      this.logger.debug(`Pushed row to queue again: ${id}`, { row });

      this.queue.push(row);
    }
  }

  private async addMclassResponse(data: OpenAIClassificationData) {
    const { answer_id, classification, meta_data } = data;
    let surveyAnswer = await this.saRepo
      .createQueryBuilder()
      .where('surv_surveyanswer_id =:answer_id', { answer_id })
      .getOne();

    this.logger.debug(`addMclassResponse: ${answer_id}`, { surveyAnswer });

    if (!surveyAnswer) return;
    let labelIds = classification?.map((item) => item.category_id) || [];
    let currentLabels = labelIds.length
      ? await this.mcrRepo.find({
          where: { answer_id, item_id: In(labelIds) },
        })
      : [];
    if (!classification?.length && data.meta_data.justification?.length) {

      this.logger.debug(
        `addMclassResponse no classification: ${answer_id}, adidng only justification`,
      );

      await this.mcrRepo
        .createQueryBuilder()
        .insert()
        .values({
          record_status: MclassRecordStatus.AILabel,
          answer_id,
          created_on: new Date(),
          modification_date: new Date(),

          item_id: 0,
          is_excluded: 1,
          is_liked: 0,
          is_potential: 0,
          istype: 0,
          confidence: data.meta_data.overall_confidence,
        })
        .execute();
    }
    let deletions = [];
    for (let item of classification || []) {
      let currentLabel = currentLabels.find(
        (label) => label.item_id == item.category_id,
      );

      let recordStatus = currentLabel?.record_status;
      if (
        recordStatus == MclassRecordStatus.ManualLabel ||
        recordStatus == MclassRecordStatus.ManualRemoved
      ) {
        continue;
      } else if (recordStatus == MclassRecordStatus.AILabel) {
        deletions.push(currentLabel.mclass_response_id);
      }
      this.logger.debug(`addMclassResponse item: ${answer_id}`, {
        item,
        recordStatus,
        currentLabel,
      });
      await this.mcrRepo
        .createQueryBuilder()
        .insert()
        .values({
          record_status: MclassRecordStatus.AILabel,
          answer_id,
          created_on: new Date(),
          modification_date: new Date(),
          item_id: item.category_id,
          is_excluded: 0,
          is_liked: 0,
          is_potential: 0,
          istype: convertSentimentToNum(item.sentiment),

          confidence: item.confidence,
        })
        .execute();
    }



    await this.saAIRepo.save(
      this.saAIRepo.create({
        answer_id,
        ai_model: data.model,
        completion_tokens: meta_data.usage.completion_tokens,
        prompt_tokens: meta_data.usage.prompt_tokens,
        total_tokens: meta_data.usage.total_tokens,
        response_justification: meta_data.justification,
        response_confidence: meta_data.overall_confidence,
      }),
    );

    surveyAnswer.ai_last_try = new Date();
    surveyAnswer.ai_tries = (surveyAnswer.ai_tries || 0) + 1;
    await this.saRepo.save(surveyAnswer);
    if (deletions.length) {

      this.logger.debug(`addMclassResponse deletions: ${answer_id}`, {
        deletions,
      });

      await this.mcrRepo.delete({ mclass_response_id: In(deletions) });
    }
  }

  private async addContactCustomer(
    answer: SurveyAnswerEntity,
    data: Pick<
      OpenAIClassificationMetaData,
      'contact_customer' | 'contact_customer_reason'
    >,
  ) {
    let clfCall = await this.clfCallRepo.findOne({
      select: {
        clf_call_id: true,
      },
      where: {
        survey_answer: {
          surv_surveyanswer_id: answer.surv_surveyanswer_id,
        },

        sec_company_id: answer.sec_company_id,
      },
    });

    if (clfCall) {
      await this.clfCallRepo.update(
        {
          clf_call_id: clfCall.clf_call_id,
        },
        {
          contact_customer: +data.contact_customer,
          contact_customer_reason: data.contact_customer_reason,
        },
      );
    }
  }

  private async addCustomCategories(
    answer: SurveyAnswerEntity,
    categories: OpenAIClassificationCustomCategories[],
  ) {
    if (!answer) return;
    const { sec_company_id } = answer;
    categories = categories.reduce((acc, item) => {
      if (!acc.some((el) => el.category_name === item.category_name)) {
        acc.push(item);
      }
      return acc;
    }, []);
    answer.customCategories = [];
    let existsCategories = await this.customCategoryRepo.find({
      where: {
        item_label: In(categories.map((cat) => cat.category_name)),
        sec_company_id,
      },
    });
    for (let category of categories) {
      const checkExists = existsCategories.find(
        (cat) => cat.item_label === category.category_name,
      );
      if (checkExists) {
        answer.customCategories.push(checkExists);
        continue;
      }
      answer.customCategories.push(
        this.customCategoryRepo.create({
          item_label: category.category_name,
          sentiment: category.sentiment,
          sec_company_id,
          parent_label: null,
          created_on: new Date(),
        }),
      );
    }
    return answer;
  }

  private async addKeywords(
    answer: SurveyAnswerEntity,
    keywords: OpenAIClassificationKeywords[],
  ) {
    if (!answer) return;
    const { sec_company_id } = answer;
    keywords = keywords.reduce((acc, item) => {
      if (!acc.some((el) => el.keyword === item.keyword)) {
        acc.push(item);
      }
      return acc;
    }, []);
    answer.keywords = [];
    let existsKeywords = await this.keywordRepo.find({
      where: {
        keyword: In(keywords.map((cat) => cat.keyword)),
        sec_company_id,
      },
    });
    for (let keyword of keywords) {
      const checkExists = existsKeywords.find(
        (k) => k.keyword === keyword.keyword,
      );

      if (checkExists) {
        answer.keywords.push(checkExists);
        continue;
      }

      answer.keywords.push(
        this.keywordRepo.create({
          keyword: keyword.keyword,
          sentiment: keyword.sentiment,
          sec_company_id,
          created_on: new Date(),
        }),
      );
    }
    return answer;
  }

  filterDuplicates(classifications: any[]) {

    const set = new Set();
    if (!classifications?.length) return [];

    return classifications.filter((item) => {
      const key = `${item.parent_category}-${item.sub_category}-${item.sentiment}`;
      if (set.has(key)) return false;
      set.add(key);
      return true;
    });

  }
}