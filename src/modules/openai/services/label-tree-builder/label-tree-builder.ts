import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { parseJSON } from '@/shared/utils/';
import { OpenAIService } from '../../openai.service';
import ChatCompletion = OpenAI.ChatCompletion;
import { env } from '@/app.env';

/*
 NOTE: There seems to be an issue with functions and encoding utf-8 for gtp Gpt-4-1106-preview. This currently resolves in, in some cases, to special characters not being outputted correctly. You would see something like this: �
 See: https://community.openai.com/t/gpt-4-1106-preview-is-not-generating-utf-8/482839
 */
@Injectable()
export class LabelTreeBuilderService {
  constructor(private openAIService: OpenAIService) {}
  async createLabelTree() {
    const translate = (await this.openAIService.createChatCompletion({
      model: env.OPENAI_NEW_MODEL,
      temperature: 0.0,
      messages: [
        {
          role: 'system',
          content: `You are a domain expert and know all about the categories that belong in a certain branche. Your job is to provide a very good set of categories that we can use to do classification with sentiments.`,
        },
        {
          role: 'user',
          content: `I'm working on a project related to a construction company. 
                I need to create a comprehensive set of categories and subcategories for classification purposes. 
                The categories should be consistent and cover all aspects of this domain. 
                I already have some categories like [Medewerkers, Klantcontact], but I need to expand this list. 
                The categories should be suitable for [specific use, e.g., organizing a knowledge base, creating a tagging system for a content library, etc.]. 
                Can you help generate a structured list of categories and subcategories that are relevant to this domain and in Dutch NL language?`,
        },
        {
          role: 'user',
          content: `Return this JSON format:
                    {
                        parent_category: [{
                               name: // name of the parent category
                            sub_category:[{
                            name: // name of the sub category
                            }]
                            }
                        ]
                    }
                `,
        },
      ],
      response_format: { type: 'json_object' },
    })) as unknown as ChatCompletion;
    return parseJSON(translate.choices[0].message.content);
  }
}
