# AI Helpers Module

## Overview

This module contains the services that are used to communicate with the AI from openai. The services are used to send and receive data from the AI.
To make the AI aware of the data, currently from the NPS Verbatim, the AI is enriched with functions. These functions are defined in the [ai-function-definitions.service](./ai-function-definitions.service.ts).
These function definitions are send to openai in first request. If openai thinks it needs to call a function to work with the data or do a web request and so on, it will return the function name and arguments.
Next the [ai-perform-function.service](./ai-perform-function.service.ts) will be called to perform the function. This service will call the function that is defined in the [ai-function-definitions.service](./ai-function-definitions.service.ts) and return the result to openai.

There are 6 classed that are used to work with the AI:

- [ai-function-definitions.service](./ai-function-definitions.service.ts)
- [ai-perform-functions.service](./ai-perform-functions.service.ts)
- [function-execution.service](./function-execution.service.ts)
- [open-ai-chat-helper.service](./open-ai-chat-helper.service.ts)
- [prompt.service](./prompt.service.ts)
- [streaming.service](./streaming.service.ts)

## How to add a new AI Function Definition

The [ai-function-definitions.service](./ai-function-definitions.service.ts) is used to define the functions that are available for the AI.
If we need to improve the AI, we need to add or modify the functions to this service. The definitions are important for the AI to understand what it can expect from a particular function.
This is done by giving it a good description. The AI will look at this description and will pick the function that is most likely to be the correct one.
Next, if needed, you need to specify the arguments that are needed for the actual function. In this part of the definition it is also key to give a good description of the argument. The AI will
look at this description and tries to fill in the value based on the user input.
It is important to understand that the definitions are written as JSON Schema reference.
For more info about the functions from openai see [openai functions](https://platform.openai.com/docs/api-reference/chat/create#functions).
And for the JSON Schema see [JSON Schema](https://json-schema.org/understanding-json-schema/)

### Example

```jsonb
{
    "name": "find_top_N_labels",
    "description": `If the user wants to find the best or worst scoring label(s) withing a main category.`,
    "parameters": {
        "type": "object",
        "properties": {
            "mainLabel":{
                "type": "string",
                "description": `The main category, called label, the user wants to find the best scoring sub label for`,
            },
            "top":{
                "type": "integer",
                "description": `How many of the best scoring sub labels the user wants to find`,
            },
            "sortOrder ":{
                "enum": ["best", "worse"],
                "description": `Should the best or worse scoring sub labels be returned`,
            },
            "sortBy ":{
                "enum": ["nps", "responses"],
                "description": `Is the user searching for the best or worse scoring sub labels based on nps or responses`,
            },
            "main_or_sub_label":{
                "enum": ["main", "sub"],
                "description": `Is the user talking about a main or sub label`,
            }
        },
        "required": ["mainLabel", "main_or_sub_label"],
    },
}
```

## How to use AI Perform Functions

The perform functions service is used to actually perform the functions that are defined in the [ai-function-definitions.service](./ai-function-definitions.service.ts).
If you have added a new function to the [ai-function-definitions.service](./ai-function-definitions.service.ts) you need to add the function to the [ai-perform-functions.service](./ai-perform-functions.service.ts).
There are 4 important things to keep in mind when adding a new function:

- Add the new function to this function:

```typescript

                getAvailableFunctions() {
                    return {
                    positive_feedback: this.positiveFeedback.bind(this),
                    find_top_N_labels: this.findTopNLabels.bind(this),
                    find_top_historical_data_for_main_label: this.find_top_historical_data_for_main_label.bind(this),
                    find_top_historical_data_for_sub_label: this.find_top_historical_data_for_sub_label.bind(this),
                    get_all_main_labels: this.get_all_main_labels.bind(this),
                    get_all_sub_labels: this.get_all_sub_labels.bind(this),
                    your_function_name: this.your_function_name.bind(this), // <--- Add your function here
                    }
                }
```

- Make sure that the function name is the same as the one in the [ai-function-definitions.service](./ai-function-definitions.service.ts)
- Make sure that the function has the same arguments as the one in the [ai-function-definitions.service](./ai-function-definitions.service.ts)
- Add the actual function to this file so that the AI can perform the function and use async/await to make sure that the function is executed correctly:

```typescript
    async your_function_name(args: any) {
        // Your function code here
    }
```

## Prompt Service

The prompt service is used to create the inital prompt that is send to openai. This prompt is basically a system prompt to instruct the AI how we want it to behave.
It includes a system prompt, the history, a prompt that tells the AI the current date and a prompt that gives the AI the user question.

## Open AI Service

The open AI service is used to communicate with the openai API. It is used to send the prompt to openai and to receive the response from openai.
It has 2 functions:

- callOpenAI is used to send the prompt to openai
- handleResponse is used to handle the response from openai. It determines if the response is a function call or a normal response. It sends the response to the [function-execution.service](./function-execution.service.ts) if it is a function call.
  If it is a normal response it will send the response to the [streaming.service](./streaming.service.ts) to be streamed to the user through WebSockets.

## Function Execution Service

The function execution service is used to execute the functions that are defined in the [ai-function-definitions.service](./ai-function-definitions.service.ts).
It gathers the response of the first openai call, which has determined that a function needs to be called, and the arguments that are needed for the function. It will then lookup the available functions in the [ai-perform-functions.service](./ai-perform-functions.service.ts) and call the function with the arguments.
Then the result of that function is delegated to the streaming service. This adds everything to the prompt and sends it to openai again. Openai will then answer with a new response and it will stream that response to the user.

## Streaming Service

The streaming service is used to stream the response from openai to the user through WebSockets. It has 4 functions:

- private addExtraHelperPrompt, this is an extra prompt that informs the AI to output the response in a nicely formatted way.
- private callOpenAI, this calls the openai api with a specific payload
- streamResponse, this calls the addExtraHelperPrompt and callOpenAI functions. It then listens for the data callback from openai and handles what to retrun to the stream. It will return [DONE] when it is finished streaming the response.
- sendToGateway, calls the gateway service which will send the response to the user through WebSockets.
