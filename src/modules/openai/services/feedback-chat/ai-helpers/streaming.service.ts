import { GatewayService } from '../../../../gateway/gateway.service';
import OpenAI from 'openai';
import { env } from '@/app.env';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});
const MODEL_NAME = env.OPENAI_NEW_MODEL;

/**
 * This service is responsible for streaming the response to the client.
 * It will first do a call to the OpenAI API and then stream the response to the client.
 * It calls the GatewayService to send the response to the client.
 */
export class StreamingService {
  constructor(private gatewayService: GatewayService) {}

  private addExtraHelperPrompt(prompt: any) {
    prompt.push({
      role: 'user',
      content: `
                Before you return the answer, Please do the html formatting, if you can, of the content so that the answer is nicely formatted and better readable.
                Use html tag like <b>, <br>, <p> and so on to format the content.
                Only return the answer in the html format and DO NOT add a text that you are formatting the text. Do not include any explanation or mention HTML formatting and return the answer in HTML and nothing more.`,
    });
    prompt.push({
      role: 'user',
      content: `Don't just return a list of items if the user didn't ask for that. Make sure you understand the question, else ask for clarification and return the answer in the same language as the user question.`,
    });
    return prompt;
  }
  private async callOpenAI(prompt: any) {
    return await openai.chat.completions.create(
      {
        model: MODEL_NAME,
        messages: prompt,
        stream: true,
      },
      { responseType: 'stream' } as any,
    );
  }
  async streamResponse(prompt: any, clientId: string) {
    // prompt = this.addExtraHelperPrompt(prompt); // Add extra helper prompt
    // const responseStream = await this.callOpenAI(prompt); // Call OpenAI API that streams the response
    // responseStream.toReadableStream().on('data', (chunk) => {
    //   // Handle response stream
    //   const lines = chunk
    //     .toString()
    //     .split('\n')
    //     .filter((line) => line.trim() !== '');
    //   for (const line of lines) {
    //     const message = line.replace(/^data: /, '');
    //     // Handle '[DONE]' message
    //     if (message === '[DONE]') {
    //       this.sendToGateway('[DONE]', clientId);
    //       return { done: true };
    //     }
    //     // Parse JSON and handle errors
    //     let parsed;
    //     try {
    //       parsed = parseJSON(message);
    //     } catch (error) {
    //       console.error('Could not JSON parse stream message');
    //       continue;
    //     }
    //     // Handle 'stop' finish reason
    //     if (parsed?.choices[0]?.finish_reason === 'stop') {
    //       this.sendToGateway('[DONE]', clientId);
    //       return { done: true };
    //     }
    //     // Send content if available
    //     if (parsed?.choices[0]?.delta?.content !== undefined) {
    //       this.sendToGateway(parsed.choices[0].delta.content, clientId);
    //     }
    //   }
    // });
  }

  sendToGateway(message: string, clientId: string) {
    this.gatewayService.sendEvent(message, clientId);
  }
}
