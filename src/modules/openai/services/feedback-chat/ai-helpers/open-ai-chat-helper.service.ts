import { StreamingService } from './streaming.service';
import { AiFunctionDefinitionsService } from './ai-function-definitions.service';
import { FunctionExecutionService } from './function-execution.service';
import { OpenAIService } from '../../../openai.service';
import { env } from '@/app.env';

const MODEL_NAME = env.OPENAI_NEW_MODEL;

/**
 * This service is responsible for calling the OpenAI API and handling the response.
 * It also calls the FunctionExecutionService if the response contains a function call.
 * Otherwise, it streams the response to the client through the StreamingService.
 */
export class OpenAIChatHelperService {
  constructor(
    private openAIService: OpenAIService,
    private streamingService: StreamingService,
    private aiFunctionDefinitionsService: AiFunctionDefinitionsService,
    private functionExecutionService: FunctionExecutionService,
  ) {}

  /**
   * This method calls the OpenAI API and adds the functions AI get access to.
   * It will use the AI function definitions from the AiFunctionDefinitionsService to inform openai about the available functions.
   * @params prompt: ChatCompletionRequestMessage[] - An array of messages that are added to the AI.
   */
  async callOpenAI(prompt: any[]) {
    return await this.openAIService.createChatCompletion({
      model: MODEL_NAME,
      messages: prompt,
      functions: this.aiFunctionDefinitionsService.getDefinitions(),
      function_call: 'auto',
    } as any);
  }

  /**
   * This method handles the response from the OpenAI API.
   * If the response contains a function call, then it calls the FunctionExecutionService.
   * Otherwise, it streams the response to the client through the StreamingService.
   * @param response
   * @param prompt
   * @param clientId
   */
  async handleResponse(response: any, prompt: any[], clientId: string) {
    const responseMessage = response?.data?.choices[0]?.message;
    if (
      responseMessage &&
      response?.data?.choices[0]?.finish_reason !== 'function_call'
    ) {
      await this.streamingService.streamResponse(prompt, clientId);
    }
    if (response?.data?.choices[0]?.finish_reason === 'function_call') {
      await this.functionExecutionService.callFunctionAndStream(
        response,
        prompt,
        clientId,
      );
    }
  }
}
