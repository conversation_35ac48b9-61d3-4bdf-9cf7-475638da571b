import { Injectable } from '@nestjs/common';

/**
 * This service is responsible for preparing the prompt that is added to the AI.
 */
export class PromptService {
    /**
     * This method prepares the initial prompt that is added to the AI.
     * @param body
     */
    initialPrompt(body: {question: string, history: any}) {
        let prompt: any[] = [
            {"role": "system", "content": `
                You are a data analyst that can explain data in a very easy to understanding way. Respond in a friendly and helpful tone, with concise answers. The question can be in a different language then English.
                                                
                Instructions: 
                - Make sure to check the functions that are available to you first.
                - Make sure to ask the user relevant follow-up questions if needed. If you need to call a function to get information, then stick to the information when writing the final answer. 
                - Make sure to also check the history of the conversation. In the conversation_history you can find topics that the user maybe referring to.
                - Don't make assumptions about what values to plug into functions, if the user does not provide any of the required parameters then you must need to ask for clarification.
                - If a user request is ambiguous, then also you need to ask for clarification.
                - Always look to see if the user is referring to something in the conversation history.
                
                Follow the instructions carefully while processing the request.`
            }
        ];
        const history = body.history.slice(Math.max(body.history.length - 80, 0));
              history.forEach((item) => { if(item.content !== undefined && item.content !== null && item.content !== '') prompt.push(item); }); // add history to prompt if it is not empty

       prompt.push({"role": 'user', "content": `Here is the current date: ${new Date()}`});
       prompt.push({"role": 'user', "content": `Once you have all the information you need, then you can call the function and return the answer to the user.`});
       prompt.push({"role": 'user', "content": `user question: ${body.question}`});
       return prompt;
    }
}
