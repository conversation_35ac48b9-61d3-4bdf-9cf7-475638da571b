import {StreamingService} from "./streaming.service";
import {AiPerformFunctionsService} from "./ai-perform-functions.service";

/**
 * This service is responsible for handling the execution of functions that are called by the AI.
 * It calls the PerformAiFunctionsService to get the available functions and then calls the function that is called by the AI.
 */
export class FunctionExecutionService {
    constructor(private streamingService: StreamingService, private performAiFunctionsService: AiPerformFunctionsService) {}

    /**
     * This method calls the function that is called by the AI and streams the response to the client through the StreamingService.
     * It uses the PerformAiFunctionsService to get the available functions. And then calls the function that is called by the AI.
     * @param response
     * @param prompt
     * @param clientId
     */
    async callFunctionAndStream(response: any, prompt: any[], clientId: string) {
        const availableFunctions = this.performAiFunctionsService.getAvailableFunctions();
        const functionName       = response.data.choices[0].message.function_call.name;
        const functionToCall     = availableFunctions[functionName].bind(this);
        const functionArgs       = JSON.parse(response.data.choices[0].message.function_call.arguments);
        const functionResponse   = await functionToCall(...Object.values(functionArgs));
        prompt.push(response.data.choices[0].message);
        prompt.push({
            "role": "function",
            "name": functionName,
            "content": functionResponse,
        } as any);
        await this.streamingService.streamResponse(prompt, clientId);
    }
}
