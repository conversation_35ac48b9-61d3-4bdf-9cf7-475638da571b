import {Injectable} from "@nestjs/common";

/**
 * Returns the definitions of the functions that can be called by the AI
 */
@Injectable()
export class AiFunctionDefinitionsService {

    /**
      * Returns the definitions of the functions that can be called by the AI
   */
   getDefinitions() {
        return [
            {
                "name": "positive_feedback",
                "description": "Use this if the user wants to know any thing related to positive feedback or how to handle it. Information about what to do with positive feedback. This will get the information from a webpage. Do not use this when the user asks anything related to data.",
                "parameters": {
                    "type": "object",
                    "properties": {

                    },
                    "required": [""],
                },
            },
            {
                "name": "find_top_N_labels",
                "description": `If the user wants to find the best or worst scoring label(s) withing a main category.`,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "mainLabel":{
                            "type": "string",
                            "description": `The main category, called label, the user wants to find the best scoring sub label for`,
                        },
                        "top":{
                            "type": "integer",
                            "description": `How many of the best scoring sub labels the user wants to find`,
                        },
                        "sortOrder ":{
                            "enum": ["best", "worse"],
                            "description": `Should the best or worse scoring sub labels be returned`,
                        },
                        "sortBy ":{
                            "enum": ["nps", "responses"],
                            "description": `Is the user searching for the best or worse scoring sub labels based on nps or responses`,
                        },
                        "main_or_sub_label":{
                            "enum": ["main", "sub"],
                            "description": `Is the user talking about a main or sub label`,
                        }
                    },
                    "required": ["mainLabel", "main_or_sub_label"],
                },
            },
            {
                "name": "find_top_historical_data_for_main_label",
                "description": `Use this function to get the historical data for a main label. If user also mentions a sub label in the question then do not use this function!`,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "mainLabel":{
                            "type": "string",
                            "description": `The main category, called label in the data`,
                        },
                        "from_date":{
                            "type": "string",
                            "description": `From which date to get the data. Use FORMAT: 'YYYY-MM-DD'. If no date is provided the function will get the data from the first date in the data set`,
                        },
                        "to_date":{
                            "type": "string",
                            "description": `To which date to get the data. Use FORMAT: 'YYYY-MM-DD'. If no date is provided the function will get the data from the last date in the data set`,
                        }
                    },
                    "required": ["mainLabel", "from_date", "to_date"],
                },
            },
            {
                "name": "find_top_historical_data_for_sub_label",
                "description": `Use this function to get the historical data for a sub label. To get this we need a main and sublabel. \n\n Ask the user questions in the following way if it is not clear what arguments to use for the function:`,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "mainLabel":{
                            "type": "string",
                            "description": `The main category or main label, called label in the data`,
                        },"subLabel":{
                            "type": "string",
                            "description": `The sub category or sub label, called label in the data`,
                        },
                        "from_date":{
                            "type": "string",
                            "description": `From which date to get the data. If no date is provided the function will get the data from the first date in the data set`,
                        },
                        "to_date":{
                            "type": "string",
                            "description": `To which date to get the data. If no date is provided the function will get the data from the last date in the data set`,
                        }
                    },
                    "required": ["mainLabel", "from_date", "to_date"],
                },
            },
            {
                "name": "get_all_main_labels",
                "description": `Use this function to get all the main labels. \n\n Ask the user questions in the following way if it is not clear what arguments to use for the function:`,
                "parameters": {
                    "type": "object",
                    "properties": {

                    },
                    "required": [],
                },
            },
            {
                "name": "get_all_sub_labels",
                "description": `Use this function to get all the main labels. \n\n Ask the user questions in the following way if it is not clear what arguments to use for the function:`,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "mainLabel":{
                            "type": "string",
                            "description": `The main category or main label, called label in the data`,
                        }
                    },
                    "required": ["mainLabel"],
                },
            }
        ];
   }

}
