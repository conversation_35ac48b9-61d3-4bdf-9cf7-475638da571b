import {Injectable} from "@nestjs/common";
import {ScraperService} from "../../../../web-scraper/scraper.service";

/**
 * This service is responsible for performing the functions that are called by the AI.
 * It has a method for each function that can be called by the AI And this can be extended with more functions.
 * To extend this with more functions, make sure to define the function in the getAvailableFunctions method and also add the function definition to the AiFunctionDefinitionsService.
 */
export class AiPerformFunctionsService {
    private data?: any []

    constructor(private scraperService: ScraperService) {}

    setData(data: any []) {
        this.data = data
    }

    getData() {
        return this.data
    }

    /**
     * This method returns the functions that can be called by the AI.
     * Note: the names of the functions should match the names of the functions in the AiFunctionDefinitionsService.
     */
    getAvailableFunctions() {
        return {
            positive_feedback: this.positiveFeedback.bind(this),
            find_top_N_labels: this.findTopNLabels.bind(this),
            find_top_historical_data_for_main_label: this.find_top_historical_data_for_main_label.bind(this),
            find_top_historical_data_for_sub_label: this.find_top_historical_data_for_sub_label.bind(this),
            get_all_main_labels: this.get_all_main_labels.bind(this),
            get_all_sub_labels: this.get_all_sub_labels.bind(this)
        }
    }

    async positiveFeedback() {
        const that = this;
        try {
            const html = await that.scraperService.scrape('https://www.focusfeedback.nl/positieve-feedback/')
            return JSON.stringify(html)
        } catch (e) {
            console.log('e', e)
            return 'error'
        }
    }

    async findTopNLabels(mainLabel, top = 1, sortOrder = 'best', sortBy = 'nps', main_or_sub_label = 'main') {
        if (!mainLabel || typeof mainLabel === "number") return 'No main label provided';
        const labels = [];

        function traverseChildren(children) {
            for (const child of children) {
                const { data, children, ...rest } = child;  // Omit the 'data' and 'children' properties
                if (rest[sortBy] !== undefined) {  // Check if sortBy exists in rest
                    labels.push({ ...rest, value: rest[sortBy] });  // Add the value for sorting
                }
                if (child.children) {
                    traverseChildren(child.children);
                }
            }
        }

        const mainLabelData = this.data.find(item => this.partialWordMatch(item.label, mainLabel));
        if (!mainLabelData) {
            return `No labels found for: ${mainLabel}`;
        }

        if (mainLabelData.children) {
            traverseChildren(mainLabelData.children);
        }

        // Sort based on sortOrder and sortBy
        labels.sort((a, b) => sortOrder === 'best' ? b.value - a.value : a.value - b.value);

        // Take top N labels
        const topLabels = labels.slice(0, top);

        // Remove the temporary 'value' property used for sorting
        const cleanedTopLabels = topLabels.map(({ value, ...rest }) => rest);

        return cleanedTopLabels.length > 0 ? `Here are the top scoring labels.${JSON.stringify(cleanedTopLabels)}` : `No labels found for: ${mainLabel}`;
    }
    partialWordMatch(str, search) {
        const regex = new RegExp(`\\b${search.toLowerCase().replace(/\s+/g, '\\s+')}\\b`);
        return regex.test(str.toLowerCase());
    }

    async find_top_historical_data_for_main_label(mainLabel, from_date, to_date) {
        if (!mainLabel) return "No label provided";

        let fromDate, toDate;

        const mainLabelData = this.data.find(item => this.partialWordMatch(item.label, mainLabel));
        if (!mainLabelData) {
            return `No labels found for: ${mainLabel}`;
        }

        if (!from_date || !to_date) {
            const dateArray = mainLabelData.data.map(entry => new Date(entry.date));
            dateArray.sort((a, b) => a - b);
            fromDate = from_date ? new Date(from_date) : dateArray[0];
            toDate = to_date ? new Date(to_date) : dateArray[dateArray.length - 1];
        } else {
            fromDate = new Date(from_date);
            toDate = new Date(to_date);
        }

        let historicalData = [];

        if (mainLabelData && mainLabelData.data) {
            historicalData = mainLabelData.data.filter(entry => {
                const entryDate = new Date(entry.date);
                return entryDate >= fromDate && entryDate <= toDate;
            });
        }
        return historicalData.length > 0 ? JSON.stringify(historicalData) : `No historical data found for: ${mainLabel}`;
    }

    async find_top_historical_data_for_sub_label(mainLabel, subLabel, from_date, to_date) {
        if (!mainLabel || !subLabel) return "Main label or sub-label not provided";

        let fromDate, toDate;

        const mainLabelData = this.data.find(item => this.partialWordMatch(item.label, mainLabel));
        if (!mainLabelData || !mainLabelData.children) {
            return `No labels found for: ${mainLabel}`;
        }

        const subLabelData = mainLabelData.children.find(item => this.partialWordMatch(item.label, subLabel));

        if (!subLabelData) {
            return `No sub-labels found for: ${subLabel}`;
        }

        if (!from_date || !to_date) {
            const dateArray = subLabelData.data.map(entry => new Date(entry.date));
            dateArray.sort((a, b) => a - b);
            fromDate = from_date ? new Date(from_date) : dateArray[0];
            toDate = to_date ? new Date(to_date) : dateArray[dateArray.length - 1];
        } else {
            fromDate = new Date(from_date);
            toDate = new Date(to_date);
        }

        let historicalData = [];

        if (subLabelData && subLabelData.data) {
            historicalData = subLabelData.data.filter(entry => {
                const entryDate = new Date(entry.date);
                return entryDate >= fromDate && entryDate <= toDate;
            });
        }
        return historicalData.length > 0 ? JSON.stringify(historicalData) : `No historical data found for: ${subLabel}`;
    }

    async get_all_main_labels() {
        const mainLabels = this.data.map(item => {
            const { data, children, ...rest } = item; // Destructure to omit 'data' and 'children'
            return rest; // Return the remaining properties
        });
        return JSON.stringify(mainLabels); // Convert to JSON string if needed
    }

    async get_all_sub_labels(mainLabel: string) {
        // Find the main label first
        const mainLabelData = this.data.find(item => this.partialWordMatch(item.label, mainLabel));

        if (!mainLabelData || !mainLabelData.children) {
            return `No labels found for: ${mainLabel}`;
        }

        // Map over the children to remove 'data' and 'children' properties
        const subLabels = mainLabelData.children.map(item => {
            const { data, children, ...rest } = item;
            return rest;
        });

        return JSON.stringify(subLabels); // Convert to JSON string if needed
    }
}
