import { Injectable } from '@nestjs/common';
import { GatewayService } from '../../../gateway/gateway.service';
import { ScraperService } from '../../../web-scraper/scraper.service';
import {
  AiFunctionDefinitionsService,
  FunctionExecutionService,
  OpenAIChatHelperService,
  AiPerformFunctionsService,
  PromptService,
  StreamingService,
} from './ai-helpers';
import { OpenAIService } from '../../openai.service';

/**
 * Get answers from <PERSON> and stream them to the client
 */
@Injectable()
export class FeedbackChatService {
  constructor(
    private openAIService: OpenAIService,
    private gatewayService: GatewayService,
    private scraperService: ScraperService,
    private aiFunctions: AiFunctionDefinitionsService,
  ) {}

  /**
   * Get answers from <PERSON> and stream them to the client
   * @param body
   */
  async answerFromAlbert(body: {
    question: string;
    history: any;
    clientId: string;
    data: any;
  }) {
    const performAiFunctionsService = new AiPerformFunctionsService(
      this.scraperService,
    );

    const promptService = new PromptService();
    const streamingService = new StreamingService(this.gatewayService);
    const functionExecutionService = new FunctionExecutionService(
      streamingService,
      performAiFunctionsService,
    );
    const openAIChatHelperService = new OpenAIChatHelperService(
      this.openAIService,
      streamingService,
      this.aiFunctions,
      functionExecutionService,
    );

    try {
      performAiFunctionsService.setData(body.data);
      const prompt = promptService.initialPrompt(body);
      const response = await openAIChatHelperService.callOpenAI(prompt);
      await openAIChatHelperService.handleResponse(
        response,
        prompt,
        body.clientId,
      );
    } catch (error) {
      console.log('error', error);
    }
  }
}
