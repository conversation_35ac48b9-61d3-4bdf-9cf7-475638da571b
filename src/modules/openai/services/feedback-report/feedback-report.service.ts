import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { parseJ<PERSON><PERSON> } from '../../../../shared/utils/parseJSON';
import { OpenAIService } from '../../openai.service';
import ChatCompletion = OpenAI.ChatCompletion;
import { env } from '@/app.env';

/*
 NOTE: There seems to be an issue with functions and encoding utf-8 for gtp Gpt-4-1106-preview. This currently resolves in, in some cases, to special characters not being outputted correctly. You would see something like this: �
 See: https://community.openai.com/t/gpt-4-1106-preview-is-not-generating-utf-8/482839
 */
@Injectable()
export class FeedbackReportService {
  constructor(private openAIService: OpenAIService) {}

  async createReport(data: any) {
    const report = (await this.openAIService.createChatCompletion({
      model: env.OPENAI_NEW_MODEL,
      temperature: 0.0,
      messages: [
        {
          role: 'system',
          content: `You are a professional data analyst that writes reports about data`,
        },
        {
          role: 'user',
          content: `Create a report, in Dutch language, that contains the following: Strong Points, Improvements, Chance of innovation`,
        },
        {
          role: 'user',
          content: `In the data you will find the following properties: f stands for feedback, p stands for positiveFeedbackCount, t stands for neutralFeedbackCount, n stands for negativeFeedbackCount`,
        },
        {
          role: 'user',
          content: `Here is the data to analyse: ${JSON.stringify(data)}`,
        },
      ],
    })) as unknown as ChatCompletion;
    console.log('report', report.choices[0].message);
    console.log('usage', report.usage);
    return report.choices[0].message.content;
  }
}
