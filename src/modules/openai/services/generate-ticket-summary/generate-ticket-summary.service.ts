import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { parseJSON } from '../../../../shared/utils/parseJSON';
import { OpenAIService } from '../../openai.service';
import ChatCompletion = OpenAI.ChatCompletion;

@Injectable()
export class GenerateTicketSummaryService {
  constructor(private openAIService: OpenAIService) {}

  async run(strengths: string, improvements: string) {
    const translate = (await this.openAIService.createChatCompletion({
      model: 'gpt-4o-mini',
      temperature: 0.0,
      messages: [
        {
          role: 'system',
          content: `You need to create summary of the ticket based on the strengths and improvements. 
          The summary should be a short description of the ticket. 
          Never return the [strength-text] or [improvement-text]. These are meant to show you where the text starts and ends.
          It should be in the same language as the ticket. 
          It should be in the same format as the ticket. 
          It should be in the same style as the ticket.
          Summary should have a maximum of 10 words.
          Always return the following JSON format:{summary: "the summary of the ticket"}.`,
        },
        {
          role: 'user',
          content: `[strength-text]${strengths}[/strength-text]`,
        },
        {
          role: 'user',
          content: `[improvement-text]${improvements}[/improvement-text]`,
        }
      ],
      response_format: { type: 'json_object' }
    })) as unknown as ChatCompletion;
    return parseJSON(translate.choices[0].message.content);
  }
}
