import {Category, WeaviateAddObject} from "../../openai.interface";

type OptionsFlags<Type> = {
    [Property in keyof Type]: boolean;
};
type Features = {
    darkMode: () => void;
    newUserProfile: () => void;
};

type FeatureOptions = OptionsFlags<Features>;

export const categories3: Category[] = [
    {
        "parent_category": "Faciliteiten",
        // "parent_category_description": "Deze categorie beoordeelt de aanwezigheid en kwaliteit van de faciliteiten die beschikbaar zijn in de sportschool. Dit kan apparatuur, kleedkamers, douches en andere voorzieningen omvatten.",
        "sub_categories": [
            {
                "name": "Apparatuur",
                // "sub_category_description": "Deze subcategorie beoordeelt de beschikbaarheid, kwaliteit en diversiteit van de fitnessapparatuur in de sportschool."
            },{
                "name": "Kleedkamers",
                // "sub_category_description": "Hoe schoon en goed onderhouden zijn de kleedkamers? Zijn er voldoende kluisjes?"
            },{
                "name": "Douches",
                // "sub_category_description": "Zijn de douchefaciliteiten schoon en goed functionerend? Is er voldoende ruimte en privacy?"
            }
        ]
    },
    {
        "parent_category": "Lessen en Programma's",
        // "parent_category_description": "Deze categorie heeft betrekking op de aangeboden fitnesslessen en programma's in de sportschool. Het kan variëren van groepslessen tot individuele trainingsprogramma's.",
        "sub_categories": [
            {
                "name": "Groepslessen",
                // "sub_category_description": "Deze subcategorie beoordeelt de verscheidenheid en kwaliteit van groepslessen die de sportschool aanbiedt."
            },{
                "name": "Persoonlijke Training",
                // "sub_category_description": "Hoe zijn de individuele trainingsprogramma's? Zijn er gecertificeerde personal trainers beschikbaar?"
            },{
                "name": "Specialiteit Programma's",
                // "sub_category_description": "Zijn er programma's voor specifieke doelen of doelgroepen, zoals gewichtsverlies, senioren, of prenatale fitness?"
            }
        ]
    },
    {
        "parent_category": "Klantenservice",
        // "parent_category_description": "Deze categorie beoordeelt de service van het personeel en de algehele klantenservice-ervaring.",
        "sub_categories": [
            {
                "name": "Personeel",
                // "sub_category_description": "Deze subcategorie beoordeelt de behulpzaamheid, professionaliteit en kennis van het personeel."
            },{
                "name": "Ervaring",
                // "sub_category_description": "Hoe is de algehele klantenservice-ervaring? Hoe worden problemen of klachten afgehandeld?"
            }
        ]
    },
    {
        "parent_category": "Prijs",
        // "parent_category_description": "Deze categorie beoordeelt de kosten van lidmaatschap en andere diensten in de sportschool. Het beoordeelt de waarde die klanten krijgen voor hun geld.",
        "sub_categories": [
            {
                "name": "Lidmaatschap",
                // "sub_category_description": "Deze subcategorie beoordeelt de kosten van het basislidmaatschap en de waarde die het biedt."
            },{
                "name": "Extra Diensten",
                // "sub_category_description": "Hoeveel kosten extra diensten zoals personal training, speciale klassen of andere voorzieningen? Bieden ze een goede waarde?"
            }
        ]
    },
    {
        "parent_category": "Veiligheid en Hygiëne",
        // "parent_category_description": "Deze categorie beoordeelt de maatregelen die de sportschool neemt om de veiligheid en hygiëne van de faciliteiten en apparatuur te waarborgen.",
        "sub_categories": [
            {
                "name": "Schoonmaak",
                // "sub_category_description": "Deze subcategorie beoordeelt de netheid van de sportschool en de frequentie waarmee de faciliteiten en apparatuur worden schoongemaakt."
            },{
                "name": "Veiligheidsmaatregelen",
                // "sub_category_description": "Hoe zijn de veiligheidsmaatregelen van de sportschool? Zijn er eerstehulpkits en defibrillatoren beschikbaar? Is het personeel getraind in eerste hulp?"
            }
        ]
    },
    {
        "parent_category": "Openingstijden",
        // "parent_category_description": "Deze categorie beoordeelt de openingstijden van de sportschool en de flexibiliteit die ze bieden aan de leden.",
        "sub_categories": [
            {
                "name": "Weekdagen",
                // "sub_category_description": "Deze subcategorie beoordeelt de openingstijden van de sportschool op weekdagen."
            },{
                "name": "Weekend",
                // "sub_category_description": "Hoe zijn de openingstijden van de sportschool in het weekend? Bieden ze flexibiliteit voor mensen die in het weekend willen trainen?"
            }
        ]
    },
    {
        "parent_category": "Atmosfeer",
        // "parent_category_description": "Deze categorie evalueert de algemene sfeer en omgeving van de sportschool. Het kan elementen zoals muziek, decoratie, en de houding van andere leden omvatten.",
        "sub_categories": [
            {
                "name": "Muziek",
                // "sub_category_description": "Deze subcategorie beoordeelt de muziek die in de sportschool wordt gespeeld. Is het motiverend? Is het volume geschikt?"
            },
            {
                "name": "Decoratie en Verlichting",
                // "sub_category_description": "Hoe is de inrichting van de sportschool? Draagt het bij aan een aangename trainingsomgeving? Is de verlichting voldoende?"
            },
            {
                "name": "Sociale Omgeving",
                // "sub_category_description": "Beoordeelt de houding en het gedrag van andere leden. Is de sportschool vriendelijk en uitnodigend?"
            },
            {
                "name": "Persoonlijke Sfeer",
                // "sub_category_description": "Hoe is de algemene sfeer in de sportschool? Is het motiverend? Is het persoonlijk? Is het een goede plek om te trainen?"
            }
        ]
    },
    {
        "parent_category": "Online Services",
        // "parent_category_description": "Deze categorie beoordeelt de online diensten aangeboden door de sportschool, zoals boekingssystemen, online lessen en mobiele apps.",
        "sub_categories": [
            {
                "name": "Online Boeking",
                // "sub_category_description": "Deze subcategorie beoordeelt het gemak en de functionaliteit van het online boekingssysteem van de sportschool."
            },
            {
                "name": "Online Lessen",
                // "sub_category_description": "Biedt de sportschool online lessen aan? Zo ja, hoe is de kwaliteit en variëteit?"
            },
            {
                "name": "Mobiele App",
                // "sub_category_description": "Heeft de sportschool een mobiele app? Zo ja, hoe nuttig is het? Hoe gebruiksvriendelijk?"
            }
        ]
    }
]

export const categories2: Category[] = [
    {
        "parent_category": "Algemeen",
        // "parent_category_description": "Deze categorie wordZt gebruikt voor algemene aspecten en kenmerken van de sportschool die niet specifiek passen in de andere categorieën. Het gaat om aspecten zoals de sfeer, de muziek, en de grootte van de sportschool.",
        "sub_categories": [
            {
                "name": "Sfeer",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de algehele sfeer van de sportschool te beoordelen, waaronder de energie, het gevoel van gemeenschap en de algemene stemming van de sportschool."
            },
            {
                "name": "Muziek",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de muziek in de sportschool te beoordelen, zoals het volume, de genrekeuze, en de invloed op de trainingservaring."
            },
            {
                "name": "Temperatuur",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de temperatuurcontrole in de sportschool te beoordelen, zoals de efficiëntie van de airconditioning en verwarming en de algemene comfort van de temperatuur tijdens het sporten."
            },
            {
                "name": "Drukte",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de drukte van de sportschool te beoordelen, zoals de menigte tijdens piekuren, de beschikbaarheid van apparatuur, en de wachttijden."
            },
            {
                "name": "Openingstijden",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de openingstijden van de sportschool te beoordelen, zoals de toegankelijkheid buiten standaard uren en het gemak van de sportschool schema voor de levensstijl van de gebruiker."
            },
            {
                "name": "Grootte",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de grootte van de sportschool te beoordelen, zoals de ruimtelijkheid, de indeling en hoe deze aspecten invloed hebben op de trainingservaring."
            },
            {
                "name": "Hygiëne",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de netheid en hygiëne van de sportschool te beoordelen, waaronder de schoonmaakpraktijken, de netheid van de apparatuur, en de beschikbaarheid van sanitaire voorzieningen."
            }
        ]
    },
    {
        "parent_category": "Begeleiding",
        // "parent_category_description": "Deze categorie wordt gebruikt om aspecten en kenmerken van de begeleiding en instructie die door de sportschool wordt geboden, te beoordelen. Dit omvat factoren zoals de kwaliteit van de instructie, de persoonlijkheid van de trainers en de mate van motivatie die zij bieden.",
        "sub_categories": [
            {
                "name": "Kwaliteit",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de algemene kwaliteit van de begeleiding en instructie die door de sportschool wordt gegeven, te beoordelen. Dit omvat de competentie van de trainers, hun kennisniveau en hun vermogen om effectieve training te geven."
            },
            {
                "name": "Persoonlijkheid",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de persoonlijkheid en het gedrag van de trainers te beoordelen. Dit omvat hun professionaliteit, hun vermogen om positieve relaties te onderhouden en hun vermogen om een motiverende en gastvrije omgeving te creëren."
            },
            {
                "name": "Motiverend",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om te beoordelen hoe motiverend de trainers en het begeleidend personeel zijn. Dit kan hun vermogen omvatten om klanten te inspireren, te motiveren en te stimuleren om hun fitnessdoelen te bereiken."
            },
            {
                "name": "Instructies",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om te beoordelen hoe duidelijk en nuttig de instructies van de trainers zijn. Dit omvat hun vermogen om oefeningen uit te leggen, technische begeleiding te geven en feedback te geven over de vorm en prestaties."
            },
            {
                "name": "Trainingsschema",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit en effectiviteit van de trainingsschema's die door de sportschool of trainers worden gegeven, te beoordelen. Dit kan de aanpassing aan individuele fitnessdoelen, de variatie in de training en de algemene logica en structuur van de schema's omvatten."
            },
            {
                "name": "Voedingsschema",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit en relevantie van de voedingsschema's die door de sportschool of trainers worden gegeven, te beoordelen. Dit kan de mate waarin de schema's zijn afgestemd op individuele doelen en dieetbehoeften omvatten."
            },
            {
                "name": "Personal training",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om specifieke aspecten van personal training te beoordelen. Dit kan de kwaliteit van één-op-één instructie, de effectiviteit van de trainingssessies en de mate van personalisatie omvatten."
            }
        ]
    },
    {
        "parent_category": "Intake",
        // "parent_category_description": "Deze categorie wordt gebruikt om de aspecten en kenmerken van de intakeprocedure bij de sportschool te beoordelen. Dit kan kwaliteit, persoonlijke aandacht, en duur van de intake omvatten.",
        "sub_categories": [
            {
                "name": "Kwaliteit",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de algehele kwaliteit van de intakeprocedure te beoordelen. Dit kan zaken omvatten zoals de grondigheid van de beoordeling, de relevantie van de vragen, en de nuttigheid van de gegeven informatie."
            },
            {
                "name": "Persoonlijk",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om te beoordelen hoe persoonlijk de intakeprocedure is. Dit kan betrekking hebben op hoe goed de procedure is afgestemd op de individuele behoeften en doelen van de klant."
            },
            {
                "name": "Tijdsduur",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de duur van de intakeprocedure te beoordelen. Dit kan betrekking hebben op of de procedure als te lang, te kort of juist adequaat wordt ervaren."
            },
            {
                "name": "Aandacht",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om te beoordelen hoeveel aandacht de klant krijgt tijdens de intakeprocedure. Dit kan zaken als betrokkenheid en interesse van het personeel omvatten."
            }
        ]
    },
    {
        "parent_category": "Medewerkers",
        // "parent_category_description": "Deze categorie wordt gebruikt om aspecten en kenmerken van het personeel van de sportschool te beoordelen. Dit kan hun vriendelijkheid, vakkundigheid en beschikbaarheid omvatten.",
        "sub_categories": [
            {
                "name": "Vriendelijkheid",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de vriendelijkheid en beleefdheid van het personeel te beoordelen. Dit kan betrekking hebben op hun algemene houding en gedrag tegenover klanten."
            },
            {
                "name": "Vakkundigheid",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de deskundigheid van het personeel te beoordelen. Dit kan zaken omvatten zoals hun kennis van fitness en training, hun bekwaamheid in het geven van advies en instructies, en hun professionaliteit."
            },
            {
                "name": "Beschikbaarheid",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de beschikbaarheid van het personeel te beoordelen. Dit kan betrekking hebben op hun bereikbaarheid voor vragen of assistentie, en hun aanwezigheid op de sportschoolvloer."
            }
        ]
    },
    // {
    //     "parent_category": "Groepslessen",
    // //     "parent_category_description": "Deze categorie wordt gebruikt om aspecten en kenmerken van de groepslessen aangeboden door de sportschool te beoordelen. Dit kan de breedte van het aanbod, de kwaliteit van de lessen en de drukte tijdens de lessen omvatten.",
    //     "sub_categories": [
    //         {
    //             "name": "Breedte van aanbod",
    // //             "sub_category_description": "Deze subcategorie wordt gebruikt om de verscheidenheid en het bereik van groepslessen die de sportschool biedt te beoordelen. Dit kan omvatten hoeveel verschillende soorten lessen er beschikbaar zijn, en of er lessen zijn die passen bij verschillende fitnessniveaus en interesses."
    //         },
    //         {
    //             "name": "Kwaliteit van groepslessen",
    // //             "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit van de groepslessen te beoordelen. Dit kan zaken omvatten zoals de deskundigheid van de instructeur, de effectiviteit van de training, en de algehele ervaring van de les."
    //         },
    //         {
    //             "name": "Drukte",
    // //             "sub_category_description": "Deze subcategorie wordt gebruikt om te beoordelen hoe druk de groepslessen zijn. Dit kan betrekking hebben op het aantal mensen in een les, of er voldoende ruimte is, en of de klassegrootte het leerproces beïnvloedt."
    //         }
    //     ]
    // },
    {
        "parent_category": "Faciliteiten",
        // "parent_category_description": "Deze categorie wordt gebruikt om de aspecten en kenmerken van de faciliteiten in de sportschool te beoordelen. Dit kan omvatten de staat en hoeveelheid van apparatuur, de aanwezigheid en kwaliteit van de sauna, zonnebank, toiletten, kleedkamers, douches en kluisjes, buiten trainen.",
        "sub_categories": [
            {
                "name": "Staat van apparatuur",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de conditie en onderhoud van de sportschoolapparatuur te beoordelen. Dit kan factoren omvatten zoals netheid, functionaliteit en moderniteit."
            },
            {
                "name": "Hoeveelheid apparatuur",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de hoeveelheid beschikbare apparatuur in de sportschool te beoordelen. Dit kan betrekking hebben op of er voldoende apparaten zijn om aan de vraag te voldoen en of er een goede variëteit is."
            },
            {
                "name": "Gebruiksvriendelijkheid apparatuur",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om te beoordelen hoe gebruiksvriendelijk de apparatuur in de sportschool is. Dit kan omvatten of de apparatuur gemakkelijk te gebruiken en aan te passen is, en of er duidelijke instructies beschikbaar zijn."
            },
            {
                "name": "Sauna",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit, netheid en beschikbaarheid van de sauna in de sportschool te beoordelen."
            },
            {
                "name": "Zonnebank",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit, netheid en beschikbaarheid van de zonnebank in de sportschool te beoordelen."
            },
            {
                "name": "Toiletten",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit, netheid en beschikbaarheid van de toiletten in de sportschool te beoordelen."
            },
            {
                "name": "Kleedkamers",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit, netheid en grootte van de kleedkamers in de sportschool te beoordelen."
            },
            {
                "name": "Douches",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit, netheid en beschikbaarheid van de douches in de sportschool te beoordelen."
            },
            {
                "name": "Kluisjes",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de beschikbaarheid, grootte en veiligheid van de kluisjes in de sportschool te beoordelen."
            }
        ]
    },
    {
        "parent_category": "Aanbod lessen",
        // "parent_category_description": "Deze categorie wordt gebruikt om de aspecten en kenmerken van de lessen die de sportschool aanbiedt te beoordelen. Dit kan omvatten de breedte van het aanbod, de kwaliteit van de lessen en de drukte tijdens de lessen.",
        "sub_categories": [
            {
                "name": "Buiten trainen",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de mogelijkheden en kwaliteit van buiten trainen bij de sportschool te beoordelen."
            },
            {
                "name": "Breedte van aanbod",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de verscheidenheid en het bereik van groepslessen die de sportschool biedt te beoordelen. Dit kan omvatten hoeveel verschillende soorten lessen er beschikbaar zijn, en of er lessen zijn die passen bij verschillende fitnessniveaus en interesses."
            },
            {
                "name": "Kwaliteit van lessen",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kwaliteit van de lessen te beoordelen. Dit kan zaken omvatten zoals de deskundigheid van de instructeur, de effectiviteit van de training, en de algehele ervaring van de les."
            },
            {
                "name": "Drukte",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om te beoordelen hoe druk de lessen zijn. Dit kan betrekking hebben op het aantal mensen in een les, of er voldoende ruimte is, en of de klassegrootte het leerproces beïnvloedt."
            }
        ]
    },
    {
        "parent_category": "Abonnementen",
        // "parent_category_description": "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de abonnementsopties van de sportschool. Dit kan de prijs, de inhoud van het abonnement en de flexibiliteit van het abonnement omvatten.",
        "sub_categories": [
            {
                "name": "Prijs",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de kosten van de abonnementen te beoordelen. Dit kan omvatten de waarde voor de prijs en de vergelijking met concurrerende sportscholen."
            },
            {
                "name": "Inhoud abonnement",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de inbegrepen diensten en voordelen van de abonnementen te beoordelen. Dit kan omvatten de toegang tot faciliteiten, lessen, apparatuur en andere diensten."
            },
            {
                "name": "Flexibiliteit",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de flexibiliteit van de abonnementen, zoals de mogelijkheid om te annuleren of te pauzeren, te beoordelen."
            }
        ]
    },
    {
        "parent_category": "App en website",
        // "parent_category_description": "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de website en applicatie van de sportschool. Dit kan de gebruiksvriendelijkheid en het reserveringsproces omvatten.",
        "sub_categories": [
            {
                "name": "Gebruiksvriendelijkheid",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de gebruiksvriendelijkheid van de website en applicatie te beoordelen, inclusief navigatie en toegankelijkheid van informatie."
            },
            {
                "name": "Reserveringen",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om het proces van het reserveren van lessen of faciliteiten via de website of applicatie te beoordelen."
            }
        ]
    },
    {
        "parent_category": "Communicatie",
        // "parent_category_description": "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de communicatie van de sportschool. Dit kan de snelheid van reactie, duidelijkheid van informatie en beschikbaarheid van contactopties omvatten.",
        "sub_categories": [
            {
                "name": "Correspondentie",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de communicatie van de sportschool, inclusief snelheid van reactie en duidelijkheid van informatie, te beoordelen."
            },
            {
                "name": "Bereikbaarheid",
                // "sub_category_description": "Is de sportschool gemakkelijk en of goed te bereiken via telefoon, e-mail, website of applicatie?"
            }
        ]
    },
    {
        "parent_category": "Locatie",
        // "parent_category_description": "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de locatie van de sportschool. Dit kan de fysieke bereikbaarheid, parkeermogelijkheden en nabijheid van openbaar vervoer omvatten.",
        "sub_categories": [
            {
                "name": "Parkeren",
                // "sub_category_description": "Deze subcategorie wordt gebruikt om de beschikbaarheid en gemak van parkeermogelijkheden bij de sportschool te beoordelen."
            },{
                "name": "Bereikbaarheid",
                // "sub_category_description": "Hoe gemakkelijk is de sportschool te bereiken met de auto, openbaar vervoer, fiets?"
            },
        ]
    },
    {
        "parent_category": "Overige",
        // "parent_category_description": "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen. Dit kan onder andere veiligheidskwesties zoals diefstal omvatten.",
        "sub_categories": [

            {
                "name": "Diefstal",
                // "sub_category_description": "Deze subcategorie wordt alleen gebruikt om incidenten van diefstal bij de sportschool te beoordelen. Gebruik deze alleen als het heel duidelijk is dat het om diefstal gaat."
            }
        ]
    }
]

export const categories: WeaviateAddObject[] = [
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Sfeer",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de algehele sfeer of gevoel van een situatie of omgeving te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de omgevingsstemming, emoties of het algemene gevoel van een situatie bevat."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Muziek",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de muziek in een situatie of omgeving te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar muziek, geluid, nummers of de auditieve ervaring van een situatie bevat."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Temperatuur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de temperatuur van een omgeving of situatie te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar temperatuur, warmte, koude of klimatologische omstandigheden bevat."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Drukte",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de hoeveelheid mensen of activiteit in een omgeving of situatie te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de hoeveelheid mensen, drukte, congestie of activiteitsniveau in een situatie bevat."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Openingstijden",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de openingstijden van een locatie of evenement te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar openingstijden, beschikbaarheid, schema of specifieke uren van een situatie bevat."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Grootte",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de fysieke grootte van een object of ruimte te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de grootte, dimensies, capaciteit of ruimtelijkheid van een situatie bevat."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Hygiëne",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de netheid of hygiëne van een object, persoon of ruimte te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar netheid, hygiëne, of sanitaire omstandigheden bevat."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Kwaliteit",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit van begeleiding of leiding in een situatie te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de kwaliteit, effectiviteit, of professionaliteit van de begeleiding bevat."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Persoonlijkheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de persoonlijkheidskenmerken van een begeleider of leider te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de persoonlijkheidskenmerken, houding, of gedrag van de begeleider bevat."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Motiverend",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het vermogen van een begeleider of leider om te motiveren of inspireren te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar motivatie, inspiratie, of de bemoedigende aard van de begeleiding bevat."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Instructies",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit en duidelijkheid van instructies gegeven door een begeleider of leider te beschrijven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de duidelijkheid, precisie, of begrijpelijkheid van de instructies bevat."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Trainingsschema",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het trainingsschema dat door de begeleider of trainer wordt verstrekt, te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de opzet, inhoud, of aanpassing van het trainingsschema bevat."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Voedingsschema",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het voedingsschema dat door de begeleider of trainer wordt verstrekt, te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de opzet, inhoud, of aanpassing van het voedingsschema bevat."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Personal training",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit en effectiviteit van persoonlijke trainingssessies te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback specifieke verwijzingen naar persoonlijke trainingssessies bevat, met inbegrip van hun effectiviteit, structuur, of personalisatie."
    },
    {
        'parent_category': "Intake",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op het intakeproces.",
        'sub_category': "Kwaliteit",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit van het intakeproces te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar het intakeproces bevat, inclusief zijn efficiëntie, organisatie, of grondigheid."
    },
    {
        'parent_category': "Intake",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op het intakeproces.",
        'sub_category': "Persoonlijk",
        'sub_category_description': "Deze subcategorie wordt gebruikt om te beoordelen hoe persoonlijk en op maat gemaakt het intakeproces is.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de personalisatie, individualisatie of maatwerk van het intakeproces bevat."
    },
    {
        'parent_category': "Intake",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op het intakeproces.",
        'sub_category': "Tijdsduur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de tijdsduur van het intakeproces te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de tijd, snelheid, efficiëntie of duur van het intakeproces bevat."
    },
    {
        'parent_category': "Intake",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op het intakeproces.",
        'sub_category': "Aandacht",
        'sub_category_description': "Deze subcategorie wordt gebruikt om te beoordelen hoeveel aandacht er tijdens het intakeproces aan de klant wordt gegeven.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de hoeveelheid aandacht, zorg of bedachtzaamheid tijdens het intakeproces bevat."
    },
    {
        'parent_category': "Medewerkers",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met de medewerkers van de instelling.",
        'sub_category': "Vriendelijkheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de vriendelijkheid van de medewerkers te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de vriendelijkheid, warmte, of hoffelijkheid van de medewerkers bevat."
    },
    {
        'parent_category': "Medewerkers",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met de medewerkers van de instelling.",
        'sub_category': "Vakkundigheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de vakkundigheid en professionaliteit van de medewerkers te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de vakkundigheid, professionaliteit of competentie van de medewerkers bevat."
    },
    {
        'parent_category': "Medewerkers",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met de medewerkers van de instelling.",
        'sub_category': "Beschikbaarheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid en bereikbaarheid van de medewerkers te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de beschikbaarheid, bereikbaarheid of responsiviteit van de medewerkers bevat."
    },
    {
        'parent_category': "Groepslessen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met groepslessen.",
        'sub_category': "Breedte van aanbod",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het assortiment of de verscheidenheid aan groepslessen die worden aangeboden te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar het assortiment, diversiteit, of keuze van groepslessen bevat."
    },
    {
        'parent_category': "Groepslessen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met groepslessen.",
        'sub_category': "Kwaliteit van groepslessen",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit van de aangeboden groepslessen te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback specifieke verwijzingen naar de kwaliteit, inhoud, of uitvoering van groepslessen bevat."
    },
    {
        'parent_category': "Groepslessen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met groepslessen.",
        'sub_category': "Drukte",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de drukte of het aantal deelnemers in de groepslessen te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de drukte, het aantal deelnemers, of de bezetting van groepslessen bevat."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Staat van apparatuur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de staat en het onderhoud van de apparatuur in de faciliteiten te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de staat, onderhoud, kwaliteit of functionaliteit van de apparatuur bevat."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Hoeveelheid apparatuur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de hoeveelheid of beschikbaarheid van apparatuur in de faciliteiten te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de hoeveelheid, beschikbaarheid of het aantal apparaten in de faciliteit bevat."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Gebruiksvriendelijkheid apparatuur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de gebruiksvriendelijkheid van de apparatuur in de faciliteiten te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar het gebruiksgemak, de interface, bediening of toegankelijkheid van de apparatuur bevat."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Sauna",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, kwaliteit en netheid van de sauna in de faciliteiten te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback specifieke verwijzingen naar de sauna, inclusief de beschikbaarheid, netheid, temperatuur of algemene ervaring bevat."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Zonnebank",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid en kwaliteit van de zonnebank in de sportschool te beoordelen. Gebruik deze dus alleen als er expliciet over een zonnebank wordt gesproken.",
        'rules': "Selecteer deze alleen als zonnebank in de feedback genoemd wordt. Als dit niet duidelijk is, selecteerd deze dan niet."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Toiletten",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, netheid en functionaliteit van de toiletten in de faciliteiten te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de toiletten, inclusief hun netheid, beschikbaarheid of functionaliteit bevat."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Kleedkamers",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, netheid en grootte van de kleedkamers in de faciliteiten te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de kleedkamers, inclusief hun grootte, netheid, beschikbaarheid of faciliteiten bevat."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Douches",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, netheid en waterdruk van de douches in de faciliteiten te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de douches, inclusief hun beschikbaarheid, netheid, waterdruk of temperatuur bevat."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Kluisjes",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, grootte en veiligheid van de kluisjes in de faciliteiten te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de kluisjes bevat, inclusief hun beschikbaarheid, grootte, veiligheid of gebruiksgemak."
    },
    {
        'parent_category': "Abonnementen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de abonnementsopties van de instelling.",
        'sub_category': "Prijs",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kosten van de abonnementen te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback specifieke verwijzingen naar de kosten, prijs of waarde van de abonnementen bevat."
    },
    {
        'parent_category': "Abonnementen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de abonnementsopties van de instelling.",
        'sub_category': "Inhoud abonnement",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de inbegrepen diensten en voordelen van de abonnementen te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de inbegrepen diensten, voordelen of kenmerken van de abonnementen bevat."
    },
    {
        'parent_category': "Abonnementen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de abonnementsopties van de instelling.",
        'sub_category': "Flexibiliteit",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de flexibiliteit van de abonnementen, zoals de mogelijkheid om te annuleren of te pauzeren, te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de flexibiliteit van de abonnementen bevat, inclusief de mogelijkheid om te annuleren, te wijzigen of te pauzeren."
    },
    {
        'parent_category': "App en website",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de website en applicatie van de instelling.",
        'sub_category': "Gebruiksvriendelijkheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de gebruiksvriendelijkheid van de website en applicatie te beoordelen, inclusief navigatie en toegankelijkheid van informatie.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de gebruiksvriendelijkheid van de website of applicatie bevat, inclusief problemen met navigatie, toegankelijkheid van informatie, of algemene gebruikservaring."
    },
    {
        'parent_category': "App en website",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de website en applicatie van de instelling.",
        'sub_category': "Reserveringen",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het proces van het reserveren van lessen of faciliteiten via de website of applicatie te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar het proces van het reserveren van lessen of faciliteiten via de website of applicatie bevat."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "Parkeren",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid en gemak van parkeermogelijkheden bij de instelling te beoordelen.",
        'rules': "Selecteer deze categorie alleen als er duidelijk iets over parkeren wordt gezegd. Zo niet, selecteer deze dan niet."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "Diefstal",
        'sub_category_description': "Deze subcategorie wordt gebruikt om incidenten van diefstal of veiligheidskwesties bij de instelling te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback specifieke incidenten van diefstal, veiligheidsproblemen of gerelateerde kwesties bevat."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "COVID-19",
        'sub_category_description': "Deze subcategorie wordt gebruikt om maatregelen, beleid en procedures met betrekking tot COVID-19 bij de instelling te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar maatregelen, beleid of procedures met betrekking tot COVID-19 bevat."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "Correspondentie",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de communicatie van de instelling, inclusief snelheid van reactie en duidelijkheid van informatie, te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de communicatie van de instelling bevat, inclusief de snelheid van reactie, duidelijkheid van informatie of algemene tevredenheid met correspondentie."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "Buiten trainen",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de mogelijkheden en kwaliteit van buiten trainen bij de instelling te beoordelen.",
        'rules': "Deze categorie kan worden geselecteerd wanneer de feedback verwijzingen naar de mogelijkheden of kwaliteit van buiten trainen bevat."
    }
]
