import {EvaluationService} from "@/modules/openai/services/classification-by-csv/evaluation.service";
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { OpenAIClassificationByCsvService } from './openai-classification-by-csv.service';
import { OpenAISurveyAnswer } from '../classification/openai-classification.interface';
import { QueueProcessingBaseService } from '../../helpers/queue-processing-base.service';
import { format } from 'fast-csv';
import * as fs from 'fs';
import {
  OPENAI_DEFAULT_MODEL,
  OPENAI_MAX_RETRIES_PER_ROW,
  OPENAI_SLEEP_TIME,
} from '../../../../config/ai.config';
import { sleep } from '../../../../shared/utils/helper-functions';
import { ProgramSettingsService } from '@/modules/common/program-settings/program-settings.service';
import { InjectRepository } from '@nestjs/typeorm';
import { SurveyAnswerEntity } from '@entities';
import { Repository } from 'typeorm';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

const Queue = require('better-queue');
const path = require('path');
/**
 * A service for processing rows of data in a queue and
 * formatting the results into a CSV file.
 * @class
 */
@Injectable()
export class OpenaiClassificationByCsvProcessingService extends QueueProcessingBaseService {
  csvStream = format({
    headers: true,
    delimiter: ';',
    quote: '"',
    escape: '"',
  });
  iterationIndex: number
  constructor(
    private programSettingsService: ProgramSettingsService,
    private evaluationService: EvaluationService,
    private logger: ModuleLogger,

    @Inject(forwardRef(() => OpenAIClassificationByCsvService))
    private openAIClassificationService: OpenAIClassificationByCsvService,

    @InjectRepository(SurveyAnswerEntity)
    private saRepo: Repository<SurveyAnswerEntity>,
  ) {
    super();
    this.queue = new Queue(
      async (row: OpenAISurveyAnswer, cb) => {
        try {
          console.log('Processing row:', row.surv_surveyanswer_id);
          const result = await this.processRow(row, row.labels);
          cb(null, result);
        } catch (error) {
          console.log('ERROR', error);
          console.log(`Sleeping ${OPENAI_SLEEP_TIME} sec`);
          await sleep(OPENAI_SLEEP_TIME);
          if (row.tryCount >= OPENAI_MAX_RETRIES_PER_ROW) {
            console.log(
              'OPENAI Classification error on row processing: ' +
                error?.message,
              {
                logger_name: 'OPENAI',
                row,
                error,
              },
            );
          } else {
            console.log(
              'Pushed current row to queue again',
              'try count',
              row.tryCount,
            );
            this.queue.push({ ...row, tryCount: (row.tryCount || 0) + 1 });
          }
          cb(error);
        }
      },
      {
        concurrent: 20,
        maxTimeout: 180 * 1000,
        afterProcessDelay: 500,
      },
    );
    this.registerEventHandlers(this.queue);
    try {
      let file = `${process.env.NODE_FOLDER}data/classification-results.csv`;
      if (fs.existsSync(file)) {
        this.csvStream.pipe(fs.createWriteStream(file));
      }
    } catch {}
  }
  /**
   * Processes a row of data, including getting its classification
   * and writing the result to the CSV stream.
   * @param {any} row - The row of data to be processed.
   * @param {any[]} categories - The categories to be included in the classification.
   * @param {string} sentiment - The sentiment to be included in the classification.
   * @param {any} csvStream - The CSV stream to write the result to.
   * @return {Promise<any>} A promise resolved with the processed row's ID.
   */
  private async processRow(
    row: OpenAISurveyAnswer,
    categories: any[],
  ): Promise<any> {
    const q_pos = row.question_pos || '';
    const pos = row.pos || '';
    const q_neg = row.question_neg || '';
    const neg = row.neg || '';
    const sentiment =
      row.neutral === 1 ? 'Positive, Neutral, Negative' : 'Positive, Negative';

    const sentiment_enum =
      row.neutral === 1
        ? ['Positive', 'Negative', 'Neutral']
        : ['Positive', 'Negative'];

    const sec_company_id = +row.sec_company_id;
    const settings = await this.programSettingsService.get(sec_company_id);
    const props = settings?.company_properties;
    const activeAIModules: number[] = props?.ai_modules || [];

    const { completion, usage } =
      await this.openAIClassificationService.getClassification({
        activeAIModules,
        categories: row.labels,
        strengths_question: q_pos,
        strengths_answer: pos,
        improvements_question: q_neg,
        improvements_answer: neg,
        sentiment,
        sentiment_enum,
        model: row.model || OPENAI_DEFAULT_MODEL,
      });
    const result = completion?.content;
    const parseResult = completion;

    if (!parseResult) {
      throw new Error(`No result for row: ${row.surv_surveyanswer_id}`);
    }
    // console.log('RESULT: ', completion, usage);
    const commentsColumn = `Question Strengths: ${q_pos} \n Answer Strengths: ${pos} \n\n Question Improvements: ${q_neg} \n Answer Improvements: ${neg}\n\n\n\n`;
    const filtered = this.filterDuplicates(parseResult.answer)
      let formattedResponse
    if(filtered && filtered.length > 0) {
        formattedResponse = filtered
            .map((item) => {
                const {
                    parent_category,
                    sub_category,
                    sub_category_id,
                    sentiment,
                    confidence,
                } = item;
                return `${parent_category} > ${sub_category} - ${sentiment} - (Confidence: ${confidence})`;
            })
            .join('\n');
    } else {
        formattedResponse = []
    }
    const formattedCustomCategories = parseResult?.custom_categories
      ? parseResult?.custom_categories
          .map((item) => {
            const { category_name, sentiment, confidence } = item;
            return `${category_name} - ${sentiment} - (Confidence: ${confidence})`;
          })
          .join('\n')
      : null;

    const response = {
      answer_id: row?.surv_surveyanswer_id,
      feedback: commentsColumn,
      classification: formattedResponse,
      customer_feedback_markup_1: parseResult?.sentiment_markup?.customer_feedback_1,
      customer_feedback_markup_2: parseResult?.sentiment_markup?.customer_feedback_2,
      justification: parseResult?.extra_info?.justification_details,
      custom_categories: formattedCustomCategories,
      keywords: parseResult?.extra_info?.keywords,
      phrases: parseResult?.extra_info?.phrases,
      action: parseResult?.extra_info?.action,
      action_priority: parseResult?.extra_info?.action_priority,
      action_difficulty: parseResult?.extra_info?.action_difficulty,
      action_category: parseResult?.extra_info?.action_category,
      contact_customer: parseResult?.extra_info?.contact_customer,
      contact_customer_reason: parseResult?.extra_info?.contact_customer_reason,
      contact_customer_email: parseResult?.extra_info?.contact_customer_email,
      overall_confidence: parseResult?.extra_info?.overall_confidence,
      prompt_tokens: usage?.prompt_tokens,
      completion_tokens: usage?.completion_tokens,
      total_tokens: usage?.total_tokens,
    };
// console.log('response for CSV: ', response)
    this.csvStream.write(response, (cb) => {
      // console.log('Row processed:', response);
      if (cb) {
        console.log('ERROR WRITE:', cb);
      }
    });

    const responseForJson = {
      answer_id: row.surv_surveyanswer_id,
      feedback: commentsColumn,
      strength_questions: q_pos,
      strength_answer: pos,
      improvement_questions: q_neg,
      improvement_answer: neg,
      classification: parseResult.answer,
      customer_feedback_markup_1: parseResult?.sentiment_markup?.customer_feedback_1,
      customer_feedback_markup_2: parseResult?.sentiment_markup?.customer_feedback_2,
      justification: parseResult?.extra_info?.justification_details,
      custom_categories: parseResult?.custom_categories,
      keywords: parseResult?.extra_info?.keywords,
      phrases: parseResult?.extra_info?.phrases,
      summary: parseResult?.summary,
      action: parseResult?.extra_info?.action,
      action_priority: parseResult?.extra_info?.action_priority,
      action_difficulty: parseResult?.extra_info?.action_difficulty,
      action_category: parseResult?.extra_info?.action_category,
      contact_customer: parseResult?.extra_info?.contact_customer,
      contact_customer_reason: parseResult?.extra_info?.contact_customer_reason,
      contact_customer_email: parseResult?.extra_info?.contact_customer_email,
      overall_confidence: parseResult?.extra_info?.overall_confidence,
      prompt_tokens: usage.prompt_tokens,
      completion_tokens: usage.completion_tokens,
      total_tokens: usage.total_tokens,
    };

    this.resultArray.push(responseForJson);


      // // Append results to JSON file
      let file = `${process.env.NODE_FOLDER}data/classification-results.json`;
      let existingResults = [];
      if (fs.existsSync(file)) {
          try {
              existingResults = JSON.parse(fs.readFileSync(file, 'utf-8'));
          } catch (error) {
              console.error('Error reading or parsing JSON file:', error);
          }
      }
      existingResults.push(responseForJson);
      fs.writeFileSync(file, JSON.stringify(existingResults, null, 2));

      return row.surv_surveyanswer_id
  }

    filterDuplicates(classifications: any[]) {
        const uniqueSet = new Set();
        if(classifications && classifications.length > 0) {
            return classifications.filter(entry => {
                const key = `${entry.parent_category}-${entry.sub_category}-${entry.sentiment}`;
                if (uniqueSet.has(key)) {
                    return false;
                }
                uniqueSet.add(key);
                return true;
            });
        } else {
            return classifications
        }
    }
}
