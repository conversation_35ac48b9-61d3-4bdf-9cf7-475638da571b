import { Injectable } from '@nestjs/common';
import * as _ from 'lodash';

@Injectable()
export class EvaluationService {
    calculateExactMatchRatio(predictions: any[], labels: any[]): number {
        const exactMatches = labels?.filter(label =>
            predictions.some(prediction =>
                prediction.parent_category === label.parent_category &&
                prediction.sub_category === label.sub_category &&
                prediction.sentiment === label.sentiment
            )
        ).length;

        return labels?.length === 0 ? 0 : exactMatches / labels?.length;
    }

    calculateHammingLoss(predictions: any[], labels: any[]): number {
        if (!Array.isArray(predictions) || !Array.isArray(labels)) {
            throw new Error('Predictions and labels must be arrays');
        }

        let totalLabels = 0;
        let incorrectLabels = 0;

        const uniqueLabels = this.getUniqueLabels(labels);
        const uniquePredictions = this.getUniqueLabels(predictions);

        // Total labels should be the combined unique labels and unique predictions
        totalLabels += uniqueLabels.length + uniquePredictions.length;

        // Incorrect labels are those that don't match between predictions and gold standard
        const incorrectSet = this.getLabelDifference(uniquePredictions, uniqueLabels);
        incorrectLabels += incorrectSet.length;
        // Calculate the proportion of incorrect labels out of the total labels
        return totalLabels === 0 ? 0 : incorrectLabels / totalLabels;
    }

    calculatePrecisionRecallF1(predictions: any[], labels: any[]): any {
        let truePositives = 0;
        let falsePositives = 0;
        let falseNegatives = 0;

        predictions.forEach(prediction => {
            if (labels.some(label =>
                prediction.parent_category === label.parent_category &&
                prediction.sub_category === label.sub_category &&
                prediction.sentiment === label.sentiment
            )) {
                truePositives++;
            } else {
                falsePositives++;
            }
        });

        labels.forEach(label => {
            if (!predictions.some(prediction =>
                prediction.parent_category === label.parent_category &&
                prediction.sub_category === label.sub_category &&
                prediction.sentiment === label.sentiment
            )) {
                falseNegatives++;
            }
        });

        const precision = truePositives / (truePositives + falsePositives);
        const recall = truePositives / (truePositives + falseNegatives);
        const f1Score = 2 * (precision * recall) / (precision + recall);

        return {
            precision: precision || 0,
            recall: recall || 0,
            f1Score: f1Score || 0,
        };
    }

    private getUniqueLabels(labels: any[]): any[] {
        return labels.filter((value, index, self) =>
                index === self.findIndex((t) => (
                    t.parent_category === value.parent_category &&
                    t.sub_category === value.sub_category &&
                    t.sentiment === value.sentiment
                ))
        );
    }


    private getLabelDifference(predictions: any[], labels: any[]): any[] {
        // Helper function to check if two labels are equal
        const areLabelsEqual = (label1: any, label2: any) => {
            return label1.parent_category === label2.parent_category &&
                label1.sub_category === label2.sub_category &&
                label1.sentiment === label2.sentiment;
        };

        // Find labels that are in predictions but not in labels
        const onlyInPredictions = predictions.filter(prediction =>
            !labels.some(label => areLabelsEqual(prediction, label))
        );

        // Find labels that are in labels but not in predictions
        const onlyInLabels = labels.filter(label =>
            !predictions.some(prediction => areLabelsEqual(label, prediction))
        );

        // Combine the two arrays and return unique labels
        return [...onlyInPredictions, ...onlyInLabels].filter((value, index, self) =>
                index === self.findIndex((t) => (
                    t.parent_category === value.parent_category &&
                    t.sub_category === value.sub_category &&
                    t.sentiment === value.sentiment
                ))
        );
    }

    evaluate(goldStandardDatasets: any[], modelPredictionsDatasets: any[]): any {
        if (goldStandardDatasets.length !== modelPredictionsDatasets.length) {
            throw new Error('Datasets must have the same length for evaluation.');
        }

        const detailedResults = [];
        const summaryResults = [];

        for (let i = 0; i < goldStandardDatasets.length; i++) {
            const goldStandardDataset = goldStandardDatasets[i];
            const modelPredictionsDataset = modelPredictionsDatasets[i];

            const exactMatchRatio = this.calculateExactMatchRatio(
                modelPredictionsDataset?.classification,
                goldStandardDataset?.classification
            );
            const hammingLoss = this.calculateHammingLoss(
                modelPredictionsDataset?.classification,
                goldStandardDataset?.classification
            );
            const { precision, recall, f1Score } = this.calculatePrecisionRecallF1(
                modelPredictionsDataset?.classification,
                goldStandardDataset?.classification
            );

            const performanceMetrics = {
                exactMatchRatio,
                hammingLoss,
                precision,
                recall,
                f1Score
            };

            const detailMetrics = {
                averageExactMatchRatio: exactMatchRatio,
                averageHammingLoss: hammingLoss,
                averagePrecision: precision,
                averageRecall: recall,
                averageF1Score: f1Score
            };

            const detailedResult = {
                answer_id: goldStandardDataset.answer_id,
                exactMatchRatio,
                hammingLoss,
                precision,
                recall,
                f1Score,
                labelsExtra: this.getCustomCategories(goldStandardDataset?.classification, modelPredictionsDataset?.classification),
                labelsMissing: this.getCustomCategories(modelPredictionsDataset?.classification, goldStandardDataset?.classification),
                performance: this.evaluatePerformance(detailMetrics)
            };

            detailedResults.push(detailedResult);

            summaryResults.push(performanceMetrics);
        }

        const averageExactMatchRatio = summaryResults.reduce((sum, result) => sum + result.exactMatchRatio, 0) / summaryResults.length;
        const averageHammingLoss = summaryResults.reduce((sum, result) => sum + result.hammingLoss, 0) / summaryResults.length;
        const averagePrecision = summaryResults.reduce((sum, result) => sum + result.precision, 0) / summaryResults.length;
        const averageRecall = summaryResults.reduce((sum, result) => sum + result.recall, 0) / summaryResults.length;
        const averageF1Score = summaryResults.reduce((sum, result) => sum + result.f1Score, 0) / summaryResults.length;

        const metrics = {
            averageExactMatchRatio,
            averageHammingLoss,
            averagePrecision,
            averageRecall,
            averageF1Score
        };

        return {
            metrics,
            performance: this.evaluatePerformance(metrics),
            detailedResults
        };
    }

    evaluatePerformance(metrics: any): string {
        const boundaries = {
            exactMatchRatio: [0.60, 0.80, 0.90],
            hammingLoss: [0.30, 0.20, 0.10],  // Adjusted boundaries for lower is better
            precision: [0.60, 0.70, 0.85],    // Adjusted boundaries for precision
            recall: [0.60, 0.70, 0.85],       // Adjusted boundaries for recall
            f1Score: [0.60, 0.70, 0.85]       // Adjusted boundaries for F1 score
        };

        const getCategory = (value: number, boundaries: number[]) => {
            if (value >= boundaries[2]) return 'Very Good';
            if (value >= boundaries[1]) return 'Good';
            if (value >= boundaries[0]) return 'Medium';
            return 'Bad';
        };

        const getHammingLossCategory = (value: number, boundaries: number[]) => {
            if (value <= boundaries[2]) return 'Very Good';
            if (value <= boundaries[1]) return 'Good';
            if (value <= boundaries[0]) return 'Medium';
            return 'Bad';
        };

        const report = {
            exactMatchRatio: getCategory(metrics.averageExactMatchRatio, boundaries.exactMatchRatio),
            hammingLoss: getHammingLossCategory(metrics.averageHammingLoss, boundaries.hammingLoss),  // Inverted for lower is better
            precision: getCategory(metrics.averagePrecision, boundaries.precision),
            recall: getCategory(metrics.averageRecall, boundaries.recall),
            f1Score: getCategory(metrics.averageF1Score, boundaries.f1Score)
        };

        return `
    Evaluation Report:
    - Exact Match Ratio: ${report.exactMatchRatio} (${metrics.averageExactMatchRatio})
    - Hamming Loss: ${report.hammingLoss} (${metrics.averageHammingLoss})
    - Precision: ${report.precision} (${metrics.averagePrecision})
    - Recall: ${report.recall} (${metrics.averageRecall})
    - F1 Score: ${report.f1Score} (${metrics.averageF1Score})
    `;
    }

    private getCustomCategories(standardSet: any[], newSet: any[]): any[] {
        const standardCategories = this.getUniqueLabels(standardSet);
        const newCategories = this.getUniqueLabels(newSet);

        return newCategories.filter(category =>
            !standardCategories.some(stdCategory =>
                stdCategory.parent_category === category.parent_category &&
                stdCategory.sub_category === category.sub_category
            )
        );
    }
}
