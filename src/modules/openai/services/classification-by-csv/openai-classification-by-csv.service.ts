import { PromptType } from '@/modules/openai/services/prompt-builder/prompt-factory';
import {AIModuleType} from "@/shared/enums";
import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import { OpenAIResponse } from '../../openai.interface';
import { OpenAISurveyAnswer } from '../classification/openai-classification.interface';
import { parseJSON } from '@/shared/utils';
import { OpenaiClassificationByCsvProcessingService } from './openai-classification-by-csv-processing.service';
import { CsvParserService } from '../../helpers/csv-parser.service';
import { OpenAIGetClassificationParamsDto } from '../classification/openai-classification.dto';
import { OpenAIService } from '../../openai.service';
import { OPENAI_DEFAULT_MODEL } from '../../../../config/ai.config';
import { PromptBuilder } from '../prompt-builder/prompt-builder';
import { ToolBuilder } from '../tool-builder/tool.builder';
import { ExtraInfoTool } from '../tool-builder/extra-info.tool';
import { ExtraInfoPrompt } from '../prompt-builder/extra-info.prompt';
import OpenAI from 'openai';
import ChatCompletion = OpenAI.ChatCompletion;

@Injectable()
export class OpenAIClassificationByCsvService {
  constructor(
    private openAiService: OpenAIService,
    private queueProcessingService: OpenaiClassificationByCsvProcessingService,
    private csvParserService: CsvParserService,
  ) {}

  async getClassification(
    params: OpenAIGetClassificationParamsDto,
  ): Promise<OpenAIResponse | any> {
    const wordList: string[] = [
      'Schok',
      'Elektriciteit',
      'Letsel',
      'ambulance',
      'beschadiging',
      'blauwe plek',
      'brand',
      'dokter',
      'explosie',
      'gewond',
      'ontploffing',
      'ontploft',
      'rook',
      'schade',
      'vuur',
      'waterschade',
      'ziekenhuis',
    ];
    const promptBuilder = new PromptBuilder();
    const { activeAIModules } = params;

    promptBuilder
      .addPrompt('SystemPrompt')
        .addPrompt('CustomerFeedback', '', params)
        .addPrompt('CategoriesPrompt', '', params)
      .addPrompt('ClassificationRulesPrompt')
      .addPrompt('CustomLabelRulesPrompt','', params)
      .addPrompt('SentimentMarkupPrompt')
      .addPrompt('JustificationPrompt', '', params)
      .addPrompt('ExtraInfoBasePrompt')
      // .addPrompt('ExtraInfoJustificationDetailsPrompt')
      // .addPrompt('ExtraInfoOverallConfidencePrompt')
      .addPrompt('ExtraInfoKeywordsPrompt')
      .addPrompt('ExtraInfoPhrasesPrompt')
      .addPrompt('ExtraInfoContactCustomerPrompt', '', params)
      .addPrompt('ExtraInfoContactCustomerReasonPrompt', )
      // .addPrompt('ExtraInfoContactCustomerWriteEmailPrompt')
      // .addPrompt('ExtraInfoActionRecommenderPrompt')
      .addPrompt('LastGuidePrompt')


      // console.log('promptBuilder', promptBuilder.getPrompts());
    const toolBuilder = new ToolBuilder();
    const extraInfoTool = new ExtraInfoTool();
    toolBuilder
      .setName()
      .setDescription()
      .addClassificationAnswerTool(params)

      .addSentimentMarkupTool(params)
      .addCustomCategoriesTool(params)
      .addActionRecommenderTool(params)
       .addSummaryTool(params)

      let addExtraInfoTools = [
          extraInfoTool.addJustificationDetails(),
          extraInfoTool.addOverallConfidence(),
          extraInfoTool.addPhrases(),
          extraInfoTool.addContactCustomerEmail(),
          extraInfoTool.addActionRecommender(),
          extraInfoTool.addKeywords(),
          extraInfoTool.addContactCustomer(),
          extraInfoTool.addContactCustomerReason()
      ];

      let requiredItems = ['justification_details', 'phrases', 'keywords', 'contact_customer', 'contact_customer_reason'];

      toolBuilder.addExtraInfoTool(
        params,
        addExtraInfoTools,
        requiredItems,
      )
            let requiredProperties = ['answer', 'extra_info', 'custom_categories']
      // this.pushRequiredConditionally(requiredProperties, activeAIModules.includes(AIModuleType.CustomCategories), 'custom_categories');
      toolBuilder.setRequiredProperties(requiredProperties)

      console.log('OPENAI_DEFAULT_MODEL', OPENAI_DEFAULT_MODEL)
      // console.log('Prompt', JSON.stringify(promptBuilder.getPrompts(), null, 2))
      // console.log('toolBuilder', JSON.stringify(toolBuilder.getTool(), null, 2))

      // return
    try {
      const classifier = (await this.openAiService.createChatCompletion({
        model: OPENAI_DEFAULT_MODEL,
        temperature: 0.0,
        top_p: 0.1,
        messages: promptBuilder.getPrompts(),
        functions: [toolBuilder.getTool()],
        function_call: { name: 'classification' },
      })) as unknown as ChatCompletion;

      const classified = parseJSON(
        classifier.choices[0].message.function_call.arguments,
      );
      // console.log('classified', JSON.stringify(classifier.choices[0], null, 4))
      return {
        completion: classified,
        usage: classifier.usage,
      };
    } catch (error) {
      console.log('CHATGPT ERROR', error);
      return false;
    }
  }
  private pushConditionally(array: any[], condition: boolean, func: Function) {
    if (condition) {
        array.push(func());
    }
  }
  private pushRequiredConditionally(array: any[], condition: boolean, requiredItem: string) {
    if (condition) {
        array.push(requiredItem);
    }
  }
  async startClassification(
    classificationLabels?: null,
    data?: null,
    model?: string,
  ) {
    let query;
    query = data
      ? await this.getClassificationListBuffer(data)
      : await this.getClassificationList(); // Perform query
    const labels = classificationLabels
      ? await this.getClassificationLabelsBuffer(classificationLabels)
      : await this.getClassificationLabels();
    const list: OpenAISurveyAnswer[] = query.map((item) => {
      // Map query to SurveyAnswer
      // item.labels = this.processCategories(labels);
      item.model = model;
      return item;
    });
    // console.log('list', list)
    await this.queueProcessingService.processQueue(list); // Start Process queue
    return {
      success: true,
      message: `${list.length} items added to queue for processing`,
    };
  }

  async processQuery(result: any[]) {
    if (result.length > 0) {
      return result?.map((item) => {
        return {
          ...item,
          labels: parseJSON(item.labels),
        };
      });
    }
    return [];
  }

  async getClassificationList() {
    return await this.csvParserService.getCSV('data/ai_data_th.csv');

  }

  async getClassificationLabels() {
    // console.log('Feedback4Sports_v3')
    return await this.csvParserService.getCSV(
      'data/Labels Feedback4Sports_v3.csv',
    );
  }

  async getClassificationListBuffer(data) {
    return await this.csvParserService.parseCSV(data);
  }

  async getClassificationLabelsBuffer(data) {
    return await this.csvParserService.parseCSV(data);
  }

  /**
   * Processes categories and groups them by parent category
   * @private
   * @param {Array} categories - Array of category objects
   * @returns {Array} - Array of grouped categories
   */
  private processCategories(categories: any[]) {
    // console.log(JSON.stringify(categories, null, 4 ))
    const categoriesMap = new Map(); // Map of parent category to array of categories
    categories.forEach((row) => {
      // Loop through categories
      if (categoriesMap.has(row.parent)) {
        // If parent category exists, add category to array
        const group = categoriesMap.get(row.parent); // Get parent category
        group.sub_categories.push({ name: row.label, id: row.id }); // Add category to array
      } else {
        // If parent category doesn't exist, create it
        categoriesMap.set(row.parent, {
          parent_category: row.parent,
          sub_categories: [{ name: row.label, id: row.id }],
        }); // Create parent category
      }
    });
    return [...categoriesMap.values()]; // Return array of grouped categories
  }
}
