import {WeaviateAddObject} from "../../openai.interface";

export const categories: WeaviateAddObject[] = [
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Sfeer",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de algehele sfeer of gevoel van een situatie of omgeving te beschrijven."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Muziek",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de muziek in een situatie of omgeving te beschrijven."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Temperatuur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de temperatuur van een omgeving of situatie te beschrijven."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Drukte",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de hoeveelheid mensen of activiteit in een omgeving of situatie te beschrijven."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Openingstijden",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de openingstijden van een locatie of evenement te beschrijven."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Grootte",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de fysieke grootte van een object of ruimte te beschrijven."
    },
    {
        'parent_category': "Algemeen",
        'parent_category_description': "Deze categorie wordt gebruikt voor algemene aspecten en kenmerken die niet onder specifieke categorieën vallen.",
        'sub_category': "Hygiëne",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de netheid of hygiëne van een object, persoon of ruimte te beschrijven."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Kwaliteit",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit van begeleiding of leiding in een situatie te beschrijven."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Persoonlijkheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de persoonlijkheidskenmerken van een begeleider of leider te beschrijven."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Motiverend",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het vermogen van een begeleider of leider om te motiveren of inspireren te beschrijven."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Instructies",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit en duidelijkheid van instructies gegeven door een begeleider of leider te beschrijven."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Trainingsschema",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het trainingsschema dat door de begeleider of trainer wordt verstrekt, te beoordelen."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Voedingsschema",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het voedingsschema dat door de begeleider of trainer wordt verstrekt, te beoordelen."
    },
    {
        'parent_category': "Begeleiding",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met begeleiding of leiding.",
        'sub_category': "Personal training",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit en effectiviteit van persoonlijke trainingssessies te beoordelen."
    },
    {
        'parent_category': "Intake",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op het intakeproces.",
        'sub_category': "Kwaliteit",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit van het intakeproces te beoordelen."
    },
    {
        'parent_category': "Intake",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op het intakeproces.",
        'sub_category': "Persoonlijk",
        'sub_category_description': "Deze subcategorie wordt gebruikt om te beoordelen hoe persoonlijk en op maat gemaakt het intakeproces is."
    },
    {
        'parent_category': "Intake",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op het intakeproces.",
        'sub_category': "Tijdsduur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de tijdsduur van het intakeproces te beoordelen."
    },
    {
        'parent_category': "Intake",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op het intakeproces.",
        'sub_category': "Aandacht",
        'sub_category_description': "Deze subcategorie wordt gebruikt om te beoordelen hoeveel aandacht er tijdens het intakeproces aan de klant wordt gegeven."
    },
    {
        'parent_category': "Medewerkers",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met de medewerkers van de instelling.",
        'sub_category': "Vriendelijkheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de vriendelijkheid van de medewerkers te beoordelen."
    },
    {
        'parent_category': "Medewerkers",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met de medewerkers van de instelling.",
        'sub_category': "Vakkundigheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de vakkundigheid en professionaliteit van de medewerkers te beoordelen."
    },
    {
        'parent_category': "Medewerkers",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met de medewerkers van de instelling.",
        'sub_category': "Beschikbaarheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid en bereikbaarheid van de medewerkers te beoordelen."
    },
    {
        'parent_category': "Groepslessen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met groepslessen.",
        'sub_category': "Breedte van aanbod",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het assortiment of de verscheidenheid aan groepslessen die worden aangeboden te beoordelen."
    },
    {
        'parent_category': "Groepslessen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met groepslessen.",
        'sub_category': "Kwaliteit van groepslessen",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kwaliteit van de aangeboden groepslessen te beoordelen."
    },
    {
        'parent_category': "Groepslessen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek verband houden met groepslessen.",
        'sub_category': "Drukte",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de drukte of het aantal deelnemers in de groepslessen te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Staat van apparatuur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de staat en het onderhoud van de apparatuur in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Hoeveelheid apparatuur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de hoeveelheid of beschikbaarheid van apparatuur in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Gebruiksvriendelijkheid apparatuur",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de gebruiksvriendelijkheid van de apparatuur in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Sauna",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, kwaliteit en netheid van de sauna in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Zonnebank",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid en kwaliteit van de zonnebank in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Toiletten",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, netheid en functionaliteit van de toiletten in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Kleedkamers",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, netheid en grootte van de kleedkamers in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Douches",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, netheid en waterdruk van de douches in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Faciliteiten",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de faciliteiten van de instelling.",
        'sub_category': "Kluisjes",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid, grootte en veiligheid van de kluisjes in de faciliteiten te beoordelen."
    },
    {
        'parent_category': "Abonnementen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de abonnementsopties van de instelling.",
        'sub_category': "Prijs",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de kosten van de abonnementen te beoordelen."
    },
    {
        'parent_category': "Abonnementen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de abonnementsopties van de instelling.",
        'sub_category': "Inhoud abonnement",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de inbegrepen diensten en voordelen van de abonnementen te beoordelen."
    },
    {
        'parent_category': "Abonnementen",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de abonnementsopties van de instelling.",
        'sub_category': "Flexibiliteit",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de flexibiliteit van de abonnementen, zoals de mogelijkheid om te annuleren of te pauzeren, te beoordelen."
    },
    {
        'parent_category': "App en website",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de website en applicatie van de instelling.",
        'sub_category': "Gebruiksvriendelijkheid",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de gebruiksvriendelijkheid van de website en applicatie te beoordelen, inclusief navigatie en toegankelijkheid van informatie."
    },
    {
        'parent_category': "App en website",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die specifiek betrekking hebben op de website en applicatie van de instelling.",
        'sub_category': "Reserveringen",
        'sub_category_description': "Deze subcategorie wordt gebruikt om het proces van het reserveren van lessen of faciliteiten via de website of applicatie te beoordelen."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "Parkeren",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de beschikbaarheid en gemak van parkeermogelijkheden bij de instelling te beoordelen."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "Diefstal",
        'sub_category_description': "Deze subcategorie wordt gebruikt om incidenten van diefstal of veiligheidskwesties bij de instelling te beoordelen."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "COVID-19",
        'sub_category_description': "Deze subcategorie wordt gebruikt om maatregelen, beleid en procedures met betrekking tot COVID-19 bij de instelling te beoordelen."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "Correspondentie",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de communicatie van de instelling, inclusief snelheid van reactie en duidelijkheid van informatie, te beoordelen."
    },
    {
        'parent_category': "Overige",
        'parent_category_description': "Deze categorie wordt gebruikt voor aspecten en kenmerken die niet onder andere categorieën vallen.",
        'sub_category': "Buiten trainen",
        'sub_category_description': "Deze subcategorie wordt gebruikt om de mogelijkheden en kwaliteit van buiten trainen bij de instelling te beoordelen."
    }
]
