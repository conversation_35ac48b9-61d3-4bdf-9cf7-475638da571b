import {WeaviateAddObject} from "../../openai.interface";

export const categories: WeaviateAddObject[] = [
    {
        "parent_category": "L01 Local label",
        "parent_category_description": "This parent label is used when local or regional factors significantly impact the context of the conversation or feedback.",
        "sub_category": "Undefinable",
        "sub_category_description": "This sub-label is used when the specific topic or aspect of a customer interaction cannot be clearly categorized under the existing labels."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes instances related to the motivations or reasons behind using specific services.",
        "sub_category": "N03_02c Booking repairs undefinable",
        "sub_category_description": "This sub-label is applied when the reasons behind booking repair services are not clear or explicitly stated."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label is used to categorize instances or conversations in which the interaction involves specific individuals or roles.",
        "sub_category": "N01_02 CIC Agent",
        "sub_category_description": "This sub-label is used when the interaction or communication is with a Customer Interaction Center (CIC) agent."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label is used to categorize instances or conversations in which the interaction involves specific individuals or roles.",
        "sub_category": "N01_06a Retailer - online",
        "sub_category_description": "This sub-label is applied when the interaction or communication is with an online retailer."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label is used to categorize instances or conversations in which the interaction involves specific individuals or roles.",
        "sub_category": "N01_06b Retailer - offline",
        "sub_category_description": "This sub-label is used when the interaction or communication is with an offline retailer or brick-and-mortar store."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label is used to categorize instances or conversations in which the interaction involves specific individuals or roles.",
        "sub_category": "N01_07 Liaison Department (Claim)",
        "sub_category_description": "This sub-label is applied when the interaction or communication involves the liaison department, especially in the context of a claim."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label is used to categorize instances or conversations in which the interaction involves specific individuals or roles.",
        "sub_category": "N01_08 Logistic service provider (BSH or external)",
        "sub_category_description": "This sub-label is used when the interaction or communication involves a logistic service provider, whether internal (BSH) or external."
    },
    {
        "parent_category": "B01 Brand Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions relating to various aspects of a brand.",
        "sub_category": "B01_03 Brand - Rebuy Consideration",
        "sub_category_description": "This sub-label is applied when the conversation involves considerations or decisions about repurchasing from the same brand."
    },
    {
        "parent_category": "B01 Brand Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions relating to various aspects of a brand.",
        "sub_category": "B01_04 Brand / Product Switch",
        "sub_category_description": "This sub-label is used when the conversation involves a switch from one brand or product to another."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label is used when discussions or mentions involve characteristics or features of a product.",
        "sub_category": "P02_02 Arrangement",
        "sub_category_description": "This sub-label is used when the arrangement or setup of a product is the focus of the conversation."
    },
	    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label is used when discussions or mentions involve characteristics or features of a product.",
        "sub_category": "P02_08 Energy Efficiency & Consumption",
        "sub_category_description": "This sub-label is used when the focus of the conversation is on the energy efficiency or power consumption of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label is used when discussions or mentions involve characteristics or features of a product.",
        "sub_category": "P02_09 Handling/Operability",
        "sub_category_description": "This sub-label is used when the discussion is centered around how easy it is to handle or operate a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label is used when discussions or mentions involve characteristics or features of a product.",
        "sub_category": "P02_10 Installation (by Consumer)",
        "sub_category_description": "This sub-label is used when the topic of conversation revolves around the installation of a product by the consumer."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label is used when discussions or mentions involve characteristics or features of a product.",
        "sub_category": "P02_20 Results",
        "sub_category_description": "This sub-label is applied when the conversation is about the output or results produced by a product."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label is used to categorize instances or conversations in which the interaction involves specific individuals or roles.",
        "sub_category": "N01_01 Advisor",
        "sub_category_description": "This sub-label is used when the interaction or communication is with an advisor or consultant."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label is used to categorize instances or conversations in which the interaction involves specific individuals or roles.",
        "sub_category": "N01_04 Third Party technician",
        "sub_category_description": "This sub-label is used when the conversation involves a technician who is not a direct employee or associate, such as a contractor or representative from another company."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_01 Telephone contact",
        "sub_category_description": "This sub-label is used when the customer interaction occurs over a phone call or telephonic communication."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_02 Website",
        "sub_category_description": "This sub-label is applied when the consumer interaction occurs on a website or web portal."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_03 E-Mail",
        "sub_category_description": "This sub-label is used when the customer interaction occurs through email communication."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_04 (Live-)Chat/ Other messengers",
        "sub_category_description": "This sub-label is used when the interaction with the customer occurs through live chat or other instant messaging platforms."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_06a BSH Point of Sales - online",
        "sub_category_description": "This sub-label is used when the interaction occurs on BSH's online points of sales or e-commerce platforms."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_06b BSH Point of Sales - offline",
        "sub_category_description": "This sub-label is used when the customer interaction takes place at BSH's offline or physical points of sales."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_06c BSH Point of Sales - undefinable",
        "sub_category_description": "This sub-label is used when it's not clear or defined where the interaction at BSH's point of sales has occurred."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes the reasons or motivations behind specific service interactions.",
        "sub_category": "N03_01 Repair",
        "sub_category_description": "This sub-label is used when the service interaction is related to repairing a product or addressing a technical issue."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes the reasons or motivations behind specific service interactions.",
        "sub_category": "N03_02a Booking repairs online",
        "sub_category_description": "This sub-label is used when the service interaction involves scheduling or booking repairs online."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes the reasons or motivations behind specific service interactions.",
        "sub_category": "N03_08 Registration",
        "sub_category_description": "This sub-label is used when the service interaction involves the registration process for a product or service."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes the reasons or motivations behind specific service interactions.",
        "sub_category": "N03_09 Return",
        "sub_category_description": "This sub-label is used when the service interaction involves returning a product or ending a service."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes the reasons or motivations behind specific service interactions.",
        "sub_category": "N03_10 Spare Parts",
        "sub_category_description": "This sub-label is used when the service interaction relates to spare parts for a product."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes the reasons or motivations behind specific service interactions.",
        "sub_category": "N03_11 Warranty",
        "sub_category_description": "This sub-label is used when the service interaction involves issues or discussions about the warranty of a product."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label is used to categorize general aspects or elements of service interactions that do not fit into other categories.",
        "sub_category": "N04_06 Pick up service",
        "sub_category_description": "This sub-label is used when the service interaction involves a pickup service, such as collecting a product for repair or delivery."
    },
    {
        "parent_category": "N05 Behavioural aspects service staff",
        "parent_category_description": "This parent label is used to categorize comments or discussions about the behaviour or conduct of service staff.",
        "sub_category": "N05_02 Professional knowledge",
        "sub_category_description": "This sub-label is used when the focus of the conversation is on the professional knowledge or expertise of the service staff."
    },
    {
        "parent_category": "N05 Behavioural aspects service staff",
        "parent_category_description": "This parent label is used to categorize comments or discussions about the behaviour or conduct of service staff.",
        "sub_category": "N05_03 Communication skills",
        "sub_category_description": "This sub-label is used when the discussion revolves around the communication skills of the service staff."
    },
    {
        "parent_category": "N05 Behavioural aspects service staff",
        "parent_category_description": "This parent label is used to categorize comments or discussions about the behaviour or conduct of service staff.",
        "sub_category": "N05_04 Product usage and purchasing advice",
        "sub_category_description": "This sub-label is used when the service staff's advice on product usage or purchasing is the focus of discussion."
    },
    {
        "parent_category": "N05 Behavioural aspects service staff",
        "parent_category_description": "This parent label is used to categorize comments or discussions about the behaviour or conduct of service staff.",
        "sub_category": "N05_05 Exceeding expectations",
        "sub_category_description": "This sub-label is used when the service staff has exceeded the customer's expectations in some way."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions that involve the cost or price aspects of a product or service.",
        "sub_category": "N07_04 Repair / Spare Parts / maintenance price",
        "sub_category_description": "This sub-label is used when the conversation involves the cost or pricing of repairs, spare parts, or maintenance services."
    },
	{
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions that involve the cost or price aspects of a product or service.",
        "sub_category": "N07_05 Costs for contacting BSH",
        "sub_category_description": "This sub-label is used when the discussion involves the costs incurred for contacting BSH."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions that involve the cost or price aspects of a product or service.",
        "sub_category": "N07_06 Call-out costs",
        "sub_category_description": "This sub-label is used when the conversation focuses on the costs associated with a service call-out."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label categorizes different individuals or groups of people with whom interactions occur.",
        "sub_category": "N01_03 BSH Technician",
        "sub_category_description": "This sub-label is used when the interaction involves a technician from BSH. Use this label if the customer mentioning something about the technician's behaviour."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label categorizes different individuals or groups of people with whom interactions occur.",
        "sub_category": "N01_05 Cooking school staff",
        "sub_category_description": "This sub-label is used when the interaction is with staff from a cooking school."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label categorizes different individuals or groups of people with whom interactions occur.",
        "sub_category": "N01_06c Retailer - undefinable",
        "sub_category_description": "This sub-label is used when it is unclear or not defined who the retailer in the interaction is."
    },
    {
        "parent_category": "N01 Persons - with whom",
        "parent_category_description": "This parent label categorizes different individuals or groups of people with whom interactions occur.",
        "sub_category": "N01_09 Others",
        "sub_category_description": "This sub-label is used when the interaction is with individuals not covered by the other sub-labels."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_05 App",
        "sub_category_description": "This sub-label is used when the interaction with the customer occurs through an application."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label is used to categorize the different platforms or mediums where consumer interactions occur.",
        "sub_category": "N02_07 Showroom",
        "sub_category_description": "This sub-label is used when the customer interaction takes place in a showroom."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes the reasons or motivations behind specific service interactions.",
        "sub_category": "N03_02b Booking repairs via phone",
        "sub_category_description": "This sub-label is used when the service interaction involves scheduling or booking repairs over the phone."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label is used to categorize general aspects or elements of service interactions that do not fit into other categories.",
        "sub_category": "N04_01 Avoid repair",
        "sub_category_description": "This sub-label is used when the service interaction involves attempts to avoid or circumvent a repair."
    },
	{
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions that involve the cost or price aspects of a product or service.",
        "sub_category": "N07_07 Good will",
        "sub_category_description": "This sub-label is used when the discussion involves good will in the context of price aspects."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions that involve the cost or price aspects of a product or service.",
        "sub_category": "N07_09 Payment (process)",
        "sub_category_description": "This sub-label is used when the conversation involves the payment process."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions that involve the cost or price aspects of a product or service.",
        "sub_category": "N07_10 Shipping costs",
        "sub_category_description": "This sub-label is used when the discussion is about shipping costs."
    },
    {
        "parent_category": "N09 Sales Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions that involve the sales aspects of a product or service.",
        "sub_category": "N09_03 Available product range",
        "sub_category_description": "This sub-label is used when the conversation involves the range of products available for sale."
    },
    {
        "parent_category": "N09 Sales Aspects",
        "parent_category_description": "This parent label is used to categorize discussions or mentions that involve the sales aspects of a product or service.",
        "sub_category": "N09_04 Sales Aspect undefinable",
        "sub_category_description": "This sub-label is used when the sales aspect being discussed cannot be clearly defined."
    },
    {
        "parent_category": "N11 Delivery aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the delivery aspects of a product or service.",
        "sub_category": "N11_05 Delivery undefinable",
        "sub_category_description": "This sub-label is used when the delivery aspect being discussed cannot be clearly defined."
    },
    {
        "parent_category": "N12 Spare part aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the spare part aspects of a product or service.",
        "sub_category": "N12_03 Spare part availability",
        "sub_category_description": "This sub-label is used when the conversation involves the availability of spare parts."
    },
    {
        "parent_category": "N12 Spare part aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the spare part aspects of a product or service.",
        "sub_category": "N12_04 Spare part undefinable",
        "sub_category_description": "This sub-label is used when the spare part aspect being discussed cannot be clearly defined."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_01 Aesthetics / Appearance / Optics",
        "sub_category_description": "This sub-label is used when the conversation involves the aesthetics, appearance, or optical characteristics of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_03 Cleaning / Hygiene / Maintenance",
        "sub_category_description": "This sub-label is used when the conversation involves the cleaning, hygiene, or maintenance characteristics of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_04 Compatibility",
        "sub_category_description": "This sub-label is used when the conversation involves the compatibility of a product with other devices or systems."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_05 Connectivity",
        "sub_category_description": "This sub-label is used when the conversation involves the connectivity features of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_06 Data Security",
        "sub_category_description": "This sub-label is used when the conversation involves the data security aspects of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_07 Display & Electronics",
        "sub_category_description": "This sub-label is used when the conversation involves the display and electronic characteristics of a product."
    },
	{
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_11 Lighting",
        "sub_category_description": "This sub-label is used when the conversation involves the lighting features of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_12 Loudness",
        "sub_category_description": "This sub-label is used when the conversation involves the loudness or sound level of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_15 Power",
        "sub_category_description": "This sub-label is used when the conversation involves the power attributes of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_16 Product Lifetime / Durability",
        "sub_category_description": "This sub-label is used when the conversation involves the lifetime or durability of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_22 Size / Capacity",
        "sub_category_description": "This sub-label is used when the conversation involves the size or capacity of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_26 Sustainability",
        "sub_category_description": "This sub-label is used when the conversation involves the sustainability aspects of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_27 Temperature",
        "sub_category_description": "This sub-label is used when the conversation involves the temperature features of a product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the characteristics of a product.",
        "sub_category": "P02_30 Others",
        "sub_category_description": "This sub-label is used when the product characteristic being discussed does not fit into any of the other defined sub-labels."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve where the consumer interactions occur.",
        "sub_category": "N02_08 Events",
        "sub_category_description": "This sub-label is used when the conversation involves consumer interaction at events."
    },
    {
        "parent_category": "N02 Consumer Interaction touchpoints - where",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve where the consumer interactions occur.",
        "sub_category": "N02_09 Others",
        "sub_category_description": "This sub-label is used when the location of consumer interaction being discussed does not fit into any of the other defined sub-labels."
    },
    {
        "parent_category": "N05 Behavioural aspects service staff",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the behavioral aspects of service staff.",
        "sub_category": "N05_01 Appearance/Manner",
        "sub_category_description": "This sub-label is used when the conversation involves the appearance or manner of the service staff."
    },
    {
        "parent_category": "N06 Information needs of the consumer",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the information needs of the consumer.",
        "sub_category": "N06_01 Error codes",
        "sub_category_description": "This sub-label is used when the conversation involves the need for information about error codes."
    },
    {
        "parent_category": "N06 Information needs of the consumer",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the information needs of the consumer.",
        "sub_category": "N06_05 Product comparison options",
        "sub_category_description": "This sub-label is used when the conversation involves the need for information about product comparison options."
    },
    {
        "parent_category": "N06 Information needs of the consumer",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the information needs of the consumer.",
        "sub_category": "N06_06 Product information",
        "sub_category_description": "This sub-label is used when the conversation involves the need for general product information."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the reasons why certain service moments occur.",
        "sub_category": "N03_03 Complaint Management",
        "sub_category_description": "This sub-label is used when the conversation involves the management of complaints."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the reasons why certain service moments occur.",
        "sub_category": "N03_04 Delivery",
        "sub_category_description": "This sub-label is used when the conversation involves delivery service moments."
    },
	    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the reasons why certain service moments occur.",
        "sub_category": "N03_05 Installation by service provider",
        "sub_category_description": "This sub-label is used when the conversation involves service moments related to installation by the service provider."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the reasons why certain service moments occur.",
        "sub_category": "N03_06a Marketing Offers - BSH",
        "sub_category_description": "This sub-label is used when the conversation involves marketing offers from BSH."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the reasons why certain service moments occur.",
        "sub_category": "N03_06b Marketing Offers - Retailer",
        "sub_category_description": "This sub-label is used when the conversation involves marketing offers from a retailer."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the reasons why certain service moments occur.",
        "sub_category": "N03_06c Marketing Offers - undefinable",
        "sub_category_description": "This sub-label is used when the conversation involves marketing offers that are not clearly defined."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the reasons why certain service moments occur.",
        "sub_category": "N03_07 Purchase",
        "sub_category_description": "This sub-label is used when the conversation involves service moments related to the purchase of a product."
    },
    {
        "parent_category": "N03 Service Moments - why",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the reasons why certain service moments occur.",
        "sub_category": "N03_12 Others",
        "sub_category_description": "This sub-label is used when the reason for the service moment being discussed does not fit into any of the other defined sub-labels."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve general service aspects.",
        "sub_category": "N04_02 Damages in consumer residence",
        "sub_category_description": "This sub-label is used when the conversation involves damage that occurred at the consumer's residence."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve general service aspects.",
        "sub_category": "N04_03 Direct sales activities",
        "sub_category_description": "This sub-label is used when the conversation involves direct sales activities."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve general service aspects.",
        "sub_category": "N04_04 Number of visits necessary",
        "sub_category_description": "This sub-label is used when the conversation involves the number of visits necessary for a service."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve general service aspects.",
        "sub_category": "N04_05 Number of unsuccessful phone calls",
        "sub_category_description": "This sub-label is used when the conversation involves the number of unsuccessful phone calls."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve general service aspects.",
        "sub_category": "N04_07 Problem not solved",
        "sub_category_description": "This sub-label is used when the conversation involves a problem that has not been solved."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve general service aspects.",
        "sub_category": "N04_08 Process communication",
        "sub_category_description": "This sub-label is used when the conversation involves process communication."
    },
    {
        "parent_category": "N04 General service aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve general service aspects.",
        "sub_category": "N04_09 General Service Aspects - Others",
        "sub_category_description": "This sub-label is used when the general service aspect being discussed does not fit into any of the other defined sub-labels."
    },
    {
        "parent_category": "N06 Information needs of the consumer",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the information needs of the consumer.",
        "sub_category": "N06_02 Findability of information",
        "sub_category_description": "This sub-label is used when the conversation involves the need for easily findable information."
    },
    {
        "parent_category": "N06 Information needs of the consumer",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the information needs of the consumer.",
        "sub_category": "N06_03 Instruction manual",
        "sub_category_description": "This sub-label is used when the conversation involves the need for an instruction manual."
    },
    {
        "parent_category": "N06 Information needs of the consumer",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the information needs of the consumer.",
        "sub_category": "N06_04 Model number /E-number/FD number",
        "sub_category_description": "This sub-label is used when the conversation involves the need for information about the model number, E-number, or FD number of a product."
    },
	 {
        "parent_category": "N06 Information needs of the consumer",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the information needs of the consumer.",
        "sub_category": "N06_07 Service information",
        "sub_category_description": "This sub-label is used when the conversation involves the need for information about the service."
    },
    {
        "parent_category": "N06 Information needs of the consumer",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the information needs of the consumer.",
        "sub_category": "N06_08 Information need undefinable",
        "sub_category_description": "This sub-label is used when the conversation involves an undefined information need."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_01 Appliance purchase price",
        "sub_category_description": "This sub-label is used when the conversation involves the purchase price of the appliance."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_03 Event price",
        "sub_category_description": "This sub-label is used when the conversation involves the price of an event."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_02 Accessories / consumables price",
        "sub_category_description": "This sub-label is used when the conversation involves the price of accessories or consumables."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_08 Exchange price",
        "sub_category_description": "This sub-label is used when the conversation involves the price of an exchange."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_11 Fees in case of return",
        "sub_category_description": "This sub-label is used when the conversation involves fees in case of a return."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_12 D2C service costs",
        "sub_category_description": "This sub-label is used when the conversation involves the costs of Direct to Customer (D2C) services."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_13 Price changes",
        "sub_category_description": "This sub-label is used when the conversation involves changes in the price."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_14 D2E prices",
        "sub_category_description": "This sub-label is used when the conversation involves the prices of Direct to End User (D2E) services."
    },
    {
        "parent_category": "N07 Price Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the price.",
        "sub_category": "N07_15 Price undefinable",
        "sub_category_description": "This sub-label is used when the conversation involves a price aspect that is undefinable."
    },
    {
        "parent_category": "N08 Time aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to time.",
        "sub_category": "N08_01 Reachability",
        "sub_category_description": "This sub-label is used when the conversation involves the aspect of reachability."
    },
    {
        "parent_category": "N08 Time aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to time.",
        "sub_category": "N08_02 Punctuality",
        "sub_category_description": "This sub-label is used when the conversation involves the aspect of punctuality."
    },
    {
        "parent_category": "N08 Time aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to time.",
        "sub_category": "N08_03 Time slot for service/contact",
        "sub_category_description": "This sub-label is used when the conversation involves the time slot for service or contact."
    },
    {
        "parent_category": "N08 Time aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to time.",
        "sub_category": "N08_04 Speed of implementation",
        "sub_category_description": "This sub-label is used when the conversation involves the speed of implementation. Think of the speed of delivery or the speed of a repair. Or the time it took from making an appointment to the actual appointment."
    },
    {
        "parent_category": "N08 Time aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to time.",
        "sub_category": "N08_05 Waiting time",
        "sub_category_description": "This sub-label is used when the conversation involves the aspect of waiting time."
    },
    {
        "parent_category": "N08 Time aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to time.",
        "sub_category": "N08_06 Time undefinable",
        "sub_category_description": "This sub-label is used when the conversation involves a time aspect that is undefinable."
    },
	   {
        "parent_category": "N09 Sales Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to sales.",
        "sub_category": "N09_01 Attractiveness of product presentation",
        "sub_category_description": "This sub-label is used when the conversation involves the attractiveness of product presentation."
    },
    {
        "parent_category": "N09 Sales Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to sales.",
        "sub_category": "N09_02 Selection of presented products",
        "sub_category_description": "This sub-label is used when the conversation involves the selection of presented products."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_01 User Friendliness (Website)",
        "sub_category_description": "This sub-label is used when the conversation involves the user-friendliness of the website."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_02 Navigation",
        "sub_category_description": "This sub-label is used when the conversation involves the navigation aspect of the website."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_03a Website content - media assets",
        "sub_category_description": "This sub-label is used when the conversation involves the media assets content on the website."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_03c Website content - undefinable",
        "sub_category_description": "This sub-label is used when the conversation involves undefinable content on the website."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_04 Loading time",
        "sub_category_description": "This sub-label is used when the conversation involves the loading time of the website."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_03b Website content - text",
        "sub_category_description": "This sub-label is used when the conversation involves text content on the website."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_05 Search related feedbacks",
        "sub_category_description": "This sub-label is used when the conversation involves feedback related to the search function on the website."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_06 Filtering related feedbacks",
        "sub_category_description": "This sub-label is used when the conversation involves feedback related to the filtering function on the website."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_08 Website undefinable",
        "sub_category_description": "This sub-label is used when the conversation involves an undefinable aspect of the website."
    },
    {
        "parent_category": "N11 Delivery aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to delivery.",
        "sub_category": "N11_04 Delivery problems",
        "sub_category_description": "This sub-label is used when the conversation involves problems related to the delivery of the product."
    },
    {
        "parent_category": "N12 Spare part aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to spare parts.",
        "sub_category": "N12_01 Spare part packaging",
        "sub_category_description": "This sub-label is used when the conversation involves the packaging of spare parts."
    },
    {
        "parent_category": "N12 Spare part aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to spare parts.",
        "sub_category": "N12_02 Spare part quality",
        "sub_category_description": "This sub-label is used when the conversation involves the quality of spare parts."
    },
    {
        "parent_category": "B01 Brand Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the brand.",
        "sub_category": "B01_01 Brand Mention",
        "sub_category_description": "This sub-label is used when the brand is mentioned in the conversation."
    },
    {
        "parent_category": "N10 Website Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the website.",
        "sub_category": "N10_07 Legal aspects",
        "sub_category_description": "This sub-label is used when the conversation involves legal aspects of the website."
    },
    {
        "parent_category": "N11 Delivery aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to delivery.",
        "sub_category": "N11_01 Product availability (out of stock)",
        "sub_category_description": "This sub-label is used when the conversation involves product availability or out of stock issues."
    },
    {
        "parent_category": "N11 Delivery aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to delivery.",
        "sub_category": "N11_02 Tracking of delivery",
        "sub_category_description": "This sub-label is used when the conversation involves tracking the delivery of the product."
    },
    {
        "parent_category": "N11 Delivery aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to delivery.",
        "sub_category": "N11_03 Date of delivery",
        "sub_category_description": "This sub-label is used when the conversation involves the date of the product delivery."
    },
	    {
        "parent_category": "P01 Product Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the product.",
        "sub_category": "P01_01 Product Mention",
        "sub_category_description": "This sub-label is used when the product is mentioned in the conversation."
    },
    {
        "parent_category": "P01 Product Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the product.",
        "sub_category": "P01_02 Product Recommendation",
        "sub_category_description": "This sub-label is used when the conversation involves a recommendation of the product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_13 Materials / Manufacturing (Robustness)",
        "sub_category_description": "This sub-label is used when the conversation involves the materials or manufacturing robustness of the product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_17 Product Packaging",
        "sub_category_description": "This sub-label is used when the conversation involves the packaging of the product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_18 Product Quality",
        "sub_category_description": "This sub-label is used when the conversation involves the quality of the product."
    },
    {
        "parent_category": "B01 Brand Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the brand.",
        "sub_category": "B01_02 Brand Recommendation",
        "sub_category_description": "This sub-label is used when the conversation involves a recommendation of the brand."
    },
    {
        "parent_category": "B01 Brand Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the brand.",
        "sub_category": "B01_05 Brand / Product Passion & Pride",
        "sub_category_description": "This sub-label is used when the conversation involves passion or pride for the brand or product."
    },
    {
        "parent_category": "P01 Product Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the product.",
        "sub_category": "P01_03 Product Rebuy Consideration",
        "sub_category_description": "This sub-label is used when the conversation involves considerations for rebuying the product."
    },
    {
        "parent_category": "P01 Product Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to the product.",
        "sub_category": "P01_04 Product Comparison",
        "sub_category_description": "This sub-label is used when the conversation involves a comparison of the product with other products."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_19 Product Safety",
        "sub_category_description": "This sub-label is used when the conversation involves the safety of the product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_21 Scope of Delivery",
        "sub_category_description": "This sub-label is used when the conversation involves the scope of delivery of the product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_23 Smell",
        "sub_category_description": "This sub-label is used when the conversation involves the smell of the product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_24 Smoke/ Fume/ Steam",
        "sub_category_description": "This sub-label is used when the conversation involves the production of smoke, fumes, or steam from the product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_25 Speed / Duration / Working Progress",
        "sub_category_description": "This sub-label is used when the conversation involves the speed, duration or working progress of the product."
    },
    {
        "parent_category": "S13 Satisfaction Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to satisfaction.",
        "sub_category": "S13_01 Brand Satisfaction",
        "sub_category_description": "This sub-label is used when the conversation involves satisfaction with the brand."
    },
    {
        "parent_category": "P03 Product Specification",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the specification of the product.",
        "sub_category": "P03_02 Product components",
        "sub_category_description": "This sub-label is used when the conversation involves the components of the product."
    },
    {
        "parent_category": "P03 Product Specification",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the specification of the product.",
        "sub_category": "P03_03 Product programs",
        "sub_category_description": "This sub-label is used when the conversation involves the programs of the product."
    },
    {
        "parent_category": "P03 Product Specification",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the specification of the product.",
        "sub_category": "P03_04 Accessories & Consumables",
        "sub_category_description": "This sub-label is used when the conversation involves accessories or consumables related to the product."
    },
    {
        "parent_category": "P04 Product Problems",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve problems with the product.",
        "sub_category": "P04_01a Functional failure",
        "sub_category_description": "This sub-label is used when the conversation involves a functional failure of the product."
    },
    {
        "parent_category": "P04 Product Problems",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve problems with the product.",
        "sub_category": "P04_01b Error & Deficiency",
        "sub_category_description": "This sub-label is used when the conversation involves an error or deficiency in the product."
    },
	{
        "parent_category": "P04 Product Problems",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve problems with the product.",
        "sub_category": "P04_01c Product problem undefinable",
        "sub_category_description": "This sub-label is used when the product problem cannot be defined or categorized."
    },
    {
        "parent_category": "P04 Product Problems",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve problems with the product.",
        "sub_category": "P04_02a Occurrence within Warranty Period",
        "sub_category_description": "This category represents customer feedback related to product issues or problems that occur within the specified warranty period. It includes instances where customers experience malfunctions, defects, or failures of the product during the valid warranty timeframe. Feedback in this category typically pertains to issues that are covered by the manufacturer's warranty and may require repair, replacement, or other forms of resolution."
    },
    {
        "parent_category": "P04 Product Problems",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve problems with the product.",
        "sub_category": "P04_02b Occurrence within Expected Product Lifetime",
        "sub_category_description": "This sub-label is used when the product problem occurs within the expected product lifetime."
    },
    {
        "parent_category": "P04 Product Problems",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve problems with the product.",
        "sub_category": "P04_02c Occurrence past Expected Product Lifetime",
        "sub_category_description": "This sub-label is used when the product problem occurs past the expected product lifetime."
    },
    {
        "parent_category": "P04 Product Problems",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve problems with the product.",
        "sub_category": "P04_02d Occurrence Undefinable",
        "sub_category_description": "This sub-label is used when the occurrence of the product problem cannot be defined or categorized."
    },
    {
        "parent_category": "P04 Product Problems",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve problems with the product.",
        "sub_category": "P04_03 Problem caused by consumer",
        "sub_category_description": "This sub-label is used when the product problem is caused by the consumer."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_28 Vibrations",
        "sub_category_description": "This sub-label is used when the conversation involves the vibrations of the product."
    },
    {
        "parent_category": "P02 Product Characteristics",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve characteristics of the product.",
        "sub_category": "P02_29 Weight",
        "sub_category_description": "This sub-label is used when the conversation involves the weight of the product."
    },
    {
        "parent_category": "S13 Satisfaction Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to satisfaction.",
        "sub_category": "S13_02 Product Satisfaction",
        "sub_category_description": "This sub-label is used when the conversation involves satisfaction with the product."
    },
    {
        "parent_category": "S13 Satisfaction Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to satisfaction.",
        "sub_category": "S13_03 Service Satisfaction",
        "sub_category_description": "This sub-label is used when the conversation involves satisfaction with the service."
    },
    {
        "parent_category": "S13 Satisfaction Aspects",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve aspects related to satisfaction.",
        "sub_category": "S13_04 Satisfaction/Dissatisfaction undefinable",
        "sub_category_description": "This sub-label is used when the satisfaction or dissatisfaction cannot be defined or categorized."
    },
    {
        "parent_category": "P03 Product Specification",
        "parent_category_description": "This parent label categorizes discussions or mentions that involve the specification of the product.",
        "sub_category": "P03_01 Product features",
        "sub_category_description": "This sub-label is used when the conversation involves the features of the product."
    }
]
