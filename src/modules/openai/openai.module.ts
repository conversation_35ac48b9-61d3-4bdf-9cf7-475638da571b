import { ActionRecommenderService } from '@/modules/openai/services/action-recommender/action-recommender.service';
import {EvaluationService} from "@/modules/openai/services/classification-by-csv/evaluation.service";
import { FeedbackReportService } from '@/modules/openai/services/feedback-report/feedback-report.service';
import { LabelTreeBuilderService } from '@/modules/openai/services/label-tree-builder/label-tree-builder';
import { WeaviateClassificationService } from '@/modules/openai/services/weaviate/weaviate-classification.service';
import { WeaviateService } from '@/modules/openai/services/weaviate/weaviate.service';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { OpenAIController } from './openai.controller';

import { OpenAIClassificationProcessingService } from './services/classification/openai-classification-processing.service';
import { OpenAIClassificationService } from './services/classification/openai-classification.service';

import { MClassResponse } from '../database/entities/mclass-response.entity';
import { SurvSurveyEntity } from '../database/entities/surv-survey.entity';
import { SurveyAnswerEntity } from '../database/entities/surv-surveyanswer.entity';
import { SurveyAnswerAIEntity } from '../database/entities/surv-surveyanswer-ai.entity';
import { CsvParserService } from './helpers/csv-parser.service';
import { OpenAIClassificationByCsvService } from './services/classification-by-csv/openai-classification-by-csv.service';
import { OpenaiClassificationByCsvProcessingService } from './services/classification-by-csv/openai-classification-by-csv-processing.service';
import { EmailGeneratorService } from './services/email-generator/email-generator';
import { FeedbackChatService } from './services/feedback-chat/feedback-chat.service';
import { GatewayModule } from '../gateway/gateway.module';
import { WebScraperModule } from '../web-scraper/web-scraper.module';
import { AiFunctionDefinitionsService } from './services/feedback-chat/ai-helpers';
import { OpenAIService } from './openai.service';
import { HttpModule } from '@nestjs/axios';
import { LoggerModule } from '@lib/logger/logger.module';
import { TranslateService } from './services/translate/translate.service';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import {
  ClfCallEntity,
  KeywordsEntity,
  MClassCustomItem,
  QueueEntity,
} from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { QueueModule } from '@lib/queue/queue.module';
import { GenerateTicketSummaryService } from './services/generate-ticket-summary/generate-ticket-summary.service';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      ClfCallEntity,
      SurvSurveyEntity,
      SurveyAnswerEntity,
      SurveyAnswerAIEntity,
      MClassCustomItem,
      MClassResponse,
      KeywordsEntity,
      QueueEntity,
    ]),
    HttpModule,
    GatewayModule,
    WebScraperModule,
    ModuleLoggerModule.register('openai'),
    QueueModule.register('openai'),
    ProgramSettingsModule,
  ],
  controllers: [OpenAIController],
  providers: [
    OpenAIService,
    OpenAIClassificationProcessingService,
    OpenAIClassificationService,
    OpenAIClassificationByCsvService,
    OpenaiClassificationByCsvProcessingService,
    CsvParserService,
    FeedbackChatService,
    AiFunctionDefinitionsService,
    TranslateService,
    GenerateTicketSummaryService,
    EmailGeneratorService,
    LabelTreeBuilderService,
    ActionRecommenderService,
    FeedbackReportService,
    WeaviateService,
    WeaviateClassificationService,
    EvaluationService
  ],
  exports: [
    OpenAIService,
    OpenAIClassificationProcessingService,
    OpenAIClassificationService,
    OpenAIClassificationByCsvService,
    OpenaiClassificationByCsvProcessingService,
    CsvParserService,
    FeedbackChatService,
    AiFunctionDefinitionsService,
    EmailGeneratorService,
    LabelTreeBuilderService,
    ActionRecommenderService,
    FeedbackReportService,
    WeaviateService,
    WeaviateClassificationService,
    EvaluationService
  ],
})
export class OpenAIModule {}
