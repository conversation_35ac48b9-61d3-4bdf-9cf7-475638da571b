import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import {
  deleteImageByName,
  saveImage,
} from '../lib/image-library/utils/file.utils';

import {
  CreateBaseTemplateBodyDto,
  CreateBaseTemplateQueryDto,
} from './dto/post-base-template.dto';
import { env } from '../../app.env';

import { TemplateBaseEntity } from '@entities';
import { CompanyType } from '@/shared/enums';
import { parseJSON, rawQuery } from '@/shared/utils';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class BaseTemplateSerivice {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(TemplateBaseEntity)
    private readonly templateBaseRepo: Repository<TemplateBaseEntity>,
    private httpService: HttpService,
  ) {}

  public async getBaseTemplates({ global = false } = {}) {
    const { company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`getBaseTemplates for ${company.id}`);

    let query = this.templateBaseRepo
      .createQueryBuilder('t')
      .select(['t.template_base_id', 't.template_name', 't.image'])
      .where('t.builder IS NOT NULL');

    if (global) {
      query.andWhere('t.sec_company_id =:cid', {
        cid: company.type == CompanyType.BSH ? 25 : 1,
      });
    } else {
      query.andWhere('t.sec_company_id =:cid', { cid: company.id });
    }
    const templates = await query.getMany();
    this.logger.debug(`getBaseTemplates query and result`, {
      query: rawQuery(query),
      templates,
    });
    return templates.map((item) => ({
      id: item.template_base_id,
      image: item.image ? `/uploads/template-base/${item.image}` : undefined,
      name: item.template_name,
    }));
  }
  public async getTamplate(id: number) {
    this.logger.debug(`getTemplate by id ${id}`);
    const template = await this.templateBaseRepo
      .createQueryBuilder('t')
      .select(['t.template_base_id', 't.template_name', 't.builder', 't.image'])
      .where('t.template_base_id =:id', { id })
      .getOne();

    this.logger.debug(`getTemplate by id ${id} result`, { template });
    return {
      id: template.template_base_id,
      image: template.image
        ? `/uploads/template-base/${template.image}`
        : undefined,
      name: template.template_name,
      data: template.builder,
    };
  }

  public async upsertTemplate(
    query: CreateBaseTemplateQueryDto,
    body: CreateBaseTemplateBodyDto,
  ) {
    const { company } = this.cls.get<AuthorizedData>('user');

    if (query.id) body.id = query.id;

    body.data = typeof body.data == 'object' ? body.data : parseJSON(body.data);
    this.logger.debug(`upsertTemplate for ${company.id}`, { body });

    let data: DeepPartial<TemplateBaseEntity> = {
      builder: body.data,
      sec_company_id: company.id,
      template_name: body.name,
    };
    let errors = [];

    if (data.builder) {
      let url = env.EMAIL_BUILDER_URL;
      let resp = await this.httpService.axiosRef
        .post(url, data.builder)
        .then((r) => r.data)
        .catch((e) => errors.push(e));
      if (resp?.html) data.template_text = resp.html;
      else errors.push(resp);
    }
    if (errors.length > 0) throw new HttpException({ errors }, 500);

    if (body.image) {
      data.image = await saveImage(body.image, 'template-base');
    }

    if (body.id) data.template_base_id = body.id;
    let result = await this.templateBaseRepo.save(data);

    this.logger.debug(`upsertTemplate for ${company.id} result`, { result });

    return { id: result.template_base_id, message: 'Data saved' };
  }

  public async getTemplatePreview(data: any) {
    let url = env.EMAIL_BUILDER_URL;
    return this.httpService.axiosRef.post(url, data).then((r) => r.data);
  }

  public async deleteTemplate(id) {
    const { company } = this.cls.get<AuthorizedData>('user');
    this.logger.debug(`deleteTemplate ${id}`);

    let template = await this.templateBaseRepo
      .createQueryBuilder('t')
      .select(['t.image', 't.sec_company_id'])
      .where('template_base_id =:id', { id })
      .getOne();

    if (template?.sec_company_id != company.id)
      throw new HttpException('template not found', HttpStatus.NOT_FOUND);

    if (template.image) {
      await deleteImageByName(template.image, 'template-base');
    }

    let result = await this.templateBaseRepo
      .createQueryBuilder()
      .where('template_base_id =:id', { id })
      .andWhere('sec_company_id =:cid', { cid: company.id })
      .delete()
      .execute();
    this.logger.debug(
      `Delete template for ${id} affected count ${result.affected}`,
    );

    if (result.affected > 0) return { id, message: 'Data deleted' };
  }
}
