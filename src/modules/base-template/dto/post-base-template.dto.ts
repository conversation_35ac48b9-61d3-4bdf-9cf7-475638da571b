import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class CreateBaseTemplateBodyDto {
  @Type()
  @ApiProperty({ required: true })
  name: string;

  @Type()
  @ApiProperty({ required: true, type: Object })
  data: any;

  @Type()
  @IsOptional()
  id: number;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  image?: any;
}

export class CreateBaseTemplateQueryDto {
  @Type()
  @IsOptional()
  @ApiProperty({ required: false })
  id: number;
}
