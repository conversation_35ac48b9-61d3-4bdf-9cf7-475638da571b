import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { BaseTemplateSerivice } from './base-template.service';

@Controller({
  path: '/global-templates',
  version: '2',
})
@ApiTags('Base Template')
@ApiBearerAuth()
export class GlobalTemplatesController {
  constructor(private readonly baseTemplateService: BaseTemplateSerivice) {}

  @Get()
  @UseGuards(AuthGuard)
  async getList(@Req() { user }: AuthorizedRequest) {
    let data = await this.baseTemplateService.getBaseTemplates({
      global: true,
    });
    return data;
  }
}
