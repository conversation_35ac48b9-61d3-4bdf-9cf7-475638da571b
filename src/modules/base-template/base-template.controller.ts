import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { BaseTemplateSerivice } from './base-template.service';
import { GetBaseTemplateQueryDto } from './dto/get-base-template.dto';
import {
  CreateBaseTemplateBodyDto,
  CreateBaseTemplateQueryDto,
} from './dto/post-base-template.dto';
import { DeleteBaseTemplateQueryDto } from './dto/delete-base-template.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { imageFileFilter } from '../lib/image-library/utils/file.utils';

@ApiTags('Base Template')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'base_template',
})
export class BaseTemplateController {
  constructor(private baseTempService: BaseTemplateSerivice) {}

  @Get('/')
  @UseGuards(AuthGuard)
  getBaseTemplates(@Query() query: GetBaseTemplateQueryDto) {
    if (query.id > 0) {
      return this.baseTempService.getTamplate(query.id);
    } else {
      return this.baseTempService.getBaseTemplates();
    }
  }

  @Post('/')
  @UseGuards(AuthGuard)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('image', {
      fileFilter: imageFileFilter,
      limits: { files: 1 },
    }),
  )
  upsertBaseTemplate(
    @Query() query: CreateBaseTemplateQueryDto,
    @UploadedFile() image: Express.Multer.File,
    @Body() body: CreateBaseTemplateBodyDto,
  ) {
    body.image = image;
    return this.baseTempService.upsertTemplate(query, body);
  }

  @Post('/preview')
  @UseGuards(AuthGuard)
  @ApiBody({ type: Object })
  async getPreview(@Body() body: any) {
    try {
      return await this.baseTempService.getTemplatePreview(body);
    } catch (e) {
      return new HttpException(e.message, 400);
    }
  }

  @Delete('/')
  @UseGuards(AuthGuard)
  deleteBaseTemplate(@Query() query: DeleteBaseTemplateQueryDto) {
    return this.baseTempService.deleteTemplate(query.id);
  }
}
