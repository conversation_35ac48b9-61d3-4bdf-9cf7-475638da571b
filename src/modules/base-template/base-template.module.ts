import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TemplateBaseEntity } from '../database/entities/template-base.entity';

import { BaseTemplateController } from './base-template.controller';
import { BaseTemplateSerivice } from './base-template.service';
import { GlobalTemplatesController } from './global-templates.controller';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TemplateBaseEntity]),
    HttpModule,
    ModuleLoggerModule.register('base-template'),
  ],
  controllers: [BaseTemplateController, GlobalTemplatesController],
  providers: [BaseTemplateSerivice],
})
export class BaseTemplateModule {}
