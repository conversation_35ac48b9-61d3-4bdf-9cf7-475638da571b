import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetAnswertableQueryDto } from './dto/get-answertable.dto';
import {
  PostAnswertableQueryDto,
  PostAnswertableBodyDto,
} from './dto/upsert-answertable.dto';
import { AnswertableService } from './answertable.service';

@ApiTags('Answertable')
@ApiBearerAuth()
@Controller({
  path: 'answertable',
  version: '2',
})
export class AnswertableController {
  constructor(private answertableService: AnswertableService) {}

  @UseGuards(AuthGuard)
  @Get()
  getAnswertable(@Query() query: GetAnswertableQueryDto) {
    return this.answertableService.getList(query);
  }

  @UseGuards(AuthGuard)
  @Post()
  upsertAnswertable(
    @Query() query: PostAnswertableQueryDto,
    @Body() body: PostAnswertableBodyDto,
  ) {
    const { action } = body;
    if (action == 'DELETE') {
      return this.answertableService.deleteList(body.ids, query.tableId);
    } else return this.answertableService.upsertItem(body, query);
  }
}
