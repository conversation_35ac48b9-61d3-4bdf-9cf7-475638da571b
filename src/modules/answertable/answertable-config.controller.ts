import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { AnswertableConfigService } from './answertable-config.service';

import { AnswertableActionType } from '@/shared/enums/answertable.enum';
import {
  DeteleAnswertableQueryDto,
  PostAnswertableServiceQueryDto,
  UpsertAnswertableConfigBodyDto,
} from './dto';

@ApiTags('Answertable')
@ApiBearerAuth()
@Controller({
  path: 'answertable_config',
  version: '2',
})
export class AnswertableConfigController {
  constructor(private answertableConfigService: AnswertableConfigService) {}

  @UseGuards(AuthGuard)
  @Get()
  getAnswertableConfig() {
    return this.answertableConfigService.getConfigList({
      join: true,
    });
  }

  @UseGuards(AuthGuard)
  @Post()
  postAnswertableConfig(
    @Query() query: PostAnswertableServiceQueryDto,
    @Body() body: UpsertAnswertableConfigBodyDto,
  ) {
    const { action } = query;
    if (action == AnswertableActionType.COLUMN) {
      return this.answertableConfigService.upsertColumn(body);
    } else {
      return this.answertableConfigService.upsertTable(body);
    }
  }

  @UseGuards(AuthGuard)
  @Delete()
  deleteAnswertableConfig(@Query() query: DeteleAnswertableQueryDto) {
    return this.answertableConfigService.deleteConfig(query);
  }
}
