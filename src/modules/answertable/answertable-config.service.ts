import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, Repository } from 'typeorm';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsService } from 'nestjs-cls';
import { AnswertableConfigEntity, AnswertableEntity } from '@entities';
import {
  DeteleAnswertableQueryDto,
  GetAnswertableConfigServiceDto,
  UpsertAnswertableConfigBodyDto,
} from './dto';
import { parseJSON } from '@/shared/utils';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class AnswertableConfigService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,

    @InjectRepository(AnswertableConfigEntity)
    private readonly answertableConfigEntity: Repository<AnswertableConfigEntity>,

    @InjectRepository(AnswertableEntity)
    private readonly answertableEntity: Repository<AnswertableEntity>,
  ) {}

  async getConfig(
    tableId,
    sec_company_id,
    join = false,
  ): Promise<AnswertableConfigEntity> {
    this.logger.debug(
      `getConfig for table ${tableId} and company ${sec_company_id}`,
    );

    let list = await this.answertableConfigEntity.query(
      `
    SELECT a.* ${
      join == true
        ? `, array_remove(array_agg(sq.surv_surveyquestion_id), NULL) as survey_question, MAX(sqr.surv_surveyquestionrow_id) as surv_surveyquestionrow`
        : ''
    } FROM answertable_config a
    ${
      join == true
        ? `
    LEFT JOIN surv_surveyquestion sq ON sq.answertable_id = a.answertable_config_uuid
    LEFT JOIN surv_surveyquestionrow sqr ON sqr.surv_surveyquestion_id = sq.surv_surveyquestion_id AND sqr.question_returnvalue = '-1'
    `
        : ''
    }
    WHERE a.answertable_config_uuid = $1 AND a.sec_company_id = $2
    GROUP BY a.answertable_config_uuid
    `,
      [tableId, sec_company_id],
    );

    this.logger.debug(
      `getConfig for table ${tableId} and company ${sec_company_id}  result`,
      { result: list[0] },
    );

    return list[0];
  }
  async getConfigList(params: GetAnswertableConfigServiceDto) {
    const { join = true } = params;
    const { company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`getConfigList for company ${company.id}`);

    let list = await this.answertableConfigEntity.query(
      `
    SELECT a.* ${
      join == true
        ? `, array_remove(array_agg(sq.surv_surveyquestion_id), NULL) as survey_question, MAX(sqr.surv_surveyquestionrow_id) as surv_surveyquestionrow`
        : ''
    } FROM answertable_config a
    ${
      join == true
        ? `
    LEFT JOIN surv_surveyquestion sq ON sq.answertable_id = a.answertable_config_uuid
    LEFT JOIN surv_surveyquestionrow sqr ON sqr.surv_surveyquestion_id = sq.surv_surveyquestion_id AND sqr.question_returnvalue = '-1'
    `
        : ''
    }
    WHERE a.sec_company_id = $1
    GROUP BY a.answertable_config_uuid
    `,
      [company.id],
    );
    list = list.map((item) => {
      return {
        ...item,
        is_locked: item.survey_question && !this.canDelete(item),
        survey_question: undefined,
        surv_surveyquestionrow: undefined,
        answertable_config: parseJSON(item.answertable_config),
      };
    });

    this.logger.debug(`getConfigList result for company ${company.id}`, {
      result: list,
    });

    return list;
  }

  canDelete(item, columnId = null) {
    let id = item.answertable_config_uuid;
    if (id == '33A97021-ECE7-4025-B739-707E33DA1D0D') {
      if (!columnId || columnId == 'medium_01') {
        return false;
      }
    }
    if (id) {
      if (!columnId && item.survey_question.length > 0) {
        // table is used, so cannot be deleted
        return false;
      } else if (item.survey_question.length > 0) {
        if (item.surv_surveyquestionrow > 0) {
          return false;
        }
      }
    }
    return true;
  }

  async deleteConfig({ tableId, columnId = null }: DeteleAnswertableQueryDto) {
    const { company } = this.cls.get<AuthorizedData>('user');

    let record = await this.getConfig(tableId, company.id, true);
    if (!record) throw new HttpException('Table is not found', 404);
    if (!this.canDelete(record, columnId))
      throw new HttpException(
        'Table used in survey creator, cannot be removed',
        406,
      );
    let message;
    if (columnId) {
      let config = parseJSON(record.answertable_config);
      config = config.filter((item) => item.columnId != columnId);
      await this.answertableConfigEntity.update(
        { answertable_config_uuid: tableId },
        { answertable_config: JSON.stringify(config) },
      );
      message = 'Column deleted';
    } else {
      await this.answertableConfigEntity.delete({
        answertable_config_uuid: tableId,
      });
      message = 'Table deleted';
    }

    return {
      message,
    };
  }
  async upsertColumn(item: UpsertAnswertableConfigBodyDto) {
    const { columnId, tableId } = item;
    const { company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(
      `UpsertColumn for columnID ${columnId}, tableID ${tableId}, companyID: ${company.id}`,
    );

    let record = await this.answertableConfigEntity.findOne({
      where: {
        answertable_config_uuid: tableId,
        sec_company_id: company.id,
      },
    });
    if (!record) throw new HttpException('record does not exists', 404);
    let columns: any[] = parseJSON(record.answertable_config);
    let columnIndex = columns.findIndex((item) => item.columnId == columnId);
    if (columnIndex >= 0) columns[columnIndex] = item;
    else columns.push(item);

    await this.answertableConfigEntity.update(
      {
        answertable_config_uuid: tableId,
      },
      { answertable_config: JSON.stringify(columns) },
    );

    this.logger.debug(`Upserted columns for tableID ${tableId}`, { columns });

    return { message: 'Column Saved' };
  }
  async upsertTable(item: UpsertAnswertableConfigBodyDto) {
    const { id, config, description, name } = item;
    const { company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(`upsertTable for company ${company.id}`, {
      id,
      description,
      name,
    });

    let record = await this.answertableConfigEntity.findOne({
      where: {
        answertable_config_uuid: id,
        sec_company_id: company.id,
      },
    });

    let data: DeepPartial<AnswertableConfigEntity> = {
      answertable_config_uuid: id,
      answertable_config:
        record && config
          ? JSON.stringify(config)
          : JSON.stringify([
              {
                columnId: 'unique_id',
                name: 'Unique number',
                columnType: 'unique_id',
              },
            ]),
      sec_company_id: company.id,
    };
    if (name) data.answertable_name = name;
    if (description) data.answertable_description = description;

    if (!record) await this.answertableConfigEntity.insert(data);
    else
      await this.answertableConfigEntity.update(
        { answertable_config_uuid: id },
        data,
      );
    this.logger.debug(`upsertTable saved`);

    return !record
      ? { id, name, config: JSON.parse(data.answertable_config) }
      : { message: 'Table Saved' };
  }

  async getQuestionRow(answertable_id: string) {
    let config = await this.answertableConfigEntity.query(
      `
    SELECT qr.question_returnvalue
    FROM surv_surveyquestionrow qr
    LEFT JOIN (SELECT answertable_id, surv_surveyquestion_id FROM surv_surveyquestion WHERE answertable_id = $1 LIMIT 1) ss ON qr.surv_surveyquestion_id = ss.surv_surveyquestion_id
    WHERE ss.answertable_id=$1
    ORDER BY qr.ordernumber;
    `,
      [answertable_id],
    );
    config = config
      .map((i) => i.question_returnvalue)
      .filter((col) => isNaN(col));
    if (config[0] == undefined) return null;

    let cols = config.pop(); // add the deepest level as main selection value
    cols += " || ' ( ' || ";
    let sql;
    while (config.length > 0) {
      sql = config.pop();
      if (config.length > 0) {
        cols += 'COALESCE(' + sql + " || ' - ', '') || ";
      } else {
        cols += 'COALESCE(' + sql + " , '') || ";
      }
    }
    cols += "' ) '";

    let list = await this.answertableEntity.query(
      `SELECT ${cols} as name, unique_id as id FROM answertable WHERE answertable_tableid=$1`,
      [answertable_id],
    );
    return list;
  }
}
