import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnswertableConfigEntity } from '../database/entities/answertable-config.entity';
import { AnswertableEntity } from '../database/entities/answertable.entity';
import { AnswertableController } from './answertable.controller';
import { AnswertableConfigService } from './answertable-config.service';
import { AnswertableConfigController } from './answertable-config.controller';
import { AnswertableService } from './answertable.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AnswertableConfigEntity, AnswertableEntity]),
    ModuleLoggerModule.register('answertable'),
  ],
  exports: [AnswertableConfigService],
  controllers: [AnswertableConfigController, AnswertableController],
  providers: [AnswertableConfigService, AnswertableService],
})
export class AnswertableModule {}
