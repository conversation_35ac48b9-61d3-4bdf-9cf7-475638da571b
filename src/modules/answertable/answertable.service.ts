import { HttpException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { v4 as uuidv4 } from 'uuid';

import { AnswertableConfigService } from './answertable-config.service';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import {
  PostAnswertableBodyDto,
  PostAnswertableQueryDto,
  GetAnswertableQueryDto,
} from './dto';

import { AnswertableEntity } from '@entities';
import { parseJSON } from '@/shared/utils';

export class AnswertableService {
  constructor(
    private cls: ClsService,
    @InjectRepository(AnswertableEntity)
    private readonly answertableEntity: Repository<AnswertableEntity>,

    private answertableConfigService: AnswertableConfigService,
  ) {}

  async getList(query: GetAnswertableQueryDto) {
    const { tableId, search, direction, active, page } = query;
    const limit = 100;

    const { company } = this.cls.get<AuthorizedData>('user');

    let orderKey = active;
    let sortType: any =
      direction?.length > 0 ? direction.toUpperCase() : 'DESC';

    let answertableConfig = await this.answertableConfigService.getConfig(
      tableId,
      company.id,
    );

    if (!answertableConfig) throw new HttpException('No config found', 400);

    let config: any[] = parseJSON(answertableConfig.answertable_config);

    let q = this.answertableEntity
      .createQueryBuilder('a')
      .select('a.answertable_uuid')
      .addSelect(config.map((i) => `a.${i.columnId}`))
      .where('a.answertable_tableid =:tableId', { tableId });
    if (search?.length > 0)
      q.andWhere(
        new Brackets((qb) => {
          for (let item of config) {
            qb.orWhere(`a.${item.columnId} ILIKE :search`, {
              search: `%${search}%`,
            });
          }
          return qb;
        }),
      );
    if (orderKey?.length > 0) {
      q.orderBy(orderKey, sortType);
    }
    q.take(limit);
    q.skip(limit * page);
    let [rows, count] = await q.getManyAndCount();
    return {
      count,
      rows: rows.map((item) => {
        return {
          id: item.answertable_uuid,
          ...item,
          answertable_uuid: undefined,
        };
      }),
    };
  }

  async deleteList(ids: string[], tableId: string) {
    const { company } = this.cls.get<AuthorizedData>('user');

    await this.answertableEntity
      .createQueryBuilder()
      .delete()
      .whereInIds(ids)
      .andWhere('sec_company_id =:cid', { cid: company.id })
      .andWhere('answertable_tableid =:tableId', { tableId })
      .execute();
    return 'Data deleted';
  }

  async upsertItem(
    body: PostAnswertableBodyDto,
    query: PostAnswertableQueryDto,
  ) {
    const { id, unique_id } = body.row;
    const { tableId } = query;
    const { company } = this.cls.get<AuthorizedData>('user');

    let answertableConfig = await this.answertableConfigService.getConfig(
      tableId,
      company.id,
    );
    let config = parseJSON(answertableConfig?.answertable_config);
    if (!config) throw new HttpException('table is not found', 404);
    if (unique_id) {
      let item = await this.answertableEntity.findOne({
        where: { unique_id, answertable_tableid: tableId },
      });
      if (item?.answertable_uuid != id)
        throw new HttpException('Unique_id already exists', 400);
    }

    let answertable: AnswertableEntity;
    if (id) {
      answertable = await this.answertableEntity.findOne({
        where: {
          answertable_uuid: id,
        },
      });
      if (!answertable) throw new HttpException('ID not present', 400);
    }
    if (!answertable) {
      answertable = this.answertableEntity.create({
        answertable_uuid: uuidv4().toUpperCase(),
        answertable_tableid: tableId,
      });
    }
    for (let column of config) {
      answertable[column.columnId] = body.row[column.columnId];
    }
    if (!answertable.unique_id) {
      throw new HttpException('unique_id not set', 400);
    }
    await this.answertableEntity.save(answertable);
    return {
      id: answertable.answertable_uuid,
      message: id ? 'Data updated' : 'New record added',
    };
  }
}
