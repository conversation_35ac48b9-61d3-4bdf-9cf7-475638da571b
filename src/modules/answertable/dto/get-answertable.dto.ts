import { TableSort } from '@/shared/enums';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class GetAnswertableConfigServiceDto {
  public join?: boolean = false;
}

export class GetAnswertableQueryDto {
  @Type()
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  tableId: string;

  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  search: string;

  @Type()
  @ApiProperty({ required: false, default: 0 })
  @IsNumber()
  @IsOptional()
  page: number = 0;

  @Type()
  @ApiProperty({ required: false, enum: TableSort })
  @IsEnum(TableSort)
  @IsOptional()
  direction?: TableSort;

  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  active: string;
}
