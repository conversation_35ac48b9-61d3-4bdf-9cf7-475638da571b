import { AnswertableActionType } from '@/shared/enums/answertable.enum';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class PostAnswertableServiceQueryDto {
  @Type()
  @ApiProperty({
    enum: AnswertableActionType,
    required: true,
  })
  action: AnswertableActionType;
}

export class UpsertAnswertableConfigBodyDto {
  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  columnId: string;

  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  columnType: string;

  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  tableId: string;

  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  name: string;

  @Type()
  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  sortOrder: number;

  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  id: string;

  @Type()
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description: string;

  @Type(() => Object)
  @ApiProperty({ required: false, type: Object })
  @IsOptional()
  config: any;
}

export class PostAnswertableQueryDto {
  @Type()
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  tableId: string;
}

export class PostAnswertableBodyDto {
  @Type()
  @ApiProperty({ required: true })
  @IsString()
  @IsOptional()
  action: string;

  @Type()
  @ApiProperty({ required: true })
  @IsArray()
  @IsOptional()
  ids: string[];

  @Type(() => Object)
  @ApiProperty({ required: true, type: Object })
  row: any;
}
