import { Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager, EntityTarget, ObjectLiteral } from 'typeorm';

@Injectable()
export class DBHelperService {
  constructor(
    @InjectEntityManager()
    private em: EntityManager,
  ) {}

  async getNextId(table: EntityTarget<ObjectLiteral> | string, column: string) {
    const item = await this.em
      .createQueryBuilder(table, 't')
      .select(`MAX(${column}) as id`)
      .getRawOne();
    const lastId = item?.id || 0;
    return lastId + 1;
  }
}
