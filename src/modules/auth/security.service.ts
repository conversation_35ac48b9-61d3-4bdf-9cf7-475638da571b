import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { ResetPasswordBodyDto } from './dto/auth.request.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity } from '../database/entities/sec-user.entity';
import { Repository } from 'typeorm';
import { UserPassword, CompanyApiKeyEntity } from '@entities';
import { PasswordResponse } from '../../shared/enums/password.enum';
import * as moment from 'moment';
import { pbkdf2Sync, randomBytes } from 'crypto';
import { PasswordRulesService } from '../password-rules/password-rules.service';
import { AuthService } from './auth.service';
import { aliasPrefix } from '@/shared/utils';

const exec = require('child_process').exec;

@Injectable()
export class SecurityService {
  constructor(
    @Inject(forwardRef(() => AuthService))
    private authService: AuthService,
    private passwordRulesService: PasswordRulesService,
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
    @InjectRepository(UserPassword)
    private passwordRepo: Repository<UserPassword>,
    @InjectRepository(CompanyApiKeyEntity)
    private companyApiKeyRepo: Repository<CompanyApiKeyEntity>,
  ) {}

  validate(password, hash) {
    if (hash.split(':').length != 3) return false;
    const [salt, iterations, encoded] = hash.split(':');

    let result = pbkdf2Sync(
      password || '',
      Buffer.from(salt, 'hex'),
      +iterations,
      20,
      'sha1',
    ).toString(`hex`);
    return encoded === result.toUpperCase();
  }
  async generate(password): Promise<string | null> {
    const salt = randomBytes(8);
    const iterations = 5000;
    let hash = pbkdf2Sync(password, salt, iterations, 20, 'sha1').toString(
      'hex',
    );
    return `${salt
      .toString('hex')
      .toUpperCase()}:${iterations}:${hash.toUpperCase()}`;
  }

  async getPassword(filter: { id?: number; username?: string }) {
    let query = this.passwordRepo
      .createQueryBuilder('p')
      .select(aliasPrefix(['p.pass', 'p.sec_user_id', 'p.valid_till']))
      .leftJoin('p.user', 'u')
      .where('p.is_active = 1');

    if (filter.username) {
      query = query.andWhere(
        'LOWER(u.email) =:username OR LOWER(u.username) = :username',
        {
          username: filter.username.toLowerCase(),
        },
      );
    } else {
      query = query.andWhere('p.sec_user_id =:id', { id: filter.id });
    }

    const item = await query.getRawOne<{
      sec_user_id: number;
      pass: string;
      valid_till: string;
    }>();

    return item;
  }

  async getCompanyPassword(filter:{ id?: string  }){
    return await this.companyApiKeyRepo.findOne({
      where: { api_key: filter.id },
    });
  }

  async setUserPassword(sec_user_id: number, password: string) {
    let user = await this.userRepo.findOne({
      where: { sec_user_id },
    });

    let password_rules = await this.passwordRulesService.getPasswordRules(
      sec_user_id,
    );

    let settings = password_rules.pwd_settings;

    let passwords = await this.passwordRepo
      .createQueryBuilder('p')
      .where('p.sec_user_id =:sec_user_id', { sec_user_id })
      .take(settings.passwordHistory || 10)
      .getMany();

    for (let { pass } of passwords) {
      if (pass) {
        if (await this.validate(password, pass)) {
          return {
            status: PasswordResponse.PasswordCycleFailed,
            passwordHistory: settings.passwordHistory,
          };
        }
      }
    }

    if (settings.disallowUsernameInPassword && password.match(user.email)) {
      return { status: PasswordResponse.PasswordIsUsername, settings };
    }

    let validation = '^';

    if (settings.mustContain?.lowercase) {
      validation += '(?=.*?[a-z])';
    }

    if (settings.mustContain?.uppercase) {
      validation += '(?=.*?[A-Z])';
    }

    if (settings.mustContain?.number) {
      validation += '(?=.*?[0-9])';
    }

    if (settings.mustContain?.punctuation) {
      validation += '(?!.*[ ])(?=.*[^A-Za-z0-9])';
    }

    validation += '.{' + settings.mustContain?.chars + ',}$';
    let regexp = new RegExp(validation, 'g');

    if (!regexp.test(password)) {
      return {
        status: PasswordResponse.NotStrong,
        settings,
      };
    }

    let hash_password = await this.generate(password);

    await this.passwordRepo
      .createQueryBuilder()
      .update({ is_active: 0 })
      .where('sec_user_id =:sec_user_id', { sec_user_id })
      .execute();

    await this.passwordRepo
      .createQueryBuilder()
      .insert()
      .values({
        is_active: 1,
        sec_user_id,
        pass: hash_password,
        valid_till: moment().add(settings.validForDays, 'days'),
      })
      .execute();

    await this.userRepo
      .createQueryBuilder()
      .update({
        activation_date: null,
        activation_pwd: null,
        failed_count: null,
        failed_last_date: null,
      })
      .where('sec_user_id =:sec_user_id', { sec_user_id })
      .execute();
    return { status: PasswordResponse.PasswordChaged };
  }

  async resetPassword(params: ResetPasswordBodyDto) {
    let user = await this.userRepo.findOne({
      where: { activation_pwd: params.id },
    });
    if (!user)
      return {
        success: false,
        status: PasswordResponse.ActivationWrong,
        msg: 'user with this activation key is not found',
      };

    if (new Date(user.activation_date) < new Date()) {
      return {
        success: false,
        status: PasswordResponse.ActivationExpired,
        msg: 'activation key is expired',
      };
    }

    let result = await this.setUserPassword(user.sec_user_id, params.password);
    if (result.status == PasswordResponse.PasswordChaged) {
      let authResult = await this.authService.authenticateUser({
        username: user.email,
        password: params.password,
      });
      return {
        success: true,
        msg: 'Password Changed',
        ...authResult,
        token_type: 'Bearer',
      };
    } else {
      return {
        success: false,
        error: result.status,
        msg: {
          3: 'contains_username',
          4: 'passwordCycles',
          5: 'newPasswordNotStrong',
        }[result.status],
        settings: result.settings,
      };
    }
  }
}
