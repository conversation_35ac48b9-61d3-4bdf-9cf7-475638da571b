import { Controller, Post, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ClsService } from 'nestjs-cls';

import { AuthorizedData } from '../../interfaces/authorized-request.interface';
import { LogOffService } from './logoff.service';
import { AuthGuard } from '@/shared/guards/auth.guard';

@ApiTags('Auth')
@ApiBearerAuth()
@Controller({
  path: 'logoff',
  version: '2',
})
export class LogOffController {
  constructor(
    private clsService: ClsService,
    private logOffService: LogOffService,
  ) {}

  @Post()
  @ApiOperation({ description: 'Log off and remove/invalidate user token' })
  @ApiResponse({ status: 200, description: 'Logged off' })
  @UseGuards(AuthGuard)
  async logOut() {
    const { user } = this.clsService.get<AuthorizedData>('user');
    if (user) await this.logOffService.removeToken(user.id);
    return {
      logoff: {
        result: true,
        message: 'Logged off',
      },
    };
  }
}
