import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserTokenEntity } from '../../../database/entities/sec-user-ws-token.entity';

export class LogOffService {
  constructor(
    @InjectRepository(UserTokenEntity)
    private readonly userTokenRepo: Repository<UserTokenEntity>,
  ) {}

  async removeToken(sec_user_ws_token_id: number) {
    return await this.userTokenRepo
      .createQueryBuilder()
      .delete()
      .where('sec_user_ws_token_id =:sec_user_ws_token_id', {
        sec_user_ws_token_id,
      })
      .execute();
  }
}
