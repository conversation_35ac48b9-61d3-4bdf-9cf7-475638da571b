import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserTokenEntity } from '../../../database/entities/sec-user-ws-token.entity';
import { LogOffController } from './logoff.controller';
import { LogOffService } from './logoff.service';

@Module({
  imports: [TypeOrmModule.forFeature([UserTokenEntity])],
  controllers: [LogOffController],
  providers: [LogOffService],
})
export class LogOffModule {}
