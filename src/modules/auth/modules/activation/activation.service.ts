import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import * as moment from 'moment';

import { FeedbackPropertiesEntity, UserEntity } from '@entities';
import { ActivationType, activationExpire } from '@/shared/enums';
import { FEEDBACK_PROPERTY_KEYS } from '@/config/contents';

@Injectable()
export class ActivationService {
  constructor(
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
    @InjectRepository(FeedbackPropertiesEntity)
    private readonly feedbackProperiesRepo: Repository<FeedbackPropertiesEntity>,
  ) {}

  generateActivationKey() {
    return uuidv4().replace(/-/g, '').toUpperCase();
  }

  async updateActivationPwd({ sec_user_id }: UserEntity) {
    let activation_pwd = this.generateActivationKey();

    const activation_date = moment()
      .add(activationExpire.Time, activationExpire.Unit as any)
      .toISOString();

    await this.userRepo
      .createQueryBuilder()
      .update({
        activation_pwd,
        activation_date,
        islocked: null,
      })
      .where(`sec_user_id =:sec_user_id`, { sec_user_id })
      .execute();

    return activation_pwd;
  }

  async generateActivation(type: ActivationType, user: UserEntity) {
    let feedbackProperty = await this.feedbackProperiesRepo
      .createQueryBuilder()
      .where('feedback_property_id =:id', {
        id: FEEDBACK_PROPERTY_KEYS.BACK_END_URL,
      })
      .getOne();
    let baseUrl = feedbackProperty?.property_value?.trim();

    let activationPwd = await this.updateActivationPwd(user);
    return {
      key: activationPwd,
      baseUrl,
      url: `${baseUrl}/authentication/set-password/${type}/${activationPwd}`,
    };
  }

  async generateFrontEndActivation(type: ActivationType, user: UserEntity) {
    let feedbackProperty = await this.feedbackProperiesRepo
      .createQueryBuilder()
      .where('feedback_property_id =:id', {
        id: FEEDBACK_PROPERTY_KEYS.FRONT_END_URL,
      })
      .getOne();
    let baseUrl = feedbackProperty?.property_value?.trim();

    let activationPwd = await this.updateActivationPwd(user);
    return {
      key: activationPwd,
      baseUrl,
      url: `${baseUrl}/authentication/set-password/${type}/${activationPwd}`,
    };
  }
}
