import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActivationService } from './activation.service';
import { FeedbackPropertiesEntity, UserEntity } from '@entities';

@Module({
  imports: [TypeOrmModule.forFeature([FeedbackPropertiesEntity, UserEntity])],
  providers: [ActivationService],
  exports: [ActivationService],
})
export class ActivationModule {}
