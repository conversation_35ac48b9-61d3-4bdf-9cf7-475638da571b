import { HttpException, Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

import { AuthorizedData } from '../../interfaces/authorized-request.interface';

import { SecurityService } from '../../security.service';
import { UsersService } from '../../../users/users.service';
import { ProgramSettingsService } from '../../../common/program-settings/program-settings.service';

import { ChangePasswordBodyDto } from './dto/change-password.dto';
import { PasswordResponse } from '@/shared/enums';

@Injectable()
export class ChangePasswordService {
  constructor(
    private cls: ClsService,
    private securityService: SecurityService,
    private userService: UsersService,
    private programSettingService: ProgramSettingsService,
  ) {}
  async changePassword(params: ChangePasswordBodyDto) {
    let { user } = await this.cls.get<AuthorizedData>('user');
    let uPass = await this.securityService.getPassword({ id: user.id });
    const pass = uPass?.pass;
    let { pwd_settings } = await this.programSettingService.getPasswordSettings(
      user.id,
    );
    let validatePassword = await this.securityService.validate(
      params.previous_password,
      pass,
    );
    if (!validatePassword)
      throw new HttpException(
        {
          success: false,
          status: PasswordResponse.CurFailed,
          msg: 'Current password is not valid',
        },
        412,
      );

    let passwordHistory = await this.userService.getUserPasswords(user.id);
    for (let item of passwordHistory) {
      let validate = this.securityService.validate(params.password, item.pass);
      if (validate)
        throw new HttpException(
          {
            success: false,
            status: PasswordResponse.PasswordCycleFailed,
            msg: 'This password has recently been used by you. Please choose another password',
          },
          412,
        );
    }
    if (
      pwd_settings.disallowUsernameInPassword &&
      params.password.match(user.email)
    ) {
      throw new HttpException(
        {
          success: false,
          status: PasswordResponse.PasswordIsUsername,
          msg: 'Password cannot contain username',
        },
        412,
      );
    }
    let regExStr = '^';
    if (pwd_settings.mustContain.lowercase) {
      regExStr += '(?=.*?[a-z])';
    }

    if (pwd_settings.mustContain.uppercase) {
      regExStr += '(?=.*?[A-Z])';
    }

    if (pwd_settings.mustContain.number) {
      regExStr += '(?=.*?[0-9])';
    }

    if (pwd_settings.mustContain.punctuation) {
      regExStr += '(?!.*[ ])(?=.*[^A-Za-z0-9])';
    }

    regExStr += '.{' + pwd_settings.mustContain.chars + ',}$';
    let checkRegex = new RegExp(regExStr, 'g');
    if (!checkRegex.test(params.password)) {
      throw new HttpException(
        {
          success: false,
          status: PasswordResponse.NotStrong,
          msg: 'Password does not meet the requirements!',
        },
        412,
      );
    }

    let newPasswordHash = await this.securityService.generate(params.password);

    this.userService.updatePassword(
      user.id,
      newPasswordHash,
      pwd_settings.validForDays,
    );

    return {
      success: true,
      status: PasswordResponse.Ok,
      msg: 'Password changed',
    };
  }
}
