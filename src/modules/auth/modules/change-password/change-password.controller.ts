import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ChangePasswordService } from './change-password.service';
import { ChangePasswordBodyDto } from './dto/change-password.dto';

@ApiTags('Auth')
@ApiBearerAuth()
@Controller({
  path: 'change_password',
  version: '2',
})
export class ChangePasswordController {
  constructor(private changePasswordService: ChangePasswordService) {}

  @Post()
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary:'Change password',
    description:`Change password for user <br />
    Body must contain 2 properties, the previous and the new password` })
  @ApiResponse({ status: 412, description: 'When old password is incorrect or new password does not match criteria. Body contains error details' })
  @ApiResponse({ status: 201, description: 'Password changed' })
  changePassword(@Body() body: ChangePasswordBodyDto) {
    return this.changePasswordService.changePassword(body);
  }
}
