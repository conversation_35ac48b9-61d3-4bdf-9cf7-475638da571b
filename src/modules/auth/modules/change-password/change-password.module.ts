import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthModule } from '../../auth.module';
import { UsersModule } from '../../../users/users.module';
import { ProgramSettingsModule } from '../../../common/program-settings/program-settings.module';

import { ChangePasswordController } from './change-password.controller';

import { ChangePasswordService } from './change-password.service';
import { UserEntity } from '@entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity]),
    AuthModule,
    UsersModule,
    ProgramSettingsModule,
  ],
  controllers: [ChangePasswordController],
  providers: [ChangePasswordService],
})
export class ChangePasswordModule {}
