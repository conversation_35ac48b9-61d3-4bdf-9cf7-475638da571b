import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { EmailModule } from '../../../email/email.module';

import { LoggerService } from '@lib/logger/logger.service';
import { ForgotPasswordService } from './forgot-password.service';
import { ActivationService } from '../activation/activation.service';
import { LanguageService } from '../../../../shared/utils/language.service';
import { ProgramSettingsModule } from '@/modules/common/program-settings/program-settings.module';

import { ForgotPasswordController } from './forgot-password.controller';

import {
  FeedbackPropertiesEntity,
  LogEventEntity,
  SecProgramSettingsEntity,
  UserEntity,
  UserTokenEntity,
} from '@entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      UserTokenEntity,
      LogEventEntity,
      FeedbackPropertiesEntity,
      SecProgramSettingsEntity,
    ]),
    ProgramSettingsModule,
    EmailModule,
  ],
  controllers: [ForgotPasswordController],
  providers: [
    ForgotPasswordService,
    LoggerService,
    ActivationService,
    LanguageService,
  ],
})
export class ForgotPasswordModule {}
