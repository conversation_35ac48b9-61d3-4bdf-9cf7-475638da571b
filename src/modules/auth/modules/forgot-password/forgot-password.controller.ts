import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ForgotPasswordBodyDto } from './dto/post-forgot-password.dto';
import { ForgotPasswordService } from './forgot-password.service';

@ApiTags('Auth')
@Controller({
  path: 'forgot_password',
  version: '2',
})
export class ForgotPasswordController {
  constructor(private forgotPasswordService: ForgotPasswordService) {}

  @Post()
  @ApiOperation({
    summary: 'Forgot password',
    description: 'Send an email to the user with a link to reset the password. Will only return an error if the email is used for multiple accounts, all other cases it will return a 201 response.'})
  @ApiResponse({ status: 201, description: 'Reset email sent' })
  @ApiResponse({ status: 400, description: 'Email used for multiple accounts' })
  async forgotPassword(@Body() body: ForgotPasswordBodyDto) {
    return this.forgotPasswordService.forgotPassword(body);
  }
}
