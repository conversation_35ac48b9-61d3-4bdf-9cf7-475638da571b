import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { ActivationService } from '../activation/activation.service';
import { EmailService } from '@/modules/email/email.service';
import { LoggerService } from '@lib/logger/logger.service';
import { LanguageService } from '@/shared/utils/language.service';
import { TranslateService } from '@/core/modules/translate/translate.service';

import { LogLevel } from '@lib/logger/LogOptions';
import { ForgotPasswordBodyDto } from './dto/post-forgot-password.dto';

import { UserEntity } from '@entities';
import { ActivationType } from '@/shared/enums';

@Injectable()
export class ForgotPasswordService {
  constructor(
    private activationService: ActivationService,
    private emailService: EmailService,
    private loggerService: LoggerService,
    private languageService: LanguageService,
    private translate: TranslateService,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
  ) {}

  async forgotPassword(params: ForgotPasswordBodyDto) {
    const { email } = params;
    if (!email) return { succes: false };
    let users = await this.userRepo
      .createQueryBuilder()
      .select()
      .where('LOWER(email) =:email', { email: email.toLowerCase() })
      .getMany();
    let user = users[0];

    if (users.length > 1)
      throw new HttpException(
        {
          success: false,
          msg: 'this email is used on multiple accounts',
        },
        400,
      );

    if (!user) {
      this.loggerService.createLog({
        logger_name: 'forgot password',
        log_message: `Non-existing mail address requested mail activation "${email}"`,
        additional_info: null,
        log_level: LogLevel.LOG,
      });
    } else {
      let { key, url, baseUrl } =
        await this.activationService.generateActivation(
          ActivationType.Reset,
          user,
        );
      let mailTemplates = this.languageService.getFile(
        this.translate.lang,
        'mail',
      );

      let template = this.emailService.generateActivationEmailTemplate({
        activation_link: url,
        application_link: baseUrl,
        scaffold: mailTemplates.pick('activation.base_api'),
        content: mailTemplates.pick('forgotten.password'),
        head: this.translate.t('props.ccs.lbl.msg.welcome_feedback'),
        email: user.email,
        firstname: user.firstname,
        name: user.username,
      });

      try {
        let emailResult = await this.emailService.sendSystemEmail({
          to: user.email,
          subject: 'Forgot Password',
          content: template,
        });
        return emailResult;
      } catch (err) {
        throw new HttpException(
          `We have issue on sending mails right now, please contact managers`,
          HttpStatus.FORBIDDEN,
        );
      }
    }

    return { success: true, msg: 'Password reset mail sent' };
  }
}
