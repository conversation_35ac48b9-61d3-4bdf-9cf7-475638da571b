import { CompanyType } from '@/shared/enums';
import { UserCompanyEntity } from '@entities';

/**
 * Interface for authorized data without user information
 * Based on AuthorizedData but with user object removed
 */
export interface AuthorizedCompanyData {
  tokenId: number;
  token: string;
  expire: Date;
  keys?: string[];
  company: AuthorizedCompany;
  userCompany?: AuthorizedUserCompany;
}

export interface AuthorizedCompany {
  id: number;
  type: CompanyType;
  code: string;
  name: string;
  isParent: boolean;
}

export interface AuthorizedUserCompany
  extends Pick<UserCompanyEntity, 'sec_group'> {}
