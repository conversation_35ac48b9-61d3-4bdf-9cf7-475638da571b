import { CompanyType } from '@/shared/enums';
import { UserCompanyEntity, UserEntity } from '@entities';
import { Request } from 'express';

export interface AuthorizedData {
  tokenId: number;
  token: string;
  expire: Date;
  keys?: string[];
  user?: AuthorizedUser;
  company: AuthorizedCompany;
  userCompany?: AuthorizedUserCompany;
}

export interface AuthorizedRequest extends Request {
  user: AuthorizedUser;
  company: AuthorizedCompany;
}

export interface AuthorizedUser
  extends Pick<
    UserEntity,
    | 'email'
    | 'firstname'
    | 'salutation'
    | 'username'
    | 'language'
    | 'fullname'
    | 'language'
  > {
  id: number;
}

export interface AuthorizedCompany {
  id: number;
  type: CompanyType;
  code: string;
  name: string;
  isParent: boolean;
}

export interface AuthorizedUserCompany
  extends Pick<UserCompanyEntity, 'sec_group'> {}
