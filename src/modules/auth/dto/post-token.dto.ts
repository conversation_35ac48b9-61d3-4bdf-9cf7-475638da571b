import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, MinLength } from 'class-validator';

export class SignInBodyDto {
  @Type()
  @IsString()
  @MinLength(2, { context: '2' })
  @ApiProperty({ required: true })
  username: string;

  @Type()
  @IsString()
  @ApiProperty({ required: true })
  password: string;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  hash?: string;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  recaptcha?: string;
}
