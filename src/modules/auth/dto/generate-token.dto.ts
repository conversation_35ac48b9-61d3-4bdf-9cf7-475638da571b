import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsString } from 'class-validator';
import { TokenPrivilege } from '../../../shared/enums/token-privilege.enum';

export class GenerateTokenDto {
  @Type()
  @IsNumber()
  sec_user_id: number;

  @Type()
  @IsNumber()
  sec_company_id: number;

  @Type()
  @IsEnum(TokenPrivilege)
  token_privilege: TokenPrivilege;

  @Type()
  @IsNumber()
  expiry_hours: number;

  @Type()
  @IsString()
  hash: string;
}
