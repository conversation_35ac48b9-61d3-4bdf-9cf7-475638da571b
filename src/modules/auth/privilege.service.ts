import { InjectRepository } from '@nestjs/typeorm';
import {
  CompanyEntity,
  SecGroupEntity,
  SecKey,
  UserCompanyEntity,
  SecUserKey,
  SecUserRight,
  UserEntity,
} from '../database/entities';
import { Repository } from 'typeorm';
import { CompanyType, SolutionType } from '@/shared/enums';
import {
  GetUserPrivilegesFilter,
  GetUserPrivilegesParams,
} from '../users/dto/get-sec-users.dto';
import { ClsService } from 'nestjs-cls';
import { AuthorizedData } from './interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents/cls.contents';
import { RESTRICTED_CONTENT } from '@/config/restricted_content';

export class PrivilegeService {
  constructor(
    private cls: ClsService,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(SecUserKey)
    private readonly userKeyRepo: Repository<SecUserKey>,
    @InjectRepository(SecKey)
    private readonly secKeyRepo: Repository<SecKey>,
  ) {}

  async userKeys(user_id: number, company_id: number): Promise<string[]> {
    let company: Partial<CompanyEntity> = (
      await this.userRepo.query(
        `SELECT company_type FROM sec_company WHERE sec_company_id =$1`,
        [company_id],
      )
    )?.[0];

    let list = await this.companyRepo.query(
      `
      SELECT DISTINCT sk.name FROM sec_key sk 
      LEFT JOIN sec_user_right sur ON sur.key_id = sk.key_id 
      LEFT JOIN sec_group sg ON sg.group_id = sur.group_id 
      LEFT JOIN sec_users su ON sg.group_id = su.sec_group 
      LEFT JOIN sec_user_company suc ON suc.sec_group = sg.group_id  AND suc.sec_company_id =  $1
      WHERE suc.sec_user_id = $2 AND sk.solution_type = 2
      `,
      [company_id, user_id],
    );

    if (company?.company_type == CompanyType.BSH) {
      let userKeys = await this.userKeyRepo.query(
        `
        SELECT DISTINCT sk.name FROM sec_user_keys suk
        LEFT JOIN sec_key sk ON sk.key_id = suk.key_id
        WHERE suk.sec_user_id = $1  AND suk.sec_company_id = $2
      `,
        [user_id, company_id],
      );
      list = new Set([
        ...list.map((item) => item.name),
        ...userKeys.map((item) => item.name),
      ]);
    } else {
      list = new Set(list.map((item) => item.name));
    }

    return [...list].filter((key) => key);
  }

  async filteredUserKeys(filter?: GetUserPrivilegesFilter) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    return this.filteredKeys({
      userId: user.id,
      companyId: company.id,
      filter,
    });
  }

  async filteredKeys({ filter, userId, companyId }: GetUserPrivilegesParams) {
    let query = this.secKeyRepo
      .createQueryBuilder()
      .select('DISTINCT sk.name')
      .from(SecKey, 'sk')
      .leftJoin(SecUserRight, 'sur', 'sk.key_id = sur.key_id')
      .leftJoin(SecGroupEntity, 'sg', 'sur.group_id = sg.group_id')
      .innerJoin(UserCompanyEntity, 'suc', 'sg.group_id = suc.sec_group')
      .where('suc.sec_user_id = :userId', { userId })
      .andWhere('suc.sec_company_id = :companyId', { companyId })
      .andWhere('sk.solution_type = :solutionType', {
        solutionType: SolutionType.Admin,
      });

    let query2 = this.userKeyRepo
      .createQueryBuilder('suk')
      .select('DISTINCT sk.name')
      .leftJoin(SecKey, 'sk', 'sk.key_id = suk.key_id')
      .where('suk.sec_user_id =:userId', { userId })
      .andWhere('suk.sec_company_id =:companyId', { companyId });

    if (filter?.sec_keys?.length) {
      query = query.andWhere('sk.key_id IN (:...secKeys)', {
        secKeys: filter.sec_keys,
      });

      query2 = query2.andWhere('sk.key_id IN (:...secKeys)', {
        secKeys: filter.sec_keys,
      });
    }

    if (filter?.names?.length) {
      query = query.andWhere('sk.name IN (:...names)', {
        names: filter.names,
      });
      query2 = query2.andWhere('sk.name IN (:...names)', {
        names: filter.names,
      });
    }

    let list = await query.getRawMany<SecKey>();
    let list2 = await query2.getRawMany<SecKey>();

    return [
      ...list.map((item) => item.name),
      ...list2.map((item) => item.name),
    ];
  }

  async restrictedContentFilter<T>(
    list: T[],
    field: keyof T,
    checks: string[],
    rules?: string[],
  ) {
    if (!rules) {
      rules = await this.filteredUserKeys({
        names: checks,
      });
    }
    for (let check of checks) {
      if (!rules?.includes(check)) {
        const restriced = RESTRICTED_CONTENT[check];
        list = list.filter((item) => {
          const col: any = item[field];
          return !restriced.find((i) => i?.includes(col) || col?.includes(i));
        });
      }
    }

    return list;
  }

  async restrictContent<T>(list: T[], checks: string[], rules?: string[]) {
    if (!rules) {
      rules = await this.filteredUserKeys({
        names: checks,
      });
    }
    for (let check of checks) {
      if (!rules?.includes(check)) {
        const restriced = RESTRICTED_CONTENT[check];
        list = list.map((item) => {
          for (let field of restriced) {
            if (item[field]) item[field] = null;
          }
          return item;
        });
      }
    }

    return list;
  }
}
