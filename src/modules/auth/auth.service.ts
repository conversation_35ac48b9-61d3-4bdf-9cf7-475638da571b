import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, IsNull, MoreThan, Repository } from 'typeorm';

import { FailedLoginEntity, UserTokenEntity } from '@entities';
import { randomNumber } from '@/shared/utils';
import { AI_MODULES, TokenPrivilege } from '@/shared/enums';

import { SecurityService } from './security.service';
import { UsersService } from '../users/users.service';
import { CompanyService } from '../company/company.service';
import { PrivilegeService } from './privilege.service';
import { TranslateService } from '@/core/modules/translate/translate.service';

import { GenerateTokenDto } from './dto/generate-token.dto';

import * as moment from 'moment';
import { v4 as uuidv4 } from 'uuid';
import { ProgramSettingsService } from '../common/program-settings/program-settings.service';
import { SignInBodyDto } from './dto/post-token.dto';
import { GoogleRecaptchaValidator } from '@nestlab/google-recaptcha';

@Injectable()
export class AuthService {
  constructor(
    private privilegeService: PrivilegeService,
    private recaptchaValidator: GoogleRecaptchaValidator,
    private securityService: SecurityService,
    private userService: UsersService,
    private companyService: CompanyService,
    private programSettingsService: ProgramSettingsService,
    @InjectRepository(UserTokenEntity)
    private userTokenEntity: Repository<UserTokenEntity>,
    @InjectRepository(FailedLoginEntity)
    private secFailedLoginRepo: Repository<FailedLoginEntity>,
    private translate: TranslateService,
  ) {}

  async validatePassword(
    username: string,
    password: string,
    captcha_used = false,
  ) {
    const passwordData = await this.securityService.getPassword({ username });

    let validatePassword = await this.securityService.validate(
      password,
      passwordData.pass,
    );
    if (!validatePassword) {
      await this.failAttempt({
        username,
        captcha_used,
        captcha_failed: false,
      });
    }

    await this.clearAttempts(username);

    if (new Date(passwordData.valid_till) < new Date()) {
      return {
        success: false,
        msg: 'ccs.lbl.info.password.expired',
        status: 2008,
      };
    }

    return {
      success: true,
      user_id: passwordData.sec_user_id,
    };
  }

  async authenticateUser(params: SignInBodyDto) {
    const { username, password, recaptcha } = params;
    let status = await this.checkIsUserAllowed(username);

    if (!status.allowed) {
      return {
        msg: this.translate.t('prop.ccs.api.msg.user_blocked'),
        status: 2001,
      };
    }

    if (status.captcha_required)
      await this.validateCaptcha(username, recaptcha);

    let passwordResult = await this.validatePassword(username, password);

    if (!passwordResult.success || !passwordResult.user_id) {
      await this.failAttempt({
        username,
        captcha_used: status.captcha_required,
        captcha_failed: false,
      });
    }

    const userCompanies = await this.companyService.getUserCompanies(
      passwordResult.user_id,
    );
    const user = await this.userService.getUser(passwordResult.user_id);
    const company = userCompanies[0];

    let sec_company_id = company.sec_company_id;
    let company_code = company.company_code;
    let company_type = company.company_type;

    let companies = userCompanies.map((item) => {
      return {
        sec_company_id: item.sec_company_id,
        company_code: item.company_code,
        country_code: item.country_code,
        is_parent_company: item.is_parent_company === 1,
        default_language_key: item.default_language_key,
      };
    });

    let companyResult: any = {};
    const companyCount = companies?.length || 0;

    if (companyCount == 1) {
      companyResult = {
        keys: await this.privilegeService.userKeys(
          user.sec_user_id,
          sec_company_id,
        ),
        sec_company_id,
        company: company_code,
        company_type,
        is_parent_company: !!company.is_parent_company,
        backend_allowed: await this.companyService.checkBackendIsAllowed(
          +company.sec_group,
        ),
        new_dashboards: await this.companyService.getDashboardCount(
          sec_company_id,
          user.sec_user_id,
        ),
      };
    } else {
      companyResult = {
        companies: companies.map((item) => item.company_code).sort(),
        keys: [],
      };
    }

    let tokenResult = await this.generateToken({
      sec_user_id: user.sec_user_id,
      sec_company_id: companyResult.sec_company_id || 0,
      expiry_hours: 10,
      hash: null,
      token_privilege: TokenPrivilege.FrontEnd,
    });

    let ai_modules = [];
    if (companyCount === 1) {
      const settings = await this.programSettingsService.get(
        company.sec_company_id,
      );
      const props = settings?.company_properties;

      companyResult.neutral_sentiment_allowed = !!settings.neutral_sentiment;
      companyResult.story_widget_activated =
        props?.storyWidget?.flag_story_widget === 1;
      companyResult.ai_active = props?.use_ai || 0;

      const ai_modules_entries = Object.entries(AI_MODULES);
      ai_modules = props?.ai_modules?.map(
        (item) => ai_modules_entries.find(([name, key]) => key === item)?.[0],
      );

      ai_modules = ai_modules?.filter((item) => item) || [];
    }

    const isSuperUser =
      companyResult.companies && companyResult.companies.includes('Welcome')
        ? true
        : undefined;

    const date_of_birth = user.date_of_birth
      ? moment(user.date_of_birth).format('YYYY-MM-DD')
      : '';
    const work_anniversary = user.work_anniversary
      ? moment(user.work_anniversary).format('YYYY-MM-DD')
      : '';

    return {
      status: 1000,
      has_opened_settings: user.has_opened_settings == 1,
      ai_modules,
      access_token: tokenResult.access_token,
      expires_in: tokenResult.expires_in,
      expire_date: `new Date(${tokenResult.expire_date.getTime()})`,
      token_type: tokenResult.token_type,
      user_id: user.sec_user_id,
      email: user.email || '',
      firstname: user.firstname || '',
      name: user.username || '',
      avatar: this.userService.getAvatar(user.avatar, user.avatar_type),
      ...companyResult,
      isSuperUser,
      job_title: user.job_title || '',
      phone: user.phone || '',
      date_of_birth,
      work_anniversary,
      new_hot_news: user.new_news_articles,
      salutation: user.salutation || '',
      initials: user.firstname.charAt(0) + user.username.charAt(0),
    };
  }

  async validateCaptcha(username: string, recaptcha: string) {
    let result: any;
    let captcha_used = false;
    if (recaptcha?.length > 0) {
      result = await this.recaptchaValidator.validate({
        response: recaptcha,
      });
      captcha_used = true;
    }
    if (!result?.success) {
      await this.failAttempt({
        username,
        captcha_used,
        captcha_failed: true,
      });
    }
  }

  async generateToken(params: GenerateTokenDto) {
    const {
      sec_user_id,
      sec_company_id,
      token_privilege,
      hash,
      expiry_hours = 24,
    } = params;
    let where: FindOptionsWhere<UserTokenEntity> = {
      expiry_date: MoreThan(new Date()),
      token_privilege,
      hash: hash ? hash : IsNull(),
    };
    if (token_privilege == TokenPrivilege.CompanyAPI)
      where.sec_company_id = sec_company_id;
    else where.sec_user_id = sec_user_id;

    let checkToken = await this.userTokenEntity.findOne({
      where,
      select: ['sec_user_ws_token_id'],
    });
    let id = checkToken?.sec_user_ws_token_id;

    if (!id) {
      let data: Partial<UserTokenEntity> = {
        sec_user_id,
        sec_company_id,
        token_privilege,
        hash,
        creation_date: new Date(),
        token: uuidv4().toUpperCase().replace(/-/g, ''),
        expiry_date: moment().add(expiry_hours, 'hours').toDate(),
      };

      id = await this.createWsToken(data);
    }
    let item = await this.userTokenEntity
      .createQueryBuilder('t')
      .leftJoinAndSelect('t.user', 'user')
      .where('t.sec_user_ws_token_id =:id', { id })
      .getOne();

    return {
      access_token: item.token,
      token_type: 'Bearer',
      expires_in: moment
        .duration(moment(item.expiry_date).diff(moment()))
        .asSeconds(),
      expire_date: item.expiry_date,
      expiry_hours,
      user_id: sec_user_id,
    };
  }

  async createWsToken(data: Partial<UserTokenEntity>): Promise<number | null> {
    let result = await this.userTokenEntity.save(
      this.userTokenEntity.create(data),
    );
    return result?.sec_user_ws_token_id;
  }

  async checkIsUserAllowed(username: string) {
    let date = moment().format('yyyy-MM-DD');
    let resp = await this.secFailedLoginRepo.query(
      `SELECT COUNT(sec_failed_login_id) AS cnt FROM sec_failed_logins WHERE username = $1 AND expires_on > $2`,
      [username, date],
    );
    let cnt = +resp?.[0]?.cnt;
    return {
      captcha_required: cnt >= 5,
      allowed: cnt < 10,
    };
  }

  async clearAttempts(username: string) {
    await this.secFailedLoginRepo
      .createQueryBuilder()
      .delete()
      .where({ username: username.toLowerCase() })
      .execute();
  }

  async failAttempt(params: Partial<FailedLoginEntity>) {
    const { username, captcha_failed, captcha_used } = params;
    let date = moment().add(randomNumber(120, 600), 'minutes');
    await this.secFailedLoginRepo
      .createQueryBuilder()
      .insert()
      .values({
        username: username.toLowerCase(),
        sec_failed_login_id: uuidv4(),
        expires_on: date,
        captcha_used: +captcha_used,
        captcha_failed: +captcha_failed,
      })
      .execute();
    throw new HttpException(
      {
        success: false,
        msg: this.translate.t('prop.ccs.api.msg.auth_failed'),
        status: 2000,
        captcha_used,
        captcha_failed,
      },
      HttpStatus.FORBIDDEN,
    );
  }
}
