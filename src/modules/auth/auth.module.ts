import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { SecurityService } from './security.service';
import { UsersModule } from '../users/users.module';
import { DashboardLayoutModule } from '../dashboard-layout/dashboard-layout.module';
import { PasswordRulesModule } from '../password-rules/password-rules.module';
import { PrivilegeService } from './privilege.service';
import {
  CompanyApiKeyEntity,
  CompanyEntity,
  FailedLoginEntity,
  SecGroupEntity,
  SecKey,
  SecUserKey,
  SecUserRight,
  UserCompanyEntity,
  UserEntity,
  UserPassword,
  UserTokenEntity
} from "@entities";
import { ValidateUserService } from './validate-user.service';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import { CompanyModule } from '../company/company.module';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserTokenEntity,
      FailedLoginEntity,
      UserEntity,
      SecUserKey,
      UserPassword,
      SecKey,
      SecUserRight,
      SecGroupEntity,
      UserCompanyEntity,
      CompanyEntity,
      CompanyApiKeyEntity,
    ]),
    UsersModule,
    DashboardLayoutModule,
    PasswordRulesModule,
    ProgramSettingsModule,
    CompanyModule,
  ],
  providers: [
    AuthService,
    SecurityService,
    PrivilegeService,
    ValidateUserService,
  ],
  exports: [
    AuthService,
    SecurityService,
    PrivilegeService,
    ValidateUserService,
  ],
  controllers: [AuthController],
})
export class AuthModule {}
