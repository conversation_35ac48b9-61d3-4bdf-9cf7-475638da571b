import * as moment from 'moment';
import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { I18n, I18nContext } from 'nestjs-i18n';

import { AuthService } from './auth.service';
import { SecurityService } from './security.service';
import { UsersService } from '../users/users.service';
import { ValidateUserService } from './validate-user.service';

import { SignInBodyDto } from './dto/post-token.dto';
import { ResetPasswordBodyDto } from './dto/auth.request.dto';
import { AuthorizedRequest } from './interfaces/authorized-request.interface';

import { enum2obj } from '@/shared/utils';
import { PasswordResponse } from '@/shared/enums';

@ApiTags('Auth')
@Controller({
  path: '/',
  version: '2',
})
export class AuthController {
  constructor(
    private authService: AuthService,
    private validateUserService: ValidateUserService,
    private userService: UsersService,
    private securityService: SecurityService,
  ) {}

  @Post('/token')
  @ApiOperation({
    summary: 'Validate user access and get token',
    description:
      'Get a token for specific user.<br />' +
      'Fill the body with username (=email), password and hash info about the device/browser using MD5.<br />' +
      'The response is a data object, which will contain the token information.<br />' +
      'Optional the response will contain a list of companies the user has access to. When this list is included a follow-up request to the Swich company API must be called to connect to a specific company. When this optional response is not included the user only has acces to one company, which is automatically selected.',
  })
  @ApiResponse({ status: 200, description: 'Token generated' })
  @ApiResponse({ status: 403, description: 'Authentication failed' })
  async auth(@Body() body: SignInBodyDto) {
    return this.authService.authenticateUser(body);
  }

  @Get('/validate_token')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Validate token with database',
    description: 'Check if token is still valid in the database',
  })
  @ApiResponse({ status: 200, description: 'Token is valid' })
  @ApiResponse({ status: 401, description: 'Token is invalid or expired' })
  async validateToken(@Req() req: AuthorizedRequest) {
    let item = await this.validateUserService.validate(
      req.headers.authorization || '',
    );
    if (!item) throw new UnauthorizedException();

    let user = await this.userService.getUser(item.user.id);
    return {
      access_token: item.token,
      token_type: 'Bearer',
      email: user.email,
      initials: user.firstname.charAt(0) + user.username.charAt(0),
      expires_in: moment
        .duration(moment(item.expire).diff(moment()))
        .asSeconds(),
      expire_date: item.expire,
      firstname: user.firstname || '',
      name: user.username || '',
      avatar: this.userService.getAvatar(user.avatar, user.avatar_type),
      salutation: user.salutation,
      job_title: user.job_title,
      date_of_birth:
        user.date_of_birth && moment(user.date_of_birth).format('YYYY-MM-DD'),
      work_anniversary:
        user.date_of_birth && moment(user.date_of_birth).format('YYYY-MM-DD'),
      new_hot_news: user.new_news_articles,
      phone: user.phone || '',
      user_id: user.sec_user_id,
      has_opened_settings: user.has_opened_settings == 1,
    };
  }

  @Post('/reset_password')
  @ApiOperation({
    summary: 'Reset password',
    description: `Reset password for user <br />
    Body must contain 2 properties, the ID property is the activation key and the new password`,
  })
  @ApiResponse({ status: 200, description: 'Password reset' })
  @ApiResponse({ status: 400, description: 'Password reset failed' })
  async resetPassword(
    @Body() body: ResetPasswordBodyDto,
    @I18n() i18n: I18nContext,
  ) {
    let result = await this.securityService.resetPassword(body);
    if (!result?.success && result?.msg) {
      let msg = i18n.t(`prop.ccs.lbl.${result.msg}`);
      result.msg = msg.includes('prop.ccs') ? result.msg : msg;
    }

    if (!result.success) {
      let password_data = enum2obj(PasswordResponse);
      throw new HttpException(
        {
          success: false,
          error:
            password_data[result.status || result.error] ||
            result.status ||
            result.error,
          msg: result.msg,
          settings: result.settings,
        },
        400,
      );
    }
    return result;
  }
}
