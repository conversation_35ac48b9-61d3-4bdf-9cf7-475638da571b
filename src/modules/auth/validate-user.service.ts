import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  AuthorizedCompany,
  AuthorizedData,
} from './interfaces/authorized-request.interface';
import { UserTokenEntity } from '@entities';

@Injectable()
export class ValidateUserService {
  constructor(
    @InjectRepository(UserTokenEntity)
    private readonly userTokenEntity: Repository<UserTokenEntity>,
  ) {}

  async validate(
    token: string,
    checkCompany: boolean = false,
  ): Promise<AuthorizedData | null> {
    const tokenData = await this.userTokenEntity
      .createQueryBuilder('userToken')
      .leftJoinAndSelect('userToken.user', 'user')
      .leftJoinAndSelect('userToken.company', 'company')
      .leftJoinAndSelect('userToken.user_company', 'userCompany')
      .where('userToken.token = :token', {
        token: token.startsWith('Bearer ') ? token.split(' ')[1] : token,
      })
      .andWhere('userToken.expiry_date > :currentDate', {
        currentDate: new Date(),
      })
      // .andWhere('userCompany.sec_user_id = userToken.sec_user_id')
      .select([
        'userToken.sec_user_ws_token_id',
        'userToken.token',
        'userToken.expiry_date',
        'user.sec_user_id',
        'user.username',
        'user.email',
        'user.firstname',
        'user.salutation',
        'user.language',
        'company.sec_company_id',
        'company.company_code',
        'company.company_type',
        'company.is_parent_company',
        'userCompany.sec_group',
      ])
      .getOne();

    if (!tokenData?.user) return null;
    if (checkCompany && !tokenData.company) return null;

    let company: AuthorizedCompany = null;
    if (tokenData.company) {
      const { company: c } = tokenData;
      company = {
        id: c.sec_company_id,
        code: c.company_code,
        name: c.company_name,
        type: c.company_type,
        isParent: !!c.is_parent_company,
      };
    }

    return {
      tokenId: tokenData.sec_user_ws_token_id,
      token: tokenData.token,
      expire: tokenData.expiry_date,
      user: {
        id: tokenData.user.sec_user_id,
        fullname: tokenData.user.fullname,
        ...tokenData.user,
        sec_user_id: undefined,
      },
      company,
      userCompany: tokenData.user_company,
    } as AuthorizedData;
  }
}
