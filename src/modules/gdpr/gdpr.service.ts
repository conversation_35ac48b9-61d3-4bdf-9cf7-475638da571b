import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import * as fs from 'fs';
import { join } from 'path';
import * as moment from 'moment';
import * as pdfCreator from 'pdf-creator-node';

import { ConsumerDetails } from './interfaces/gdpr.interface';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { GdprSearchResponseDto, GetGdprQueryDto } from './dto';

import { env } from '../../app.env';
import { ClsProperty } from '@/config/contents';
import { CompanyType } from '@/shared/enums';

import { SurveyAnswerEntity } from '@entities';

@Injectable()
export class GdprService {
  constructor(
    private cls: ClsService,
    @InjectRepository(SurveyAnswerEntity)
    private readonly surveyAnswerEntity: Repository<SurveyAnswerEntity>,
  ) {}

  /**
   * node-postgres driver protects requests from sql injection
   * more info here: https://github.com/brianc/node-postgres/wiki/FAQ#8-does-node-postgres-handle-sql-injection
   * @param search
   * @param companyType
   * @returns
   */
  public async getGdpr(query: GetGdprQueryDto): Promise<GdprSearchResponseDto> {
    let { search, search_field = [] } = query;
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    let companyType = company.type;

    if (search?.length < 5)
      throw new HttpException(
        'search should have at least 5 symbols',
        HttpStatus.BAD_REQUEST,
      );

    let allowedFields = [
      'xml_customername',
      'xml_email',
      'xml_rasnumber',
      'xml_phone2',
    ];
    if (search_field.find((item) => !allowedFields.includes(item)))
      throw new HttpException(
        'Search fields must be ' + allowedFields.join(),
        400,
      );

    let whereSql = '';
    if (search_field.length == 0) search_field = ['xml_email'];
    for (let item of search_field) {
      if (item.indexOf('phone') >= 0) {
        whereSql += ` OR regexp_replace(ec.${item}, '[^0-9]+', '', 'g') LIKE $1`;
      } else {
        whereSql += ` OR LOWER(ec.${item}) ILIKE LOWER($1)`;
      }
    }

    whereSql = whereSql.slice(4);
    if (whereSql.length > 10) {
      whereSql = ` AND (${whereSql})`;
    }

    const result = await this.surveyAnswerEntity.query(
      `
          SELECT
            ec.email_customer_id AS id,
            ec.xml_salutation as salutation,
            ec.xml_initials as initials,
            ec.xml_customername AS name,
            ec.xml_email as email,
            ec.xml_phone1 as phone1,
            ec.xml_phone2 as phone2,
            ec.survey_done AS completed
    
            -- only for company type = 0
            ${
              companyType === CompanyType.BOUW
                ? `, p.project_nr ||' - '|| p.project_name AS project`
                : ''
            }
    
            -- only for company type = 1
            ${
              companyType === CompanyType.BSH
                ? ', ec.xml_rasnumber AS risno'
                : ''
            }
    
            -- only for company type = 2
            ${
              companyType === CompanyType.SPORT
                ? ', p.project_name AS club'
                : ''
            }
  
          FROM email_customer ec
    
          -- include join only for company type = 0 and 2
          ${
            [CompanyType.BOUW, CompanyType.SPORT].includes(companyType)
              ? 'LEFT JOIN projects p on ec.xml_internal_external = p.project_id'
              : ''
          }
    
          WHERE ec.sec_company_id =$2 ${whereSql}
        `,
      // Use parameters to avoid SQL Injection attacks
      [`%${search}%`, company.id],
    );

    return {
      gdpr: result,
    };
  }

  public async deleteGdpr(ids: string[]) {
    const surveyAnswers = await this.surveyAnswerEntity.find({
      where: {
        email_customer_id: In(ids),
      },
      select: ['surv_surveyanswer_id'],
    });

    if (surveyAnswers.length) {
      const surveyAnswerIdsQuery = `(${surveyAnswers.map(
        (i) => i.surv_surveyanswer_id,
      )})`;

      const deleteRelatedDataSqls = `
          DELETE FROM surv_answer_preparse WHERE surv_surveyanswer_id in ${surveyAnswerIdsQuery};
          DELETE FROM mclass_response WHERE answer_id in ${surveyAnswerIdsQuery};
          DELETE FROM redacted_answers WHERE surv_surveyansweritem_id IN (
            SELECT surv_surveyansweritem_id FROM surv_surveyansweritem WHERE surv_surveyanswer_id in ${surveyAnswerIdsQuery}
          );
          DELETE FROM surv_surveyansweritem WHERE surv_surveyanswer_id in ${surveyAnswerIdsQuery};
          DELETE FROM surv_surveyanswer WHERE surv_surveyanswer_id in ${surveyAnswerIdsQuery}
        `;

      await Promise.all(
        deleteRelatedDataSqls
          .split(';')
          .map((deleteSql) => this.surveyAnswerEntity.query(deleteSql)),
      );
    }

    const emailCustomerIdsSql = ids.join(',');

    const deleteConsumerDataSqls = `
        DELETE FROM clf_call_note WHERE clf_call_id IN (
          SELECT clf_call_id FROM clf_call WHERE email_customer_id in (${emailCustomerIdsSql})
        );
        DELETE FROM clf_call WHERE email_customer_id in (${emailCustomerIdsSql});
        DELETE FROM project_stages WHERE email_customer_id in (${emailCustomerIdsSql});
        DELETE FROM email_customer WHERE email_customer_id in (${emailCustomerIdsSql})
      `;

    await Promise.all(
      deleteConsumerDataSqls
        .split(';')
        .map((deleteSql) => this.surveyAnswerEntity.query(deleteSql)),
    );
  }

  public async getReport(ids: string[]) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const path = join(
      env.APP_PATH,
      'dist/templates',
      'gdpr-report-template-new.html',
    );

    const htmlTemplate = fs.readFileSync(path, 'utf-8');

    const consumerDetails = (await this.surveyAnswerEntity.query(
      `
      SELECT
        xml_salutation AS salutation,
        xml_initials AS firstname,
        xml_customername AS lastname,
        xml_email AS email,
        xml_phone1 AS phone1,
        xml_phone2 AS phone2,
      
        ${
          company.type === CompanyType.BOUW
            ? `p.project_nr ||'-'|| p.project_name AS project,`
            : ''
        }

        -- only for company type = 1
        ${
          company.type === CompanyType.BSH
            ? 'xml_rasnumber AS risnumber, ec.xml_brand AS brand,'
            : ''
        }

        -- only for company type = 2
        ${company.type === CompanyType.SPORT ? 'p.project_name AS club,' : ''}

        to_char(coalesce(import_date, ec.creation_date), 'DD-MM-YYYY HH:mm') AS creation_date,
        to_char(send_date, 'DD-MM-YYYY HH:mm') as send_date,
        CASE WHEN a.creation_date IS NULL THEN false ELSE true END AS response_received,
        to_char(a.creation_date, 'DD-MM-YYYY HH:mm') AS response_date,
        a.nps_value AS nps,
        neg.return_value AS comment_1,
        pos.return_value AS comment_2

        FROM email_customer ec
        LEFT JOIN projects p on ec.xml_internal_external = p.project_id
        LEFT JOIN surv_survey ss on ec.surv_survey_id = ss.surv_survey_id
        LEFT JOIN surv_surveyanswer a ON ec.email_customer_id = a.email_customer_id
        LEFT JOIN surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND 
          ss.clf_question1 = neg.surv_surveyquestion_id
        LEFT JOIN surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND 
          ss.clf_question2 = pos.surv_surveyquestion_id
        WHERE ec.email_customer_id IN (${ids.join(',')})
      `,
    )) as ConsumerDetails[];

    const pdfBuffer = await pdfCreator.create({
      html: htmlTemplate,
      data: {
        user: user.fullname,
        date: moment().utc().format('DD-MM-YYYY HH:mm'),
        count: consumerDetails.length,
        consumers: consumerDetails,
      },
      type: 'buffer',
    });

    return pdfBuffer;
  }

  public async exportReport(id: number) {
    const { user, company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    const path = join(
      env.APP_PATH,
      'dist/templates',
      'gdpr-report-template-new.html',
    );

    const htmlTemplate = fs.readFileSync(path, 'utf-8');

    const consumerDetails: ConsumerDetails[] =
      await this.surveyAnswerEntity.query(
        `
      SELECT
        xml_salutation AS salutation,
        xml_initials AS firstname,
        xml_customername AS lastname,
        xml_email AS email,
        xml_phone1 AS phone1,
        xml_phone2 AS phone2,
      
        ${
          company.type === CompanyType.BOUW
            ? `p.project_nr ||'-'|| p.project_name AS project,`
            : ''
        }

        -- only for company type = 1
        ${
          company.type === CompanyType.BSH
            ? 'xml_rasnumber AS risnumber, ec.xml_brand AS brand,'
            : ''
        }

        -- only for company type = 2
        ${company.type === CompanyType.SPORT ? 'p.project_name AS club,' : ''}

        to_char(coalesce(import_date, ec.creation_date), 'DD-MM-YYYY HH:mm') AS creation_date,
        to_char(send_date, 'DD-MM-YYYY HH:mm') as send_date,
        CASE WHEN a.creation_date IS NULL THEN false ELSE true END AS response_received,
        to_char(a.creation_date, 'DD-MM-YYYY HH:mm') AS response_date,
        a.nps_value AS nps,
        neg.return_value AS comment_1,
        pos.return_value AS comment_2

        FROM email_customer ec
        LEFT JOIN projects p on ec.xml_internal_external = p.project_id
        LEFT JOIN surv_survey ss on ec.surv_survey_id = ss.surv_survey_id
        LEFT JOIN surv_surveyanswer a ON ec.email_customer_id = a.email_customer_id
        LEFT JOIN surv_surveyansweritem neg ON a.surv_surveyanswer_id = neg.surv_surveyanswer_id AND 
          ss.clf_question1 = neg.surv_surveyquestion_id
        LEFT JOIN surv_surveyansweritem pos ON a.surv_surveyanswer_id = pos.surv_surveyanswer_id AND 
          ss.clf_question2 = pos.surv_surveyquestion_id
        WHERE ec.email_customer_id = $1
      `,
        [id],
      );

    const pdfBuffer = await pdfCreator.create({
      html: htmlTemplate,
      data: {
        user: user.fullname,
        date: moment().utc().format('DD-MM-YYYY HH:mm'),
        count: consumerDetails.length,
        consumers: consumerDetails,
      },
      type: 'buffer',
    });

    return pdfBuffer;
  }
}
