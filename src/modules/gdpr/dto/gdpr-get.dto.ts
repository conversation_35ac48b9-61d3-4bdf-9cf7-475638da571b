import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional, IsString } from 'class-validator';

export class GetGdprQueryDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  search: string;

  @ApiProperty({ required: false, type: String })
  @Transform(({ value }: any) => value?.split(',')?.filter((i) => i.length))
  @IsOptional()
  search_field: string[];
}
