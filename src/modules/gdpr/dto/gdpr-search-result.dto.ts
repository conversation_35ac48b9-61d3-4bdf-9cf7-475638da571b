import { ApiProperty } from '@nestjs/swagger';

export class GdprSearchResultDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  salutation: string;

  @ApiProperty()
  initials: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  phone1: string;

  @ApiProperty()
  phone2: string;

  @ApiProperty()
  completed: boolean;

  @ApiProperty({
    required: false,
    description: 'Available for CompanyType BOUW',
  })
  project?: string;

  @ApiProperty({
    required: false,
    description: 'Available for CompanyType BSH',
  })
  risno?: string;

  @ApiProperty({
    required: false,
    description: 'Available for CompanyType SPORT',
  })
  club?: string;
}

export class GdprData {
  @ApiProperty({
    type: [GdprSearchResultDto],
  })
  gdpr: GdprSearchResultDto[];
}

export class GdprSearchResponseDto extends GdprData {
  // @ApiProperty()
  // data: GdprData;
}
