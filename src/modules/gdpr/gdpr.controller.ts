import {
  Controller,
  Delete,
  Get,
  HttpStatus,
  Query,
  Res,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';

import { StreamService } from '../../shared/stream.service';
import { GdprService } from './gdpr.service';

import { GdprDeleteQueryDto } from './dto/gdpr-delete-query.dto';
import { GetGdprQueryDto } from './dto/gdpr-get.dto';
import { GdprSearchResponseDto } from './dto/gdpr-search-result.dto';

@ApiTags('GDPR')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'gdpr',
})
export class GdprController {
  constructor(private gdprService: GdprService) {}

  @UseGuards(AuthGuard)
  @ApiOkResponse({
    description: 'GDPR search result',
    type: [GdprSearchResponseDto],
  })
  @Get()
  getGdpr(@Query() query: GetGdprQueryDto) {
    return this.gdprService.getGdpr(query);
  }

  @UseGuards(AuthGuard)
  @Delete()
  @ApiOkResponse({
    status: HttpStatus.OK,
  })
  @ApiQuery({ name: 'ids', required: true, type: [String] })
  deleteGdrp(
    @Query(new ValidationPipe({ transform: true }))
    { ids }: GdprDeleteQueryDto,
  ) {
    return this.gdprService.deleteGdpr(ids);
  }

  @UseGuards(AuthGuard)
  @Get('/report')
  @ApiOkResponse({
    description: 'PDF file',
    content: { 'application/pdf': {} },
  })
  @ApiQuery({ name: 'ids', required: true, type: [String] })
  async getReport(
    @Query(new ValidationPipe({ transform: true }))
    { ids }: GdprDeleteQueryDto,
    @Res() response: Response,
  ) {
    const buffer = await this.gdprService.getReport(ids);

    const stream = StreamService.getReadableStream(buffer);

    response.set({
      'Content-Type': 'application/pdf',
      'Content-Length': buffer.length,
      'Content-Disposition': 'attachment; filename=report.pdf',
    });

    stream.pipe(response);
  }

  @UseGuards(AuthGuard)
  @Get('/export')
  @ApiOkResponse({
    description: 'PDF file',
    content: { 'application/pdf': {} },
  })
  async exportReport(@Query('id') id: number, @Res() response: Response) {
    const buffer = await this.gdprService.exportReport(id);
    const stream = StreamService.getReadableStream(buffer);
    response.set({
      'Content-Type': 'application/pdf',
      'Content-Length': buffer.length,
      'Content-Disposition': 'attachment; filename=report.pdf',
    });
    stream.pipe(response);
  }
}
