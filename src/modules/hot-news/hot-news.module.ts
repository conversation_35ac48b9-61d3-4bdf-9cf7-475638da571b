import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HotNewsEntity } from '../database/entities/hot-news.entity';
import { HotNewsController } from './hot-news.controller';
import { HotNewsService } from './hot-news.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([HotNewsEntity]),
    ModuleLoggerModule.register('hot-news'),
  ],
  controllers: [HotNewsController],
  providers: [HotNewsService],
})
export class HotNewsModule {}
