import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsN<PERSON><PERSON>, IsOptional, <PERSON>, Min } from 'class-validator';

export class GetHotNewsQueryDto {
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
    default: 5,
    type: Number,
  })
  @Min(5)
  @Max(50)
  limit = 5;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    required: false,
    default: 0,
    type: Number,
  })
  @Min(0)
  page = 0;
}
