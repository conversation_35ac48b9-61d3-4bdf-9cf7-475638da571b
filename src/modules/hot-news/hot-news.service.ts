import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HotNewsEntity } from '../database/entities/hot-news.entity';
import { GetHotNewsParams } from './interfaces/hot-news-service.params';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { rawQuery } from '@/shared/utils';

export class HotNewsService {
  constructor(
    private logger: ModuleLogger,
    @InjectRepository(HotNewsEntity)
    private readonly hotNewsEntity: Repository<HotNewsEntity>,
  ) {}

  // select hot_news_title, description, published_date, hot_news_link from hot_news where is_published = 1 and (company_type is null or company_type = ? ) order by published_date desc LIMIT ?
  async getHotNews(params: GetHotNewsParams) {
    const { company_type, limit = 5, page = 0 } = params;
    this.logger.debug(`getHotNews`, { params });
    let query = this.hotNewsEntity
      .createQueryBuilder()
      .select([
        'hot_news_title',
        'description',
        'published_date',
        'hot_news_link',
      ]);
    query.where(
      'is_published = 1 AND (company_type IS NULL OR company_type = :company_type)',
      { company_type },
    );
    query.orderBy('published_date', 'DESC');
    query.take(limit);
    query.skip(page * limit);

    this.logger.debug(`getHotNews query`, { sql: rawQuery(query) });

    let list = await query.getRawMany();
    list = list.map((item) => {
      return {
        link: item.hot_news_link,
        hotnews_description: item.description,
        title: item.hot_news_title,
        posted_date: item.published_date,
      };
    });
    let countRecords = await this.hotNewsEntity
      .createQueryBuilder()
      .where(
        'is_published = 1 AND (company_type IS NULL OR company_type = :company_type)',
        { company_type },
      )
      .getCount();

    this.logger.debug(`getHotNews result`, { list, countRecords });

    return {
      list,
      moreRecords: countRecords > limit * (page + 1),
    };
  }
}
