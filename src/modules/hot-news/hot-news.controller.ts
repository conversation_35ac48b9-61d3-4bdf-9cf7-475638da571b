import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { GetHotNewsQueryDto } from './dto/get-hot-news.dto';
import { HotNewsService } from './hot-news.service';

@ApiTags('Hot News')
@ApiBearerAuth()
@Controller({
  path: 'hot_news',
  version: '2',
})
export class HotNewsController {
  constructor(private hotNewsService: HotNewsService) {}

  @UseGuards(AuthGuard)
  @ApiResponse({
    description: 'Get Hot News',
  })
  @Get()
  async getHotNews(
    @Query() query: GetHotNewsQueryDto,
    @Req() { company }: AuthorizedRequest,
  ) {
    const { limit, page } = query;
    let result = await this.hotNewsService.getHotNews({
      company_type: company.type,
      limit,
      page,
    });
    return {
      hot_news: result,
    };
  }
}
