import { Controller, Get, Query } from '@nestjs/common';
import { ApiLanguageService } from './language.service';
import { Auth } from '@/shared/decorators';
import { ApiTags } from '@nestjs/swagger';

@Controller({
  path: 'language',
  version: '2',
})
@ApiTags('Language Api')
export class ApiLanguageController {
  constructor(private languageService: ApiLanguageService) {}

  @Get()
  @Auth()
  list(@Query('filtered') filtered: boolean) {
    return this.languageService.list(filtered);
  }
}
