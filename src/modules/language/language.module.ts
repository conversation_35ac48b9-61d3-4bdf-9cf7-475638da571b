import { Module } from '@nestjs/common';
import { ApiLanguageController } from './language.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiLanguageService } from './language.service';

import { LanguagesEntity, SurvMessagesLocal } from '@entities';

@Module({
  imports: [TypeOrmModule.forFeature([LanguagesEntity, SurvMessagesLocal])],
  controllers: [ApiLanguageController],
  providers: [ApiLanguageService],
  exports: [ApiLanguageService],
})
export class ApiLanguageModule {}
