import { LanguagesEntity } from '@entities';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { ClsProperty } from '@/config/contents';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class ApiLanguageService {
  constructor(
    private cls: ClsService,
    @InjectRepository(LanguagesEntity)
    private readonly langRepo: Repository<LanguagesEntity>,
  ) {}
  async list(filtered: boolean) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);
    if (!filtered)
      return this.langRepo
        .createQueryBuilder('l')
        .select([
          'l.language_id as id',
          'l.lang_desc as name',
          'l.lang_369_1 as code',
        ])
        .orderBy('l.lang_desc', 'ASC')
        .getRawMany();

    const queryBuilder = this.langRepo
      .createQueryBuilder('l')
      .select('l.language_id', 'id')
      .addSelect('l.lang_desc', 'name')
      .addSelect('l.lang_369_1', 'code')
      .leftJoin(
        'surv_messages_local',
        'ml',
        '(l.lang_369_1 = ml.language OR l.lang_369_1 = ml.language) AND ml.sec_company_id = :sec_company_id',
        { sec_company_id: company.id },
      )
      .where('ml.language IS NULL')
      .andWhere('l.isselectable = 1')
      .orderBy('l.lang_desc', 'ASC');

    const languages = await queryBuilder.getRawMany();

    return languages;
  }
}
