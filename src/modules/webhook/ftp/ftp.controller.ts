import { Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import WebhookGuard from '../webhook.guard';
import { WebhookFtpDownloadItemsQueryDto } from './dto/ftp.dto';
import { WebhookFtpService } from './ftp.service';

@Controller({
  path: '/webhook/ftp',
  version: '2',
})
@ApiTags('Webhook')
@ApiHeader({
  name: 'x-webhook-key',
  required: true,
  schema: { default: 'supersecret123' },
})
export class WebhookFtpController {
  constructor(private ftpService: WebhookFtpService) {}

  @Get('/download')
  @UseGuards(WebhookGuard)
  downloadItems(@Query() query: WebhookFtpDownloadItemsQueryDto) {
    return this.ftpService.downloadCompanyItems(query.sec_company_id);
  }

  @Get('/count')
  @UseGuards(WebhookGuard)
  countItems(@Query() query: WebhookFtpDownloadItemsQueryDto) {
    return this.ftpService.countCompanyItems(query.sec_company_id);
  }
}
