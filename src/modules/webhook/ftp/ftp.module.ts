import { Modu<PERSON> } from '@nestjs/common';
import { WebhookFtpController } from './ftp.controller';
import { WebhookFtpService } from './ftp.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecProgramSettingsEntity } from '../../database/entities/sec-programsettings.entity';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SecProgramSettingsEntity]),
    ModuleLoggerModule.register('webhook.ftp'),
  ],
  controllers: [WebhookFtpController],
  providers: [WebhookFtpService],
  exports: [WebhookFtpService],
})
export class WebhookFtpModule {}
