import { InjectRepository } from '@nestjs/typeorm';
import { SecProgramSettingsEntity } from '../../database/entities/sec-programsettings.entity';
import { Repository } from 'typeorm';
import { HttpException } from '@nestjs/common';
import { WebhookFtpConnectParams } from './dto/ftp.dto';
import { join } from 'path';
import { promises as fs } from 'fs';
import { createDecipheriv } from 'crypto';
import { Client } from 'basic-ftp';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

export class WebhookFtpService {
  private TEMP_FOLDER = process.env.SFTP_TEMP_FOLDER;
  constructor(
    private logger: ModuleLogger,
    @InjectRepository(SecProgramSettingsEntity)
    private psRepo: Repository<SecProgramSettingsEntity>,
  ) {}

  public async downloadCompanyItems(sec_company_id: number) {
    let settings = await this.psRepo.findOne({ where: { sec_company_id } });
    if (!settings?.ftp_host || !settings.ftp_username)
      throw new HttpException(
        'no ftp details found for given sec_company_id',
        404,
      );

    const { ftp_host, ftp_port, ftp_username, ftp_password, ftp_path } =
      settings;

    let password = this.getPassword(ftp_password);

    try {
      this.logger.debug(
        `sending request to get list\ncompanyId: ${sec_company_id}`,
      );

      let { client } = await this.connect({
        host: ftp_host,
        port: ftp_port,
        username: ftp_username,
        password,
      });

      let files = await this.listFolder(client, ftp_path);

      files = files.filter((file) => {
        return file.name.endsWith('.csv');
      });

      let result = await this.downloadFiles(
        client,
        files,
        ftp_path,
        this.TEMP_FOLDER,
      );

      await this.removeRemoteFiles(client, files, ftp_path);

      this.logger.debug(`finished ftp task for company ${sec_company_id}`, {
        files: files.map((file) => file.name),
        ftp_host,
        ftp_path,
      });
      client.close();
      return result;
    } catch (err) {
      this.logger.error(
        `Error sending request to list\ncompanyId: ${sec_company_id}\nhost: ${ftp_host}`,
        { error: err?.message || err },
      );
      return new HttpException(
        `Error connecting to SFTP server: ${err.message}`,
        400,
      );
    }
  }

  public async countCompanyItems(sec_company_id: number) {
    let settings = await this.psRepo.findOne({ where: { sec_company_id } });
    if (!settings?.ftp_host || !settings.ftp_username)
      throw new HttpException(
        'no ftp details found for given sec_company_id',
        404,
      );

    const { ftp_host, ftp_port, ftp_username, ftp_password, ftp_path } =
      settings;
    let password = this.getPassword(ftp_password);

    try {
      let { client } = await this.connect({
        host: ftp_host,
        port: ftp_port,
        username: ftp_username,
        password,
      });

      let files = await this.listFolder(client, ftp_path);

      files = files.filter((file) => {
        return file.name.endsWith('.csv');
      });

      client.close();
      return files.length;
    } catch (err) {
      return new HttpException(
        `Error connecting to SFTP server: ${err.message}`,
        400,
      );
    }
  }

  public async uploadFile(
    localFile: string,
    remoteFile: string,
    sec_company_id: number,
  ) {
    let settings = await this.psRepo.findOne({ where: { sec_company_id } });
    if (!settings?.ftp_host || !settings.ftp_username)
      throw new HttpException(
        'no ftp details found for given sec_company_id',
        404,
      );

    const { ftp_host, ftp_port, ftp_username, ftp_password } = settings;
    let password = this.getPassword(ftp_password);

    try {
      this.logger.debug(
        `uploading file\nfile: ${localFile}\ncompanyId: ${sec_company_id}\nhost:${ftp_host}`,
      );
      let { client } = await this.connect({
        host: ftp_host,
        port: ftp_port,
        username: ftp_username,
        password,
      });

      await client.uploadFrom(localFile, remoteFile);

      client.close();
      return { [remoteFile]: true };
    } catch (err) {
      this.logger.error(
        `Error on uploading file\nlocal file: ${localFile}\nremoteFile:${remoteFile}\ncompanyId${sec_company_id}\nhost:${ftp_host}`,
        {
          localFile,
          remoteFile,
          error: err?.message || err,
        },
      );
      return { [remoteFile]: err?.message || err };
    }
  }

  getPassword(ftp_password: string) {
    try {
      const decipher = createDecipheriv(
        'aes-256-ecb',
        process.env.SFTP_PASSWORD_DECRYPT_KEY,
        '',
      );

      let password = decipher.update(ftp_password, 'base64', 'utf8');
      password += decipher.final('utf8');
      return password;
    } catch (e) {
      throw new Error('Password decryption error: ' + e?.message);
    }
  }

  public async connect({
    host,
    port,
    username,
    password,
  }: WebhookFtpConnectParams) {
    const client = new Client();
    await client.access({
      host,
      port: port ? +port : undefined,
      user: username,
      password,
      secure: false,
    });

    return { client };
  }

  public listFolder(client: Client, path: string) {
    return client.list(path);
  }

  public async downloadFiles(
    client: Client,
    files: any,
    ftpPath: string,
    localPath: string,
  ) {
    let results = { success: [], error: [] };
    let folderPath = join(localPath, ftpPath);

    this.logger.log('downloading files', {
      files: files.map((file) => file.name),
      localPath,
      ftpPath,
    });

    await fs.mkdir(folderPath, { recursive: true }).catch(() => false);
    for (let file of files) {
      let remotePath = join(ftpPath, file.name);
      let filePath = join(folderPath, file.name);
      let result: any = await client
        .downloadTo(filePath, remotePath)
        .catch((error) => ({ error }));
      if (!result?.error) {
        results.success.push(join(localPath, ftpPath, file.name));
      } else {
        this.logger.error('error while downloading file', {
          file: file.name,
          localPath,
          ftpPath,
          result,
        });

        results.error.push(result);
      }
    }
    return results;
  }

  public async removeRemoteFiles(client: Client, files: any, ftpPath: string) {
    for (let file of files) {
      let remotePath = join(ftpPath, file.name);
      await client.remove(remotePath).catch((error) => {
        this.logger.error('Error whıle deletıng fıle', {
          file: file.name,
          error: error?.message || error,
        });
      });
    }
  }
}
