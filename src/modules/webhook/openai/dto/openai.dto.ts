import { ApiProperty } from '@nestjs/swagger';
import {ChatCompletionFunctionCallOption, ChatCompletionFunctionMessageParam, ChatCompletionMessageParam, } from "openai/resources";

export class WebhookOpenAICreateChatCompletion {
  @ApiProperty({ required: false })
  'model': string;

  @ApiProperty({ required: false })
  'messages': Array<ChatCompletionMessageParam>;

  @ApiProperty({ required: false })
  'functions'?: Array<ChatCompletionFunctionMessageParam>; //ChatCompletionFunctions

  @ApiProperty({ required: false })
  'function_call'?: ChatCompletionFunctionCallOption; //CreateChatCompletionRequestFunctionCall

  @ApiProperty({ required: false })
  'temperature'?: number | null;

  @ApiProperty({ required: false })
  'top_p'?: number | null;

  @ApiProperty({ required: false })
  'n'?: number | null;

  @ApiProperty({ required: false })
  'stream'?: boolean | null;

  @ApiProperty({ required: false })
  'stop'?: any; //CreateChatCompletionRequestStop

  @ApiProperty({ required: false })
  'max_tokens'?: number;

  @ApiProperty({ required: false })
  'presence_penalty'?: number | null;

  @ApiProperty({ required: false })
  'frequency_penalty'?: number | null;

  @ApiProperty({ required: false })
  'logit_bias'?: object | null;

  @ApiProperty({ required: false })
  'user'?: string;
}
