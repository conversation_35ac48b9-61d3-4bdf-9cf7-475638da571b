import { Body, Controller, Post, Headers, UseGuards } from '@nestjs/common';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import WebhookGuard from '../webhook.guard';
import { OpenAIService } from '../../openai/openai.service';
import { WebhookOpenAICreateChatCompletion } from './dto/openai.dto';

@Controller({
  path: '/webhook/openai',
  version: '2',
})
@ApiTags('Webhook')
@ApiHeader({
  name: 'x-webhook-key',
  required: true,
  schema: { default: 'supersecret123' },
})
export class WebhookOpenAIController {
  constructor(private openAIService: OpenAIService) {}

  @Post('/create-chat-completion')
  @UseGuards(WebhookGuard)
  public createChatCompletion(
    @Body() body: WebhookOpenAICreateChatCompletion,
    @Headers() headers: Record<string, string>,
  ) {
    const apiKey = headers['x-api-key'];
    return this.openAIService.createChatCompletion(body, { apiKey });
  }
}
