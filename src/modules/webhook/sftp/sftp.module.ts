import { Modu<PERSON> } from '@nestjs/common';
import { WebhookSftpController } from './sftp.controller';
import { WebhookSftpService } from './sftp.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecProgramSettingsEntity } from '../../database/entities/sec-programsettings.entity';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SecProgramSettingsEntity]),
    ModuleLoggerModule.register('webhook.sftp'),
  ],
  controllers: [WebhookSftpController],
  providers: [WebhookSftpService],
  exports: [WebhookSftpService],
})
export class WebhookSftpModule {}
