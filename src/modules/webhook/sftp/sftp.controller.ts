import { Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import WebhookGuard from '../webhook.guard';
import { WebhookSftpDownloadItemsQueryDto } from './dto/sftp.dto';
import { WebhookSftpService } from './sftp.service';

@Controller({
  path: '/webhook/sftp',
  version: '2',
})
@ApiTags('Webhook')
@ApiHeader({
  name: 'x-webhook-key',
  required: true,
  schema: { default: 'supersecret123' },
})
export class WebhookSftpController {
  constructor(private sftpService: WebhookSftpService) {}

  @Get('/download')
  @UseGuards(WebhookGuard)
  downloadItems(@Query() query: WebhookSftpDownloadItemsQueryDto) {
    return this.sftpService.downloadCompanyItems(query.sec_company_id);
  }

  @Get('/count')
  @UseGuards(WebhookGuard)
  countItems(@Query() query: WebhookSftpDownloadItemsQueryDto) {
    return this.sftpService.countCompanyItems(query.sec_company_id);
  }
}
