import { InjectRepository } from '@nestjs/typeorm';
import { SecProgramSettingsEntity } from '../../database/entities/sec-programsettings.entity';
import { Repository } from 'typeorm';
import { HttpException } from '@nestjs/common';
import Client = require('ssh2-sftp-client');
import { WebhookSftpConnectParams } from './dto/sftp.dto';
import { join } from 'path';
import { promises as fs } from 'fs';
import { createDecipheriv } from 'crypto';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

export class WebhookSftpService {
  private TEMP_FOLDER = process.env.SFTP_TEMP_FOLDER;
  constructor(
    private logger: ModuleLogger,
    @InjectRepository(SecProgramSettingsEntity)
    private psRepo: Repository<SecProgramSettingsEntity>,
  ) {}

  public async downloadCompanyItems(sec_company_id: number) {
    let settings = await this.psRepo.findOne({ where: { sec_company_id } });
    if (!settings?.ftp_host || !settings.ftp_username)
      throw new HttpException(
        'no ftp details found for given sec_company_id',
        404,
      );

    const { ftp_host, ftp_port, ftp_username, ftp_password, ftp_path } =
      settings;

    let password = this.getPassword(ftp_password);

    try {
      this.logger.debug(
        `sending request to get list\ncompanyId: ${sec_company_id}`,
      );

      let { client } = await this.connect({
        host: ftp_host,
        port: ftp_port,
        username: ftp_username,
        password,
      });

      let files = await this.listFolder(client, ftp_path);

      files = files.filter((file) => {
        return file.name.endsWith('.csv');
      });

      let result = await this.downloadFiles(
        client,
        files,
        ftp_path,
        this.TEMP_FOLDER,
      );

      await this.removeRemoteFiles(client, files, ftp_path);

      this.logger.debug(`finished sftp task for company ${sec_company_id}`, {
        files: files.map((file) => file.name),
        ftp_host,
        ftp_path,
      });
      client.end();
      return result;
    } catch (err) {
      this.logger.error(
        `Error sending request to list\ncompanyId: ${sec_company_id}\nhost: ${ftp_host}`,
        { error: err?.message || err },
      );
      return new HttpException(
        `Error connecting to SFTP server: ${err.message}`,
        400,
      );
    }
  }

  public async countCompanyItems(sec_company_id: number) {
    let settings = await this.psRepo.findOne({ where: { sec_company_id } });
    if (!settings?.ftp_host || !settings.ftp_username)
      throw new HttpException(
        'no ftp details found for given sec_company_id',
        404,
      );

    const { ftp_host, ftp_port, ftp_username, ftp_password, ftp_path } =
      settings;
    let password = this.getPassword(ftp_password);

    try {
      let { client } = await this.connect({
        host: ftp_host,
        port: ftp_port,
        username: ftp_username,
        password,
      });

      let files = await this.listFolder(client, ftp_path);

      files = files.filter((file) => {
        return file.name.endsWith('.csv');
      });

      client.end();
      return files.length;
    } catch (err) {
      return new HttpException(
        `Error connecting to SFTP server: ${err.message}`,
        400,
      );
    }
  }

  public async uploadFile(
    localFile: string,
    remoteFile: string,
    sec_company_id: number,
  ) {
    let settings = await this.psRepo.findOne({ where: { sec_company_id } });
    if (!settings?.ftp_host || !settings.ftp_username)
      throw new HttpException(
        'no ftp details found for given sec_company_id',
        404,
      );

    const { ftp_host, ftp_port, ftp_username, ftp_password } = settings;
    let password = this.getPassword(ftp_password);

    try {
      this.logger.debug(
        `uploading file\nfile: ${localFile}\ncompanyId: ${sec_company_id}\nhost:${ftp_host}`,
      );
      let { client } = await this.connect({
        host: ftp_host,
        port: ftp_port,
        username: ftp_username,
        password,
      });

      await client.fastPut(localFile, remoteFile);

      client.end();
      return { [remoteFile]: true };
    } catch (err) {
      this.logger.error(
        `Error on uploading file\nlocal file: ${localFile}\nremoteFile:${remoteFile}\ncompanyId${sec_company_id}\nhost:${ftp_host}`,
        {
          localFile,
          remoteFile,
          error: err?.message || err,
        },
      );
      return { [remoteFile]: err?.message || err };
    }
  }

  getPassword(ftp_password: string) {
    const decipher = createDecipheriv(
      'aes-256-ecb',
      process.env.SFTP_PASSWORD_DECRYPT_KEY,
      '',
    );

    let password = decipher.update(ftp_password, 'base64', 'utf8');
    password += decipher.final('utf8');
    return password;
  }

  public async connect({
    host,
    port,
    username,
    password,
  }: WebhookSftpConnectParams) {
    const client = new Client();
    const connection = await client.connect({ host, port, username, password });
    return { client, connection };
  }

  public listFolder(client: Client, path: string) {
    return client.list(path);
  }

  public async downloadFiles(
    client: Client,
    files: any,
    ftpPath: string,
    localPath: string,
  ) {
    this.logger.log('downloading files', {
      files: files.map((file) => file.name),
      localPath,
      ftpPath,
    });
    let results = { success: [], error: [] };
    let folderPath = join(localPath, ftpPath);
    await fs.mkdir(folderPath, { recursive: true }).catch(() => false);
    for (let file of files) {
      let remotePath = join(ftpPath, file.name);
      let filePath = join(folderPath, file.name);
      let result = await client
        .fastGet(remotePath, filePath)
        .catch((error) => ({ error }));
      if (!result?.error) {
        results.success.push(join(localPath, ftpPath, file.name));
      } else {
        this.logger.error('error while downloading file', {
          file: file.name,
          localPath,
          ftpPath,
          result,
        });
        results.error.push(result);
      }
    }
    return results;
  }

  public async removeRemoteFiles(client: Client, files: any, ftpPath: string) {
    this.logger.debug(`Deleting remote files`, {
      files: files.map((file) => file.name),
    });
    for (let file of files) {
      let remotePath = join(ftpPath, file.name);
      await client.delete(remotePath).catch((error) => {
        this.logger.error('Error whıle deletıng fıle', {
          file: file.name,
          error: error?.message || error,
        });
      });
    }
  }
}
