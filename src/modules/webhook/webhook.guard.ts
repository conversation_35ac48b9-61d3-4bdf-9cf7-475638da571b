import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
} from '@nestjs/common';

@Injectable()
export class WebhookGuard implements CanActivate {
  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-webhook-key'];
    const validApiKey = process.env.API_WEBHOOK_SECRET;

    return apiKey === validApiKey;
  }
}

export default WebhookGuard;
