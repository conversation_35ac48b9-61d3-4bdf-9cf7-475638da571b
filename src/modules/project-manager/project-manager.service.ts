import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { SurveyAnswerEntity } from '../database/entities/surv-surveyanswer.entity';
import { ClsService } from 'nestjs-cls';
import { ModuleLogger } from '@helpers/logger/module-logger/module-logger.service';

@Injectable()
export class ProjectManagerService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(SurveyAnswerEntity)
    private readonly surveyAnswerRepo: Repository<SurveyAnswerEntity>,
  ) {}
  async getAllowedProjects(showAll = false) {
    const { company, userCompany } = this.cls.get<AuthorizedData>('user');
    this.logger.debug('getAllowedProjects', { showAll, company, userCompany });
    let result;
    if (showAll) {
      let list = await this.surveyAnswerRepo.query(
        `SELECT project_id FROM projects WHERE sec_company_id = ${company.id}`,
      );
      result = list.map((i) => i.project_id);
    } else {
      let list = await this.surveyAnswerRepo.query(`
          SELECT project_id 
          FROM projects 
          WHERE sec_company_id =${company.id} AND project_id NOT IN ( 
            SELECT project_id 
              FROM project_groups) 
                OR project_id IN (SELECT project_id 
                                      FROM project_groups 
                                      WHERE group_id = ${userCompany.sec_group}) 
          ORDER BY project_id
          `);
      result = list.map((i) => i.project_id);
    }

    this.logger.debug('getAllowedProjects result', { result });
    return result;
  }
}
