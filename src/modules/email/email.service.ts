import { ISendMailOptions, MailerService } from '@nestjs-modules/mailer';
import { InjectRepository } from '@nestjs/typeorm';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';

import { LanguageService } from '@/shared/utils';
import { ActivationType } from '@/shared/enums/activation-type.enum';
import { UserEntity } from '@entities';

import { ActivationService } from '../auth/modules/activation/activation.service';
import { LogLevel } from '@lib/logger/LogOptions';

import { GenerateActivationEmailTemplateDto } from './dto/generate-email-template.dto';
import { SendSystemEmailDto } from './dto/send-system-email.dto';
import { SendCustomerEmailDto } from './dto/send-customer-email.dto';
import {
  ACTIVATION_METHOD,
  SendActivationEmailBodyDto,
} from './dto/send-activation-email.dto';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class EmailService {
  constructor(
    private mailerService: MailerService,
    private configService: ConfigService,
    private languageService: LanguageService,
    private activationService: ActivationService,
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
    private logger: ModuleLogger,
  ) {}

  generateActivationEmailTemplate(params: GenerateActivationEmailTemplateDto) {
    let {
      scaffold,
      content,
      head,
      activation_link,
      application_link,
      email,
      firstname,
      name,
    } = params;
    let msg = scaffold.replace('</content>', '');
    msg = msg.replace('<content>', content);
    msg = msg.replace(/(\[content\])/g, '');
    msg = msg.replace(/(\[head\])/g, head);
    msg = msg.replace(/(\[activation_link\])/g, activation_link);
    msg = msg.replace(/(\[application_link\])/g, application_link);
    msg = msg.replace(/(\[firstname\])/g, firstname);
    msg = msg.replace(/(\[name\])/g, name);
    msg = msg.replace(/(\[email\])/g, email);

    return msg;
  }

  async sendCustomerEmail(params: SendCustomerEmailDto) {
    // TODO: remove reference to smtp_config column
    // let { sec_company_id, to, subject, content } = params;
    // let settings = await this.programSettingsService.getCompanyProgramSettings(
    //   sec_company_id,
    // );
    // let smtp = settings.smtp_config;
    // let transport = createTransport({
    //   host: smtp.host,
    //   port: smtp.port,
    //   name: smtp.name,
    //   secure: smtp.secure,
    //   auth: {
    //     user: smtp.username,
    //     pass: smtp.password,
    //   },
    // });
    // let overrideAddress =
    //   this.configService.get('OVERRIDE_EMAIL_ADDRESS') || '';
    //
    // let mailOptions = {
    //   from: smtp.username,
    //   to,
    //   subject,
    //   html: content,
    // };
    // if (overrideAddress.length > 0) {
    //   mailOptions.to = overrideAddress;
    //   mailOptions.subject = `(Override: TO=${to}, CC=${overrideAddress}, BCC=${overrideAddress})`;
    // }
    // let resp = await transport.sendMail(mailOptions);
    // console.log(resp);
    // return resp;
  }

  async sendSystemEmail(params: SendSystemEmailDto) {
    let { to, subject, content } = params;
    this.logger.debug(`sendSystemEmail to ${to}`, { params });
    let overrideAddress = this.configService.get('OVERRIDE_EMAIL_ADDRESS');

    let mailOptions: ISendMailOptions = {
      from: this.configService.get('EMAIL_FROM'),
      to,
      subject,
      html: content,
    };

    if (overrideAddress?.length > 0) {
      let overrideCCList = this.configService.get('OVERRIDE_CC_LIST');
      mailOptions.to = overrideAddress;
      mailOptions.subject = `(Override: TO=${to}, CC=${
        overrideCCList?.length ? overrideCCList : overrideAddress
      }, BCC=${overrideAddress})`;
      if (overrideCCList?.length) {
        mailOptions.cc = overrideCCList;
      }
    }

    let result = await this.mailerService.sendMail(mailOptions);
    this.logger.debug(`sendSystemEmail result`, { result, params });

    return result;
  }

  async sendActivationEmail(params: SendActivationEmailBodyDto) {
    let { userIds, method } = params;
    this.logger.debug(`sendActivationEmail for users ${userIds.join(',')}`, {
      params,
    });
    let users = await this.userRepo
      .createQueryBuilder()
      .where(`sec_user_id IN(:...userIds)`, { userIds })

      .getMany();

    let results = [];
    let mailTemplates = this.languageService.getFile('en', 'mail');
    for (let user of users) {
      let url, baseUrl;
      // if (user.activation_date && new Date(user.activation_date) > new Date()) {
      //   results.push({
      //     userId: user.sec_user_id,
      //     message: 'An email has already been sent, please check your mailbox',
      //   });
      //   continue;
      // }
      if (method == ACTIVATION_METHOD.FRONT_END) {
        let data = await this.activationService.generateFrontEndActivation(
          ActivationType.FirstTime,
          user,
        );
        url = data.url;
        baseUrl = data.baseUrl;
      } else {
        let data = await this.activationService.generateActivation(
          ActivationType.FirstTime,
          user,
        );
        url = data.url;
        baseUrl = data.baseUrl;
      }
      let template = this.generateActivationEmailTemplate({
        activation_link: url,
        application_link: baseUrl,
        scaffold: mailTemplates.pick('activation.base_api'),
        content: mailTemplates.pick('activation.content'),
        head: 'Activation link',
        email: user.email,
        firstname: user.firstname,
        name: user.username,
      });

      try {
        let emailResult = await this.sendSystemEmail({
          to: user.email,
          subject: 'Activation link',
          content: template,
        });

        results.push({
          userId: user.sec_user_id,
          message: emailResult?.accepted?.length
            ? 'Email has been sent, please check your mailbox'
            : 'Error occurred while sending email',
          result: emailResult,
        });
        this.logger.debug(`sendActivationEmail result`, { emailResult, user });
      } catch (e) {
        this.logger.error(`sendActivationEmail error`, {
          error: e?.message || e,
          user,
        });
        results.push({
          error: 'SMPT Configuration is not correct',
          details: e,
        });
      }
    }

    return results;
  }
}
