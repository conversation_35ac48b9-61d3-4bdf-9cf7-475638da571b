import { MailerModule } from '@nestjs-modules/mailer';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import { EmailService } from './email.service';
import { LanguageService } from '../../shared/utils/language.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../database/entities/sec-user.entity';
import { ActivationModule } from '../auth/modules/activation/activation.module';
import { EmailController } from './email.controller';
import { LoggerModule } from '@lib/logger/logger.module';
import { AuthModule } from '../auth/auth.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity]),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory(configService: ConfigService) {
        return {
          transport: {
            host: configService.get('SMTP_HOST'),
            port: configService.get('SMTP_PORT'),
            secure: !!+configService.get('SMTP_SECURE'),
            name: configService.get('SMTP_NAME'),
            auth: configService.get('SMTP_USERNAME')?.length > 0 && {
              user: configService.get('SMTP_USERNAME'),
              pass:
                configService.get('SMTP_PASSWORD')?.length > 0 &&
                configService.get('SMTP_PASSWORD'),
            },
            requireTLS: !!+configService.get('SMTP_REQUIRE_TLS'),
          },
        };
      },
      inject: [ConfigService],
    }),
    ProgramSettingsModule,
    ConfigModule,
    ActivationModule,
    ModuleLoggerModule.register('email'),
    AuthModule,
  ],
  controllers: [EmailController],
  providers: [EmailService, LanguageService],
  exports: [EmailService],
})
export class EmailModule {}
