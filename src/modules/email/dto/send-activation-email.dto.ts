import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum } from 'class-validator';

export enum ACTIVATION_METHOD {
  FRONT_END = 'front-end',
  DEFAULT = 'default',
}
export class SendActivationEmailBodyDto {
  @Type()
  @IsEnum(ACTIVATION_METHOD)
  @ApiProperty({ enum: ACTIVATION_METHOD, required: true })
  method: ACTIVATION_METHOD;

  @Type()
  @ApiProperty()
  userIds: number[] = [];
}
