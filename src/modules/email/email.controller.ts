import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { Keys } from '@/shared/decorators';
import { EmailService } from './email.service';
import { SendActivationEmailBodyDto } from './dto/send-activation-email.dto';

@Controller({
  path: 'email',
  version: '2',
})
@ApiTags('Email')
@ApiBearerAuth()
export class EmailController {
  constructor(private emailService: EmailService) {}

  @Post('/user-activation')
  @Keys('USERS_EDITOR')
  @UseGuards(AuthGuard)
  userActivation(@Body() body: SendActivationEmailBodyDto) {
    return this.emailService.sendActivationEmail(body);
  }
}
