import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';

import { EmailTemplateEntity } from '../../database/entities/email-template.entity';
import { AuthorizedData } from '@/modules/auth/interfaces/authorized-request.interface';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class EmailTemplateSerivice {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,

    @InjectRepository(EmailTemplateEntity)
    private readonly emailTemplateRepository: Repository<EmailTemplateEntity>,
  ) {}

  public async getEmailTempaltes(surv_survey_id: number) {
    const { company } = this.cls.get<AuthorizedData>('user');

    this.logger.debug(
      `getEmailTemplates for surveyId ${surv_survey_id}, company ${company.id}`,
    );

    const templates = await this.emailTemplateRepository.query(`
        SELECT et.email_template_id as id, et.companyname as name
        FROM surv_surveytemplate st
        INNER JOIN email_template et ON st.email_template_id = et.email_template_id
        WHERE et.sec_company_id = ${company.id} 
        AND st.surv_survey_id = ${surv_survey_id}
        ORDER BY LOWER(et.companyname)
      `);
    this.logger.debug(`getEmailTemplates result`, { templates });

    return {
      email_templates: templates,
    };
  }
}
