import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmailTemplateController } from './email-template.controller';
import { EmailTemplateSerivice } from './email-template.service';
import { EmailTemplateEntity, UserEntity } from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity, EmailTemplateEntity]),
    ModuleLoggerModule.register('email-template'),
  ],
  controllers: [EmailTemplateController],
  providers: [EmailTemplateSerivice],
})
export class EmailTemplateModule {}
