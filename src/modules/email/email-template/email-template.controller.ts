import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { AuthorizedRequest } from '../../auth/interfaces/authorized-request.interface';
import { GetEmailTemplatesListResponse } from '../../connector/dto/get-email-template.dto';
import { EmailTemplateSerivice } from './email-template.service';

@ApiTags('Email template')
@ApiBearerAuth()
@Controller({
  version: '2',
  path: 'email_template',
})
export class EmailTemplateController {
  constructor(private emailTemplateSerivice: EmailTemplateSerivice) {}

  @ApiQuery({ name: 'survey_id' })
  @ApiOkResponse({
    description: 'List of Brand email templates',
    type: GetEmailTemplatesListResponse,
  })
  @UseGuards(AuthGuard)
  @Get('/')
  getEmailTemplates(@Query('survey_id') survey_id) {
    return this.emailTemplateSerivice.getEmailTempaltes(survey_id);
  }
}
