import { HttpException, Injectable } from '@nestjs/common';
import {
  AuthorizedData,
  AuthorizedUser,
} from '../auth/interfaces/authorized-request.interface';
import { DateUtilsService } from '../../shared/utils/date-utils.service';
import { between, floatFixed } from '../../shared/utils/string-utils';
import {
  IQuadrantMainLabel,
  IQuadrantMetric,
  IQuadrantPeriodData,
  IQuadrantResponse,
  IQuadrantSegment,
  IQuadrantSubLabel,
  UpdateMetricComparisonOptions,
} from './interface/quadrant.interface';
import {
  IVerbatimChartType,
  IVerbatimChartsData,
  IVerbatimMainChartsData,
} from './interface/verbatim.interface';
import { VerbatimService } from './verbatim.service';
import * as moment from 'moment';
import { EventsLogger } from '@lib/logger/handlers/events.db-logger';
import { colors } from '../charts/constants/colors';
import { ProgramSettingsService } from '../common/program-settings/program-settings.service';

@Injectable()
export class QuadrantService {
  constructor(
    private verbatimService: VerbatimService,
    private dateService: DateUtilsService,
    private eventsLogger: EventsLogger,
    private readonly programSettingsService: ProgramSettingsService,
  ) {}

  async getData(body: any, query: any) {
    let response: IQuadrantResponse = {};

    this.updateParams(body);

    let settings = await this.programSettingsService.get();

    let chartsData = await this.verbatimService.getChartsData(
      body,
      query,
      settings,
    );

    let mainData = await this.verbatimService.getMainChartsData(
      body,
      query,
      settings,
    );

    console.log(`Start main  ${moment().toISOString()}`);

    chartsData = chartsData.map((item) => ({
      ...item,
      formattedDate: this.dateService.getPeriodLabel(item.dt, body.period),
    }));

    response.metric = this.getMetrics(chartsData, chartsData.length);

    response.mainlabels = this.getMainLabels(body, mainData, chartsData);

    console.log(`END main  ${moment().toISOString()}`);
    return response;
  }

  updateParams(params: any) {
    params.date_field = [{ id: 1 }];
    params.period = params.periods?.[0]?.id || 'week';

    let dates = this.dateService.formatDates({
      fromDate: params.from_date,
      toDate: params.to_date,
    });

    if (!dates.fromDate.isValid() || !dates.toDate.isValid()) {
      throw new HttpException('Dates are not correct', 400);
    }

    params.from_date = dates.fromDate;
    params.to_date = dates.toDate;

    params.fixedPeriods = this.verbatimService.getPeriods(params);
  }

  getMetrics(list: IVerbatimChartType[], totalCount: number): IQuadrantMetric {
    let uniqueList = list.reduce((prev, item) => {
      if (!prev[item.sa_id]) {
        prev[item.sa_id] = item;
      }
      return prev;
    }, {});

    list = Object.values(uniqueList);

    let categorizedCount = this.getCategorizedCount(list);

    const { promoters, detractors } = categorizedCount;

    const segments = this.getSegments(list, categorizedCount);

    return {
      type: 'NPS',
      value:
        list.length == 0 ? 0 : this.getNPS(promoters, detractors, list.length),
      responses: list.length,
      procent: this.getProcent(list.length, totalCount),
      metric_improvement: 0,
      response_improvement: 0,
      segments,
    };
  }

  getProcent(count, totalCount, removeDecimal: boolean = false) {
    let result = (100 / totalCount) * count;
    return floatFixed(result, removeDecimal ? 0 : 2);
  }

  getNPS(promoters: number, detractors: number, total: number) {
    return Math.round((100 * (promoters - detractors)) / total);
  }

  getSegments(
    list: IVerbatimChartType[],
    categorizedCount?: any,
  ): IQuadrantSegment[] {
    let promoters, detractors, passives;
    if (!categorizedCount) {
      categorizedCount = this.getCategorizedCount(list);
    }

    promoters = categorizedCount.promoters;
    detractors = categorizedCount.detractors;
    passives = categorizedCount.passives;

    return [
      {
        name: 'detractors',
        value: this.getProcent(detractors, list.length, true),
      },
      {
        name: 'passives',
        value: this.getProcent(passives, list.length, true),
      },
      {
        name: 'promoters',
        value: this.getProcent(promoters, list.length, true),
      },
    ];
  }
  getCategorizedCount(list: IVerbatimChartType[]) {
    const promoters = list.filter((i) => i.return_value >= 9).length;
    const detractors = list.filter((i) => between(i.return_value, 0, 6)).length;

    const passives = list.filter((i) => between(i.return_value, 7, 8)).length;

    return {
      promoters,
      detractors,
      passives,
    };
  }

  getMainLabels(
    params: any,
    list: IVerbatimMainChartsData[],
    allCharts: IVerbatimChartsData[],
  ): IQuadrantMainLabel[] {
    let result: IQuadrantMainLabel[] = [];

    for (let item of list) {
      if (!item.pid) continue;
      if (result.find((i) => i.label === item.parent_label)) continue;

      let childList = allCharts.filter(
        (subItem) => subItem.parent_label === item.parent_label,
      );

      result.push({
        id: item.pid,
        label: item.parent_label,
        period_data: this.getPeriodData(params, childList, allCharts),
        metric: this.getMetrics(childList, allCharts.length),
        sublabels: this.getSubLabels(params, item, childList, allCharts),
        color: item.graph_color,
        transparent_color: item.graph_color + '80', // 50% opacity in hex is 80
      });
    }

    for (let mainLabel of result) {
      this.updateImprovements(mainLabel.period_data || []);
      for (let subLabel of mainLabel.sublabels) {
        this.updateImprovements(subLabel.period_data || []);
      }
    }

    this.updateComparsions(result, params.fixedPeriods);
    for (let mainLabel of result) {
      this.updateComparsions(mainLabel.sublabels, params.fixedPeriods);
    }

    return result;
  }

  getSubLabels(
    params: any,
    mainLabel: IVerbatimMainChartsData,
    list: IVerbatimChartsData[],
    allCharts: IVerbatimChartsData[],
  ): IQuadrantSubLabel[] {
    let result: IQuadrantSubLabel[] = [];
    let index = 0;
    for (let item of list) {
      if (result.find((i) => i.label === item.child_label)) continue;
      let childList = list.filter(
        (subItem) => subItem.child_label === item.child_label,
      );
      const color = this.getIndexColor(index);
      result.push({
        id: item.item_id,
        parent_id: mainLabel.pid,
        label: item.child_label,
        period_data: this.getPeriodData(params, childList, allCharts),
        metric: this.getMetrics(childList, allCharts.length),
        color: color.borderColor,
        transparent_color: color.backgroundColor,
      });

      index++;
    }

    return result;
  }

  getPeriodData(
    params: any,
    list: IVerbatimChartsData[],
    allCharts: IVerbatimChartsData[],
  ): IQuadrantPeriodData[] {
    let result: IQuadrantPeriodData[] = [];

    for (let { date, label } of params.fixedPeriods) {
      let periodList = list.filter((i) => i.formattedDate === label);

      let metric = this.getMetrics(periodList, allCharts.length);
      const color = this.verbatimService.getColor(metric.value);
      result.push({
        period_label: label,
        period_value: date,
        color,
        metric,
        is_highest_metric_value: false,
        is_highest_response: false,
        is_lowest_metric_value: false,
        is_lowest_response: false,
        previous_period: result[result.length - 1]?.period_label || '',
        transparent_color: color + '80', // 50% opacity in hex is 80
      });
    }

    return result;
  }

  updateImprovements(list: IQuadrantPeriodData[]) {
    list.reduce((prev: UpdateMetricComparisonOptions, item) => {
      // calculate improvements
      if (prev.metric_improvement !== undefined) {
        item.metric.metric_improvement =
          item.metric.value - prev.metric_improvement;
      }
      prev.metric_improvement = item.metric.value;

      if (prev.response_improvement !== undefined) {
        item.metric.response_improvement =
          item.metric.responses - prev.response_improvement;
      }

      prev.response_improvement = item.metric.responses;

      return prev;
    }, {});
  }

  updateComparsions(
    list: Array<IQuadrantMainLabel | IQuadrantSubLabel>,
    periods: any,
  ) {
    for (let { date } of periods) {
      const metrics = list.reduce(
        (prev: UpdateMetricComparisonOptions, item, index) => {
          let periodData = item.period_data.find(
            (i) => i.period_value === date,
          );
          if (periodData) {
            let metric = periodData.metric;
            // highest metric
            if (
              metric.metric_improvement >
              (prev.is_highest_metric_value?.value || 0)
            ) {
              prev.is_highest_metric_value = {
                value: metric.metric_improvement,
                indexes: [index],
              };
            } else if (
              prev.is_highest_metric_value?.value === metric.metric_improvement
            ) {
              prev.is_highest_metric_value &&
                prev.is_highest_metric_value.indexes.push(index);
            }

            // lowest metric
            if (
              prev.is_lowest_metric_value?.value === undefined ||
              metric.metric_improvement < prev.is_lowest_metric_value?.value
            ) {
              prev.is_lowest_metric_value = {
                value: metric.metric_improvement,
                indexes: [index],
              };
            } else if (
              prev.is_lowest_metric_value?.value === metric.metric_improvement
            ) {
              prev.is_lowest_metric_value.indexes.push(index);
            }

            // highest response
            if (
              metric.response_improvement >
              (prev.is_highest_response?.value || 0)
            ) {
              prev.is_highest_response = {
                value: metric.response_improvement,
                indexes: [index],
              };
            } else if (
              prev.is_highest_response?.value === metric.response_improvement
            ) {
              prev.is_highest_response &&
                prev.is_highest_response.indexes.push(index);
            }

            // lowest response
            if (
              prev.is_lowest_response?.value === undefined ||
              metric.response_improvement < prev.is_lowest_response?.value
            ) {
              prev.is_lowest_response = {
                value: metric.response_improvement,
                indexes: [index],
              };
            } else if (
              prev.is_lowest_response?.value === metric.metric_improvement
            ) {
              prev.is_lowest_response.indexes.push(index);
            }
          }
          return prev;
        },
        {},
      );

      for (let key in metrics) {
        let metric = metrics[key];
        for (let index of metric.indexes) {
          list[index].period_data.find((p) => p.period_value === date)[key] =
            true;
        }
      }
    }
  }

  getIndexColor(index = 0) {
    if (index > colors.length - 1) {
      return this.getIndexColor(index - colors.length);
    }
    return colors[index];
  }
}
