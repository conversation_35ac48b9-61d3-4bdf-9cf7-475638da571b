import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyAnswerEntity } from '../database/entities/surv-surveyanswer.entity';
import { DateUtilsService } from '../../shared/utils/date-utils.service';
import { VerbatimAnalyseController } from './verbatim.controller';
import { VerbatimService } from './verbatim.service';
import { QuadrantService } from './quadrant.service';
import { LoggerModule } from '@lib/logger/logger.module';
import { ProgramSettingsModule } from '../common/program-settings/program-settings.module';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { SurveyModule } from '../survey/survey.module';

@Module({
  imports: [
    ModuleLoggerModule.register('verbatim'),
    TypeOrmModule.forFeature([SurveyAnswerEntity]),
    LoggerModule,
    ProgramSettingsModule,
    SurveyModule,
  ],
  controllers: [VerbatimAnalyseController],
  providers: [VerbatimService, DateUtilsService, QuadrantService],
  exports: [VerbatimService, QuadrantService],
})
export class VerbatimModule {}
