export interface IVerbatimChartsData {
  return_value: number;
  item_id: number;
  sa_id: number;
  child_label: string;
  parent_label: string;
  pid: number;
  pos: number;
  neg: number;
  dt: string;
  formattedDate?: string;
  graph_color: string;
}

export interface IVerbatimMainChartsData {
  return_value: number;
  sa_id: number;
  parent_label: string;
  pid: number;
  pos: number;
  neg: number;
  creation_date: string;
  dt: string;
  graph_color: string;
}

export type IVerbatimChartType = IVerbatimMainChartsData | IVerbatimChartsData;
