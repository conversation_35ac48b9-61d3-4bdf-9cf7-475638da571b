export interface IQuadrantResponse {
  metric?: IQuadrantMetric;
  mainlabels?: IQuadrantMainLabel[];
}

export interface IQuadrantMetric {
  type: 'NPS' | 'CSAT';
  value: number;
  responses: number;
  procent: number;
  metric_improvement: number;
  response_improvement: number;
  segments: IQuadrantSegment[];
}

export interface IQuadrantSegment {
  name: 'promoters' | 'passives' | 'detractors';
  value: number;
}

export interface IQuadrantMainLabel {
  id: number;
  label: string;
  color: string;
  transparent_color: string;
  metric: IQuadrantMetric;
  period_data: IQuadrantPeriodData[];
  sublabels: IQuadrantSubLabel[];
}

export interface IQuadrantPeriodData extends IMetricComparisonOptions {
  period_label: string;
  period_value: string;
  color: string;
  transparent_color: string;
  previous_period: string;

  metric: IQuadrantMetric;
}

export interface IMetricComparisonOptions {
  is_highest_metric_value: boolean;
  is_lowest_metric_value: boolean;
  is_highest_response: boolean;
  is_lowest_response: boolean;
}
export interface IQuadrantSubLabel
  extends Omit<IQuadrantMainLabel, 'sublabels'> {
  parent_id: number;
}

export type UpdateMetricComparisonOptions = {
  metric_improvement?: number;
  response_improvement?: number;
} & {
  [K in keyof IMetricComparisonOptions]?: {
    value: number;
    indexes?: number[];
  };
};
