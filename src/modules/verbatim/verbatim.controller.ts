import { Body, Controller, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@/shared/guards/auth.guard';
import { VerbatimService } from './verbatim.service';
import { ApiBearerAuth, ApiBody, ApiTags } from "@nestjs/swagger";
import { QuadrantService } from './quadrant.service';

@ApiTags('Verbatim analysis')
@Controller({
  path: 'verbatim_analysis',
  version: '2',
})
@ApiBearerAuth()
export class VerbatimAnalyseController {
  constructor(
    private verbatimService: VerbatimService,
    private quadrantService: QuadrantService,
  ) {}

  @Post()
  @UseGuards(AuthGuard)
  @ApiBody({})
  async getVerbatimAnalyse(@Body() body: any, @Query() query: any) {
    return this.verbatimService.getVerbatimAnalyse(body, query);
  }

  @Post('/quadrant')
  @UseGuards(AuthGuard)
  @ApiBody({})
  async getQuadrantChart(@Body() body: any, @Query() query: any) {
    return this.quadrantService.getData(body, query);
  }
}
