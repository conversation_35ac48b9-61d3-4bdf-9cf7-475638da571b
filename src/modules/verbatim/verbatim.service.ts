import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';
import { CompanyService } from '../company/company.service';
import { SurveyAnswerEntity } from '../database/entities/surv-surveyanswer.entity';

import { queryConvert } from '../../shared/utils/query-convert';

import * as moment from 'moment';
import { CompanyType } from '../../shared/enums/company-type.enum';
import {
  DatePeriod,
  DateUtilsService,
} from '../../shared/utils/date-utils.service';
import { DateField } from '../../shared/enums/datefield.enum';
import { numberFormat } from '../../shared/utils/string-utils';
import {
  IVerbatimChartsData,
  IVerbatimMainChartsData,
} from './interface/verbatim.interface';
import { SecProgramSettingsEntity } from '../database/entities/sec-programsettings.entity';
import { ProgramSettingsService } from '../common/program-settings/program-settings.service';
import { AI_THRESHOLD_DEFAULT } from '../../config/ai.config';
import { ClsService } from 'nestjs-cls';
import { ClsProperty } from '@/config/contents';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
import { SurveyService } from '../survey/survey.service';

@Injectable()
export class VerbatimService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    private dateservice: DateUtilsService,
    private surveyService: SurveyService,
    @InjectRepository(SurveyAnswerEntity)
    private readonly surveyAnswerRepo: Repository<SurveyAnswerEntity>,
    private readonly programSettingsService: ProgramSettingsService,
  ) {}
  public getBaseQuery(sec_company_id: number) {
    return `
    FROM surv_surveyanswer a  
    LEFT JOIN d_date ON a.creation_date::date = d_date.date 
    LEFT JOIN surv_survey s ON a.surv_survey_id = s.surv_survey_id 
    LEFT JOIN email_customer ec ON a.email_customer_id = ec.email_customer_id 
    LEFT JOIN techgrp tg ON ec.xml_technician = tg.techgrp_techn AND tg.sec_company_id = ${sec_company_id} AND tg.techgrp_type = 'xml_technician' 
    `;
  }

  private getBaseJoins(params: any) {
    return `
  ${
    params.tags
      ? `LEFT JOIN clf_call call ON a.email_customer_id = call.email_customer_id`
      : ''
  }
  ${
    params.callback_question
      ? 'LEFT JOIN surv_surveyansweritem callback on a.surv_surveyanswer_id = callback.surv_surveyanswer_id and s.callback_question = callback.surv_surveyquestion_id '
      : ''
  }
  ${
    params.contact_center?.length > 0
      ? `LEFT JOIN techgrp techDOB ON ec.xml_acceptance_by = techDOB.techgrp_techn AND techDOB.techgrp_type = 'xml_acceptance_by' `
      : ''
  }
  ${
    params.dispatcher?.length > 0
      ? `LEFT JOIN techgrp techDispatch ON ec.xml_planning = techDispatch.techgrp_techn AND techDispatch.techgrp_type = 'xml_planning'`
      : ''
  }
  ${
    params.preprepper?.length > 0
      ? `LEFT JOIN techgrp techPreppep ON ec.xml_wvb = techPreppep.techgrp_techn AND techPreppep.techgrp_type = 'xml_wvb' `
      : ''
  }
  ${
    params.service_partner?.length > 0
      ? ` LEFT JOIN techgrp techServicePartner ON ec.xml_custcreditnr = techServicePartner.techgrp_techn AND techServicePartner.techgrp_type = 'xml_custcreditnr' `
      : ''
  }
  ${
    params.counselor?.length > 0
      ? `LEFT JOIN techgrp techCounselor ON ec.xml_employee = techCounselor.techgrp_techn AND techCounselor.techgrp_type = 'xml_employee' `
      : ''
  }
  ${
    params.engineer?.length > 0
      ? `LEFT JOIN techgrp tech ON ec.xml_technician = tech.techgrp_techn AND tech.techgrp_type = 'xml_technician'`
      : ''
  }

  ${
    params.dealer?.length > 0
      ? `LEFT JOIN techgrp techDealer ON ec.xml_dealer = techDealer.techgrp_techn AND techDealer.techgrp_type = 'xml_dealer' `
      : ''
  }
  `;
  }

  private getMclassQuery(settings: SecProgramSettingsEntity) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    let props = settings?.company_properties;
    const ai_threshold = props?.ai_threshold || AI_THRESHOLD_DEFAULT;

    return `
  LEFT JOIN mclass_response mr ON a.surv_surveyanswer_id = mr.answer_id  
  LEFT JOIN mclass_item mi ON mr.item_id = mi.mclass_item_id 
  LEFT JOIN mclass_item mip ON mi.parent_id = mip.mclass_item_id 
  LEFT JOIN mclass_item_local mil ON mil.mclass_item_id = mi.mclass_item_id AND mil.sec_company_id = ${company.id} 
  LEFT JOIN mclass_item_local milp ON milp.mclass_item_id = mi.parent_id AND milp.sec_company_id = ${company.id} 
  WHERE ec.survey_done = 1 AND 
  mi.sec_company_id IN (${company.id}, 25) AND 
  mr.item_id IS NOT NULL 
  AND mr.record_status != 3
  AND (mr.confidence >= ${ai_threshold} OR mr.confidence IS NULL)
  AND (mr.is_excluded = 0 OR mr.is_excluded IS NULL) 
  `;
  }
  async getVerbatimAnalyse(params: any, query: any = {}) {
    params.date_field = [{ id: 1 }];
    let period = params.periods?.[0]?.id || 'week';

    this.logger.debug('getVerbatimAnalyse', { params, query });

    const { fromDate, toDate } = this.dateservice.formatDates({
      fromDate: params.from_date,
      toDate: params.to_date,
    });

    if (!fromDate.isValid() || !toDate.isValid()) {
      throw new HttpException('Dates are not correct', 400);
    }

    params.from_date = fromDate;
    params.to_date = toDate;

    let settings = await this.programSettingsService.get();

    let chartsData = await this.getChartsData(params, query, settings);
    let parentData = await this.getMainCharts(params, query, settings);

    let result = {
      totalAnswers: 0,
      treemap: {
        name: 'root',
        children: [],
        nps: 'NPS',
        back: 'Back',
        responded: 'Responded',
      },
      table_responses: [],
      table: [],
    };

    let answerIds = [...new Set(chartsData.map((item) => item.sa_id))];

    let filteredChart = {};
    let datesChart = {};
    let ids = [];
    for (let item of chartsData) {
      const {
        parent_label,
        child_label,
        sa_id,
        dt,
        pid,
        item_id,
        return_value,
      } = item;

      let dateLabel = this.dateservice.getPeriodLabel(dt, period);

      let obj = filteredChart[parent_label] || {
        children: {},
        dates: {},
        ids: [],
      };
      obj.ids.push(sa_id);
      ids.push(sa_id);

      if (!obj.dates[dateLabel]) {
        obj.dates[dateLabel] = {
          pos: 0,
          neg: 0,
          pas: 0,
          ids: [],
        };
      }

      if (!obj.dates[dateLabel].ids.includes(sa_id)) {
        obj.dates[dateLabel].ids.push(sa_id);
        if (return_value <= 6) {
          obj.dates[dateLabel].neg++;
        } else if (return_value == 7 || return_value == 8) {
          obj.dates[dateLabel].pas++;
        } else if (return_value == 9 || return_value == 10) {
          obj.dates[dateLabel].pos++;
        }
      }

      if (!obj.children[child_label]) {
        obj.children[child_label] = {
          nps: [],
          dates: {},
          pos: 0,
          neg: 0,
          pas: 0,
          parent_id: pid,
          item_id,
        };
      }

      obj.children[child_label].nps.push(return_value);

      if (!obj.children[child_label].dates[dateLabel]) {
        obj.children[child_label].dates[dateLabel] = { pos: 0, neg: 0, pas: 0 };
      }

      if (return_value <= 6) {
        obj.children[child_label].neg++;
        obj.children[child_label].dates[dateLabel].neg++;
      } else if (return_value == 7 || return_value == 8) {
        obj.children[child_label].pas++;
        obj.children[child_label].dates[dateLabel].pas++;
      } else if (return_value == 9 || return_value == 10) {
        obj.children[child_label].pos++;
        obj.children[child_label].dates[dateLabel].pos++;
      }
      datesChart[dateLabel] = datesChart[dateLabel] || true;

      filteredChart[parent_label] = obj;
    }

    ids = [...new Set(ids)];

    let periods = this.getPeriods(params);

    for (let [key, chart] of Object.entries(filteredChart) as any) {
      const [children, childrenTable] = this.getChartChildren({
        key,
        chart,
        answerIds,
        periods,
        parentData,
      });

      const neg = parentData[key].nps.filter((i) => i <= 6).length;
      const pos = parentData[key].nps.filter((i) =>
        [9, 10].includes(+i),
      ).length;
      const total = [...new Set(parentData[key].ids)].length;

      const grade = Math.round((pos / total - neg / total) * 100);
      const procent = (100 / answerIds.length) * total;

      result.treemap.children.push({
        children,
        grade: `${grade}`,
        name: key,
        procent: numberFormat(procent, 2),
        id: parentData[key].parent_id,
        value: total,
      });

      let data = periods.map((period) => {
        const { date, label } = period;
        if (parentData.dates[label]) {
          const { pos, neg, pas } = chart.dates[label] || {};
          let totalResponses = parentData.dates[label].ids.length;
          let responses = chart.dates[label]?.ids?.length || 0;
          let nps =
            responses > 0
              ? Math.round((pos / responses - neg / responses) * 100)
              : '';
          let procent = (100 / totalResponses) * responses;
          return {
            date_value: moment(date).format('YYYY-MM-DD HH:mm:ss'),
            date: label,
            responses,
            nps,
            procent: numberFormat(procent || 0, 2),
            nps_color: this.getColor(nps),
          };
        } else {
          return {
            date_value: moment(date).format('YYYY-MM-DD HH:mm:ss'),
            date: label,
            responses: 0,
            nps: '',
            procent: 0,
          };
        }
      });

      result.table.push({
        tg: 'tableObj',
        color: parentData[key].graph_color,
        data,
        children: childrenTable,
        label: key,
        id: parentData[key].parent_id,
        responses: undefined,
      });
    }

    result.table_responses = periods
      .filter((period) => parentData.dates[period.label])
      .map((period) => {
        let { label } = period;
        return parentData.dates[label].ids?.length || 0;
      });

    result.totalAnswers = answerIds.length;

    this.logger.debug('getVerbatimAnalyse result', { result, periods });

    return result;
  }

  private getChartChildren(params) {
    const { key, chart, answerIds, periods, parentData } = params;
    let children = [];
    let childrenTable = [];
    this.logger.debug(`getChartChildren ${key}`, { params });
    for (let [subkey, subchart] of Object.entries(chart.children) as any) {
      let { nps, pos, neg } = subchart;

      let grade = nps.reduce((sum, num) => sum + +num, 0);
      grade = Math.round((pos / nps.length - neg / nps.length) * 100);

      let procent = (100 / answerIds.length) * nps.length;
      children.push({
        name: subkey,
        value: nps.length,
        grade: `${grade}`,
        procent: numberFormat(procent, 2),
        color: this.getColor(grade),
        parent_id: subchart.parent_id,
        id: subchart.item_id,
      });

      let data = periods.map((period) => {
        const { date, label } = period;
        let subChartDate = subchart.dates[label];
        if (subChartDate) {
          const { pos, neg, pas } = subChartDate;
          let responses = pos + neg + pas;
          let nps = Math.round((pos / responses - neg / responses) * 100);
          let procent =
            (100 / parentData.dates[label]?.ids?.length) * responses;
          return {
            date_value: moment(date).format('YYYY-MM-DD HH:mm:ss'),
            date: label,
            responses,
            nps,
            procent: numberFormat(procent || 0, 2),
            nps_color: this.getColor(nps),
          };
        } else {
          return {
            date_value: moment(date).format('YYYY-MM-DD HH:mm:ss'),
            date: label,
            responses: 0,
            nps: '',
            procent: 0,
          };
        }
      });

      childrenTable.push({
        color: parentData[key]?.graph_color,
        label: subkey,
        id: subchart.item_id,
        data,
      });
    }

    this.logger.debug(`getChartChildren result for key ${key}`, {
      children,
      childrenTable,
    });

    return [children, childrenTable];
  }

  async getChartsData(
    params: any,
    query: any = {},
    settings: SecProgramSettingsEntity,
  ): Promise<IVerbatimChartsData[]> {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`getChartsData`, { params, query });
    let args = {
      dt: params.periods[0].id,
    };

    let ignoreDates = false;
    if (query.ignoreDates == 'true' || (params && params.ignoreDates)) {
      ignoreDates = true;
    }

    let sql = `
    SELECT a.nps_value return_value, 
    mr.item_id, a.surv_surveyanswer_id AS sa_id,  
    COALESCE(mil.item_label_locale ,mi.item_label) child_label, 
    COALESCE(milp.item_label_locale ,mip.item_label) parent_label, 
    mi.parent_id AS pid,  
    CASE WHEN mr.istype = 1 THEN 1 ELSE 0 END AS pos,  
    CASE WHEN mr.istype = 2 THEN 1 ELSE 0 END AS neg, 
    a.creation_date, date_trunc(:dt, a.creation_date) dt
    ${this.getBaseQuery(company.id)}
    ${this.getBaseJoins(params)}
    ${this.getMclassQuery(settings)}
    AND mi.parent_id is not null 
    ${
      params.callback_question?.[0]?.id == 1
        ? ` AND callback.return_value='1' `
        : ''
    }
    ${
      params.callback_question?.[0]?.id == 2
        ? ` AND callback.return_value='0' `
        : ''
    }
    `;

    const [filterQuery, filterArgs] = await this.getFilterData(params, {
      ignoreDates,
    });
    sql += filterQuery;

    sql += ` ORDER BY parent_label, child_label, a.creation_date`;
    args = { ...args, ...filterArgs };

    this.logger.debug(`getChartsData sql`, { sql, sqlArgs: args });

    let [rawQuery, queryValues] = queryConvert(sql, args);
    let result = await this.surveyAnswerRepo.query(rawQuery, queryValues);

    this.logger.debug(`getChartsData result`, { result });

    return result as IVerbatimChartsData[];
  }

  async getMainChartsData(
    params: any,
    query?: any,
    settings?: SecProgramSettingsEntity,
  ) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`getMainChartsData `, { params, query, company });

    var ignoreDates = false;
    if (query?.ignoreDates == 'true' || (params && params.ignoreDates)) {
      ignoreDates = true;
    }

    let dateFormatter = {
      day: 'date',
      month: 'monthstartdate',
      quarter: 'quarterstartdate',
      year: 'yearstartdate',
      week: 'weekstartdate',
    };

    let period = params.periods?.[0]?.id || 'week';

    let sql = `
    SELECT a.nps_value return_value,
    a.surv_surveyanswer_id AS sa_id, 
    COALESCE(milp.item_label_locale, mip.item_label) parent_label,
    mi.parent_id AS pid, 
    CASE WHEN mr.istype = 1 THEN 1 ELSE 0 END AS pos, 
    CASE WHEN mr.istype = 2 THEN 1 ELSE 0 END AS neg,
    a.creation_date, d_date.${
      dateFormatter[period] || dateFormatter.week
    } AS dt,
    COALESCE(mip.graph_color, '#142459') as graph_color
    ${this.getBaseQuery(company.id)}
    ${this.getBaseJoins(params)}
    ${this.getMclassQuery(settings)}

    ${
      params.callback_question?.[0]?.id == 1
        ? ` AND callback.return_value='1' `
        : ''
    }
    ${
      params.callback_question?.[0]?.id == 2
        ? ` AND callback.return_value='0' `
        : ''
    }
      `;

    const [filterQuery, filterArgs] = await this.getFilterData(params, {
      ignoreDates,
    });

    sql += filterQuery;
    sql += ` GROUP BY sa_id, parent_label, mi.parent_id, mr.istype, a.creation_date, dt, mip.graph_color ORDER BY parent_label, a.creation_date`;

    this.logger.debug(`getMainChartsData sql`, { sql, sqlArgs: filterArgs });

    let [rawQuery, queryValues] = queryConvert(sql, filterArgs);
    let list: IVerbatimMainChartsData[] = await this.surveyAnswerRepo.query(
      rawQuery,
      queryValues,
    );

    this.logger.debug(`getMainChartsData result`, { list });

    return list;
  }

  async getMainCharts(
    params: any,
    query: any = {},
    settings: SecProgramSettingsEntity,
  ) {
    let period = params.periods?.[0]?.id || 'week';

    this.logger.debug(`getMainCharts `, { params, query, period });

    let list = await this.getMainChartsData(params, query, settings);
    let result = {
      dates: {},
    };
    for (let item of list) {
      let { parent_label, graph_color, pid, dt, sa_id, return_value } = item;
      let dateLabel = this.dateservice.getPeriodLabel(dt, period);
      if (!result[parent_label]) {
        result[parent_label] = {
          children: {},
          dates: {},
          nps: [],
          graph_color,
          parent_id: pid,
          ids: [],
        };
      }
      if (!result[parent_label].dates[dateLabel]) {
        result[parent_label].dates[dateLabel] = {
          pos: 0,
          neg: 0,
          pas: 0,
          ids: [],
        };
      }
      if (!result[parent_label].ids.includes(sa_id)) {
        result[parent_label].nps.push(return_value);
        result[parent_label].ids.push(sa_id);
      }

      if (!result.dates[dateLabel]) {
        result.dates[dateLabel] = { ids: [] };
      }

      if (!result[parent_label].dates[dateLabel].ids.includes(sa_id)) {
        result.dates[dateLabel].ids.push(sa_id);
        result[parent_label].dates[dateLabel].ids.push(sa_id);

        if (return_value <= 6) result[parent_label].dates[dateLabel].neg++;
        else if (return_value == 7 || return_value == 8)
          result[parent_label].dates[dateLabel].pas++;
        else if (return_value == 9 || return_value == 10)
          result[parent_label].dates[dateLabel].pos++;
      }
    }

    for (let j in result.dates) {
      result.dates[j].ids = [...new Set(result.dates[j].ids)];
    }

    this.logger.debug(`getMainCharts result`, { result });

    return result;
  }

  async getFilterData(params: any, other: any) {
    const { company, userCompany } = this.cls.get<AuthorizedData>(
      ClsProperty.user,
    );

    this.logger.debug(`getFilterData `, { params, company });

    let args: any = {};
    let sql = '';
    if (params.brands?.length) {
      // if (user.is_parent_company) {
      //   sql += ' AND (';
      //   sql += (Object.entries(params.brands) as any).map(([index, item]) => {
      //     args[`brandCompany${index}`] = item.id;
      //     args[`brand${index}`] = item.name;
      //     return `(ec.sec_company_id = :brandCompany${index}  AND LOWER(xml_brand) = :brand${index} )`;
      //   });
      //   sql += ') ';
      // } else {
      args.brands = params.brands.map((item) => item.id.toLowerCase()); // item.name;
      sql += `  AND LOWER(xml_brand) =  ANY(:brands::text[])`;
      // }
    }

    if (params.survey?.length) {
      args.surveys = params.survey.map((item) => item.id);
      sql += ` AND a.surv_survey_id = ANY(:surveys::int[])`;
    } else {
      let allowedSurveys = await this.surveyService.list();
      if (allowedSurveys?.length) {
        args.surveys = allowedSurveys.map((item) => item.id);
        sql += ` AND a.surv_survey_id = ANY(:surveys::int[])`;
      }
    }
    if (!other.ignoreDates) {
      if (!params.date_field)
        throw new HttpException('No date_field in payload', 400);

      args.dateFrom = params.from_date;
      args.dateTo = params.to_date;

      if (params.date_field[0].id == DateField.ResponseDate) {
        sql += ` AND a.creation_date BETWEEN :dateFrom AND :dateTo`;
      } else {
        sql += ` AND ec.xml_readydate BETWEEN :dateFrom AND :dateTo`;
      }
    }

    if (params.language?.length) {
      args.languages = params.language.map((item) => item.id); // item.name
      sql += ` AND ec.xml_language  = ANY(:languages::text[])`;
    }

    if (params.labels?.length) {
      args.labels = params.labels.map((item) => item.id);
      sql += ` AND mr.item_id = ANY(:labels::int[])`;
    }
    if (params.medium?.length) {
      let m = params.medium.map((item) => item.id);
      if (!m.includes(1) || !m.includes(2)) {
        if (m[0] === 1) {
          sql += ` AND ec.sms_id IS NULL`;
        } else if (m[0] === 2) {
          sql += ` AND ec.sms_id IS NOT NULL`;
        }
      }
    }
    if (params.tags?.length) {
      args.tags = params.tags.map((item) => item.id);
      sql += ` AND call.clf_call_id IN (select ct2.clf_call_id from clf_tags ct2 where ct2.clf_call_id = call.clf_call_id AND ct2.tag_id = ANY(:tags::int[]) )`;
    }

    if (
      (params.template_id == 15 ||
        params.isExport ||
        params.isVerbatimAnalysis) &&
      params?.label_type.length
    ) {
      args.labelType = params.label_type.map((item) => item.id);
      sql += ` AND mr.record_status = ANY(:labelType::int[])`;
    }
    if (params.regions?.length) {
      args.regions = params.regions.map((item) => item.id); // item.name
      sql += ` AND ${
        company.id == 2 ? 'ec.xml_techgrp' : 'ec.xml_region'
      }  = ANY(:regions::text[]) `;
    }

    if (params.productgroup?.length) {
      args.productGroup = params.productgroup.map((item) => item.id); // item.name
      sql += ` AND ec.xml_prodarea = ANY(:productGroup::text[])`;
    }

    if (params.product_division?.length) {
      args.productDivision = params.product_division.map((item) => item.id); // item.name
      sql += ` AND ec.xml_proddivision = ANY(:productDivision::text[])`;
    }

    if (params.cp?.length) {
      args.cp = params.cp.map((item) => item.id); // item.name
      sql += ` AND ec.xml_cp  = ANY(:cp::text[])`;
    }
    if (params.sv?.length) {
      args.sv = params.sv.map((item) => item.id); // item.name
      sql += ` AND ec.xml_sv  = ANY(:sv::text[])`;
    }

    if (params.contact_center?.length) {
      args.contactCenter = params.contact_center.map((item) => item.id);
      sql += ` AND techDOB.techgrp_id  = ANY(:contactCenter::int[])`;
    }

    if (params.dispatcher?.length) {
      args.dispatcher = params.dispatcher.map((item) => item.id);
      sql += ` AND techDispatch.techgrp_id  = ANY(:dispatcher::int[])`;
    }

    if (params.preprepper?.length) {
      args.preprepper = params.preprepper.map((item) => item.id);
      sql += ` AND techPreppep.techgrp_id  = ANY(:preprepper::int[])`;
    }

    if (params.service_partner?.length) {
      args.service_partner = params.service_partner.map((item) => item.id);
      sql += ` AND techServicePartner.techgrp_id  = ANY(:service_partner::int[])`;
    }

    if (params.counselor?.length) {
      args.counselor = params.counselor.map((item) => item.id);
      sql += ` AND techCounselor.techgrp_id  = ANY(:counselor::int[])`;
    }

    if (params.engineer?.length) {
      args.engineer = params.engineer.map((item) => item.id);
      sql += ` AND tech.techgrp_id  = ANY(:engineer::int[])`;
    }

    if (params.dealer?.length) {
      args.dealer = params.dealer.map((item) => item.id);
      sql += ` AND techDealer.techgrp_id  = ANY(:dealer::int[])`;
    }

    if (params.project_category?.length) {
      args.project_category = params.project_category.map((item) => item.id);
      sql += ` AND ec.xml_internal_external IN (select p.project_id  from project_pcategories pc, projects p where p.project_id = pc.project_id AND pc.pcategory_id = ANY(:project_category::text[]) )`;
    }

    if (params.project?.length) {
      let pdiv = ['BO-MDA', 'BO-SDA', 'SI-MDA', 'SI-SDA'];
      args.projectDiv = params.project.filter((item) => pdiv.includes(item.id)); // item.name
      args.projectNr = params.project.filter((item) => !pdiv.includes(item.id)); // item.name

      if (args.projectDiv?.length > 0) {
        sql += ` AND ec.xml_projectdiv = ANY(:projectDiv::text[])`;
      }
      if (args.projectNr?.length > 0) {
        sql += ` AND ec.xml_projectnr = ANY(:projectNr::text[])`;
      }
    }

    if (company.type == CompanyType.BOUW || company.type == CompanyType.SPORT) {
      if (params.project_bouw?.length) {
        args.projectBouw = params.project_bouw.map((item) => item.id); // item.name
        sql += ` AND ec.xml_internal_external = ANY(:projectBouw::text[])`;
      } else {
        args.projectBouw = await this.getAllowedProjects(userCompany.sec_group);
        sql += ` AND (ec.xml_internal_external = ANY(:projectBouw::text[]) OR ec.xml_internal_external IS NULL)`;
      }
    }

    if (company.type == CompanyType.SPORT) {
      if (params.club?.length) {
        args.club = params.club.map((item) => item.id); // item.name
        sql += ` AND ec.xml_internal_external = ANY(:club::text[])`;
      } else if (!args.projectBouw) {
        args.projectBouw = await this.getAllowedProjects(userCompany.sec_group);
        sql += ` AND (ec.xml_internal_external = ANY(:projectBouw::text[]) OR ec.xml_internal_external IS NULL)`;
      }
    }

    if (params.score?.length) {
      args.score = [
        ...new Set(
          [].concat(
            ...params.score.map((item) => {
              if (item.id == 11) return [9, 10];
              else if (item.id == 12) return [7, 8];
              else if (item.id == 13) return [0, 1, 2, 3, 4, 5, 6];
              else return [item.id];
            }),
          ),
        ),
      ];
      sql += ` AND  a.nps_value = ANY(:score::int[])`;
    }

    if (params.bsh_survey_categories?.length) {
      args.bshCid = params.bsh_survey_categories.map((item) => item.id);
      sql += ` AND s.bsh_category_id  = ANY(:bshCid::int[])`;
    }

    if (params.warranty?.[0]?.id) {
      let wid = params.warranty[0].id;
      if (wid == 1) sql += ` AND ec.xml_la IN ('01', '83', '84') `;
      else if (wid == 2)
        sql += ` AND (ec.xml_la NOT IN ('01', '83', '84') OR ec.xml_la IS NULL)`;
    }

    if (params.label_sentiment?.[0]?.id > 0) {
      args.labelSentiment = params.label_sentiment[0].id;
      sql += ` AND  mr.istype = :labelSentiment `;
    }

    this.logger.debug(`getFilterData sql result`, { sql, sqlArgs: args });

    return [sql, args];
  }

  async getAllowedProjects(groupId: any, showAll = false) {
    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    if (showAll) {
      let list = await this.surveyAnswerRepo.query(
        `SELECT project_id FROM projects WHERE sec_company_id = ${company.id}`,
      );
      return list.map((i) => i.project_id);
    } else {
      let list = await this.surveyAnswerRepo.query(`
      SELECT project_id 
      FROM projects 
      WHERE sec_company_id =${company.id} AND project_id NOT IN ( 
        SELECT project_id 
          FROM project_groups) 
            OR project_id IN (SELECT project_id 
                                  FROM project_groups 
                                  WHERE group_id = ${groupId}) 
      ORDER BY project_id
      `);
      return list.map((i) => i.project_id);
    }
  }

  getPeriods(params: any) {
    let period: DatePeriod =
      params.period?.[0]?.id || params.periods?.[0]?.id || 'week';
    let startDate = moment(params.from_date);
    let endDate = moment(params.to_date);

    let dates = [];
    if (period == 'week') {
      startDate = startDate.startOf('isoWeek');
    } else if (period == 'month') {
      startDate = startDate.startOf('month');
    } else if (period == 'year') {
      startDate = startDate.startOf('year');
    } else if (period == 'quarter') {
      let quarterMonth = this.getQuarterMonth(startDate.month());
      startDate = startDate.set('month', quarterMonth);
      startDate = startDate.startOf('month');
    }

    while (startDate <= endDate) {
      dates.push({
        date: moment(startDate),
        label: this.dateservice.getPeriodLabel(startDate, period),
      });
      if (period == 'day') startDate = startDate.add(1, 'days');
      if (period == 'week') startDate = startDate.add(1, 'week');
      if (period == 'month') startDate = startDate.add(1, 'months');
      if (period == 'quarter') startDate = startDate.add(3, 'months');
      if (period == 'year') startDate = startDate.add(1, 'years');
    }
    return dates;
  }

  getQuarterMonth(month) {
    switch (month) {
      case 1:
      case 2:
        return 0;
      case 4:
      case 5:
        return 3;
      case 7:
      case 8:
        return 6;
      case 10:
      case 11:
        return 9;
      default:
        return month;
    }
  }

  getColor(vValue) {
    if (vValue < -25) {
      return '#E2231A';
    } else if (vValue < -10) {
      return '#EA6033';
    } else if (vValue < 0) {
      return '#F09B4B';
    } else if (vValue < 10) {
      return '#F0C75C';
    } else if (vValue < 20) {
      return '#F0D56B';
    } else if (vValue < 30) {
      return '#B0D56D';
    } else if (vValue < 40) {
      return '#8FCC6D';
    } else if (vValue < 50) {
      return '#7BBF58';
    } else {
      return '#6CB549';
    }

    //	if (vValue < -50) {
    //		return '#c00000'
    //	} else if (vValue <= 0) {
    //		return '#ff0000'
    //	} else if (vValue <= 20) {
    //		return '#f97167'
    //	} else if (vValue <= 30) {
    //		return '#c55a11'
    //	} else if (vValue <= 40) {
    //		return '#f4b183'
    //	} else if (vValue <= 50) {
    //		return '#c2d69a'
    //	} else if (vValue <= 60) {
    //		return '#b2cb7f'
    //	} else if (vValue <= 70) {
    //		return '#9cbc5c'
    //	} else if (vValue <= 80) {
    //		return '#6b8537'
    //	} else if (vValue <= 90) {
    //		return '#4a5c26'
    //	} else if (vValue <= 100) {
    //		return '#323e1a'
    //	}
    //	return '#ffffff'
  }
}
