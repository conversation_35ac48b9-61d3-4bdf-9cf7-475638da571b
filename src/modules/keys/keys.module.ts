import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '../auth/auth.module';
import { ReportLayoutEntity } from '../database/entities/report-layout.entity';
import { CompanyEntity } from '../database/entities/sec-company.entity';
import { SecProgramSettingsEntity } from '../database/entities/sec-programsettings.entity';
import { UserCompanyEntity } from '../database/entities/sec-user-company.entity';
import { UserTokenEntity } from '../database/entities/sec-user-ws-token.entity';
import { KeysController } from './keys.controller';
import { KeysService } from './keys.service';
import { UsersModule } from '../users/users.module';
import { Sec<PERSON>ey } from '../database/entities/sec-key.entity';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SecKey]),
    ModuleLoggerModule.register('keys'),
  ],
  providers: [KeysService],
  controllers: [KeysController],
  exports: [KeysService],
})
export class KeysModule {}
