import { SolutionType } from '@/shared/enums';
import { <PERSON><PERSON><PERSON><PERSON> } from '@entities';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class KeysService {
  constructor(
    private logger: ModuleLogger,
    @InjectRepository(SecKey)
    private readonly keyRepo: Repository<SecKey>,
  ) {}

  async getKeyList() {
    this.logger.debug('getKeyList');
    let items = await this.keyRepo
      .createQueryBuilder('k')
      .leftJoinAndSelect(
        'k.description_translated',
        'dt',
        '(dt.message_language IS NULL OR dt.message_language = :lng)',
        {
          lng: 'en',
        },
      )
      .leftJoinAndSelect(
        'k.tooltip_translated',
        'tt',
        '(tt.message_language IS NULL OR tt.message_language = :lng)',
        {
          lng: 'en',
        },
      )
      .where('k.solution_type =:solution_type', {
        solution_type: SolutionType.Admin,
      })
      .getMany();

    this.logger.debug('getKeyList result', { items });

    return items.map((key) => ({
      ...key,
      description: key.description_translated?.message_value || key.description,
      description_translated: undefined,
      tooltip: key.tooltip_translated?.message_value || key.tooltip,
      tooltip_translated: undefined,
    }));
  }
}
