import { Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { KeysService } from './keys.service';

import { AuthGuard } from '@/shared/guards/auth.guard';

@ApiTags('Keys')
@ApiBearerAuth()
@Controller({
  path: 'keys',
  version: '2',
})
export class KeysController {
  constructor(private keyService: KeysService) {}

  @Get()
  @UseGuards(AuthGuard)
  getKeys() {
    return this.keyService.getKeyList();
  }
}
