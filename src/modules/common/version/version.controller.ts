import { Controller, Get } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { ApiTags } from '@nestjs/swagger';
import { promises as fs } from 'fs';
import { join } from 'path';
import { env } from '../../../app.env';
import { parseJSON } from '@/shared/utils';
import { InjectBrowser } from 'nestjs-puppeteer';
import { Browser } from 'puppeteer';

@ApiTags('version')
@Controller('version')
export class VersionController {
  constructor(
    private readonly config: ConfigService,
    @InjectBrowser() private browser: Browser,
  ) {}

  @Get('/')
  async getVersion() {
    let data = await fs
      .readFile(join(env.APP_PATH, 'dist/app-version.json'))
      .catch(() => false);
    let json = parseJSON(data?.toString()) || {};
    const version = `${json.apiVersion || '1.0.0'}-${json.hash || 'hash'}`;
    return {
      version,
    };
  }
  @Get('/browser')
  async browserVersion() {
    return { version: await this.browser.version() };
  }
}
