import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FeedbackPropertiesService } from './feedback-properties.service';
import { FeedbackPropertiesEntity } from '../../database/entities/feedback-properties.entity';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([FeedbackPropertiesEntity]),
    ModuleLoggerModule.register('feedback-properties'),
  ],
  providers: [FeedbackPropertiesService],
  exports: [FeedbackPropertiesService],
})
export class FeedbackPropertiesModule {}
