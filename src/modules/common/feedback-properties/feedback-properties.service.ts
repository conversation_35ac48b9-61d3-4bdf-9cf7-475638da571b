import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FeedbackPropertiesEntity } from '../../database/entities/feedback-properties.entity';
import { Repository } from 'typeorm';
import { FEEDBACK_PROPERTY_KEYS } from '@/config/contents/feedback-property.contents';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';

@Injectable()
export class FeedbackPropertiesService {
  constructor(
    private logger: ModuleLogger,
    @InjectRepository(FeedbackPropertiesEntity)
    private fpRepo: Repository<FeedbackPropertiesEntity>,
  ) {}

  async getItem(feedback_property_id: FEEDBACK_PROPERTY_KEYS | string) {
    let d = await this.fpRepo.findOne({ where: { feedback_property_id } });
    this.logger.debug(`getItem ${feedback_property_id} result`, { result: d });
    return d?.property_value;
  }
}
