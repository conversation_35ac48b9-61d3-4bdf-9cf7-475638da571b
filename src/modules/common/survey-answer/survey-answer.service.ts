import { ScoreType } from '@/shared/enums';
import { QuestionType } from '@/shared/enums/question-type.enum';
import { SurveyAnswerEntity } from '@entities';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';

@Injectable()
export class SruveyAnswerService {
  constructor(
    @InjectRepository(SurveyAnswerEntity)
    private saRepo: Repository<SurveyAnswerEntity>,
  ) {}

  cesCsatQuery(type: QuestionType) {
    return `
        select a.surv_surveyanswer_id, ans.question_id, ans.value::integer, ans.type
        from surv_surveyanswer a
        cross join jsonb_to_recordset(answers) ans(value text, question_id integer, type integer)
        where ans.type=${type}
    `;
  }

  feedbackFieldsQueryBuilder<T>(
    qb: SelectQueryBuilder<T>,
    feedbackField: number,
  ) {
    let where;
    if (feedbackField == 1) where = ` neg.return_value IS NOT NULL`;
    if (feedbackField == 2) where = `pos.return_value IS NOT NULL`;
    if (feedbackField == 3)
      where = `(neg.return_value IS NOT NULL OR pos.return_value IS NOT NULL)`;

    if (where) qb.andWhere(where);
  }

  getScoreType(storyWidget): ScoreType {
    let scoreType: ScoreType = ScoreType.NPS;
    if (storyWidget.useCESasScore) scoreType = ScoreType.CES;
    else if (storyWidget.useCSATasScore) scoreType = ScoreType.CSAT;

    return scoreType;
  }

  isCesCSAT(scoreType: ScoreType) {
    return scoreType === ScoreType.CES || scoreType === ScoreType.CSAT;
  }

  respondentName(item: any, rn: number) {
    if (rn == 1) {
      return item.xml_initials;
    } else if (rn == 2) {
      return item.xml_customername;
    } else if (rn == 3) {
      if (item.xml_initials?.length || item.xml_customername?.length) {
        return `${item.xml_initials || ''} ${
          item.xml_customername || ''
        }`.trim();
      } else {
        return item.xml_initials || item.xml_customername || null;
      }
    } else if (rn == 4) {
      return item.project_name;
    }
  }

  filterFeedbackFields(item: any, feedbackField: number) {
    switch (feedbackField) {
      case 1:
        item.strengths = undefined;
        break;
      case 2:
        item.improvements = undefined;
        break;
      case 3: // both needs to show
        break;
      default:
        item.improvements = undefined;
    }
    return item;
  }
}
