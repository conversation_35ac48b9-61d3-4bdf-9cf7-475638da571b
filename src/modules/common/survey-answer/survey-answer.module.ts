import { Module } from '@nestjs/common';
import { SruveyAnswerService } from './survey-answer.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyAnswerEntity } from '@entities';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SurveyAnswerEntity]),
    ModuleLoggerModule.register('survey-answer'),
  ],
  providers: [SruveyAnswerService],
  exports: [SruveyAnswerService],
})
export class SurveyAnswerModule {}
