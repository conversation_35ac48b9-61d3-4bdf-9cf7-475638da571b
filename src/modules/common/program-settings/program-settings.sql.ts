export const ACTIVE_AI_MODULES_SQL = `
SELECT 
(company_properties::jsonb)->>'ai_modules' AS ai_modules
FROM sec_programsettings 
WHERE sec_company_id = $1
ORDER BY sec_programsettings.sec_programsetting_id DESC LIMIT 1
`;

export const PASSWORD_SETTINGS_SQL = `
SELECT 
sec_programsettings.pwd_score AS pwd_score,  
sec_programsettings.pwd_settings AS pwd_settings 
FROM sec_users 
INNER JOIN sec_user_company ON sec_users.sec_user_id = sec_user_company.sec_user_id 
INNER JOIN sec_programsettings ON sec_user_company.sec_company_id = sec_programsettings.sec_company_id 
WHERE (sec_users.sec_user_id = $1) AND sec_programsettings.pwd_score IS NOT NULL
ORDER BY sec_programsettings.pwd_score DESC LIMIT 1
`;
