import { ClsProperty } from '@/config/contents';
import { Mo<PERSON>leLogger } from '@lib/logger/module-logger/module-logger.service';
import { parseJSON } from '@/shared/utils';
import { SecProgramSettingsEntity } from '@entities';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { Repository } from 'typeorm';
import {
  ACTIVE_AI_MODULES_SQL,
  PASSWORD_SETTINGS_SQL,
} from './program-settings.sql';

@Injectable()
export class ProgramSettingsService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(SecProgramSettingsEntity)
    private readonly programSettingsRepo: Repository<SecProgramSettingsEntity>,
  ) {}

  async get(sec_company_id?: number) {
    if (!sec_company_id) {
      sec_company_id = this.cls.get(ClsProperty.user)?.company?.id;
    }
    let companySettings = this.cls.get(ClsProperty.companySettings);
    if (companySettings) return companySettings;

    this.logger.debug(`get for company ${sec_company_id}`);
    let result = await this.programSettingsRepo
      .createQueryBuilder()
      .where('sec_company_id =:sec_company_id', { sec_company_id })
      .orderBy('sec_programsetting_id', 'ASC')
      .getOne();
    this.logger.debug(`get result`, { result });

    this.cls.set(ClsProperty.companySettings, result);
    return result;
  }

  async AIModules(companyId: number) {
    if (!companyId) return [];
    try {
      let list = await this.programSettingsRepo.query(ACTIVE_AI_MODULES_SQL, [
        companyId,
      ]);
      return parseJSON(list[0].ai_modules) || [];
    } catch {
      return [];
    }
  }

  async getPasswordSettings(sec_user_id) {
    let list = await this.programSettingsRepo.query(PASSWORD_SETTINGS_SQL, [
      sec_user_id,
    ]);
    let item = list[0];
    let resp = {
      pwd_score: item?.pwd_score,
      pwd_settings: parseJSON(item?.pwd_settings),
    };
    if (!resp.pwd_settings)
      resp.pwd_settings = {
        mustContain: {
          chars: 8,
          uppercase: true,
          lowercase: true,
          punctuation: false,
          number: true,
        },
      };

    return resp;
  }
}
