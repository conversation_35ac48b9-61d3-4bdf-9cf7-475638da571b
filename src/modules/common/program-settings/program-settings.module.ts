import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecProgramSettingsEntity } from '../../database/entities/sec-programsettings.entity';
import { ProgramSettingsService } from './program-settings.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SecProgramSettingsEntity]),
    ModuleLoggerModule.register('program-settings'),
  ],
  providers: [ProgramSettingsService],
  exports: [ProgramSettingsService],
})
export class ProgramSettingsModule {}
