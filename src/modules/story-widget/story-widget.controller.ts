import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { AuthGuard } from '@/shared/guards/auth.guard';
import { AuthorizedRequest } from '../auth/interfaces/authorized-request.interface';
import { GetStoryWidgetDto } from './dto/get-story-widget.dto';

import { StoryWidgetService } from './story-widget.service';

@ApiTags('Story Widgets')
@ApiBearerAuth()
@Controller({
  path: 'story_widget',
  version: '2',
})
export class StoryWidgetController {
  constructor(private storyWidgetService: StoryWidgetService) {}

  @Get()
  @UseGuards(AuthGuard)
  @ApiOkResponse({
    status: HttpStatus.OK,
  })
  async getStoryWidget(@Query() query: GetStoryWidgetDto) {
    let { isEnabled, prop_story_widget } =
      await this.storyWidgetService.getPropStoryWidget();
    if (!isEnabled) {
      throw new HttpException('Widget is not active', HttpStatus.BAD_REQUEST);
    }
    return this.storyWidgetService.getStoryWidget({
      ...query,
      prop_story_widget,
    });
  }
}
