import { InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { Repository } from 'typeorm';

import { AuthorizedData } from '../auth/interfaces/authorized-request.interface';

import { PublicationsEntity, SecProgramSettingsEntity } from '@entities';
import { ClsProperty } from '@/config/contents';
import { CompanyType } from '@/shared/enums';
import { ModuleLogger } from '@lib/logger/module-logger/module-logger.service';
export class StoryWidgetService {
  constructor(
    private logger: ModuleLogger,
    private cls: ClsService,
    @InjectRepository(SecProgramSettingsEntity)
    private readonly storyWidgetRepository: Repository<SecProgramSettingsEntity>,
    @InjectRepository(PublicationsEntity)
    private readonly publicationsEntity: Repository<PublicationsEntity>,
  ) {}

  async getPropStoryWidget(sec_company_id?: number) {
    if (!sec_company_id) {
      sec_company_id = this.cls.get<AuthorizedData>(ClsProperty.user).company
        .id;
    }
    this.logger.debug(`getPropStoryWidget for company ${sec_company_id}`);
    let result = await this.storyWidgetRepository
      .query(`SELECT company_properties::json->'storyWidget' AS prop_story_widget
    FROM sec_programsettings
    WHERE sec_company_id = ${sec_company_id}`);
    const prop_story_widget = result[0]?.prop_story_widget;

    this.logger.debug(
      `getPropStoryWidget result for company ${sec_company_id}`,
      { result },
    );

    return {
      isEnabled: prop_story_widget?.flag_story_widget == 1,
      prop_story_widget,
    };
  }

  async getStoryWidget(params: any) {
    const {
      brand,
      project_nr,
      survey_category,
      count,
      page,
      prop_story_widget,
    } = params;

    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`getStoryWidget for company ${company.id}`, { params });

    let sqlQuery = `
    SELECT publication_id, score, survey_name, response_date, strengths, respondent_customername, respondent_initials
    FROM publications p
    ${
      survey_category > 0
        ? `
    INNER JOIN surv_survey s on p.surv_survey_id = s.surv_survey_id
    INNER JOIN surv_survey_categories ssc  ON ssc.surv_survey_id = s.surv_survey_id
    `
        : ''
    }
    WHERE p.sec_company_id = ${company.id}
    ${survey_category > 0 ? ` AND ssc.category_id = ${survey_category}` : ''}
    ${brand?.length > 0 ? ` AND p.brand = '${brand}'` : ''}
    ${project_nr > 0 ? ` AND p.project_nr = ${project_nr}` : ''}
    ORDER BY response_date desc
    LIMIT ${count}
    ${page !== undefined ? `OFFSET ${page * count}` : ''}
    `;
    this.logger.debug(`getStoryWidget query for company ${company.id}`, {
      sql: sqlQuery,
    });

    let results = await this.publicationsEntity.query(sqlQuery);
    let publicationReport = await this.getPublicationReport(params);
    let feedbackList = results.map((item) => {
      switch (prop_story_widget.respondent_name) {
        case 0:
          item.title = '';
          break;
        case 1:
          item.title = item.respondent_initials;
          break;
        case 2:
          item.title = item.respondent_customername;
          break;
        case 3:
          item.title =
            item.respondent_initials + ' ' + item.respondent_customername;
          break;
        default:
          item.title =
            company.type == CompanyType.SPORT
              ? item.respondent_initials
              : item.respondent_initials + ' ' + item.respondent_customername;
          break;
      }
      return {
        title: item.title,
        survey_name: item.survey_name,
        pos: item.strengths,
        score: item.score,
        postedOn: item.postedOn,
      };
    });

    let result = {
      widget_background: prop_story_widget.bg_color,
      font_title: prop_story_widget.font_title,
      font_subtitle: prop_story_widget.font_subtitle,
      font_content: prop_story_widget.font_content,
      score_text_color: prop_story_widget.score_text_color,
      score_background_color: prop_story_widget.score_background_color,
      arrow_color: prop_story_widget.carousel_color,
      dot_active_color: prop_story_widget.carousel_color,
      dot_inactive_color: prop_story_widget.carousel_color_inactive,
      font_family: prop_story_widget.font_style,
      more_records: true,
      company_name: prop_story_widget.company_name,
      company_description: prop_story_widget.company_description,
      url_website_title: prop_story_widget.url_website_title,
      url_website: prop_story_widget.url_website,
      url_logo: prop_story_widget.url_logo,
      score: publicationReport?.score,
      based_on: publicationReport?.based_on,
      score_amount_pos: publicationReport?.based_on_pos,
      score_amount_pas: publicationReport?.based_on_pas,
      score_amount_neg: publicationReport?.based_on_neg,
      feedback: feedbackList,
    };

    this.logger.debug(`getStoryWidget result for company ${company.id}`, {
      result,
    });

    return result;
  }

  async getPublicationReport(params: any) {
    const { project_nr, survey_category, brand } = params;

    const { company } = this.cls.get<AuthorizedData>(ClsProperty.user);

    this.logger.debug(`getPublicationReport`, { params, company });

    let result = await this.publicationsEntity.query(`
    SELECT avg_score, based_on, based_on_pos, based_on_pas, based_on_neg
    FROM publication_reports
    WHERE sec_company_id = ${company.id}
    ${project_nr > 0 ? `AND project_nr = ${+project_nr}` : ''}
    ${survey_category > 0 ? `AND survey_category_id = ${+survey_category}` : ''}
    ${
      brand?.length > 0
        ? `AND brand = '${brand.replace(/[^a-zA-Z0-9]/gi, '')}'`
        : ''
    }
    `);

    this.logger.debug(`getPublicationReport result`, { result: result[0] });

    return result[0];
  }
}
