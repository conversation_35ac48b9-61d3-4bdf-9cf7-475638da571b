import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PublicationsEntity } from '../database/entities/publications.entity';
import { SecProgramSettingsEntity } from '../database/entities/sec-programsettings.entity';
import { StoryWidgetController } from './story-widget.controller';
import { StoryWidgetService } from './story-widget.service';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';

@Module({
  imports: [
    ModuleLoggerModule.register('story-widget'),
    TypeOrmModule.forFeature([SecProgramSettingsEntity, PublicationsEntity]),
  ],
  controllers: [StoryWidgetController],
  providers: [StoryWidgetService],
})
export class StoryWidgetModule {}
