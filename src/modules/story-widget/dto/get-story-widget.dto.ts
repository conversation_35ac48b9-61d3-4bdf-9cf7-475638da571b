import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsN<PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';

export class GetStoryWidgetDto {
  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({ required: false })
  survey_category: number;

  @Type()
  @IsNumber()
  @IsOptional()
  @ApiProperty({ required: false })
  project_no: number;

  @Type()
  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  brand: string;

  @Type(() => Number)
  @ApiProperty({ required: false, default: 11, type: Number })
  @Min(1)
  @Max(200)
  @IsNumber()
  count: number = 11;

  @Type(() => Number)
  @ApiProperty({ required: false, default: 0, type: Number })
  @Min(0)
  @IsNumber()
  page: number = 0;
}
