import { I18nContext, I18nService } from 'nestjs-i18n';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nEntity } from '@entities';
import { In, IsNull, Or, Repository } from 'typeorm';

@Injectable()
export class TranslateService {
  constructor(
    private i18n: I18nService,
    @InjectRepository(I18nEntity)
    private readonly i18nRepo: Repository<I18nEntity>,
  ) {}

  public t(key: string): string {
    return this.i18n.t(key, {
      lang: this.lang,
    });
  }

  public async db(key: string): Promise<{ key: string; value: string }> {
    let list = await this.i18nRepo.find({
      where: [
        {
          message_key: key,
          message_language: this.lang,
        },
        {
          message_key: key,
          message_language: IsNull(),
        },
      ],
      select: ['message_value', 'message_key', 'message_language'],
    });

    let item = list.find((i) => i.message_language?.length) || list[0];
    return {
      key,
      value: item?.message_value || key,
    };
  }

  public async dbs(keys: string[]): Promise<{ key: string; value: string }[]> {
    let list = await this.i18nRepo.find({
      where: [
        {
          message_key: In(keys),
          message_language: this.lang,
        },
        {
          message_key: In(keys),
          message_language: IsNull(),
        },
      ],
      select: ['message_value', 'message_key', 'message_language'],
    });

    list = list.map((item) => ({
      ...item,
      message_language: !!item.message_language?.length,
    })) as any;

    list = list.filter((item) => {
      return (
        item.message_language ||
        (!item.message_language &&
          !list.some(
            (subitem) =>
              subitem.message_key === item.message_key &&
              subitem.message_language,
          ))
      );
    });

    return list.map((item) => ({
      key: item.message_key,
      value: item.message_value,
    }));
  }

  public get lang() {
    return I18nContext?.current()?.lang || 'en';
  }
}
