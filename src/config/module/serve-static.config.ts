import { ServeStaticModuleAsyncOptions } from '@nestjs/serve-static';
import { existsSync } from 'fs';
import { join } from 'path';
import { env } from '../../app.env';

export default {
  useFactory() {
    const clientPath = join(env.APP_PATH, 'client');
    let paths: any = [
      {
        rootPath: join(env.APP_PATH, 'uploads'),
        serveRoot: '/uploads',
      },
      { 
        rootPath: join(env.APP_PATH, 'public'),
        serveRoot: '/',
      },
    ];

    if (existsSync(clientPath)) paths = [...paths, { rootPath: clientPath }];

    return paths;
  },
} as ServeStaticModuleAsyncOptions;
