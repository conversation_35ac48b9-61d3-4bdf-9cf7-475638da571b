import {
  AcceptLanguageResolver,
  I18nOptions,
  QueryResolver,
} from 'nestjs-i18n';
import { join } from 'path';
import { env } from '../../app.env';

export default {
  fallbackLanguage: 'en',
  loaderOptions: {
    path: join(env.APP_PATH, 'dist/i18n'),
    watch: true,
  },
  resolvers: [
    {
      use: QueryResolver,
      options: ['lang'],
    },
    AcceptLanguageResolver,
  ],
} as I18nOptions;
