import { join } from 'path';
import { env } from '../../app.env';
import * as entities from '../../modules/database/entities';

import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { QueryLogger } from '@lib/logger/handlers/query.console-logger';

export default {
  type: 'postgres',
  host: env.POSTGRES_HOST || 'localhost',
  port: parseInt(`${env.POSTGRES_POST}`, 10) || 5432,
  username: env.POSTGRES_USER || 'postgres',
  password: env.POSTGRES_PASSWORD || 'qwerty1',
  database: env.POSTGRES_DATABASE || 'FocusGroup',
  entities,
  migrations: [join(env.APP_PATH, 'dist/migrations', '*{.ts,.js}')],
  migrationsTableName: 'typeorm_migrations',
  migrationsRun: true,
  logger: new QueryLogger(['error', 'query']),
} as TypeOrmModuleOptions;
