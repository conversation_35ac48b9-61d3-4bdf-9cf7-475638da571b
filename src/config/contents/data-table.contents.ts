import { BSHMapping, EmployeeType } from '@/shared/enums/datatable.enum';

export const employeeFields = Object.keys(EmployeeType).reduce(
  (acc: string[], key) => {
    const value = EmployeeType[key as keyof typeof EmployeeType];
    if (value.startsWith('xml_')) {
      acc.push(value);
    }
    return acc;
  },
  [],
);

export const bshMappingFields = Object.keys(BSHMapping).reduce(
  (acc: string[], key) => {
    const value = BSHMapping[key as keyof typeof BSHMapping];
    if (value.startsWith('xml_')) {
      acc.push(value);
    }
    return acc;
  },
  [],
);
