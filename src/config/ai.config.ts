import { env } from '@/app.env';

export const AI_THRESHOLD_DEFAULT = 0.7;

export const OPENAI_SLEEP_TIME = 60; // seconds
export const OPENAI_MAX_RETRIES_PER_ROW = 5;
export const OPENAI_RETRY_DELAY_MULTIPLIER = 5; // Logic  OPENAI_SLEEP_TIME * OPENAI_RETRY_DELAY_MULTIPLIER * CURRENT_TRY_NUMBER
export const OPENAI_DEFAULT_MODEL = env.OPENAI_NEW_MODEL;

// Classification config
export const OPENAI_CLASSIFICATION_CONCURRENT_JOB = 5;
export const OPENAI_CLASSIFICATION_MIN_LETTER =
  env.OPENAI_CLASSIFICATION_MIN_LETTER || 1; // positive and negative min character limit
