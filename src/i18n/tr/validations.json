{"isObject": "{property} bir nesne <PERSON>", "isNotEmpty": "{property} bo<PERSON>ı<PERSON>", "isString": "{property} bir metin o<PERSON>", "isEmail": "{property} bir e-posta adresi olmalıdır", "minLength": "{property} en az {minLength} karakter olmalıdır", "maxLength": "{property} en fazla {maxLength} karakter olmalıdır", "matches": "{property} <PERSON>u ile eşleşmelidir: {matches}", "isNumber": "{property} bir sayı olmalıdır", "isInt": "{property} bir tam sayı olmalıdır", "isPositive": "{property} bir pozitif sayı olmalıdır", "isNegative": "{property} bir negatif <PERSON>ır", "isArray": "{property} bir dizi olmalı<PERSON><PERSON>r", "arrayNotEmpty": "{property} bo<PERSON>ı<PERSON>", "arrayMinSize": "{property} en az {arrayMinSize} öğe içermelidir", "arrayMaxSize": "{property} en fazla {arrayMaxSize} öğe içermelidir", "arrayUnique": "{property} <PERSON><PERSON><PERSON>", "isBoolean": "{property} bir <PERSON><PERSON><PERSON><PERSON><PERSON> olmalıdır", "isDate": "{property} bir ta<PERSON>h <PERSON>", "isDateString": "{property} geçerli bir ISO 8601 tarih dizesi olmalıdır", "isUUID": "{property} bir UUID olmalıdır", "isUrl": "{property} bir URL olmalıdır", "isEnum": "{property} geç<PERSON>li bir enum değeri olmalıdır", "isPhoneNumber": "{property} geçerli bir telefon numarası olmalıdır"}