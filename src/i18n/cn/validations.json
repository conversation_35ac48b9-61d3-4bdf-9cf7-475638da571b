{"isObject": "{property} 必须是一个对象", "isNotEmpty": "{property} 不能为空", "isString": "{property} 必须是一个字符串", "isEmail": "{property} 必须是一个电子邮件地址", "minLength": "{property} 至少应有 {min} 个字符", "maxLength": "{property} 最多可有 {max} 个字符", "min": "{property} 必须大于或等于 {min}", "max": "{property} 必须小于或等于 {max}", "length": "{property} 必须在 {min} 到 {max} 个字符之间", "matches": "{property} 必须匹配: {matches}", "isNumber": "{property} 必须是一个数字", "isInt": "{property} 必须是一个整数", "isPositive": "{property} 必须是一个正数", "isNegative": "{property} 必须是一个负数", "isArray": "{property} 必须是一个数组", "arrayNotEmpty": "{property} 不能为空", "arrayMinSize": "{property} 至少应包含 {min} 个元素", "arrayMaxSize": "{property} 最多可包含 {max} 个元素", "arrayUnique": "{property} 必须是唯一的", "isBoolean": "{property} 必须是一个布尔值", "isDate": "{property} 必须是一个日期", "isDateString": "{property} 必须是一个有效的 ISO 8601 日期字符串", "isUUID": "{property} 必须是一个 UUID", "isUrl": "{property} 必须是一个 URL", "isEnum": "{property} 必须是 {values} 之一", "isPhoneNumber": "{property} 必须是一个有效的电话号码"}