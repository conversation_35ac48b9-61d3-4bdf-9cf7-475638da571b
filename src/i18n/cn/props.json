{"welcome_feedback": "欢迎使用Focus Feedback", "errors": {"something_went_wrong": "出错了", "not_found": "资源未找到", "bad_request": "无效的请求", "internal_server_error": "内部服务器错误", "unauthorized": "需要身份验证", "forbidden": "您无权访问此资源", "user_not_found": "用户未找到", "fill_required_fields": "请填写所有必填字段", "invalid_search_fields": "搜索字段必须是 {fields}", "token_expired": "令牌已过期"}, "success": {"data_created": "数据已创建", "data_deleted": "数据已删除", "data_updated": "数据已更新", "data_saved": "数据已保存", "added": "已添加", "updated": "已更新", "deleted": "已删除"}, "sort": {"visit_date": "访问日期", "send_date": "发送日期", "response_date": "回复日期", "modification_date": "修改日期", "close_date": "关闭日期"}, "auth": {"password_changed": "密码已更改", "mail_sent": "密码重置邮件已发送", "errors": {"auth_failed": "用户认证失败", "user_blocked": "出于安全原因，您的帐户已暂时锁定", "expired": "<html>您的密码已过期！<br />请输入新密码。</html>", "activation_not_found": "未找到具有此激活密钥的用户", "activation_expired": "激活密钥已过期", "contains_username": "密码不能包含用户名", "password_cycles": "您最近使用过此密码。请选择其他密码", "password_weak": "密码不符合要求", "current_password_wrong": "当前密码错误", "multiple_users": "此电子邮件已用于多个帐户", "mail_error": "我们现在发送邮件时遇到问题，请联系管理员"}}, "techgrp": {"technici": "工程师", "callcenteragent": "呼叫中心座席", "preprepper": "预处理员", "dispatch": "调度", "counselor": "顾问", "service_partner": "服务合作伙伴", "dealer": "经销商"}, "survey": {"errors": {"not_found": "问卷未找到", "group_not_found": "问卷组未找到", "brand_theme_not_found": "品牌主题未找到", "theme_not_found": "主题未找到", "brand_not_found": "品牌未找到", "survey_brand_theme_exists": "此问卷品牌主题已存在", "brand_theme_company_different": "您无法更新此品牌主题，因为公司不同", "logic_not_found": "问卷逻辑未找到", "question_not_found": "问题未找到", "question_template_not_found": "问题模板未找到", "template_not_found": "模板未找到", "survey_with_key_not_found": "未找到带有此密钥的问卷", "survey_with_key_not_published": "带有此密钥的问卷未发布", "company_with_key_not_found": "未找到具有此密钥的公司", "survey_already_done": "客户ID: {customerId} 的问卷已完成", "key_already_exists": "问卷密钥已存在", "surveys_not_updated_due_local_changes": "由于本地更改，问卷未更新: {surveys}", "template_linked": "此问题模板已链接到其他问卷，您不能删除它", "email_or_phone_required": "需要电子邮件或电话", "theme_linked": "此主题已链接到其他问卷，您不能删除它", "invalid_message_type": "无效的消息类型", "cannot_publish_without_questions": "您不能在没有问题的情况下发布问卷"}, "deleted": "问卷已删除", "project_deleted": "项目已删除", "delivery_method_updated": "交付配置已更新", "logic_updated": "问卷逻辑已成功更新", "unsubscribe_success": "您已成功取消订阅此问卷", "published": "问卷已成功发布", "publish_config_updated": "发布配置已成功更新", "key_saved": "问卷密钥已成功保存", "question_cloned": "问题已成功克隆", "blank_question_created": "空白问题已成功保存", "blank_answers_created": "空白答案已成功保存", "blank_question_template_created": "空白问题模板已成功保存", "blank_question_template_answers_created": "空白问题模板答案已成功保存", "question_template_published": "模板已成功发布，并更新了答案行", "question_template_created": "问题模板已成功创建", "question_template_updated": "问题模板已成功更新", "question_template_deleted": "问题模板已成功删除", "question_template_cloned": "问题模板已成功复制", "recipient_updated": "收件人已成功更新", "recipient_deleted": "收件人已成功删除", "theme_updated": "主题已成功更新", "theme_deleted": "主题已成功删除"}, "group": {"errors": {"not_found": "组未找到", "forbidden": "您无权访问此组", "missing_type_filter": "缺少类型筛选器"}}, "user": {"errors": {"not_found": "用户未找到", "use_parent_company": "请为此公司使用“母公司”组", "email_exists": "提供的电子邮件地址已存在于我们的系统中。请使用其他电子邮件地址。", "region_required": "您必须至少指定一个区域", "create_failed": "用户创建失败"}}, "answertable": {"column_deleted": "列已删除", "table_deleted": "表已删除", "column_saved": "列已保存", "table_saved": "表已保存", "created": "新记录已创建", "errors": {"not_found": "答案表未找到", "table_not_found": "表未找到", "config_not_found": "表配置未找到", "table_is_used": "表已在问卷创建器中使用，无法删除", "unique_id_exists": "唯一ID已存在", "unique_id_required": "需要唯一ID"}}, "chart": {"errors": {"no_params": "未指定参数", "one_param": "请指定一个参数", "missing_group_by_field": "您需要传递 group_by_field", "user_report_not_found": "用户报告未找到"}}, "brand": {"added": "品牌 {name} 已添加", "updated": "品牌 {name} 已更新", "some_brands_not_found": "未找到某些品牌", "brand_not_found": "品牌未找到", "brand_has_touchpoints": "品牌已在问卷中使用", "brands_deleted": "品牌已删除"}, "feedback": {"email_customer_not_found": "未找到此项目的电子邮件客户数据"}, "gdpr": {"search_min_length": "搜索词必须至少为 {count} 个字符长"}, "company": {"errors": {"not_found": "公司未找到"}}, "connector": {"deleted": "连接器已删除", "not_found": "连接器不存在", "settings_added": "连接器设置已添加", "settings_updated": "连接器设置已更新", "delete_error": "无法删除连接器", "connector_object_added": "连接器对象已添加"}, "datatable": {"table_not_found": "表未找到"}, "image_library": {"folder_exists": "具有此名称的文件夹已存在", "image_uploaded": "图片已上传", "image_deleted": "图片已删除"}, "import_template": {"metatable_not_found": "元表未找到", "metafield_not_found": "元字段未找到", "template_updated": "模板已更新", "template_created": "模板已创建", "data_imported": "数据已导入"}, "user_reports": {"report_updated": "报告已成功保存", "report_cloned": "报告已成功克隆"}, "quadrant": {"invalid_dates": "日期不正确"}, "tags": {"tag_deleted": "标签已删除", "tag_in_use": "此标签正在 CLF 中使用，因此无法删除"}, "external": {"errors": {"invalid_api_key_or_secret": "(cn) Invalid API key or secret"}}}