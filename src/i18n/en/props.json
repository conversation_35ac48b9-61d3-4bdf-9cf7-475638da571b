{"welcome_feedback": "Welcome to Focus Feedback", "errors": {"something_went_wrong": "Something went wrong", "not_found": "Resource not found", "bad_request": "Invalid request", "internal_server_error": "Internal server error", "unauthorized": "Authentication required", "forbidden": "You don't have permission to access this resource", "user_not_found": "User not found", "fill_required_fields": "Please fill all required fields", "invalid_search_fields": "Search fields must be {fields}", "token_expired": "Token expired"}, "success": {"data_created": "Data created", "data_deleted": "Data deleted", "data_updated": "Data updated", "data_saved": "Data saved", "added": "Added", "updated": "Updated", "deleted": "Deleted"}, "sort": {"visit_date": "Visit Date", "send_date": "Send Date", "response_date": "Response Date", "modification_date": "Modification Date", "close_date": "Close Date"}, "auth": {"password_changed": "Password has been changed", "mail_sent": "Password reset mail sent", "errors": {"auth_failed": "User authentication failed", "user_blocked": "For security reasons your account is temporarily locked", "expired": "<html>Your password has expired! <br />Please enter a new password.</html>", "activation_not_found": "User with this activation key is not found", "activation_expired": "Activation key has expired", "contains_username": "Password cannot contain username", "password_cycles": "This password has recently been used by you. Please choose another password", "password_weak": "Password does not meet the requirements", "current_password_wrong": "Current password is wrong", "multiple_users": "This email is used on multiple accounts", "mail_error": "We have issue on sending mails right now, please contact managers"}}, "feedback": {"email_customer_not_found": "Email Customer data is not found for this item"}, "techgrp": {"technici": "Engineer", "callcenteragent": "Contact Center agent", "preprepper": "<PERSON>p<PERSON><PERSON>", "dispatch": "Dispatch", "counselor": "Counselor", "service_partner": "Service partner", "dealer": "Dealer"}, "gdpr": {"search_min_length": "Search term must be at least {count} characters long"}, "survey": {"deleted": "Survey(s) deleted", "project_deleted": "Project(s) deleted", "delivery_method_updated": "Delivery config updated", "logic_updated": "Survey logic updated successfully", "unsubscribe_success": "You have been successfully unsubscribed from this survey", "published": "Survey is successfully published", "publish_config_updated": "Publish Config updated successfully", "key_saved": "Survey key saved successfully", "question_cloned": "Question has been successfully cloned", "blank_question_created": "Blank Question has been successfully saved", "blank_answers_created": "Blank Answers have been successfully saved", "blank_question_template_created": "Blank Question Template has been successfully saved", "blank_question_template_answers_created": "Blank Question Template Answers have been successfully saved", "question_template_published": "Template published successfully with updated answer rows", "question_template_created": "Question Template created successfully", "question_template_updated": "Question Template updated successfully", "question_template_deleted": "Question Template deleted successfully", "question_template_cloned": "Question Template copied successfully", "recipient_updated": "Recipient updated successfully", "recipient_deleted": "Recipient deleted successfully", "theme_updated": "Theme updated successfully", "theme_deleted": "Theme deleted successfully", "errors": {"not_found": "Survey not found", "group_not_found": "Survey group is not found", "brand_theme_not_found": "Brand theme not found", "logic_not_found": "Survey logic not found", "theme_not_found": "Theme is not found", "brand_not_found": "Brand is not found", "question_not_found": "Question not found", "question_template_not_found": "Question template not found", "template_not_found": "Temp<PERSON> not found", "survey_brand_theme_exists": "This survey brand theme is already exists", "brand_theme_company_different": "You cannot update this brand theme because company is different", "survey_with_key_not_found": "Survey with this key not found", "survey_with_key_not_published": "Survey with this key is not published", "company_with_key_not_found": "Company with this key is not found", "survey_already_done": "Survey already completed for customer ID: {customerId}", "key_already_exists": "Survey key already exists", "surveys_not_updated_due_local_changes": "Surveys not updated because they have local changes: {surveys}", "template_linked": "This question template is linked to other surveys, you cannot delete it", "email_or_phone_required": "Email or phone is required", "theme_linked": "This theme is linked to other surveys, you cannot delete it", "invalid_message_type": "Invalid message type", "cannot_publish_without_questions": "You cannot publish survey without questions"}}, "group": {"errors": {"not_found": "Group not found", "forbidden": "You don't have permission to access this group", "missing_type_filter": "Type filter is missing"}}, "user": {"errors": {"not_found": "User not found", "use_parent_company": "Please use \"Parent Company\" group for this company", "email_exists": "The provided email address already exists in our system. Please use a different email address.", "region_required": "You must need to specify at least one region", "create_failed": "User creation failed"}}, "user_reports": {"report_updated": "Report saved successfully", "report_cloned": "Report cloned successfully"}, "company": {"errors": {"not_found": "Company not found"}}, "connector": {"deleted": "Connector deleted", "not_found": "Connector don't exists", "settings_added": "Connector settings added", "settings_updated": "Connector settings updated", "delete_error": "Unable to delete connector", "connector_object_added": "Connector object added"}, "answertable": {"column_deleted": "Column deleted", "table_deleted": "Table deleted", "column_saved": "<PERSON><PERSON><PERSON> Saved", "table_saved": "Table Saved", "created": "New record created", "errors": {"not_found": "Answer table not found", "table_not_found": "Table not found", "config_not_found": "Table config not found", "table_is_used": "Table used in survey creator, cannot be removed", "unique_id_exists": "Unique ID already exists", "unique_id_required": "Unique ID is required"}}, "chart": {"errors": {"no_params": "Params not specified", "one_param": "Please specify one param", "missing_group_by_field": "you need to pass group_by_field", "user_report_not_found": "User report not found"}}, "external": {"errors": {"invalid_api_key_or_secret": "Invalid API key or secret"}}, "brand": {"added": "Brand {name} added", "updated": "Brand {name} updated", "some_brands_not_found": "Some brands not found", "brand_not_found": "Brand not found", "brand_has_touchpoints": "Brand is in use in surveys", "brands_deleted": "Brand(s) deleted"}, "datatable": {"table_not_found": "Table not found"}, "image_library": {"folder_exists": "Folder with this name is exists", "image_uploaded": "Image uploaded", "image_deleted": "Image deleted"}, "import_template": {"metatable_not_found": "Metatable not found", "metafield_not_found": "Metafield not found", "template_updated": "Template updated", "template_created": "Template created", "data_imported": "Data imported"}, "quadrant": {"invalid_dates": "Dates are not correct"}, "tags": {"tag_deleted": "Tag deleted", "tag_in_use": "This tag is in use in CLF and can therefore not be deleted"}}