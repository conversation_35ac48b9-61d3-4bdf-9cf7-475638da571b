{"isObject": "{property} must be an object", "isNotEmpty": "{property} should not be empty", "isString": "{property} must be a string", "isEmail": "{property} must be an email", "minLength": "{property} must be at least {minLength} characters", "maxLength": "{property} must be less than or equal to {maxLength} characters", "matches": "{property} must match the following: {matches}", "isNumber": "{property} must be a number", "isInt": "{property} must be an integer", "isPositive": "{property} must be a positive number", "isNegative": "{property} must be a negative number", "isArray": "{property} must be an array", "arrayNotEmpty": "{property} must not be empty", "arrayMinSize": "{property} must contain at least {arrayMinSize} items", "arrayMaxSize": "{property} must contain at most {arrayMaxSize} items", "arrayUnique": "{property} must be unique", "isBoolean": "{property} must be a boolean", "isDate": "{property} must be a Date", "isDateString": "{property} must be a valid ISO 8601 date string", "isUUID": "{property} must be a UUID", "isUrl": "{property} must be a URL", "isEnum": "{property} must be a valid enum value", "isPhoneNumber": "{property} must be a valid phone number"}