{"answers": {"stoplist": "Answers stoplis"}, "ccs": {"api": {"msg": {"auth_failed": "User authentication failed", "captcha_required": "For security reasons a captcha is required to continue", "invalid_survey_link": "Invalid survey line", "no_filters": "No filters available", "no_mc_questions": "No Multiple choice questions found", "user_blocked": "For security reasons your account is temporarily locked"}}, "btn": {"back": "Back", "change_extern_pwd": "Extern password", "continue": "Continue", "dashboard_external_link": "External link", "delete": "Delete", "delete_connector": "Delete connector", "delete_language": "Delete Language", "exportmailer": "Export mailer", "field_selection": "Field Selection", "import": "Import and Sen", "import_all": "Import and Send Al", "list": "<PERSON><PERSON>", "no": "N", "question_selection": "Question Selectio", "reset": "<PERSON><PERSON>", "run": "<PERSON><PERSON>", "select": "Se<PERSON><PERSON>", "selectall": "Select Al", "send": "<PERSON>", "send_activationlink": "Send activation lin", "send_activationlink_fe": "Frontend Activatio", "show_feedback": "Show all feedbac", "showtexts": "Show text", "test": "<PERSON><PERSON>", "unlock_user": "Unlock use", "yes": "Ye"}, "caption": {"Extra": "Extra", "addoption": "Option", "addquestion": "Question", "advancedreports": "Advanced report", "advancedreportsview": "Advanced reports (view", "answerClassification": "Answer Classificatio", "answers": "Answer", "authorizations": "Authorisation", "authorizations_admin": "Authorisations admi", "basetemplates": "Base template", "blacklist": "<PERSON><PERSON>", "brand": "<PERSON><PERSON>", "bsh_mapping": "Mapping tabl", "callback_selector": "Callback <PERSON><PERSON>", "category": "Survey categorie", "charts": "Chart", "classification": {"subitem": "Classification Subite"}, "classification_item_main": "Classification group name", "classificationgroups": "Classification group", "classificationlabels": "Classification label", "clfsettings": "CLF Setting", "closedloopfeedback": "CL Feedbac", "clubs": "Club", "companies": "Companie", "dashboard": "Dashboar", "dashboard1": "Dashboard ", "dashboard2": "Dashboard I", "dashboard_List": "Dashboard lis", "dashboard_list": "Dashboard lis", "dashboard_questi": "Dashboard Quest", "dashboard_settings": "Dashboard setting", "dashboard_view": "View dashboard", "emailtemplate": "<PERSON>ail templat", "employees": "Employee", "export": "Expor", "extra": "Extra Chart", "gdpr_report": "GDPR Repor", "global_dashboard": "Global dashboar", "group": "Grou", "groups": "Group", "highlights": "Highlight", "insightslist": "Insights list", "loggers": "<PERSON><PERSON>", "mailinglist": "Mailing lis", "manageWordlists": "Manage Wordlist", "management": "Managemen", "nps_performace_overview": "NPS performance overvie", "nps_preformace_overview": "NPS performance overvie", "nps_verbatim_analysis": "NPS verbatim analysi", "other": "Othe", "poll_question": "<PERSON><PERSON> question", "privacyviews": "Privacy View", "procent_respons": "Response percentag", "projects": "Project", "questi_survey": "Questi survey", "question": "Question", "reportmailer": "Report Maile", "reports": "Report", "responses_per_period": "Responses per perio", "searchlists": "Searchlist", "stage": "Stag", "statistics": "Statistic", "stoplist": "Stop lis", "survey": "Survey", "survey_groups": "Survey grou", "surveycreator": "Survey Creato", "systememailtemplates": "System mailtemplate", "tables": "Table", "tags": "Tag", "topiclist": "Topic lis", "topics": "Insight", "users": "User", "views": "View"}, "chart": {"lbl": {"after": {"five": {"days": "After five day"}, "four": {"days": "After four day", "weeks": "After four week"}, "longer": "<PERSON><PERSON>", "one": {"day": "After one da"}, "seven": {"days": "After seven day"}, "six": {"days": "After six day"}, "three": {"days": "After three day", "weeks": "After three week"}, "two": {"days": "After two day", "weeks": "After two week"}}, "response": "Respons", "same": {"day": "On the same da"}}}, "charts": {"lbl": {"nps_per_period": "NPS per perio"}}, "clf": {"add_category": "Add categor", "add_tag": "{0} added the following tag: {1", "add_tags": "{0} added the following tags: {1", "all_calls": "All call", "assigned_to": "Assigned t", "btn_assign": "Assig", "btn_close": "<PERSON><PERSON>", "btn_decline": "Declin", "btn_hide_answers": "Hide other answer", "btn_hide_filter": "Hide filter", "btn_hide_search": "<PERSON>de searc", "btn_onhold": "On hol", "btn_show_answers": "Show other answer", "btn_show_filter": "Show filter", "btn_show_search": "Show searc", "callback_na": "Contact info not availabl", "callback_no": "Contact NOT allowed", "callback_yes": "Contact allowed", "categories": "CLF Categorie", "clear": "<PERSON><PERSON>", "dlg": {"select_user": "Select User to Assign t"}, "error": {"no_calls_selected": "You have not selected any calls"}, "footer": "{0} records foun", "footer_selected": "{0} records selecte", "hide": {"instructions": "Hide instruction"}, "instructions": "CLF instruction", "lane_0": "Backlo", "lane_1": "To-d", "lane_2": "In progres", "lane_3": "Don", "lane_4": "Archive", "lbl": {"cause": "Caus", "caused_by": "Caused b", "closed_by": "Closed b", "credit": "<PERSON><PERSON><PERSON>", "result": "<PERSON><PERSON>", "results": "Result", "tooltip": {"hide_answers": "Click to hide answer", "show_answers": "Click to show answer"}}, "lists": "CLF list", "load_default_filter": "Load default filte", "msg": {"add_tags": "Tags update", "assigned_to": "Assigned this call to {0}", "categories_incomplete": "The results are incomplet", "confirm_assign_user": "Are you sure you want to assign these calls to {0}", "confirm_close": "Are you sure you want to close this call", "favorite_off": "Call unmarked as favorit", "favorite_on": "Call marked as favorit", "lane_change_to": "Call moved to swimming lane {0}", "lane_change_to_by": "Call moved to swimming lane {0} by {1", "notes_added": "Notes adde", "remove_tags": "Tags remove", "status_changed": "Status change", "summary_added": "Summary adde"}, "msg_assigned": "assigned this call t", "msg_closed": "has closed this cal", "msg_decline_reason": "Reason you decline this cal", "msg_decline_reason_req": "Reason is required to decline this call", "msg_declined": "declined this call. Reason", "msg_onhold_reason": "Reason you put this call on hol", "msg_onhold_reason_req": "Reason is required to put this call on hold", "my_calls": "My call", "notes": "Note", "remove_tags": "{0} removed the following tags: {1", "removed_tag": "{0} removed the following tag: {1", "reset": "<PERSON><PERSON>", "save_default_filter": "Save as default filte", "show": {"instructions": "Show instruction"}, "status": "Statu", "vl": {"current": "<PERSON><PERSON><PERSON>", "previous": "Previou"}}, "csi_1": "<span class=\"title\">Satisfaction with appliance</span><br /><span class=\"subtitle\"> - Average score, must be below Tolerance Level</span", "csi_2": "<span class=\"title\">Total satisfaction with service</span><br /><span class=\"subtitle\"> - Average score, must be below Tolerance Level</span", "csi_3": "<span class=\"title\">Availability of the phone</span><br /><span class=\"subtitle\"> - One call, in %, must be above Tolerance Level</span", "csi_4": "<span class=\"title\">Number of visits by the engineer</span><br /><span class=\"subtitle\"> - One visit, in %, must be above Tolerance Level</span", "csi_5": "<span class=\"title\">Downtime</span><br/><span class=\"subtitle\"> - sum max 7 days, in %, must be above Tolerance Level</span", "csi_6": "<span class=\"title\">Service loyalty</span><br/><span class=\"subtitle\"> - Top2 Yes, in %, must be above Tolerance Level</span", "csi_7": "<span class=\"title\">Brand loyalty</span><br/><span class=\"subtitle\"> - Top2 Yes, in %, must be above Tolerance Level</span", "dashboard": {"lbl": {"avg_nps": "AVG NP", "nps_per_period": "NPS per perio", "nps_per_team": "NPS per tea", "nps_progression_chart": "NPS Progression char", "nps_progression_chart_team": "NPS Progression team chart fo", "show_nps_previous_year": "Show NPS previous yea"}}, "date": {"dmy": "Day-Month-Year (dd-mm-yyyy", "mdy": "Month-Day-Year (mm-dd-yyyy", "ydm": "Year-Day-Month (yyyy-dd-mm", "ymd": "Year-Month-Day (yyyy-mm-dd"}, "dlg": {"activated_segments": "Only load data from activated segments in this filte", "classified_pos": "Filter data based on the data being classified as positive or negative for a main/sub-classification grou", "exportAllVisitDate": "'Export All' only works well with 'Visit date'. Change 'Response date' to 'Visit date'", "export_prefix": "For example if the export prefix is \"GER\" the files wil be called “GERNPS.GERINT.Field Service.04092019", "export_to_ftp": "Please enter a export prefix first", "fieldsRequired": "Please enter all the fields firs", "groupForUser": "Please select a group for this use", "mandatoryFields": "You have to enter the following mandatory fields", "maxCharacters": "Maximum number of characters reached", "morethen10surveys": "You have selected {0} surveys for the export view you can only select up to 14. Please change your selection", "noCriticalWordsFound": "No critical words found, please first enter critical words at 'Insigths'> 'Topics list'", "noDataFoundForThisPeriod": "No data found for this period", "no_surveys_found": "No surveys found for selected employees in this period", "numberBlackListed": "There were {0} e-mails into the blacklist", "numberEmailsSend": "{0} lines are parse", "participateBenchmark": "If you want to participate in the benchmark you have to select a survey for all the benchmark type", "recordsImported": "{0} records were imported succesfully, {1} records failed", "required_question_publish": "'Check to publish question' is required if Widget is activ", "required_question_report_grade": "'Report grade question' is required if report is not on NPS and Widget is activ", "selectedOptIn": "When selected only load data where consumer accepted opt-i", "survey_has_expired": "Survey has expired", "toManySurveys": "Te veel surveys geselecteer", "unknown_username": "Unknown username, contact your application manager"}, "error": {"phoneNotValid": "The phone number has to start with a '+' or '00'"}, "fld": {"company": {"placeholder": "Enter compan"}, "gdpr": {"email_placeholder": "Full email addres", "phone_placeholder": "Phonenumber without country cod"}, "new_password": {"placeholder": "Enter new password"}, "old_password": {"placeholder": "Enter current password"}, "password": {"placeholder": "Enter password"}, "repeat_password": {"placeholder": "Repeat new password"}, "username": {"placeholder": "<PERSON>ter <PERSON>s"}}, "import": {"ask": {"usecurrentmappings": "Do you want to use the current mappings"}, "cntrecords": "{0} lines are parsed. The result is: {1} new records and {2} records modified", "confirmload": "You are about to load the file, is that ok", "confirmreload": "reloading will remove all current mappings. Do you want to proceed", "date": {"separator": "Date separato"}, "destfield": "Destinationfiel", "done": "Import done", "duplicateLink": "Source field '{0}' is already linked to Destination field '{1}'", "excel": {"readError": "The Excel file cannot be read"}, "field": {"separator": "Field separato"}, "firstrow": {"fieldnames": "Please note!!! There must be field names in the first row of the source file"}, "info": {"map": "Attention, the coupling has been removed for the fields below: {0}"}, "mandatory_sourcefield": "{0} is a required field. Please connect {1}", "newtemplate": "Name new templat", "nofields": "No fields linked", "nokey": "No key field set. Do you wish to continue", "nosourcefield": "Sourcefield must not be empty for a keyfield. Please connect {0}", "occupants": "Occupant", "openfile": "Select import fil", "rejectedrows": "The following rows are rejected, because unique identification is not possible with the specified keys", "running": "Importing.......", "sourcefield": "Sourcefiel", "sourcefile": "Source file (csv", "specification": "You will now import {0}", "stepdescription1": "<html><p><strong>Step 1/4</strong>:</p> <p>Which template do you want to use?</p> <p>Select a source file</p></html", "stepdescription2": "<html><p><strong>Step 3/4</strong>:</p><p>Selecting a Source Field to a Target Field establishes a link between the two fields.</p> <p>Enter through key field (<img src='media:///Key-nw.png'>) which combination of Source Fields is unique.</p> <p>A Source Field whereby (<img src='media:///!!.png'>) or  (<img src='media:///Key-nw.png'>)  is checked, need to be linked.</p></html", "stepdescription3": "<html><p><strong>Step4/4</strong>:</p> <p>The following fields are linked by you and will be imported.</p> <p>Click Run If your choice is correct.</p><br/> <p>note: {0} records will be imported. Only the first 10 are shown.</p> </html", "stepdescription_detail": "<html><p><strong>Step 2/4</strong>:</p><p>If necessary, change the delimiter and description.</p></html", "template": "Mapping templat", "time": {"separator": "Time separato"}}, "lang": {"da": "<PERSON><PERSON>", "de": "G<PERSON><PERSON>", "el": "Gree", "en": "<PERSON><PERSON><PERSON>", "es": "<PERSON><PERSON>", "fi": "<PERSON><PERSON>", "fr": "Frenc", "gr": "Gree", "it": "Italia", "nl": "Dutc", "no": "Norwegia", "pl": "Polis", "pt": "Portuges", "ru": "Russia", "sv": "Swedis", "tr": "Turkisc", "zh": "<PERSON><PERSON>"}, "lbl": {"***anonymous": "*** Anonymous **", "AnswertableQuestion": "Answer Tabl", "CESQuestion": "CES Question", "CSATQuestion": "CSAT question", "DOB": "Direct Order Bookin", "NPSQuestion": "NPS question", "SaveExport": "Save Expor", "ShowExport": "Show Expor", "StartQuestion": "Start question", "UseSFTP": "Use SFT", "Webshop": "Websho", "acceptanceby": "Acceptance B", "acceptancedate": "Acceptance dat", "activate_lang": "Activate languag", "activate_story_widget": "Activate story widge", "active": "Activ", "active_subitems": "Active Subitem", "activitydate_dMMMM": "Activitydate (i.e. 1 January", "add": "Ad", "add_classification_item": "Group name", "add_classification_item_locale": "Group name translatio", "add_department": "Add proces", "add_groups": "Add group", "add_language": "<PERSON><PERSON>", "add_note": "Add not", "add_templates": "Add email template", "addition": "Additio", "additional": "Ad<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "all": "Al", "allProjects": "All project", "all_clubs": "(An empty field means all clubs!", "all_projects": "(An empty field means all projects!", "all_responses": "All response", "allow_neutral_sentiment": "Allow neutral sentimen", "allow_next_blocked_send": "Allowed blocked sen", "answer": "Answe", "answerLabel": "Label Answe", "answerShort": "Answer Shor", "answer_hi": "Answer Hig", "answer_lo": "Answer Low", "answercount": "Answer count", "answered": "Complete", "api_key": "API ke", "api_pwd": "API password", "applicationPart": "Application Par", "april": "<PERSON>i", "area": "Are", "assignLabels": "Select the labels for survey {0}", "assigned_by": "Assigned b", "assigned_to": "Assigned t", "assigned_to_firstname": "Assigned to firstnam", "assigned_to_lastname": "Assigned to lastnam", "august": "Augus", "average": "Averag", "b2b": "Business to Busines", "b2c": "Business to Consume", "backend": "Access to Admi", "background_color": "Background colo", "backlog_months": "Backlog month", "basetemplate": "Base templat", "bhs_name": "BSH Name", "blacklistDetail": "Blacklist <PERSON><PERSON>", "blacklistItem": "Blacklist <PERSON><PERSON>", "branch": "Branc", "brand": "Brand", "brandFilter": "Brand filte", "brandFilterCleared": "Brand filter is cleared because there is a new survey selectio", "btn_show_feedback": "Button 'Show all feedback", "bucket": "<PERSON><PERSON>", "buildnr": "Building number", "businessfield": "Appliance typ", "button_txt": "Button tex", "buyers": "Buyer", "callback": "Callback question", "callback_no": "Callback no", "callback_yes": "Callback yes", "callboard": "Callboar", "callcenteragent": "Contact Center agen", "callnumberFB": "Call Number Focus Brick", "campaign": "Campaig", "campaigns": "Campaign", "carousel_color": "Carousel colo", "carousel_color_inactive": "Carousel color inactiv", "category": "Survey categor", "category_local": "Category loca", "category_nb": "Category number", "chainview": "Chain overvie", "changePassword": "Change password", "changesSaved": "The changes are saved", "charts": "Chart", "charts_v2": "Charts (v2", "checkboxquestionhorizontal": "Multiple Choice Question (Multiple Answers", "checkboxquestionvertical": "Multiple Choice Question Vertical (Multiple Answers", "city": "Cit", "classification": "Classificatio", "classification_cjs": "Classification(cjs", "classification_item_main": "Classification group name", "classification_item_sub": "Classification name subite", "classification_item_sub_local": "Classification local name subite", "classification_v2": "Labeling (v2", "classification_v2_cjs": "Classification(cjs.v2", "classified": "Classifie", "classifiedAnswers": "{0} classified response", "classifiedAnswers_sub": "{0} - {1} classified responses (of {2} responses in total", "clear": "<PERSON><PERSON>", "clf_field": "CLF fiel", "clf_notunique": "Category already exist", "closeSurveyDays": "Close Survey Afte", "closedloopfeedback": "CL Feedbac", "closuredate": "Closuredat", "club": "<PERSON><PERSON>", "club_categories": "Club categorie", "club_category": "Club categor", "club_nm": "Club name", "club_nr": "Club n", "clubdetail": "Club Detai", "comment_responses": "Responses with comment", "company": "Compan", "companyBrand": "Company / Bran", "companyCode": "Company Cod", "companyName": "Company Name", "companyType": "Company Typ", "complainagent": "Comp<PERSON>t owne", "compulsoryQuestion": "Required Question", "confirm": "<PERSON><PERSON><PERSON>", "connectors": "Connector", "consecnr": "Consec n", "construction": "Constructio", "contactDetail": "Contact <PERSON>ai", "contactcenter": "Contact cente", "contactdate": "Contact dat", "contacts": "Contact", "copy": "<PERSON><PERSON>", "copy_group": "Copy grou", "counselor": "<PERSON><PERSON>", "count": "<PERSON><PERSON>", "count_by": "Count b", "country_code": "Country code (ISO-3166 2 letter code", "coupon_code": "Coupon Cod", "coupon_end": "Coupon Enddat", "coupon_text": "Coupon tex", "create_dashboard": "Create dashboar", "create_report": "Create repor", "csi": "CS", "csvKey": "CSV Ke", "custcreditnr": "Customer creditn", "customername": "Customer Name", "customernumber": "Customer Number", "custum_q": "Custom ", "dashboard_link": "Link to the dashboar", "dashboard_list": "Dashboard lis", "dashboard_name": "Name of the shared dashboar", "dashboard_nps_callback": "<html>Good to have you here. When you want to call respondents to ask them questions about their feedback, the Callback Selector helps you to make a selection. You can filter and assign feedback to keep things manageable.  <br><br>\r Do you want to know how to add critical words? Or are you curious why long answers or answers without text are interesting to call back? Check the info below.  </html", "dashboard_nps_performance": "<html>The NPS Performance Overview gives you insight into the performance of selected periods, teams and individual employees. There are many selection options for different views.  <br><br> Do you want to know more? Check the info below. </html", "dashboard_nps_verbatim": "<html>The NPS Verbatim Analysis is a handy method to gain direct insight into the sentiment among your respondents. Please note, this dashboard only works if feedback is classified.   <br><br> Do you want to get started with classifying feedback? Get started via \"classification\" in Focus Feedback.  <br><br> Would you like to know more about the NPS Verbatim Analysis? Check the info below. </html", "data": "<PERSON>t", "date": "<PERSON>t", "dateFrom": "Date fro", "dateTo": "Date t", "date_blacklisted": "Date Blackliste", "date_of_birth": "Date of birt", "dateformat": "Date Forma", "day_of_week": "Day of wee", "days": "Day", "dealer": "<PERSON>e", "december": "Decembe", "deleteBlacklistMsg": "Are you sure you want to remove this e-mail address from the blacklist", "deleteWarning": "Delete Warnin", "deleteWarningMsg": "Are you sure you want to delete this record", "deleteWarningMsgOwner": "Are you sure you want to delete all the data of  this owner, this could take a while", "delete_logo": "Remove log", "delete_report": "Delete repor", "delete_template": "Delete template", "delete_view": "Delete vie", "deletenotgranted": "Deletion not allowed", "delivery": "Deliver", "department": "Departmen", "description": "Descriptio", "descriptionLeft": "Scale Label Lef", "descriptionRight": "Scale Label Righ", "descriptivetext": "Descriptive Tex", "detractors": "Detractor", "device": "Devic", "discard": "Discar", "disclaimer": "<PERSON><PERSON><PERSON>", "disclaimer_lbl": "Disclaimer labe", "dispatch": "Dispatc", "distribution_over": "Distibution ove", "doesNotApply": "N/", "domains": "Domain", "done": "Don", "downtime": "Downtim", "duphotnews": "Create a duplicate of the selected hot news", "duplicate": "Duplicate", "dupquestion": "Create a duplicate of this question", "dupsurvey": "Create a duplicate of this survey", "duptemplate": "Create a duplicate of the selected template", "e_nr": "E no", "edit": "<PERSON>i", "editUsers": "User", "edit_department": "Edit proces", "eg": "Appliance cat", "email": "<PERSON><PERSON>", "emailFrom": "Email fro", "emailReply": "Email repl", "email_or_phone": "Email or phon", "email_send_block_procent": "Email block percen", "email_to": "To", "employee": "Employe", "empty_selection": "Empty selectio", "engineer_area": "Engineer Are", "enumber": "E number", "error": "Erro", "export": "Expor", "exportQuesti": "Export Quest", "export_all": "Export al", "export_answered": "Export complete", "export_prefix": "Export prefi", "export_to_ftp": "Export to FT", "extern": "Out Of Guarante", "fase": "<PERSON><PERSON>", "favorite": "Highligh", "fdnumber": "FD number", "februari": "<PERSON><PERSON><PERSON>", "february": "<PERSON><PERSON><PERSON>", "field": "<PERSON><PERSON>", "file": "Fil", "file_encoding": "File encodin", "filters": "Filter", "firstEmail": "First Emai", "firstEmailDate": "First Email dat", "firstmails": "First Email", "firstmsg": "First Messag", "firstname": "First name", "firstpagetext": "First Page Tex", "flag_sentthreshold": "Not sent - Threshol", "flag_sentthresshold": "Not sent - <PERSON><PERSON><PERSON><PERSON>", "flex": "<PERSON><PERSON><PERSON><PERSON>", "font_color": "Font colo", "font_color2": "Font color ", "font_family": "Font famil", "forgotten": {"password": "Forgot your password"}, "friday_abbr": "Fr", "from": "fro", "fromto": "t", "frontend": "Access to new Front en", "ftpHost": "FTP Hos", "ftpPassword": "FTP password", "ftpPath": "FTP Pat", "ftpPort": "FTP Por", "full_customername": "Full customernam", "function": "Functio", "functionbsh": "Function (BSH", "garantie": "In Guarante", "gdpr_months": "GDPR month", "gdpr_report": "GDPR Repor", "general": "Genera", "generate": "Generat", "generateChart": "Generate char", "global_category": "Global categor", "grossinvoice": "Gross invoic", "history_log": "History lo", "host": "<PERSON><PERSON>", "hotnews": "Hot new", "hour_of_day": "Hour of da", "housenr": "Number", "import": "Impor", "import_filter": "Import filte", "importdate": "Import dat", "imported": "Importe", "imprint_lbl": "<PERSON><PERSON><PERSON>t labe", "imprint_link": "Imprint lin", "info": {"forgotton": {"password": "Enter details below to reset your password"}, "password": {"expired": "<html>Your password has expired! <br />Please enter a new password.</html", "incorrect": "Current password is incorrect"}, "toomanyloginattempts": "This user has been temporarily blocked. Please try again later or contact your administrator"}, "initials": "Initial", "invalid_email": "Invalid email address", "ip_range": "Ip rang", "is4sports": "Feedback4Sport", "january": "<PERSON><PERSON><PERSON>", "jasperprint": "<PERSON> prin", "jaspersettings": "JasperServer setting", "job_title": "<PERSON>l", "july": "Jul", "june": "Jun", "key": "<PERSON>", "key2": "Key ", "known_user": "This person is already known in Focus Feedback", "kpi": "NPS Brand", "kpi_v2": "NPS Brands (v2", "label": "Lab<PERSON>", "label_locale": "Label translatio", "label_type": "Label Typ", "labeling": "Labelin", "labels": "Label", "language": "Lang<PERSON><PERSON>", "last14days": "Last 14 day", "last7days": "Last 7 day", "lastMonth": "Last 30 days (Month", "lastQuarter": "Last 90 days (Quarter", "lastQuestionOnPage": "Last question on the pag", "lastYear": "Last 365 days (Year", "lastvisitdate": "Last visit dat", "legendaEmailTemplate": "<html>  <body>   <table>    <tr>     <th>      Tag     </th>     <th>      Description     </th>    </tr>    <tr>     <td>      [senddate]     </td>     <td>      First E-mail     </td>    </tr>     <td>      [companyname]     </td>     <td>      Companyname     </td>    </tr>    <tr>     <td>      [rasnumber]     </td>     <td>      Ras number     </td>    </tr>    <tr>     <td>      [initials]     </td>     <td>      Initials     </td>    </tr>    <tr>     <td>      [salutation]     </td>     <td>      Salutation     </td>    </tr>    <tr>     <td>      [name]     </td>     <td>      Customer name     </td>    </tr>    <tr>     <td>      [email]     </td>     <td>      Customer E-mail     </td>    </tr>    <tr>     <td>      [technician]     </td>     <td>      Technician     </td>    </tr>    <tr>     <td>      [logo]     </td>     <td>      Companylogo     </td>    </tr>    <tr>     <td>      [survey_link]     </td>     <td>      Survey link     </td>    </tr>   <table>  <body> <html", "level": "<PERSON><PERSON>", "like": "Lik", "linkedEmailTemplate": "Linked <PERSON><PERSON> Template", "linkedgroupes": "Linked group", "list": "<PERSON><PERSON>", "loading_verbatim": "Loading verbatim..", "loggerName": "Logger Name", "login": "<PERSON><PERSON>", "loginError": "Login details are incorrect", "login_intro": "Log in to view your latests reviews", "logisticpartner": "Logistic partne", "logo": "Log", "logout": "Logou", "mailSubject": "<PERSON><PERSON>", "mailtype": "Mailtyp", "maingroup": "Maingrou", "manageLabels": "Manage label", "manageLabels_v2": "Manage labels (v2", "manageQuesti": "Manage Quest", "mapping": "Mappin", "march": "<PERSON>", "maxQuestionPage": "Number of question on a pag", "maxRowsMessage": "You can add up to 11 lines ma", "maxRowsWarning": "Maximum rows warning", "may": "Ma", "medium": "Me<PERSON>u", "member": "<PERSON><PERSON>", "memberDetail": "Member <PERSON><PERSON>", "messageMedium": "Message Mediu", "monday_abbr": "Mo", "msg": {"welcome_feedback": "Welcome to Focus Feedback"}, "multi_select": "Multi-selec", "multiplechoice": "Multiple choic", "name": "Nam", "name_extern": "Name (external", "narrowcast_token": "Narrowcast Toke", "negative_comment_responses": "- Negative comment", "negative_sentiment": "Negative sentimen", "net_price": "(Net) pric", "netigate_survey": "Netigate survey", "neutral_sentiment": "Neutral sentimen", "new": "<new", "newPassword": "New password", "newPasswordMismatch": "Your new passwords dont match", "newPasswordNotStrong": "Password does not meet the requirements", "new_value": "New valu", "nextQuestion": "Next Question", "noEmployee": "No employee with those letters in his/here name", "noLabelsForSurvey": "There are no labels associated with this survey", "noNPSquestion": "No NPS Question found for this Survey", "no_data": "No data availabl", "no_medium": "No medium availabl", "noblankPassword": "Blank password is not allowed", "nonDOB": "No DO", "nonWebshop": "No Websho", "none": "Non", "nonflex": "<PERSON>", "notClassified": "Not Classifie", "notToBeLabeled": "Exclude from classificatio", "november": "Novembe", "nps": "NP", "npsscore": "NPS Scor", "nrOfResponses": "Responses ({0}", "nr_responses": "# responses {0}", "nrvisits": "Number of visit", "number": "Number", "numberAnswers": "({0} responses", "numberToGo": "Results  {0} / {1", "object": "<PERSON><PERSON><PERSON><PERSON>", "october": "Octobe", "oldPassword": "Old password", "old_value": "Old valu", "oneOfTwoRequired": "* {0} or {1} must be filled", "onlyDOB": "DO", "onlyWebshop": "Websho", "open": "Ope", "openChanges": "Open Change", "openQuestionCheckMessage": "An open question doesn't have any return value, do you want to delete the return values for this question", "openquestion": "Open Question", "operationtime": "Operation tim", "optin": "Opt-i", "optout_address": "Opt-out addres", "optout_link": "Opt-out lin", "optout_linkdisplay": "Opt-out <PERSON>la", "optout_redirect_settings": "Opt-out Setting", "order": "Orde", "organizations": "Organization", "originservice": "Origin of servic", "other": "Other (please specify", "own": "Ow", "part": "Par", "partSubFilter": "Part filte", "passives": "Passive", "password": "password", "passwordCycles": "This password has recently been used by you. Please choose another password", "password_settings": "Password setting", "payment": "Payment metho", "payments": "Payment", "pending": "<PERSON><PERSON>", "percentage": "Percentag", "period": "<PERSON><PERSON>", "phase": "<PERSON><PERSON>", "phaseMembers": "Phase Member", "phaseOccupants": "Phase Occupant", "phaseOrganizations": "Phase Organization", "phone": "<PERSON><PERSON>", "phone_1": "Phone ", "phone_2": "Phone ", "polling": "Quest", "port": "Por", "positive_comment_responses": "- Positive comment", "positive_sentiment": "Positive sentimen", "postalcode": "Postalcod", "potential": "Potential", "potential_and_like": "Potential & Lik", "potlikeanswers": "{0} potential/likes i", "preprepper": "<PERSON>p<PERSON><PERSON>", "previewAddress": "Preview addres", "print": "<PERSON><PERSON>", "process": "Proces", "processed": "Processe", "prod_div": "Product div", "prodarea": "Prod are", "proddate": "Production dat", "product": "Product", "product_age": "Product ag", "product_category": "Product categor", "programSettings": "Setting", "progression": "NPS Tren", "project": "<PERSON><PERSON><PERSON>", "projectCity": "Project Cit", "projectDetail": "Project Detai", "project_categories": "Project categorie", "project_category": "Project categor", "projectname": "Project name", "projectname_extern": "Project name (external", "projectnr": "Project ", "projectnumber": "Project number", "promotors": "Promoter", "promoters": "Promoter", "publishQuestion": "Publish Question", "purchase": "<PERSON><PERSON><PERSON><PERSON>", "purchasedate": "Purchase dat", "questi": "Quest", "question": "Question", "questionLabel": "Question labe", "questionTemplate": "Question templat", "questionTitle": "Question titl", "questionType": "Question typ", "question_feedback": "Feedbac", "question_shortlabel": "Question Short-labe", "ras_e_nr": "RIS no. / E no", "rasnr": "RIS no", "rasnumber": "RIS number", "read_manual": "<PERSON>", "reason_declined": "Reason decline", "refnr": "Referenc", "refresh": "Re<PERSON><PERSON>", "refresh_projects": "Refresh project", "refresh_view": "Refresh vie", "remainderAfter": "Reminder <PERSON><PERSON>", "remainderCount": "<PERSON><PERSON><PERSON>", "remainderEmail": "Re<PERSON><PERSON>", "remainderSubject": "<PERSON><PERSON><PERSON> sub<PERSON><PERSON>", "reminderAfter": "<PERSON><PERSON><PERSON>", "remindermails": "Reminder <PERSON>", "remindermsg": "<PERSON><PERSON><PERSON>", "removedAssignedTo": "Removed assigned t", "repair_email": "Repair email", "repeatNewPassword": "Repeat new password", "repeatPassword": "Repeat password", "reportQuesti": "Report Quest", "reported_by": "Reported B", "reporting_month": "Reporting mont", "required": "* Required Field", "requiredField": "Field '{0}' has to be completed", "responded": "Responde", "respondent_name": "Respondent name", "response": "Respons", "response_detail": "Response detail", "response_rate": "Response rat", "responsebehaviour": "Survey response behaviou", "responsebehaviour_v2": "Survey response behaviour (v2", "responsedate": "Response dat", "responsedate_HHmm": "Responsetime (i.e. 09:12", "responsedate_dMMMM": "Responsedate (i.e. 1 January", "responsedate_ddMMMMyyyy": "Responsedate (i.e. 01 Januari 2010", "responsedate_ddMMyyyy": "Responsedate (i.e. 01-01-2010", "responsedatefrom": "Responsedate fro", "restoreWarning": "<PERSON><PERSON>", "restoreWarningMsg": "Are you sure you want to restore this label", "returnValue": "Valu", "salutation": "Salutatio", "saturday_abbr": "Sa", "save": "Sav", "save_new": "Save & Ne", "save_view_as": "Save view as ..", "scalequestionhorizontal": "Rating <PERSON>", "scalequestionnumbers": "Rating Scale (numbers", "scalequestionvertical": "Rating Scale Vertica", "score": "<PERSON><PERSON>", "search": "Searc", "searchvalues": "Search value", "security_officer": "Security office", "selectSurvey": "Select survey", "select_view": "Select a vie", "selectall": "Select/Deselect Al", "selectall:": "Select all", "selected": "Selected", "send": "<PERSON>", "sendThankMail": "Send Thank-you <PERSON><PERSON>", "sendThankMessage": "Send Thank-you Messag", "send_answers": "(Sent: {0} Responded: {1}", "sendcount": "Send coun", "sender_firstname": "Sender firstnam", "sender_lastname": "Sender lastnam", "sendmails": "Sent email", "sendmsges": "Sent message", "sendtime": "Send tim", "sentdate": "Sent dat", "september": "Septembe", "service_partner": "Service partne", "shared_by_firstname": "Shared by firstnam", "shared_by_lastname": "Shared by lastnam", "show": "Show", "showExport": "Show Expor", "showOther": "Show option other", "show_callback": "Show callback question", "show_on_dashboard2": "Show on Dashboard I", "show_report": "Show repor", "show_responses": "Show responses ({0}", "singletextboxquestion": "Single Textbox Question", "smileys_2": "2 smiley", "smileys_3": "3 smiley", "smileys_4": "4 smiley", "smileys_5": "5 smiley", "sms": "SM", "smsExplanation": "(Posted in {0} parts. This includes the survey link but excludes the length of the text for the other tags)", "smsFrom": "SMS fro", "smsMessage": "First SMS message", "smsReminder": "Reminder SMS message", "smsTags": "SMS Tag", "smsTemplates": "SMS Template", "smsThank": "Thank-you SMS message", "smsThankExplanation": "(Posted in {0} parts. This excludes the length of the text for the tags)", "sms_settings": "SMS Setting", "sms_url": "UR", "smtp_settings": "SMTP Setting", "solution": "Solutio", "sort": "Sor", "sot": "Service order typ", "stageDetail": "Stage Detai", "start_button": "Start Butto", "subject": "Question", "subjectShort": "Question Shor", "summary": "<PERSON><PERSON><PERSON>", "sunday_abbr": "Su", "support": "<PERSON><PERSON><PERSON>", "surv_clf": "CLF Survey", "survey": "Survey", "surveyAddress": "Survey Addres", "surveyAlreadyClosed": "Warning when survey is already closed", "surveyAlreadyDone": "Warning when survey is already completed", "surveyDetail": "Survey Detai", "surveyEmailTemplateCompany": "E-mail template company name", "surveyEndText": "Last Page Tex", "surveyLink": "Survey Lin", "surveyLinkDisplay": "Link <PERSON>", "surveyName": "Survey name", "surveyName_intern": "Survey name (internal", "surveyQuestion": "Survey Question", "surveyQuestionMaxVraag": "You can add up to a maximum of 11 lines", "synonyms": "Synonym", "tags": "Tag", "target": "Target current yea", "team": "Tea", "techgroup": "Engineer grou", "techgrp": "Regio", "techgrp_code": "Region cod", "technici": "Enginee", "technici_code": "Engineer cod", "template_q": "Template ", "templates": "Template", "testMail": "Send test e-mai", "testMailOrOriginal": "Please fill out the sent to e-mail adress, if empty the stored email addresses are used", "testMailToAdress": "Please fill out the sent to e-mail adres", "testMail_notsent": "Test e-mail not sen", "testMail_sent": "Test e-mail sen", "testSMS": "Send test SM", "testSMSToPhone": "Please fill out the sent to phone number (remember the country code)", "testSMS_notsent": "Test SMS not sen", "testSMS_sent": "Test SMS sen", "test_email": "Test email addres", "test_mode": "Test mod", "test_phone_nr": "Test phone number", "thank_mail": "Thank-you e-mai", "thank_subject": "Thank-you e-mail subjec", "thankmails": "Thank-you Email", "thankmsg": "Thank-you <PERSON><PERSON><PERSON>", "threshold": "Threshol", "thursday_abbr": "Th", "time": "tim", "to": "t", "to_send": "<PERSON>", "token": "<PERSON><PERSON>", "top5topicssegment": "Top 5 Insights per segmen", "topic": "<PERSON>i", "tosurveys": "To Survey", "total": "To<PERSON>", "totalAnswers": "(of {0} responses in total", "touchpoint": "Touchpoint", "triggerName": "Trigger Name", "tuesday_abbr": "Tu", "type": "<PERSON><PERSON>", "un_used_coupons": "Available Coupon", "unassigned": "Unassigne", "unique": "Unique number", "unknownerror": "Unknown erro", "unlinkedEmailTemplates": "Unlinked Email Template", "unsubscribed": "Unsubscribed tex", "uploadCoupons": "Upload Coupon", "uploadLogo": "Upload log", "upload_file": "Drop a logo here or click icon to uploa", "url": "UR", "url_logo": "Url log", "url_shortener": "URL Shortene", "url_website": "Url websit", "url_website_title": "Url website titl", "usabillaApiAccessKey": "Usabilla Acces Ke", "usabillaSecretKey": "Usabilla Secret Ke", "use3digittechnician": "Use 3-digit technician number", "use_import_ftp_settings": "Use import FTP setting", "use_nps_colors": "Use color", "use_nps_smileys": "Show smiley", "used_coupons": "Used Coupon", "user": {"firstname": "Firstname use", "lastname": "Lastname use"}, "userAnswers": "<html><body>User</br>answers</body></html", "userEmail": "User <PERSON>", "userGroup": "User <PERSON>", "userName": "User Name", "user_detail": "User Detail", "username": "username", "vico_settings": "VICO Setting", "views": "View", "visitdate": "Visit dat", "visitdate_from": "Visit date fro", "visitdate_to": "Visit date t", "waitingtime": "Waiting tim", "waranty": "Warrant", "warning": "<PERSON><PERSON>", "warningResponseDate": "Please notes, that filtering on Response date always gives 100% for Response", "watch_video": "Watch vide", "wednesday_abbr": "We", "welcome": "Welcom", "whichEmail": "Which e-mail", "widget_html_tag": "Widget html ta", "wordlist": "<PERSON><PERSON>", "work_annyversary": "Work anniversar", "wrongPassword": "The passwords do not match", "wvb": "Prepreparatio", "your_company": "Your compan", "your_password": "Your password", "your_projects": "Your project", "your_username": "Your username"}, "mail": {"activation": {"base_1": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\"> <html xmlns=\"http://www.w3.org/1999/xhtml\"> <head> <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" /> <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/> <meta name=\"format-detection\" content=\"telephone=no\" /> <title></title> <style type=\"text/css\"> body {  width: 100% !important;  margin: 0;  padding: 0;  background-color: #ffffff;  -webkit-font-smoothing: antialiased;  -webkit-text-size-adjust: none;  -ms-text-size-adjust: none; } .ReadMsgBody {  width: 100%; } .ExternalClass {  width: 100%; } .ExternalClass * {  line-height: 100%; } table td {  border-collapse: collapse; } h1 {  font-family: Aria<PERSON>, Gotham, 'Helvetica Neue', Helvetica, sans-serif;  font-size: 16px;  font-weight: bold;  line-height: 18px;  color: #f76300;  padding-top: 40px; } .wrapperHeader, .wrapperContent, .wrapperFooter {  margin: 0;  padding: 0;  width: 100% !important; } a, a:link, a:visited, a:hover {  color: #f76300;  text-decoration: underline; } img {  display: block;  line-height: 100%;  outline: none;  text-decoration: none;  margin: 0;  border: none; } img a {  outline: none;  border: none; } buttonradiusleft {  -webkit-border-top-left-radius: 6px;  -webkit-border-bottom-left-radius: 6px;  -moz-border-radius-bottomleft: 6px;  -moz-border-radius-topleft: 6px;  border-top-left-radius: 6px;  border-bottom-left-radius: 6px; } buttonradiusright {  -webkit-border-top-right-radius: 6px;  -webkit-border-bottom-right-radius: 6px;  -moz-border-radius-bottomright: 6px;  -moz-border-radius-topright: 6px;  border-top-right-radius: 6px;  border-bottom-right-radius: 6px; ", "base_2": " content {  font-family: Arial, Gotham, 'Helvetica Neue', Helvetica, sans-serif;  font-size: 14px;  line-height: 16px;  color: #58585a;  padding-top: 20px;  padding-bottom: 30px; } content a {  text-decoration: underline;  color: #f76300; } @media only screen and (max-width: 575px) { [class=fullWidth], [class=fullWidthHeightImg], [class=floatLeft], [class=floatPaddingLeft], [class=fullWidthPaddingLeft], [class=floatpaddingLeftCenter] {  width: 100% !important;  max-width: 100% !important; } [class=halfWidth] {  width: 50% !important;  max-width: 50% !important; } [class=floatLeft], [class=floatPaddingLeft] {  float: left !important;  text-align: left; } [class=floatpaddingLeftCenter] {  float: left !important;  text-align: center !important; } [class=hide] {  display: none !important; } [class=floatPaddingLeft], [class=fullWidthPaddingLeft], [class=floatpaddingLeftCenter] {  padding-left: 15px !important;  padding-right: 15px !important; } }  @media only screen and (max-width: 570px) { td[class=pattern] div {  position: relative;  overflow: hidden;  width: 100%;  height: 30px; } td[class=pattern] img {  position: absolute;  top: 0;  left: 50%;  margin-left: -300px; } } </style> <!--[if gte mso 15]> <style type=\"text/css\">  table {      font-size: 1px;         mso-line-height-alt: 0;         line-height: 0;         mso-margin-top-alt: 1px;  } </style> <![endif]--> </head>", "base_3": "<body> <table id=\"header\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">   <tr>     <td align=\"center\" valign=\"top\"><table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">         <tr>           <td valign=\"top\" align=\"center\" bgcolor=\"#cccccc\" height=\"10\"><table class=\"fullWidth\" width=\"600\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">               <tr>                 <td align=\"center\" bgcolor=\"#cccccc\" width=\"100%\" height=\"10\" style=\"font-family:Arial,Gotham, 'Helvetica Neue', Helvetica, sans-serif; font-size:12px; line-height:14px; color:#000001;\"></td>               </tr>             </table></td>         </tr>         <tr>           <td valign=\"top\" align=\"center\" bgcolor=\"#f76300\" height=\"80\"><table class=\"fullWidth\" width=\"600\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">               <tr>                 <td bgcolor=\"#f76300\" width=\"15\" height=\"80\"></td>                 <td class=\"fullWidthPaddingLeft\" bgcolor=\"#f76300\" width=\"570\" height=\"80\" style=\"font-family:Arial,Gotham, 'Helvetica Neue', Helvetica, sans-serif; font-size:30px; font-weight:bold; line-height:38px; color:#FFFFFF;\">[head]</td>                 <td bgcolor=\"#f76300\" width=\"15\" height=\"80\"></td>               </tr>             </table></td>         </tr>       </table></td>   </tr> </table", "base_4": "<table id=\"ArticleText\" width=\"100%\" bgcolor=\"#ececec\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">   <tr>     <td align=\"center\"><table class=\"fullWidth\" width=\"600\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">         <tr>           <td align=\"left\" valign=\"top\" width=\"15\"></td>           <td align=\"left\" valign=\"top\" style=\"font-family:Arial,Gotham, 'Helvetica Neue', Helvetica, sans-serif; font-size:14px; line-height:16px; color:#58585a; padding-top:20px; padding-bottom:30px;\"><content>[content]</content></td>           <td align=\"left\" valign=\"top\" width=\"15\"></td>         </tr>       </table></td>   </tr> </table> <table id=\"footer\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">   <tr>     <td align=\"center\" valign=\"top\"><table class=\"fullWidth\" width=\"600\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">         <tr>           <td align=\"left\" valign=\"top\"><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\">               <tr>                 <td class=\"hide\" width=\"15\"></td>                 <td width=\"570\" align=\"left\" valign=\"top\"><table class=\"floatPaddingLeft\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">                     <tr>                       <td colspan=\"7\" style=\"font-family:Arial,Gotham, 'Helvetica Neue', Helvetica, sans-serif; font-size:14px; line-height:18px; color:#f76300; padding-top:20px; padding-bottom:20px;\"> <a style=\"color: #f76300;text-decoration: underline;\" href=\"http://www.FocusFeedback.nl\" target=\"_blank\">© Focus Feedback</a></td>                     </tr>                   </table></td>                 <td class=\"hide\" width=\"15\"></td>               </tr>             </table></td>         </tr>       </table></td>   </tr> </table> </body> </html", "base_api": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\"><html xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\"><head> <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" /> <!--[if !mso]><!--> <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" /> <!--<![endif]--> <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /> <meta name=\"x-apple-disable-message-reformatting\" /> <title></title> <!--[if !mso]><!--> <link href=\"https://fonts.googleapis.com/css?family=Roboto:400,700\" rel=\"stylesheet\" /> <style type=\"text/css\"> @import url('https://fonts.googleapis.com/css?family=Poppins&display=swap'); table, td { font-family: 'Poppins', Roboto, Helvetica, Arial, sans-serif !important; } </style> <!--<![endif]--> <style type=\"text/css\"> html, body { Margin: 0; padding: 0; } table { border-collapse: collapse; } td { font-family: 'Poppins', Helvetica, Arial, sans-serif; color: #00325d; font-size: 16px; line-height: 21px; } a { color: #DC5422; text-decoration: none; } a img { border-style: none; } a[href^=\"x-apple-data-detectors:\"] { color: inherit; text-decoration: inherit; } a[href^=tel], a[href^=sms] { color: inherit; text-decoration: none; } p { Margin: 10px 0; } h1 { color: #40B2D9; font-weight: bold; line-height: 125%; } h2, h3, h4 { color: #202020; font-weight: bold; line-height: 125%; } h1 { font-size: 26px; } h2 { font-size: 22px; } h3 { font-size: 20px; } h4 { font-size: 18px; } </style> <!--[if gte mso 9]> <xml> <o:OfficeDocumentSettings> <o:AllowPNG/> <o:PixelsPerInch>96</o:PixelsPerInch> </o:OfficeDocumentSettings> </xml><![endif]--> <style type=\"text/css\"> @media screen and (min-width: 1px) { table.btn td { padding: 0 !important; } table.btn td a { display: block !important; padding: 15px 15px !important; } table.btn.btn-larger td { padding: 0 !important; } table.btn.btn-larger td a { display: block !important; padding: 11px 10px !important; } table.btn.btn-xl td { padding: 0 !important; } table.btn.btn-xl td a { display: block !important; padding: 15px 10px !important; } } @media only screen and (max-width: 480px) { h1 { font-size: 22px !important; line-height: 125% !important; } h2 { font-size: 20px !important; line-height: 125% !important; } h3 { font-size: 18px !important; line-height: 125% !important; } h4 { font-size: 16px !important; line-height: 150% !important; } } </style> <!-- Made with Virtuosoft's Bulletproof HTML Email Framework --></head><body> <table style=\"border-spacing:0;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;font-family: 'Poppins', Helvetica,Arial,sans-serif;color:#00325d;\" width=\"100%\"> <tr> <td style=\"padding:0;background-color:#ffffff;font-size:0;\"> </td> <td style=\"padding:0;width:600px;background-color:#ffffff;\" width=\"600\"> <table style=\"border-spacing:0;\" width=\"100%\"> <!--- Begin: header --> <tr> <td style=\"padding:0;font-size:0;text-align:center;\"> <table style=\"border-spacing:0;display:inline-table;max-width:100%;min-width:100%;vertical-align:middle;\" width=\"100%\"> <tr> <td style=\"padding:0;\"> <table style=\"border-spacing:0;\" width=\"100%\"> <tr> <td align=\"center\" style=\"padding:0;font-size:16px;line-height:21px;\"> <a href=\"https://focusfeedback.nl/en/\" style=\"color:#DC5422;text-decoration:none;\"><img src=\"https://enquete3.welcomeccs.nl/images/mail/WelcomeCCS/FFBanner.jpg\" width=\"600\" alt=\"\" style=\"border:0;display:block;max-width:100%;height:auto!important;\" /></a> </td> </tr> </table> </td> </tr> </table> </td> </tr> <!--- End: header --> <!--- Begin: maincontent --> <tr> <td style=\"padding:0;font-size:0;text-align:center;\"> <table style=\"border-spacing:0;display:inline-table;max-width:88%;min-width:88%;vertical-align:middle;\" width=\"88%\"> <tr> <td style=\"padding:0;\"> <table style=\"border-spacing:0;\" width=\"100%\"> <tr> <td style=\"font-size:14px;line-height:21px;text-align:left;padding:23px 0;\"> <table style=\"border-spacing:0;\" width=\"100%\"> <tr> <td style=\"font-size:14px;padding:0;text-align:left;\"> <content>[content]</content> </td> </tr> <tr> <td style=\"font-size:14px;padding:0;text-align:left;\"> <p>Best regards,<br /><br /> The Focus Feedback Team </p> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </td> </tr> <!--- End: maincontent --> </table> </td> <td style=\"padding:0;background-color:#ffffff;font-size:0;\"> </td> </tr> </table> <table style=\"border-spacing:0;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;font-family:'Poppins', Helvetica,Arial,sans-serif;color:#00325d;\" width=\"100%\"> <tr> <td style=\"padding:0;font-size:0;background-color:#FAFAFA;\"> </td> <td style=\"padding:0;width:600px;background-color:#FAFAFA;\" width=\"600\"> <table style=\"border-spacing:0;\" width=\"100%\"> <!--- Begin: icons --> <tr> <td style=\"padding:0;font-size:0;text-align:center;\"> <table style=\"border-spacing:0;display:inline-table;max-width:94%;min-width:94%;vertical-align:middle;\" width=\"94%\"> <tr> <td style=\"padding:0;\"> <table style=\"border-spacing:0;\" width=\"100%\"> <tr> <td align=\"center\" style=\"font-size:16px;line-height:21px;padding:30px 0;border-bottom:2px solid #EEEEEE;\"> <table style=\"border-spacing:0;width:auto;\"> <tr> <td style=\"text-align:left;padding:0 10px;\"> <table style=\"border-spacing:0;\" width=\"100%\"> <tr> <td style=\"padding:0;text-align:left;\"> <a href=\"https://twitter.com/focusfeedback\" style=\"color:#DC5422;text-decoration:none;\"><img src=\"https://enquete3.welcomeccs.nl/images/mail/WelcomeCCS/icon-twitter.png\" width=\"24\" alt=\"\" style=\"border:0;display:block;max-width:100%;height:auto!important;\" /></a> </td> <td style=\"padding:0 0 0 5px;font-size:12px;line-height:12px;text-align:left;\"> <a href=\"https://twitter.com/focusfeedback\" style=\"text-decoration:none;color:#656565;\">Focus Feedback</a> </td> </tr> </table> </td> <td style=\"text-align:left;padding:0 10px;\"> <table style=\"border-spacing:0;\" width=\"100%\"> <tr> <td style=\"padding:0;text-align:left;\"> <a href=\"https://www.focusfeedback.nl/\" style=\"color:#DC5422;text-decoration:none;\"><img src=\"https://enquete3.welcomeccs.nl/images/mail/WelcomeCCS/icon-website.png\" width=\"24\" alt=\"\" style=\"border:0;display:block;max-width:100%;height:auto!important;\" /></a> </td> <td style=\"padding:0 0 0 5px;font-size:12px;line-height:12px;text-align:left;\"> <a href=\"https://www.focusfeedback.nl/en\" style=\"text-decoration:none;color:#656565;\">Website</a> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </td> </tr> <!--- End: icons --> <!--- Begin: footer --> <tr> <td style=\"padding:0;font-size:0;text-align:center;\"> <table style=\"border-spacing:0;display:inline-table;max-width:94%;min-width:94%;vertical-align:middle;\" width=\"94%\"> <tr> <td style=\"padding:0;\"> <table style=\"border-spacing:0;\" width=\"100%\"> <tr> <td style=\"font-size:11px;line-height:18px;color:#656565;text-align:left;padding:20px 0;\"> <table align=\"left\" style=\"border-spacing:0;\" width=\"100%\"> <tr> <td style=\"font-size:11px;line-height:18px;color:#656565;text-align:left;padding:10px 0;\"> <i> Copyright 2019 - FocusFeedback</i><br /> You receive this mail, because you're a user within your company's Focus Feedback application.<br /><br /> <b>Contact Information:</b><br /> E-mail: <a href=\"mailto:<EMAIL>\"><EMAIL></a><br /> Phone: <a href=\"tel:00310135718844\">+31 (0) 13-571 88 44</a><br /><br /> Focus Feedback<br /> Ericssonstraat 2<br /> NL-5121 ML RIJEN<br /> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </td> </tr> <!--- End: footer --> </table> </td> <td style=\"padding:0;font-size:0;background-color:#FAFAFA;\"> </td> </tr> </table> <!--iosfix --></body></html>"}, "first_login": "<p>Welcome [firstname],</p> <p>&nbsp;</p> <p>You&rsquo;re about to start with Focus Feedback.</p> <p>&nbsp;</p> <p>Your username is [email].</p> <p>&nbsp;</p> <p>Take the first step and create your password via <u><a style=\"color: #40b1d9;\" href=\"[activation_link]\">this link.</a></u> Please note: the link is only valid for 24 hours.</p> <p>&nbsp;</p> <p>Would you like to log in in the future? No problem. You can easily log in via <u><a style=\"color: #40b1d9;\" href=\"[application_link]\">this link.</a></u> Tip: add <a style=\"color: #40b1d9;\" href=\"[application_link]\">[application_link]</a> to your favorites for quick access.</p> <p>&nbsp;</p> <p>With enthusiastic regards,</p> <p>&nbsp;</p> <p>Team Focus Feedback</p>", "forgotten": {"password": "<p>Hi [firstname],</p> <p>&nbsp;</p> <p>Forgot your password? No problem! It happens to the best of us.</p> <p>&nbsp;</p> <p>You can easily set your new password using <u><a style=\"color: #40b1d9;\" href=\"[activation_link]\">this link.</a></u></p> <p>&nbsp;</p> <p>But don't wait too long. This link is only valid for 24 hours.</p> <p>&nbsp;</p> <p>With enthusiastic regards,</p> <p>&nbsp;</p> <p>Team Focus Feedback</p>"}, "password": {"reset": "<p>Dear [name],</p> <p>Your password was successfully changed.</p> <p>Use username <b>[username]</b> and the following link to start Focus Feedback in the future:<br/> [link]</p> <p>With kind regards,<br/><br/>[sender]</p>"}, "send": {"password": "<p>Hi [firstname],</p> <p>Welcome to Focus Feedback.</p> <p>Your username is: <strong>[email]</strong></p> <p>First <a href=\"[activation_link]\">click here</a> to enter your personal password.<br/> But please don't wait too long; this link will expire after 24 hours</p> <p>For the next time you want to visit Focus Feedback: please <a href=\"[application_link]\">click here</a>.<br/> Or make a bookmark of [application_link]</p> <p>Best regards,<br/>[sender_firstname] [sender_lastname]</p>"}}, "mail_subject": {"link": "NPS - Focus Feedback link", "password": "Get Started with Focus Feedback - Activate Your Account"}, "menuitem": {"import": "Import", "newRecord": "New Record"}, "message": {"allsurveys": "The survey called \"All\" is not allowed for this review"}, "msg": {"activate_story_widget": "Are you sure you don't want to active the story widget", "activation_sent": "Congratulations! An activation link has been sent to the user", "activation_sent_error": "Error sending activation link to user", "active_subitems": "This Group item has active subitems, delete these first", "add_stopword": "Enter the new stop-word", "angular_unsubscribe": "Use angular for unsubscribe", "api_secret": "Secret generated on {0}", "api_secret_blank": "Press button to generate secret", "ask_new_clf_survey": "Survey {0} is currently the CLF Survey, do you want to replace", "ask_save": "Do you want to save your changes", "base_template_used_mail": "This basetemplate is connected to <b>{0}</b> and can't be removed. If you want to remove it, then first disconnect this basetemplate within the email template", "base_template_used_system": "This basetemplate is connected to <b>{0}</b> and can't be removed. If you want to remove it, then first disconnect this basetemplate within the system mailtemplate", "change_csvkey": "Are you sure you want to change CSV key", "classification": {"confirm_changeorder": "Changing the order will affect the classification for all processes. Are you sure?", "done": "You have finished labeling all the record", "instructions": "Corresponding answers will be shown below, when clicking on a bar in the chart"}, "company_notunique": "The company name is not unique", "confirm_add_stoplist": "Add selected words to stoplist", "confirm_add_topicslist": "Select the topi", "confirm_delete_optout": "Do you want to delete the Opt-out settings for {0}? Press ALL if you want to delete all Opt-out settings", "confirm_delete_smtp": "Do you want to delete the SMTP settings for {0}? Press ALL if you want to delete all custom SMTP settings", "confirm_new_callbackquestion": "Do you want to change the Callback question", "confirm_new_clfquestion": "Do you want to change the CLF question", "confirm_new_npsquestion": "Do you want to change the NPS question", "confirm_password_change": "Do you really want to change the password", "confirm_sheet": "What sheet do you want to process", "contact_already_exists": "The contact information already exists for this survey", "contactdeletenotgranted": "This contact should not be removed, because there is already sent a survey to the contact person", "copy_group": "Group has been copie", "customer_already_invited": "This customer is already invited for this survey", "customer_notunique": "The customer id is not unique", "dashboard": {"copy_link": "Copy the dashboard link below to use in an external applicatio"}, "default_language": {"deactivate": "You cannot deactivate the company default language for the mail templat"}, "delete_classification": "Do you want to delete the classification", "delete_classification_multi": "Do you want to delete these {0} classifications", "delete_currentuser": "You cannot delete this recor", "delete_employee": "Delete employee", "delete_employee_multi": "Do you want to delete these {0} employees", "delete_language": "Do you want to delete this language", "delete_mapping": "Do you want to delete this mapping record", "delete_mapping_multi": "Do you want to delete these {0} mapping records", "delete_multi_records": "Do you want to delete these {0} selected records", "delete_project_categories_multi": "Do you want to delete these {0} project categories", "delete_project_multi": "Do you want to delete these {0} projects", "delete_report": "Are you sure you want to remove this report", "delete_result": "{0} records delete", "delete_selected_rows": "Do you want to delete the selected rows", "delete_selectedstopwords": "Do you want to delete all selected words from the stoplist", "delete_stage": "Do you want to delete this stag", "delete_stopword": "Do you want to delete the following word from the stoplist: {0}", "delete_survey_categories_multi": "Do you want to delete these {0} survey categories", "delete_tag": "Do you want to delete the ta", "delete_tag_cl_feedback": "This tag is in use in CL feedback and can therefore not be delete", "delete_tags_multi": "Do you want to delete these {0} tags", "delete_template": "Do you want to delete the templat", "department_inuse": "Process already in use", "differenceInNumber": "The number of answers may differ from the number of answers displayed in the 'NPS segments' screen. This is because the answers with only stop words are not shown here", "duplicate_topic": "Topic already exist", "duplicate_topicname": "Topic name already in use", "emailInBlacklist": "{0} is already in the Blacklist for this Brand", "emailTypeExists": "Only one System mailtemplate is allowed for this type. Do you want to deactivate the old one", "emailUnknown": "Sorry. Your email address is not in our database. Contact your Focus Feedback administrator", "emailsent": "Emails sen", "error": {"answer_value_empty": "Answer value cannot be empty", "answer_value_other": "Answer value other is not allowed", "delete": {"category": "Cannot delete category, still in use"}, "executing": {"method": "Errors executing method. {0}"}, "testmode_emailrequired": "Test emailaddress and test phone number are required when test mode is activated"}, "expired_activationkey": "It looks like you waited to long to activate your account.<br/>Please contact your application manager for a new activation link", "export": {"error": "Error During Export", "failed": "Export failed", "success": "Export succesfull"}, "gdpr_min_length_phone": "Minimal length for phone number is 6 digit", "gdpr_month_change": "Changing the GDPR months will have consequences for the stored privacy data in Focus Feedback. Are you sure?", "gdpr_no_search_data": "Please enter an emailaddress or (part of) a phonenumber", "info": {"departmentEmpty": "You have not entered a department. Therefore, the survey can not be linked to a stage"}, "invalidImportValue": "The value of field '{0}' in line '{1}' is invalid. Correct the file and offers again", "invalid_activationkey": "It looks like you already used this activation key.<br/>You can enter your credentials here", "invalid_contact_type": "Type of contact must be entered", "invalid_date": "The date cannot be before 01-01-2010", "invalid_department_name": "You need a survey", "invalid_email": "The e-mail address does not seem to be valid. Do you really wish to use it", "invalid_employeenr": "Invalid employee number, empty or duplicate", "invalid_name": "Invalid name", "invalid_project": "Project number and Project name must both be filled in", "invalid_project_number": "The introduced project number already exists", "invalid_stage": "The combination contact type and sequence is already activ", "invalid_stage_order": "Please, enter an order. The order of an active stage must be known", "invalid_topicname": "Invalid topic name", "jasper": {"confirm": {"extern_pwd": {"change": "Are you sure you want to change the external Jasper password? NOTE: you will have to update all external dashboard links"}}}, "known_user_company": "This email address is already known within the company. We will use the original information of this colleague", "known_user_department": "This person is already known within {0}, the inputfield will be reset", "max_active_groups": "This process already has the maximum number of 14 active group", "max_active_items": "This Group already has the maximum number of 10 active item", "multi_select_available": "Multi-select is not available here, please select one survey", "multipledepartments": "There is already a survey linked to this process", "noDummyCompany": "BEWARE!     {0} is not one of the neutral base companies.   Are you sure you want to continue", "noSMSTemplate": "No template found for testSM", "no_department": "There is no process associated with this card", "no_email_template": "There is no 'Email Template' for this stage", "no_empty_list": "Empty list not allowed", "no_i18n": "Survey has no languages define", "no_records_to_select": "There are no records to select", "no_stages": "There are no stages for this contact type", "noperiod": "No period selecte", "object_already_exists": "The object already exists for this project", "object_identity_required": "You are required to provide a Buildnumber", "onNPS": "On NP", "onQuestionTemplate": "On question templat", "openChanges": "<html>There are unsaved changes, <b>save</b> or <b>discard</b> unsaved changes first</html", "optout-tag": "Use [email] as placeholder for emailaddress in redirect ur", "optout_cdc": "Use CDC optout lin", "process_inuse": "Process already in use", "processkey_inuse": "Process Key already in use", "projectdeletenotgranted": "This project should not be removed, because there are contacts linked to the project", "pwd": {"contains_username": "Password cannot contain username"}, "questionTypeRequired": "You are required to provide a Question Type", "rejected_values": "The following values are rejected", "reportsent": "The reports are sent. You can continue working", "required_fields": "Please complete all required fields", "required_phone_email": "Either email or phone must be entere", "required_questions": "The questi survey '{0}' cannot be used in multi-select mode because the survey doesn't have the mandatory questions connected", "reverse_order": "Do you wish to reverse the order of the rows", "salutation_already_exists": "The salutation already exists, do you want to add to this salutaion instead", "salutation_required": "You are required to provide a Salutation", "salutation_too_big": "The length of the salutation should not contain more than 40 positions", "search": {"noResults": "No results, do you want to try again without the current filter"}, "select_check_to_publish_question": "Select check to publish question", "select_report_grade": "Select report grad", "select_report_grade_question": "Select report grade question", "send_selected": "Send the selected invitations", "senderSMSToLong": "The value of 'SMS from' can be up to 16 characters if the value is numeric, or 11 characters if the value is alphanumeric", "senderSMSrequired": "'SMS from' is required if you want to use SMS", "sheet_processed": "Data processed. {0} records update", "sourcefieldError": "Something is wrong with source field '{0}'!  Check the file", "story_widget": {"copy": "Story widget HTML copied to clipboar"}, "succesfull": {"alter": {"user": "User details have been succesfully edited or added"}}, "survey_already_send": "A survey is already send out for this stage. Do you want the phase to be deactivated", "survey_linked_to_stage": "This survey may not be removed, because it is associated with one or more stages", "tagMissing": "The tag {0} is missing", "template_used": "This template is connected to <b>{0}</b> and can't be removed. If you want to remove it, then first disconnect this template within the survey creato", "tomanyperiods": "Unable to retrieve report. Try another period filter or smaller date periode", "toolTipErrorCode": "<html><p>Purple: unknown error</p> <p>Red: invalid email address</p> <p>Orange: threshold</p> <p>Gray: blacklist</p></html", "topic_already_exists": "The topic already exists, do you want to add the values to the existing topic instead", "topic_error": "Something went wrong while saving the data", "topic_is_topicitem": "You can not add this topic because the topic already exist as a value in another topic", "topicitem_already_exists": "{0} is a value of topic {1}", "topicitem_is_topic": "{0} exist as topic {1}", "url_start_http": "The url must start with http:// or https:/", "user_locked": "User locke", "user_unlocked": "User is unlocked and a new activation mail has been sent", "word_is_topic": "{0} is a topic, do you want to merge this topic into {1}"}, "nav": {"basetemplates": "Base template", "bsh_categories": "BSH categorie", "bsh_category": "BSH categor", "emailTemplate": "<PERSON><PERSON>", "frontend": "Ne", "hotnews": "Hot new", "inspirationcenter": "Inspiration Cente", "mailingList": "Mailing Lis", "surveyCreator": "Survey Creato", "tables": "Table"}, "new_answer_text": "**** There are still missing 'Answer' texts. ***", "new_email_template_text": "**** There are still missing 'Email template' texts. ***", "new_language": "**** This field must be filled. ***", "new_question_text": "**** There are still missing 'Question' texts. ***", "new_stopword": "New stop-wor", "new_survey_text": "**** There are still missing 'Survey' texts. ***", "new_topic": "new topi", "report": {"appliances": "Appliance", "nrofvisits": "No. of visit", "organisation": "Organizatio", "waitingtime": "Waiting tim"}, "salutation": {"family": "famil", "mme": "mr", "mmes": "ladie", "sir": "si", "sir_mme": "sir/mr", "sirs": "gentleme"}, "session": {"3_times_logged_in": "3 times logged i", "dlg": {"3_times_logged_in": "You are already logged in 3 tree times push 'Continue' to close the oldest session"}}, "sms": {"ali": {"keyid": "Key I", "secret": "Secre", "templatecode": "Template Cod"}}, "tooltip": {"change_extern_pwd": "Change password for user Exter", "dashboard_external_link": "Copy external link to clipboar"}, "vl": {"admins": "Administrator", "b2b": "Organizatio", "b2c": "O<PERSON>up<PERSON>", "clf_field1": "Improvement", "clf_field2": "Strength", "dashboard": {"gauge": "Show as dashboard gaug", "hide": "Do not use on dashboar", "overall": "Use in dashboard gauge Genera"}, "day": "Da", "export_blacklist": "<PERSON><PERSON>", "export_responses": "Response", "mailtype": {"assign_dashboard": "Assign dashboar", "clf-assign": "Assign-mai", "clf-result": "Result-mai", "feedback_notification": "Feedback notification-mai", "followup": "Followup mai", "notification": "Notification-mai", "reportmailer": "Reportmaile"}, "month": "Mont", "neg_classification": "Negativ", "no": "N", "pos_classification": "Positiv", "quarter": "Quarte", "reporttype": {"adhoc": "<PERSON><PERSON>", "fixed": "Fixe", "unknown": "Unknow"}, "respondent_firstname": "Firstname / Initia", "respondent_fullname": "<PERSON><PERSON>", "respondent_lastname": "<PERSON><PERSON>", "test_mode": {"compact": "Compact test mod", "deactive": "Normal mod", "full": "Full test mod"}, "users": "User", "week": "Wee", "year": "<PERSON>a", "yes": "Ye"}, "word": {"has": "ha"}}, "clf": {"vl": {"mailtype": {"assign": "Assign-mai", "result": "Result-mai"}}}, "css": {"caption": {"contacttype": "Type of contac", "settingtraject": "Setting traject"}, "html": {"columnHeader": "<html>   <head>   </head>   <body>     <table cellspacing=\"0\" border=\"0\" cellpadding=\"2\">       <tr>         <td width=\"10000\" align=\"{0}\" height=\"16\" valign=\"bottom\" nowrap>           {1}         </td>       </tr>     </table>   </body> </html"}, "lbl": {"benchmark": "Benchmark", "code": "Cod", "employee": "Employe", "firstvisit": "Your first time here", "ftpUsername": "FTP User Name", "participates_in_benchmark": "Participates in benchmar", "searchuser": "Name or emai", "sms_api": "Provide", "sms_code": "Cod", "sms_handle": "<PERSON><PERSON>", "sms_password": "Password", "sms_username": "Username"}, "msg": {"csi": {"responses": "CSI Responses current yea"}, "more_100_records": "More than 100 records found, loading can be very slow, do you just want to load the first 100 records", "topic_group": "Give the name for the new topi"}}, "servoy": {"button": {"browse": "Brows", "cancel": "<PERSON><PERSON>", "close": "<PERSON><PERSON>", "copy": "<PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON>", "finish": "<PERSON><PERSON>", "next": "Nex", "ok": "O", "prev": "pre", "quit": "qui"}, "general": {"confirm": "Confirm", "info": "Info", "preview": "Preview", "status": {"ready": "Ready"}}, "menuitem": {"excel": "Excel", "newRecord": "New Record", "pdf": "PDF"}, "plugin": {"import": {"status": {"doneImporting": "Importing done: {0} Rows imported", "rowsImported": "Importing rows: {0} Rows imported"}}}}, "survey": {"btn": {"back": "Back", "close": "Close", "next": "Next", "send": "Send", "send_now": "Send now"}, "lbl": {"required_fields": "Required Field"}, "msg": {"placeholdertxt": "Write your answer here ..", "required_fields": "Please complete all required field"}, "search": {"placeholdertxt": "Search here ..."}}}