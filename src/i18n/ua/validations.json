{"isObject": "{property} має бути об'єктом", "isNotEmpty": "{property} не повинно бути порожнім", "isString": "{property} має бути текстовим рядком", "isEmail": "{property} має бути адресою електронної пошти", "minLength": "{property} має містити щонайменше {min} символів", "maxLength": "{property} має містити не більше {max} символів", "min": "{property} має бути {min} або більше", "max": "{property} має бути {max} або менше", "length": "{property} має бути довжиною від {min} до {max} символів", "matches": "{property} має відповідати: {matches}", "isNumber": "{property} має бути числом", "isInt": "{property} має бути цілим числом", "isPositive": "{property} має бути додатним числом", "isNegative": "{property} має бути від'ємним числом", "isArray": "{property} має бути масивом", "arrayNotEmpty": "{property} не повинно бути порожнім", "arrayMinSize": "{property} має містити щонайменше {min} елементів", "arrayMaxSize": "{property} має містити не більше {max} елементів", "arrayUnique": "{property} має містити унікальні значення", "isBoolean": "{property} має бути логічним значенням", "isDate": "{property} має бути датою", "isDateString": "{property} має бути дійсною датою у форматі ISO 8601", "isUUID": "{property} має бути UUID", "isUrl": "{property} має бути URL-адресою", "isEnum": "{property} має бути одним із наступних значень: {values}", "isPhoneNumber": "{property} має бути дійсним номером телефону"}