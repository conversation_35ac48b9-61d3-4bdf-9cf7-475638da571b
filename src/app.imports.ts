import { join } from 'path';
import * as crypto from 'crypto';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { ScheduleModule } from '@nestjs/schedule';
import { ServeStaticModule } from '@nestjs/serve-static';
import { PuppeteerModule } from 'nestjs-puppeteer';

import { I18nModule } from 'nestjs-i18n';

// Make crypto available globally
(global as any).crypto = crypto;

import { GoogleRecaptchaModule } from '@nestlab/google-recaptcha';

import * as modules from './modules';
import adminModules from './modules/admin';
import serveStaticConfig from './config/module/serve-static.config';
import typeormConfig from './config/module/typeorm.config';
import i18nConfig from './config/module/i18n.config';
import googleRecaptchaConfig from './config/module/google-recaptcha.config';
import multerConfig from './config/module/multer.config';
import { DBHelperModule } from './modules/core/db-helper/db-helper.module';
import { env } from './app.env';
import { ClsModule } from 'nestjs-cls';
import { TranslateModule } from './core/modules/translate/translate.module';
import { v4 } from 'uuid';
import { ModuleLoggerModule } from '@lib/logger/module-logger/module-logger.module';
import { WorkersModule } from './workers/workers.module';
import { SwaggerCoreModule } from './libs/swagger';

export default [
  // replace  with env.config, remove configService.get from all
  ConfigModule.forRoot({
    envFilePath: join(env.APP_PATH, '.env'),
  }),
  TypeOrmModule.forRoot(typeormConfig),
  I18nModule.forRoot(i18nConfig),
  TranslateModule,
  ClsModule.forRoot({
    global: true,
    middleware: { mount: true, generateId: true, idGenerator: () => v4() },
  }),
  SwaggerCoreModule,
  GoogleRecaptchaModule.forRoot(googleRecaptchaConfig),
  MulterModule.register(multerConfig),
  ScheduleModule.forRoot(),
  ServeStaticModule.forRootAsync(serveStaticConfig),
  PuppeteerModule.forRoot({
    headless: true,
    userDataDir: join(env.APP_PATH, 'tmp/browser'),
    dumpio: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-extensions',
      '--disable-storage-reset=true',
      '--disable-component-extensions-with-background-pages',
      '--disable-background-networking',
      '--disable-default-apps',
      '--disable-sync',
      '--disable-translate',
      '--metrics-recording-only',
      '--no-default-browser-check',
      '--no-first-run',
    ],
  } as any),
  DBHelperModule,
  ModuleLoggerModule.register('general'),
  WorkersModule,
  ...Object.values(modules),
  ...adminModules,
];
