import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ClsService } from 'nestjs-cls';
import { AuthService } from '../../modules/auth/auth.service';
import { AuthorizedData } from '../../modules/auth/interfaces/authorized-request.interface';
import { ADMIN_COMPANIES } from '@/config/contents/admin.contents';
import { PrivilegeService } from '../../modules/auth/privilege.service';
import { ValidateUserService } from '@/modules/auth/validate-user.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly cls: ClsService,
    private reflector: Reflector,
    private readonly validateUserService: ValidateUserService,
    private readonly privilegeService: PrivilegeService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const { authorization }: any = request.headers;
    if (!authorization || authorization.trim() === '') {
      throw new UnauthorizedException('Please provide token');
    }
    const authToken = authorization.replace(/bearer/gim, '').trim();

    const result = await this.validateUserService.validate(authToken, true);

    if (!result) throw new UnauthorizedException('UNAUTHORIZED');

    if (!this.checkAdmin(context, result))
      throw new ForbiddenException('ACCESS_DEINED');

    const keys = this.checkKeys(context, result);
    if (!keys) throw new ForbiddenException('ACCESS_DEINED');

    request.user = result.user;
    request.company = result.company;

    this.cls.set('user', { ...result, keys });

    return true;
  }

  private checkAdmin(ctx: any, { company }: AuthorizedData) {
    const isAdmin = this.reflector.get<boolean>('isAdmin', ctx.getHandler());

    if (!isAdmin) return true;

    return ADMIN_COMPANIES.includes(+company.id);
  }

  private async checkKeys(ctx: any, { user, company }: AuthorizedData) {
    const names = this.reflector.get<string[]>('keys', ctx.getHandler());

    if (!names) return true;

    let keys = await this.privilegeService.filteredKeys({
      userId: user.id,
      companyId: company.id,
      filter: { names: names },
    });

    for (let name of names) {
      if (!keys.includes(name))
        throw new HttpException(`User don't have ${name} privilege`, 403);
    }

    return keys;
  }
}
