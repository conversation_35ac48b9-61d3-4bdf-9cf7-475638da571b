import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ClsService } from 'nestjs-cls';
import { I18nContext, I18nMiddleware } from 'nestjs-i18n';

I18nMiddleware;
@Injectable()
export class LanguageMiddleware implements NestMiddleware {
  constructor(private cls: ClsService) {}
  use(req: Request, res: Response, next: NextFunction) {
    this.cls.set('lang', I18nContext.current()?.lang || 'en');
    next();
  }
}
