export enum EmployeeType {
  TECHNICIAN = 'xml_technician',
  DOB = 'xml_acceptance_by',
  PREPEPPER = 'xml_wvb',
  DISPATCHER = 'xml_planning',
  EMPLOYEE = 'xml_employee',
  SERVICE_PARTNER = 'xml_custcreditnr',
  DEALER = 'xml_dealer',
}

export enum BSHMapping {
  REGION = 'xml_region',
  TECHGRP = 'xml_techgrp',
  PRODAREA = 'xml_prodarea',
  PRODDIVISION = 'xml_proddivision',
  PAYMENT = 'xml_payment',
  CP = 'xml_cp',
  SV = 'xml_sv',
  ORDER_CHANNEL = 'xml_orderchannel',
  SALES_CHANNEL = 'xml_saleschannel',
}

// export type BSHMappingType = EnumValues<typeof BSHMapping>;
// export type TechgrpType = EnumValues<typeof TechgrpEnum>;
