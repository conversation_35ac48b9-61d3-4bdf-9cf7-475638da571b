import {
  PipeTransform,
  Injectable,
  Scope,
  ValidationPipe,
  BadRequestException,
} from '@nestjs/common';

@Injectable({ scope: Scope.REQUEST })
export class InjectRequestValidationPipe implements PipeTransform {
  private i18nValidationPipe;
  constructor() {
    this.i18nValidationPipe = new ValidationPipe({
      transform: true,
      validationError: {
        target: false,
        value: false,
      },

      exceptionFactory: (errors) => {
        const messages = this.filterErrors(errors);
        return new BadRequestException(messages);
      },
    });
  }

  filterErrors(errors, key = null) {
    return errors.reduce((prev, error) => {
      let property = error.property;
      if (key) property = `${key}.${property}`;

      if (error?.children?.length) {
        prev = [...prev, ...this.filterErrors(error.children, property)];
      } else {
        for (let messageKey of Object.keys(error.constraints)) {
          let contexts = Object.entries(error.contexts || {}).reduce(
            (prev, [key, val]: any) => {
              return {
                ...prev,
                [key]: val?.[0] ? Object.values(val).join('') : val?.value,
              };
            },
            {},
          );
          prev.push({
            key: messageKey,
            args: { property, ...contexts },
            constraints: error.constraints,
          });
        }
      }

      return prev;
    }, []);
  }

  async transform(value: any, metadata: any) {
    return await this.i18nValidationPipe.transform(value, metadata);
  }
}
