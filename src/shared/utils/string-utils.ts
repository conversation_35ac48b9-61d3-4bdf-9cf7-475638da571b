export const stringCapitalize = (variable: string) => {
  let words = variable.split(/\b/);
  let capitalizedWords = words.map((word) => {
    if (/[a-zA-Z]/.test(word[0])) {
      return word.charAt(0).toUpperCase() + word.slice(1);
    } else {
      return word;
    }
  });
  return capitalizedWords.join('');
};

export const stringPatternCount = (string: string, word: string) => {
  return string.split(word).length - 1;
};

export function escapeDoubleQuotes(str) {
  return str.replace(/"/g, '\\"');
}

export function numberFormat(number, digits = 2, decimalSeparator = ',') {
  if (!number) return 0;
  return number
    .toLocaleString(undefined, {
      minimumFractionDigits: digits,
      maximumFractionDigits: digits,
    })
    .replace('.', decimalSeparator);
}

export function floatFixed(number, digits = 2) {
  if (!number) return 0;
  return parseFloat(number.toFixed(digits));
}

export function hexToRgba(hex, opacity) {
  // Remove the '#' character if it's included in the input
  hex = hex.replace(/^#/, '');

  // Parse the hex values for red, green, and blue components
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  // Ensure opacity is a valid number between 0 and 1
  opacity = Math.min(1, Math.max(0, opacity));

  // Return the RGBA color as a string
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

export function between(number, min, max) {
  return number >= min && number <= max;
}
