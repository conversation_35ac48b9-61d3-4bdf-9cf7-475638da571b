export function groupNested(
  list: any[],
  primaryKey: string = 'id',
  parentKey: string = 'parent_id',
  childKey: string = 'children',
  removeParentKey: boolean = false,
) {
  let group = groupBy(list, parentKey, removeParentKey);
  return childrenOf(group, '0', primaryKey, childKey);
}

export function childrenOf(
  group: any,
  parentId: string,
  primaryKey: string = 'id',
  childKey: string = 'children',
) {
  return (group[parentId] || []).map((item) => ({
    ...item,
    [childKey]: [
      ...childrenOf(group, item[primaryKey], primaryKey, childKey),
      ...(item[childKey] || []),
    ],
  }));
}

export function groupBy(
  list: any[],
  key: any,
  removeParentKey: boolean = false,
) {
  return list.reduce((rv, x) => {
    let uniqueVal = x[key];
    if (uniqueVal === null) uniqueVal = 0;
    if (removeParentKey) delete x[key];
    (rv[uniqueVal] = rv[uniqueVal] || []).push(x);
    return rv;
  }, {});
}

export function hasDuplicates(arr: any[]): boolean {
  // Create a new Set from the array, which only keeps unique values
  const uniqueSet = new Set(arr);

  // If the size of the Set is smaller than the original array's length,
  // it means there were duplicates
  return uniqueSet.size !== arr.length;
}

export function findDuplicates(arr: any[]): any[] {
  const counts: any = {};
  const duplicates = [];

  // Count the occurrences of each element in the array
  for (const item of arr) {
    counts[item] = (counts[item] || 0) + 1;
  }

  // Add elements with count greater than 1 to the duplicates array
  for (const item in counts) {
    if (counts[item] > 1) {
      duplicates.push(item);
    }
  }

  return duplicates;
}

export function chunkArray<T>(array, chunkSize) {
  const chunkedArray = [];

  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    chunkedArray.push(chunk);
  }

  return chunkedArray as Array<T[]>;
}
