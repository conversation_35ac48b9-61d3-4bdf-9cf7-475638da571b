import { readFileSync } from 'fs';
import { join } from 'path';
import { parseJ<PERSON><PERSON> } from './parseJSON';
import { env } from '../../app.env';
export class LanguageService {
  getFile(lang, path) {
    let content = readFileSync(
      join(env.APP_PATH, `dist/i18n/${lang}/${path}.json`),
    ).toString();
    content = parseJSON(content);
    return {
      pick(key) {
        return content[key];
      },
    };
  }
  getItem(lang, path, key) {
    let content = readFileSync(
      join(env.APP_PATH, `dist/i18n/${lang}/${path}.json`),
    ).toString();
    content = parseJSON(content);
    return content[key];
  }
  pickItem(content, key) {
    return content[key];
  }
}
