import { v4 as uuidv4 } from 'uuid';
import { queryConvert } from './query-convert';
import {
  Connection,
  Query<PERSON><PERSON>er,
  <PERSON>ry<PERSON><PERSON><PERSON>,
  SelectQueryBuilder,
} from 'typeorm';

export function convertToInsert(
  list: any[],
  returning?: string,
): [string, any] {
  if (list.length == 0) return [null, null];
  let obj = list[0];
  if (!returning) returning = obj.keyField;
  let keys = Object.keys(obj.data).map((key) => `"${key}"`);
  let items = [];
  let params = {};
  for (let item of list) {
    let uniqueKey = uuidv4().replace(/\-/g, '');
    let values = [];
    for (let [key, value] of Object.entries(item.data)) {
      let k = key + uniqueKey;
      params[k] = value;
      values.push(`:${k}`);
    }
    items.push(`(${values.join(' , ')})`);
  }
  if (items.length == 0) return [null, null];
  let sql = `INSERT INTO ${obj.table}  (${keys.join(',')}) VALUES ${items.join(
    ',',
  )}`;

  if (returning) sql += ` RETURNING "${returning}"`;
  return queryConvert(sql, params);
}

export function convertToUpdate(
  table: string,
  keyFields: string[] = [],
  data: any,
  returning?: string,
): [string, any] {
  let uniqueKey = uuidv4().replace(/\-/g, '');

  let params = {};
  let values = [];
  let where = [];
  for (let [key, value] of Object.entries(data)) {
    let k = key + uniqueKey;
    params[k] = value;
    values.push(`${key} =:${k}`);
  }
  for (let i in keyFields) {
    let keyField = keyFields[i];
    let whereKey = 'w' + keyField + uniqueKey;
    params[whereKey] = data[keyField];
    where.push(`${keyField} =:${whereKey}`);
  }
  let sql = `UPDATE ${table} SET ${values.join(' , ')} WHERE ${where.join(
    ' AND ',
  )}`;

  if (returning) sql += ` RETURNING "${returning}"`;

  return queryConvert(sql, params);
}

export function convertToDelete(
  list: any[],
  keyFields: string[],
): [string, any] {
  if (!list.length) return null;

  let params = {};
  let where = [];

  for (let item of list) {
    let uniqueKey = uuidv4().replace(/\-/g, '');
    let itemWhere = [];
    for (let i in keyFields) {
      let keyField = keyFields[i];
      let whereKey = 'w' + keyField + uniqueKey;
      params[whereKey] = item.data[keyField];
      itemWhere.push(`${keyField} =:${whereKey}`);
    }
    where.push(`(${itemWhere.join(' AND ')})`);
  }

  let sql = `DELETE FROM ${list[0].table} WHERE ${where.join(' OR ')}`;

  return queryConvert(sql, params);
}

export function nonParameterizedQuery(query: string, values: any[] = []) {
  return query.replace(/\$[0-9]+/g, (match) => {
    const index = parseInt(match.slice(1)) - 1;
    let value = values[index];
    if (value?.toISOString !== undefined) {
      value = value.toISOString();
    }
    return `'${value}'`;
  });
}

export function withItemAS<T>(params: {
  qb: QueryBuilder<T>;
  alias: string;
  query: string;
  parameters?: any;
}) {
  const { qb, alias, query, parameters } = params;
  const originalQuery = qb.getQuery;
  const originalParams = qb.getParameters;

  qb.getQuery = () => {
    const a = originalQuery.call(qb);
    return `WITH ${alias} AS (${query}) ${a}`;
  };
  qb.getParameters = () => {
    const a = originalParams.call(qb);
    if (parameters) {
      return { ...a, ...parameters };
    }
    return a;
  };

  return qb;
}
