import { HttpException } from '@nestjs/common';

export const enum2obj = (data: any) => {
  const obj = {} as { [key: string]: string };

  for (const k in data) {
    const v = data[k as keyof typeof data];
    if (typeof v === 'string') {
      obj[k] = v;
    }
  }
  return obj;
};
export const enum2objrevert = (data: any) => {
  const obj = {} as { [key: string]: string };

  for (const k in data) {
    const v = data[k as keyof typeof data];
    if (typeof v !== 'string') {
      obj[k] = v;
    }
  }
  return obj;
};
