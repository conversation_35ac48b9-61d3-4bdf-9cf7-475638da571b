import { QueryBuilder, SelectQueryBuilder } from 'typeorm';
import { stringPatternCount } from './string-utils';

type QueryReducerArray = [string, any[], number];
export function queryConvert(
  parameterizedSql: string,
  params: any,
): [string, any[]] {
  const [text, values] = Object.entries(params).reduce(
    ([sql, array, index], [key, value]) => {
      let arr = array;
      if (stringPatternCount(sql, `:${key}`)) {
        arr.push(value);
      }
      return [
        sql.replace(`:${key}`, `$${index}`),
        arr,
        index + 1,
      ] as QueryReducerArray;
    },
    [parameterizedSql, [], 1] as QueryReducerArray,
  );
  return [text, values];
}

export function convertJsonAgg(params: { [key: string]: string }): string {
  const fields = Object.entries(params).map(
    ([key, value]) => `'${key}', ${value}`,
  );
  return `json_agg(json_build_object(${fields.join(', ')}))`;
}

export function aliasPrefix(list: string[], alias: string = '') {
  return list.map((item) => {
    if (item.includes(' AS ') || item.includes(' as ')) {
      return item;
    } else {
      return `${item} AS ${alias.length ? `${alias}_` : ''}${
        item.split('.')[1] || item
      }`;
    }
  });
}

export function alias(...list: string[]) {
  return list.map((item) => {
    if (item.includes(' AS ') || item.includes(' as ')) {
      return item;
    } else {
      return `${item} AS ${item.split('.')[1] || item}`;
    }
  });
}

export function rawQuery(qb: SelectQueryBuilder<any>) {
  const [sql, parameters] = qb.getQueryAndParameters();

  let interpolatedSql = sql;
  parameters.forEach((paramValue, index) => {
    interpolatedSql = interpolatedSql.replace(
      `$${index + 1}`,
      typeof paramValue === 'string' ? `'${paramValue}'` : paramValue,
    );
  });

  return interpolatedSql;
}
