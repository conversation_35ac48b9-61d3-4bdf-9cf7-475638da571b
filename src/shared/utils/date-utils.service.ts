import * as moment from 'moment';
import { momentTz } from './moment-tz';

export type DatePeriod = 'year' | 'month' | 'quarter' | 'week' | 'day';

export class DateUtilsService {
  public isDynamicDate(fromDate: string): boolean {
    return fromDate.split(' ').length === 2;
  }
  public formatDates({
    fromDate,
    toDate,
  }: {
    fromDate: string;
    toDate: string;
  }): { fromDate: moment.Moment; toDate: moment.Moment } {
    const now = moment().utc().endOf('day');
    if (this.isDynamicDate(fromDate)) {
      let [periodAmount, periodUnit] = fromDate.split(' ') as any;
      periodAmount = +periodAmount;
      switch (periodUnit) {
        case 'days':
          return {
            fromDate: moment()
              .utc()
              .startOf('day')
              .add(1 - periodAmount, periodUnit),
            toDate: now,
          };
        case 'weeks': {
          // Old Version (maybe in other places it can work like this)
          // let fromDate = momentTz()
          //   .add(-periodAmount, 'weeks')
          //   .startOf('day')
          //   .startOf('isoWeek');

          // return {
          //   fromDate: fromDate,
          //   toDate: now,
          // };

          let fromDate = now
            .clone()
            .startOf('isoWeek')
            .add(-(periodAmount - 1), 'weeks');

          return {
            fromDate,
            toDate: now,
          };
        }
        case 'months':
          return {
            fromDate: moment()
              .utc()
              .startOf('day')
              .startOf('month')
              .add(1 - periodAmount, periodUnit),
            toDate: now,
          };
        case 'years': {
          return {
            fromDate: moment()
              .utc()
              .startOf('months')
              .startOf('years')
              .add(1 - periodAmount, periodUnit),
            toDate: now,
          };
        }
        case 'quarters': {
          return {
            fromDate: this.getLastQuarter(periodAmount - 1),
            toDate: now,
          };
        }
      }
    }

    switch (toDate) {
      case 'current_day':
        return {
          fromDate: moment().utc().startOf('day'),
          toDate: moment().utc().endOf('day'),
        };
      case 'last_day':
        return {
          fromDate: moment().utc().subtract(1, 'day').startOf('day'),
          toDate: moment().utc().subtract(1, 'day').endOf('day'),
        };
      case 'current_week':
        return {
          fromDate: moment().utc().startOf('isoWeek'),
          toDate: moment().utc().endOf('day'),
        };
      case 'last_week':
        return {
          fromDate: moment().utc().subtract(1, 'week').startOf('isoWeek'),
          toDate: moment().utc().subtract(1, 'week').endOf('isoWeek'),
        };
      case 'current_month':
        return {
          fromDate: moment().utc().startOf('month'),
          toDate: moment().utc().endOf('day'),
        };
      case 'last_month':
        return {
          fromDate: moment().utc().subtract(1, 'month').startOf('month'),
          toDate: moment().utc().subtract(1, 'month').endOf('month'),
        };
      case 'current_year':
        return {
          fromDate: moment().utc().startOf('year'),
          toDate: moment().utc().endOf('day'),
        };
      case 'last_year':
        return {
          fromDate: moment().utc().subtract(1, 'year').startOf('year'),
          toDate: moment().utc().subtract(1, 'year').endOf('year'),
        };
      case 'current_quarter': {
        let date = moment();
        let month = date.get('month');
        let minusMonth = 0;
        if ([0, 3, 6, 9].includes(month)) minusMonth = 0;
        else if ([1, 4, 7, 10].includes(month)) minusMonth = -1;
        else if ([2, 5, 8, 11].includes(month)) minusMonth = -2;

        return {
          fromDate: date.utc().startOf('month').add(minusMonth, 'month'),
          toDate: moment().endOf('day'),
        };
      }
      case 'last_quarter': {
        let date = moment();
        let month = moment().get('month');
        let minusMonth = 0;
        if ([0, 3, 6, 9].includes(month)) minusMonth = -3;
        else if ([1, 4, 7, 10].includes(month)) minusMonth = -4;
        else if ([2, 5, 8, 11].includes(month)) minusMonth = -5;

        return {
          fromDate: date.utc().startOf('month').add(minusMonth, 'month'),
          toDate: moment(date).add(2, 'month').endOf('month'),
        };
      }
    }

    return {
      fromDate: moment.utc(fromDate).startOf('day'),
      toDate: moment.utc(toDate).endOf('day'),
    };
  }

  getLastQuarter(num: number): moment.Moment {
    const today = moment().utc();
    const currentQuarter = Math.ceil((today.month() + 1) / 3);
    let lastQuarter = currentQuarter - num - 1;

    let year = today.year();
    if (lastQuarter < 0) {
      year--;
      lastQuarter += 4;
    }

    const quarterStartMonth = lastQuarter * 3;
    const quarterStartDate = moment([year, quarterStartMonth])
      .utc()
      .endOf('day')
      .add(1, 'seconds');

    return quarterStartDate;
  }

  public getPeriod(date: moment.Moment, period: DatePeriod) {
    if (period == 'day') {
      return date.format('ddd');
    } else if (period == 'week') {
      return date.format('MMM');
    } else if (period == 'quarter') {
      return date.quarter();
    } else if (period == 'year') {
      return date.format('YYYY');
    }
  }

  public getPeriodLabel(dt, period, labelsLength = 12) {
    let date = moment(dt);
    switch (period) {
      case 'day':
        return (
          date.format('D') + '-' + date.format('MMM') + ' - ' + date.format('Y')
        );
        break;
      case 'week':
        let year = +date.format('Y');
        if (date.format('MM') == '12' && date.isoWeek() == 1) {
          year++;
        }
        return 'wk' + date.isoWeek() + '-' + year;
        break;
      case 'month':
        if (labelsLength > 12) {
          return date.format('MMM YY');
        } else {
          return date.format('MMMM');
        }
        break;
      case 'quarter':
        return date.format('Y [Q]Q');
        break;
      case 'year':
      default:
        return date.format('Y');
        break;
    }
  }

  public getPeriods(
    fromDate: string | Date | moment.Moment,
    toDate: string | Date | moment.Moment,
    periodType: 'day' | 'week' | 'month' | 'quarter' | 'year',
  ): Date[] {
    const periods: Date[] = [];
    let current = moment.utc(fromDate);
    let previous;
    while (current <= moment.utc(toDate)) {
      switch (periodType) {
        case 'day':
          periods.push(current.toDate());
          previous = current.date();
          current.date(previous + 1);
          break;
        case 'week':
          periods.push(current.toDate());
          previous = current.date();
          current.date(previous + 7);
          break;
        case 'month':
          periods.push(current.startOf('month').toDate());
          previous = current.month();
          current.month(previous + 1);
          break;
        case 'year':
          periods.push(current.startOf('year').toDate());
          previous = current.year();
          current.year(previous + 1);
          break;
        case 'quarter':
          const quarterMonth = Math.floor(current.month() / 3) * 3;
          periods.push(current.startOf('month').month(quarterMonth).toDate());
          previous = quarterMonth;
          current.month(previous + 3);
          break;
        default:
          throw new Error(`Invalid period type: ${periodType}`);
      }
    }
    return periods;
  }
}
