export function compareListItems<T>(
  allItems: T[],
  items: T[],
  fields: (keyof T)[],
): {
  updatedItems: T[];
  createdItems: T[];
  deletedItems: T[];
} {
  const allItemIds = allItems.map((item) =>
    fields.map((field) => item[field]).join('-'),
  );
  const updatedItems = [];
  const createdItems = [];
  const deletedItems = [];

  for (const item of items) {
    const itemId = fields.map((field) => item[field]).join('-');
    if (allItemIds.includes(itemId)) {
      const index = allItemIds.indexOf(itemId);
      const originalItem = allItems[index];
      let hasChanged = false;

      for (const field in item) {
        if (item[field] !== originalItem[field]) {
          hasChanged = true;
          break;
        }
      }

      if (hasChanged) {
        updatedItems.push(item);
      }
    } else {
      createdItems.push(item);
    }
  }

  for (const allItem of allItems) {
    const allItemId = fields.map((field) => allItem[field]).join('-');
    if (
      !items.find(
        (item) => allItemId === fields.map((field) => item[field]).join('-'),
      )
    ) {
      deletedItems.push(allItem);
    }
  }

  return { updatedItems, createdItems, deletedItems };
}
