import { applyDecorators, UseInterceptors } from '@nestjs/common';
import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Response } from 'express';

@Injectable()
export class ExternalCorsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const response = context.switchToHttp().getResponse<Response>();
    
    // Set CORS headers for external API access
    response.header('Access-Control-Allow-Origin', '*');
    response.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
    response.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With');
    response.header('Access-Control-Allow-Credentials', 'false');
    
    return next.handle();
  }
}

/**
 * Decorator to enable permissive CORS for external API endpoints
 * Use this for endpoints that need to be accessed by external applications
 */
export function ExternalCors() {
  return applyDecorators(
    UseInterceptors(ExternalCorsInterceptor)
  );
}
