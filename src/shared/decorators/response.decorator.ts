import { applyDecorators } from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';

export const ApiRes = (...schemas: any[]) => {
  return applyDecorators(
    ApiResponse({
      status: schemas[0].code,
      content: {
        'application/json': {
          examples: schemas.reduce((list, schema) => {
            let dto = new schema();
            list[dto.message || schema.code] = { value: dto };
            return list;
          }, {}),
        },
      },
    }),
  );
};
