import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsDefined, IsInt, IsOptional, Min } from 'class-validator';
import { BadRequestException } from '@nestjs/common';

export class PaginationDto {
  @ApiProperty({
    description: 'Page number (0-based or 1-based depending on configuration)',
    type: Number,
    default: 1,
    required: false,
  })
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === null ? 0 : value;
  })
  page: number = 0;

  @ApiProperty({
    description: 'Number of items per page',
    type: Number,
    default: 100,
    required: false,
  })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => {
    // Default limit is 100
    if (value === undefined || value === null) {
      return 100;
    }

    // Min limit is 1
    if (value < 1) {
      return 1;
    }

    // Max limit is 100
    if (value > 100) {
      return 100;
    }

    return value;
  })
  limit: number = 100;

  constructor(
    options: {
      defaultPage?: number;
      defaultLimit?: number;
      minLimit?: number;
      maxLimit?: number;
    } = {},
  ) {
    // Initialize with defaults that respect options
    if (options.defaultPage !== undefined) {
      this.page = options.defaultPage;
    }

    if (options.defaultLimit !== undefined) {
      const minLimit = options.minLimit ?? 1;
      const maxLimit = options.maxLimit ?? 100;

      // Ensure defaultLimit respects boundaries
      if (options.defaultLimit < minLimit) {
        this.limit = minLimit;
      } else if (options.defaultLimit > maxLimit) {
        this.limit = maxLimit;
      } else {
        this.limit = options.defaultLimit;
      }
    }
  }
}

export interface PaginationOptions {
  defaultPage?: number;
  defaultLimit?: number;
  minLimit?: number;
  maxLimit?: number;
}
