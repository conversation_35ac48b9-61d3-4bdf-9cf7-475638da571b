// error-logging.interceptor.ts

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { EventsLogger } from '@lib/logger/handlers/events.db-logger';

@Injectable()
export class ErrorLoggingInterceptor implements NestInterceptor {
  constructor(private eventsLogger: EventsLogger) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((error) => {
        // Log the error to the database
        this.eventsLogger.error(error.stack || error.message, {
          logger_name: 'Query failed',
        });

        throw error; // Re-throw the error to propagate it further
      }),
    );
  }
}
