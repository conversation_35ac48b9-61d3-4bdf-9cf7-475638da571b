import { Catch, ArgumentsHost, BadRequestException } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { HttpException } from '@nestjs/common';
import { Response } from 'express';
import { Req } from '../interface/request.interface';

@Catch(BadRequestException)
export class GlobalExceptionFilter extends BaseExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Req>();

    let status = exception.getStatus();
    if (exception instanceof HttpException) {
      status = exception.getStatus();
    } else {
      status = 500;
    }
    let i18n = request.i18nService;
    let lang = request.user?.language || request.i18nLang;

    let errorMessage: any;
    if (Array.isArray(exception.response?.message)) {
      errorMessage = exception.response?.message.map((error) => {
        return error.key
          ? i18n.translate(`validations.${error.key}`, {
              lang,
              args: error.args,
            })
          : error.constraints || error;
      });
    } else
      errorMessage =
        exception.response?.message || exception.response || exception;

    response.status(status).json({
      statusCode: status,
      error: exception.response?.error,
      message: errorMessage,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
