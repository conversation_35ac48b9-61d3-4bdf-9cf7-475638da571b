import { Module, OnModuleInit } from '@nestjs/common';

import { APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';

import { InjectRequestValidationPipe } from './shared/pipe/validation.pipe';
import { GlobalExceptionFilter } from './shared/filters/global-exception.filter';

import { AppService } from './app.service';

import imports from './app.imports';
import { ErrorLoggingInterceptor } from './shared/filters/error-logging.interceptor';

@Module({
  imports: [...imports],
  providers: [
    AppService,
    GlobalExceptionFilter,
    {
      provide: APP_INTERCEPTOR,
      useClass: ErrorLoggingInterceptor,
    },
    {
      provide: APP_PIPE,
      useClass: InjectRequestValidationPipe,
    },
  ],
})
export class AppModule implements OnModuleInit {
  constructor(private appService: AppService) {}
  onModuleInit() {
    console.log('---- Application Started ----');

    this.appService.syncLoggers();
    // this.appService.checkDatabase();
  }
}
