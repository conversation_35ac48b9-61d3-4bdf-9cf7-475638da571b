#!/bin/bash

# Gympare API Test Script
# Replace YOUR_TOKEN_HERE with an actual valid token

TOKEN="YOUR_TOKEN_HERE"
BASE_URL="http://localhost:3000/v2/external/gympare"

echo "🧪 Testing Gympare API with curl..."
echo "📡 Endpoint: $BASE_URL"
echo "🔑 Token: $TOKEN"
echo ""

# Test 1: Basic API call
echo "📋 Test 1: Basic API call"
curl -X GET "$BASE_URL" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -s

echo ""
echo ""

# Test 2: Check response structure with jq (if available)
echo "📋 Test 2: Response structure validation"
RESPONSE=$(curl -X GET "$BASE_URL" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -s)

if command -v jq &> /dev/null; then
    echo "✅ jq is available - parsing response..."
    echo "$RESPONSE" | jq '.'
    
    # Check if clubs array exists
    CLUBS_COUNT=$(echo "$RESPONSE" | jq '.clubs | length')
    echo "🏢 Number of clubs: $CLUBS_COUNT"
    
    # Check first club link if exists
    if [ "$CLUBS_COUNT" -gt 0 ]; then
        FIRST_LINK=$(echo "$RESPONSE" | jq -r '.clubs[0].link')
        echo "🔗 First club link: $FIRST_LINK"
        
        if [ "$FIRST_LINK" != "null" ] && [ "$FIRST_LINK" != "" ]; then
            echo "✅ Link is present"
            
            # Extract and decode base64 part
            BASE64_PART=$(echo "$FIRST_LINK" | sed 's/.*?t=//')
            if [ ! -z "$BASE64_PART" ]; then
                echo "📋 Decoded link object:"
                echo "$BASE64_PART" | base64 -d | jq '.'
            fi
        else
            echo "⚠️  No link provided (might be expected if no StoryWidget token exists)"
        fi
    fi
else
    echo "⚠️  jq not available - showing raw response:"
    echo "$RESPONSE"
fi

echo ""
echo "🏁 Test completed"
