# Gympare External API

## Overview

The Gympare External API provides access to gympare clubs data and their reviews. This API requires specific authorization and is only available to companies that have been granted access to the gympare API.

## Features

- **Club Data**: Retrieves club information including name, average score, and review count
- **Review Data**: Returns up to 10 most recent reviews per club (only for clubs with 5+ reviews)
- **Story Widget Links**: Generates secure links to the story widget for each club
- **Optimized Queries**: Uses efficient SQL with CTEs for better performance
- **Data Filtering**: Only returns clubs marked as gympare clubs (`gympare = 1`)

## Authentication

The API uses Bearer token authentication. The token must be valid and the company must have access to the gympare API (API ID: 15) in the `sec_company_api_rights` table.

## Endpoints

### GET /v2/external/gympare

Retrieves all gympare clubs with their reviews.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "clubs": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Fitness Club Amsterdam",
      "score": 8.5,
      "based_on": 127,
      "reviews": [
        {
          "id": 12345,
          "improvements": "Could improve equipment maintenance",
          "strengths": "Great staff and clean facilities",
          "postedOn": "2025-01-15 14:30",
          "score": 9
        },
        {
          "id": 12344,
          "improvements": "More variety in group classes",
          "strengths": "Excellent personal trainers",
          "postedOn": "2025-01-14 09:15",
          "score": 8
        }
      ],
      "link": "https://feedback4sports.com/verhalen/?t=eyJ0b2tlbiI6IkFCQ0RFRjEyMzQ1NiIsInNlcnZlciI6Imh0dHBzOi8vc2Vydm95NC53ZWxjb21lY2NzLm5sL0ZGIiwicHJvamVjdCI6IiIsImNvbXBhbnlUeXBlIjoyfQ=="
    }
  ]
}
```

**Response Fields:**

- `clubs`: Array of club objects
  - `id`: Unique club identifier (UUID from projects table)
  - `name`: Club name from projects table
  - `score`: Average score (decimal, from publication_reports)
  - `based_on`: Total number of reviews (integer, from publication_reports)
  - `reviews`: Array of recent reviews (max 10, only for clubs with 5+ reviews)
    - `id`: Review ID (from surv_surveyanswer_id)
    - `improvements`: Customer feedback on improvements
    - `strengths`: Customer feedback on strengths
    - `postedOn`: Review date in format "YYYY-MM-DD HH:MM"
    - `score`: Individual review score (decimal)
  - `link`: Story widget link (base64 encoded with token, empty if no StoryWidget token available)

## Error Responses

### 401 Unauthorized
- **No token provided**: Missing Authorization header
- **Invalid token**: Token is invalid or expired
- **No access to gympare API**: Company doesn't have access to the gympare API
- **No valid API key found**: Company's API key is expired or invalid

### 500 Internal Server Error
- **Database connection issues**: Unable to connect to database
- **Query execution errors**: SQL query failed to execute

## Database Requirements

### Tables

1. **sec_company_api_rights** - Controls API access
   - `sec_company_api_key_id`: References the API key ID from sec_company_api_key table
   - `sec_company_api_id`: API ID (15 for gympare)
   - `is_active`: Whether access is active

2. **projects** - Club data
   - `gympare`: Boolean flag indicating if project is a gympare club

3. **publication_reports** - Club summary data
   - `project_id`: References projects table
   - `avg_score`: Average score
   - `based_on`: Number of reviews

4. **publications** - Review data
   - `project_id`: References projects table
   - `response_date`: When review was posted
   - `score`: Review score
   - `strengths`: Strengths feedback
   - `improvements`: Improvements feedback
   - `surv_surveyanswer_id`: Review ID
   - `respondent_customername`: Customer last name
   - `respondent_initials`: Customer initials

5. **sec_user_ws_token** - Token management
   - `token`: Bearer token for authentication
   - `sec_company_id`: Company ID
   - `token_privilege`: Token type (StoryWidget = specific value)
   - `expiry_date`: Token expiration date

## Data Processing Logic

### Club Data Query
The service uses the following SQL to retrieve club data:
```sql
SELECT pr.project_id AS id, p2.project_name AS name, avg_score, based_on
FROM publication_reports pr
LEFT JOIN projects p2 ON pr.project_id = p2.project_id
WHERE p2.gympare = 1
```

### Review Data Query
Reviews are retrieved using a CTE (Common Table Expression) for optimal performance:
```sql
WITH ranked AS (
  SELECT
    p2.project_id,
    p.respondent_customername,
    p.respondent_initials,
    p.response_date,
    p.score,
    p.strengths,
    p.improvements,
    p.surv_surveyanswer_id AS answer_id,
    ROW_NUMBER() OVER (
      PARTITION BY p2.project_id
      ORDER BY p.response_date DESC
    ) AS rn,
    COUNT(*) OVER (PARTITION BY p2.project_id) AS cnt
  FROM publications p
  JOIN projects p2 ON p.project_id = p2.project_id
  WHERE p2.gympare = 1
)
SELECT * FROM ranked
WHERE rn <= 10          -- max 10 per project
  AND cnt >= 5          -- only projects with at least 5 reviews
  AND project_id = $1   -- filter by specific project
ORDER BY response_date DESC
```

### Story Widget Link Generation
1. Retrieves StoryWidget token for the company
2. Creates a link object with token, server URL, and company type
3. Base64 encodes the object
4. Generates final URL: `https://feedback4sports.com/verhalen/?t={base64Object}`

## Setup

1. Run the DDL script: `ddl/gympare-setup.ddl`
2. Create API keys for companies in `sec_company_api_key` table
3. Grant access to companies by inserting records into `sec_company_api_rights` with the corresponding `sec_company_api_key_id`
4. Mark projects as gympare clubs by setting `gympare = true`
5. Ensure StoryWidget tokens are available for companies that need story widget links
6. Configure CORS settings (see CORS Configuration section below)

## CORS Configuration

The external APIs require proper CORS configuration for cross-origin requests. You have several options:

### Option 1: Set ALLOWED_ORIGINS Environment Variable
```bash
# Single origin
ALLOWED_ORIGINS=https://yourdomain.com

# Multiple origins (comma-separated)
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Allow all origins (not recommended for production)
ALLOWED_ORIGINS=*
```

### Option 2: Use ExternalCors Decorator
The external API endpoints use the `@ExternalCors()` decorator which automatically sets permissive CORS headers:
- `Access-Control-Allow-Origin: *`
- `Access-Control-Allow-Methods: GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS`
- `Access-Control-Allow-Headers: Content-Type,Authorization,X-Requested-With`

### Option 3: No Origin Requests
Requests without an origin (like from mobile apps, Postman, or curl) are automatically allowed.

## API ID Reference

The following API IDs are defined in the `ApiId` enum (`src/shared/enums/api-id.enum.ts`):

```typescript
export enum ApiId {
  CLUBS = 1,
  CONTACTS = 2,
  CUSTOMER_DATA = 3,
  DATA = 4,
  LABELS = 5,
  MEMBERS = 6,
  NPS_RESPONSES = 7,
  OPTOUTS = 8,
  PROJECTS = 9,
  RESPONDENTS = 10,
  RESPONSES = 11,
  SERVICE = 12,
  SURVEY_URL = 13,
  SURVEY = 14,
  GYMPARE = 15,
}
```

**Usage in code:**
```typescript
import { ApiId } from '@/shared/enums/api-id.enum';

// Check for gympare access
const hasAccess = await apiRightsRepo
  .createQueryBuilder('rights')
  .where('rights.sec_company_api_id = :apiId', { apiId: ApiId.GYMPARE })
  .getOne();
```
