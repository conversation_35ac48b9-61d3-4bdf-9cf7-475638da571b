# Gympare External API

## Overview

The Gympare External API provides access to gympare clubs data and their reviews. This API requires specific authorization and is only available to companies that have been granted access to the gympare API.

## Authentication

The API uses Bearer token authentication. The token must be valid and the company must have access to the gympare API (API ID: 15) in the `sec_company_api_rights` table.

## Endpoints

### GET /v2/external/gympare

Retrieves all gympare clubs with their reviews.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "clubs": [
    {
      "id": "<uuid>",
      "name": "Club Name",
      "score": 7.7,
      "based_on": 1234,
      "reviews": [
        {
          "id": 123,
          "improvements": "Could improve equipment",
          "strengths": "Great staff",
          "postedOn": "2025-08-17 14:13",
          "score": 9
        }
      ],
      "link": ""
    }
  ]
}
```

## Error Responses

### 401 Unauthorized
- **No token provided**: Missing Authorization header
- **Invalid token**: Token is invalid or expired
- **No access to gympare API**: Company doesn't have access to the gympare API

## Database Requirements

### Tables

1. **sec_company_api_rights** - Controls API access
   - `sec_company_api_key_id`: References the API key ID from sec_company_api_key table
   - `sec_company_api_id`: API ID (15 for gympare)
   - `is_active`: Whether access is active

2. **projects** - Club data
   - `gympare`: Boolean flag indicating if project is a gympare club

3. **publication_reports** - Club summary data
   - `project_id`: References projects table
   - `avg_score`: Average score
   - `based_on`: Number of reviews

4. **publications** - Review data
   - `project_id`: References projects table
   - `response_date`: When review was posted
   - `score`: Review score
   - `strengths`: Strengths feedback
   - `improvements`: Improvements feedback
   - `surv_surveyanswer_id`: Review ID

## Setup

1. Run the DDL script: `ddl/gympare-setup.ddl`
2. Create API keys for companies in `sec_company_api_key` table
3. Grant access to companies by inserting records into `sec_company_api_rights` with the corresponding `sec_company_api_key_id`
4. Mark projects as gympare clubs by setting `gympare = true`

## API ID Reference

The following API IDs are defined in the `ApiId` enum (`src/shared/enums/api-id.enum.ts`):

```typescript
export enum ApiId {
  CLUBS = 1,
  CONTACTS = 2,
  CUSTOMER_DATA = 3,
  DATA = 4,
  LABELS = 5,
  MEMBERS = 6,
  NPS_RESPONSES = 7,
  OPTOUTS = 8,
  PROJECTS = 9,
  RESPONDENTS = 10,
  RESPONSES = 11,
  SERVICE = 12,
  SURVEY_URL = 13,
  SURVEY = 14,
  GYMPARE = 15,
}
```

**Usage in code:**
```typescript
import { ApiId } from '@/shared/enums/api-id.enum';

// Check for gympare access
const hasAccess = await apiRightsRepo
  .createQueryBuilder('rights')
  .where('rights.sec_company_api_id = :apiId', { apiId: ApiId.GYMPARE })
  .getOne();
```
