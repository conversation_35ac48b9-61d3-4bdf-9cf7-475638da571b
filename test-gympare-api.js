const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/v2/external/gympare';
const TEST_TOKEN = 'your-test-token-here'; // Replace with actual test token

async function testGympareAPI() {
  console.log('🧪 Testing Gympare API...\n');

  try {
    // Test 1: Call the API endpoint
    console.log('📡 Making request to:', BASE_URL);
    console.log('🔑 Using token:', TEST_TOKEN);
    
    const response = await axios.get(BASE_URL, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('\n✅ API Response Status:', response.status);
    console.log('📊 Response Data:');
    console.log(JSON.stringify(response.data, null, 2));

    // Test 2: Validate response structure
    console.log('\n🔍 Validating response structure...');
    
    if (response.data && typeof response.data === 'object') {
      console.log('✅ Response is an object');
      
      if (response.data.clubs && Array.isArray(response.data.clubs)) {
        console.log(`✅ Clubs array found with ${response.data.clubs.length} clubs`);
        
        // Check first club structure
        if (response.data.clubs.length > 0) {
          const firstClub = response.data.clubs[0];
          console.log('\n🏢 First club structure:');
          console.log('- ID:', firstClub.id);
          console.log('- Name:', firstClub.name);
          console.log('- Score:', firstClub.score);
          console.log('- Based on:', firstClub.based_on);
          console.log('- Link:', firstClub.link);
          
          if (firstClub.reviews && Array.isArray(firstClub.reviews)) {
            console.log(`- Reviews: ${firstClub.reviews.length} reviews`);
          }
          
          // Test link format
          if (firstClub.link) {
            console.log('\n🔗 Testing link format...');
            if (firstClub.link.startsWith('https://feedback4sports.com/verhalen/?t=')) {
              console.log('✅ Link format is correct');
              
              // Decode base64 to verify structure
              try {
                const base64Part = firstClub.link.split('?t=')[1];
                const decoded = Buffer.from(base64Part, 'base64').toString('utf-8');
                const linkObject = JSON.parse(decoded);
                
                console.log('✅ Base64 decoded successfully');
                console.log('📋 Link object structure:');
                console.log('- token:', linkObject.token ? '✅ Present' : '❌ Missing');
                console.log('- server:', linkObject.server === 'https://servoy4.welcomeccs.nl/FF' ? '✅ Correct' : '❌ Incorrect');
                console.log('- project:', linkObject.project === '' ? '✅ Empty' : '❌ Not empty');
                console.log('- companyType:', linkObject.companyType === 2 ? '✅ Correct' : '❌ Incorrect');
                
              } catch (error) {
                console.log('❌ Failed to decode base64:', error.message);
              }
            } else {
              console.log('❌ Link format is incorrect');
            }
          } else {
            console.log('⚠️  No link provided (this might be expected if no StoryWidget token exists)');
          }
        }
      } else {
        console.log('❌ Clubs array not found or not an array');
      }
    } else {
      console.log('❌ Response is not an object');
    }

  } catch (error) {
    console.log('\n❌ API Test Failed:');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Status Text:', error.response.statusText);
      console.log('Response Data:', error.response.data);
    } else if (error.request) {
      console.log('No response received. Is the server running?');
      console.log('Error:', error.message);
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Run the test
console.log('🚀 Starting Gympare API Test...\n');
testGympareAPI().then(() => {
  console.log('\n🏁 Test completed');
}).catch((error) => {
  console.log('\n💥 Test failed with error:', error.message);
});
