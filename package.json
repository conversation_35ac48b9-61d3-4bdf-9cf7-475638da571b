{"name": "focus-server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"clear": "<PERSON><PERSON><PERSON> dist", "clear-public": "rimraf public", "build": "npm run clear && nest build -p ./tsconfig.prod.json && node ./get-git-version.js", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "dependencies": {"@anthropic-ai/sdk": "^0.18.0", "@nestjs-modules/mailer": "^1.7.1", "@nestjs/axios": "^1.0.0", "@nestjs/common": "^9.4.0", "@nestjs/config": "^2.3.1", "@nestjs/core": "^9.4.0", "@nestjs/platform-express": "^9.4.0", "@nestjs/platform-socket.io": "^9.4.3", "@nestjs/schedule": "^3.0.1", "@nestjs/serve-static": "^3.0.1", "@nestjs/swagger": "^6.3.0", "@nestjs/typeorm": "^9.0.1", "@nestjs/websockets": "^9.4.0", "@nestlab/google-recaptcha": "^3.3.1", "@types/nodemailer": "^6.4.7", "axios": "^1.4.0", "basic-ftp": "^5.0.3", "better-queue": "^3.8.12", "cheerio": "^1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cloudscraper": "^4.6.0", "cluster": "^0.7.7", "cohere-ai": "^7.8.0", "country-list": "^2.3.0", "cron": "^2.3.0", "dotenv": "^16.0.3", "fast-csv": "^4.3.6", "graphql": "^16.8.1", "hbs": "^4.2.0", "md5": "^2.3.0", "moment": "^2.29.1", "moment-timezone": "^0.5.43", "nestjs-cls": "^4.1.0", "nestjs-i18n": "^10.4.5", "nestjs-puppeteer": "^2.1.0", "nodemailer": "^6.7.5", "openai": "^4.20.1", "papaparse": "^5.4.1", "pdf-creator-node": "^2.2.4", "pg": "^8.10.0", "pg-format": "^1.0.4", "puppeteer": "^21", "puppeteer-extra": "^3.3.6", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "socket.io": "^4.7.2", "ssh2-sftp-client": "^9.0.4", "swagger-ui-express": "^4.6.3", "typeorm": "^0.3.6", "weaviate-client": "^2.14.5", "weaviate-ts-client": "^2.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.19.3/xlsx-0.19.3.tgz", "zod": "^3.22.2"}, "devDependencies": {"@nestjs/cli": "^9.4.2", "@nestjs/schematics": "^9.1.0", "@nestjs/testing": "^9.4.0", "@types/cron": "^2.0.1", "@types/express": "^4.17.13", "@types/jest": "^27.0.1", "@types/json2csv": "^5.0.3", "@types/md5": "^2.3.1", "@types/multer": "^1.4.7", "@types/node": "^16.18.23", "@types/pg-format": "^1.0.2", "@types/supertest": "^2.0.11", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "adm-zip": "^0.5.10", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "jest": "^27.0.6", "prettier": "^2.3.2", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.5.0", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}