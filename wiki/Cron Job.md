**Cron Job**

**Usage of Cron Job module**

We have multiple endpoints: **GET /2/cron-jobs**

_This endpoint allows you to retrieve a list of all scheduled cron jobs on your system._

**POST /2/cron-jobs**

_This endpoint allows you to create a new cron job. You can specify the frequency, start time, and command to be executed for the new job._

Parameters:

- **name**: This field should contain service name and function name that cron will call. Example: GlobalExportService.export Please check GET /2/cron- jobs/available-methods to get a list of available methods
  - **parameters**: This field should contain parameters that called function accepts Example: ["2023-05-10 00:00", "2023-05-10 23:00"]
    - **timer**: You need to specify frequency of job. Please use following structure [Crontab](https://crontab.guru/)
  - **callOnStart**: If you want to execute a cron job immediately upon creation, you need to set the value of this field to "true". Additionally, if the application restarts, this cron job will be executed on-demand.

**DELETE /2/cron-jobs/{id}**

\*This endpoint allows you to delete a specific cron job by its ID.

**GET /2/cron-jobs/available-methods**

_This endpoint allows you to retrieve a list of available methods that can be executed for a cron job._

**POST /2/cron-jobs/call-job-manually**

_This endpoint allows you to manually trigger the execution of a specific method that exists on available-methods endpoint._

**Steps for addinng new service,method to Cron Job**

\*To add a new job to the cron-job module you can follow these steps: Create module in src/modules/cron-jobs/jobs/{module-name}/{module-name}.module.ts Create service in src/modules/cron-jobs/jobs/{module-name}/{module-name}.service.ts Create this 2 files as regular NestJS file structure for module and services

_Open src/modules/cron-job/cron-job.module.ts file and add Module to this line:_

_imports: [..., <ModuleName>],_

Open src/modules/cron-job/cron-job.service.ts file and add newly created service to constructor:

```
constructor(
private schedulerRegistry: SchedulerRegistry,

@InjectRepository(CronJobsEntity)

private cronJobRepo: Repository<CronJobsEntity>,     private <serviceName>: <ServiceName>
) {}
```

in here key is declaring base name for this service. For Example: if you put private userJob: UserJobService, it means when you use POST /2/cron-jobs API, You need to fill "name" field like this: "userJob.getUser"

### Create method in service file.

For example getUser(id:number){ return 'user'+id }

Add this method to src/modules/cron-jobs/jobs/schema.ts This schema is only for guiding user to which methods and which parameters we have, so it doesn't affect to original methods, parameters or parameter orders
For example:

```
JOB_SCHEMA = [
  other methods,

  {

    "name": "<module-name>.<method-name>",
    "parameters":[
      {
        "index":0,
        "name": "User ID",
        "defaultValue":"1"
      }
    ]
  }
]

```
