# Gympare End-to-End Tests

This directory contains end-to-end tests for the Gympare external API module.

## Overview

E2E tests simulate complete user workflows and verify that the entire system works together correctly.

## Test Scenarios

- Complete API request/response flow
- Authentication and authorization workflows
- Data retrieval and transformation
- Error handling and edge cases
- Performance and load testing

## Test Files

- `gympare-workflow.e2e.spec.ts` - Complete workflow tests
- `gympare-performance.e2e.spec.ts` - Performance and load tests
- `gympare-error-handling.e2e.spec.ts` - Error scenario tests

## Running Tests

```bash
# Run all gympare E2E tests
npm run test:e2e:gympare

# Run specific test file
npm run test:e2e test/e2e/gympare/gympare-workflow.e2e.spec.ts

# Run with headless browser
npm run test:e2e:gympare -- --headless
```

## Test Environment

These tests require:
- Full application stack running
- Database with test data
- External services available
- Network connectivity

## Configuration

Set the following environment variables:
- `E2E_BASE_URL` - Application base URL
- `E2E_TEST_TOKEN` - Valid test token
- `E2E_TIMEOUT` - Test timeout in milliseconds
