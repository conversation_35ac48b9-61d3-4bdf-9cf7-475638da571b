// Integration test setup
import { config } from 'dotenv';

// Load environment variables for integration tests
config({ path: '.env.test' });

// Global test setup
beforeAll(async () => {
  // Set up test environment
  process.env.NODE_ENV = 'test';
  
  // Ensure required environment variables are set
  if (!process.env.GYMPARE_TEST_TOKEN) {
    console.warn('GYMPARE_TEST_TOKEN not set. Some tests may fail.');
  }
  
  if (!process.env.GYMPARE_API_URL) {
    process.env.GYMPARE_API_URL = 'http://localhost:3000/v2/external/gympare';
  }
});

// Global test teardown
afterAll(async () => {
  // Clean up test environment
});

// Increase timeout for integration tests
jest.setTimeout(30000);
