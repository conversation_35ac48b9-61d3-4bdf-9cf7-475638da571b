import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../src/app.module';

describe('Gympare API Integration Tests', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get authentication token for testing
    authToken = process.env.GYMPARE_TEST_TOKEN || 'test-token';
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /v2/external/gympare', () => {
    it('should return clubs data with valid authentication', async () => {
      const response = await request(app.getHttpServer())
        .get('/v2/external/gympare')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.clubs).toBeDefined();
      expect(Array.isArray(response.body.clubs)).toBe(true);
    });

    it('should return 401 with invalid authentication', async () => {
      await request(app.getHttpServer())
        .get('/v2/external/gympare')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer())
        .get('/v2/external/gympare')
        .expect(401);
    });

    it('should include link field in club data when StoryWidget token exists', async () => {
      const response = await request(app.getHttpServer())
        .get('/v2/external/gympare')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      if (response.body.clubs.length > 0) {
        const firstClub = response.body.clubs[0];
        
        // Check if link field exists (may be null if no StoryWidget token)
        expect(firstClub).toHaveProperty('link');
        
        // If link exists, validate its format
        if (firstClub.link) {
          expect(firstClub.link).toMatch(/^https:\/\/feedback4sports\.com\/verhalen\/\?t=/);
          
          // Validate base64 content
          const base64Part = firstClub.link.split('?t=')[1];
          expect(base64Part).toBeDefined();
          
          // Try to decode base64
          try {
            const decoded = Buffer.from(base64Part, 'base64').toString('utf-8');
            const linkObject = JSON.parse(decoded);
            
            expect(linkObject).toHaveProperty('token');
            expect(linkObject).toHaveProperty('server');
            expect(linkObject).toHaveProperty('project');
            expect(linkObject).toHaveProperty('companyType');
            expect(linkObject.server).toBe('https://servoy4.welcomeccs.nl/FF');
            expect(linkObject.companyType).toBe(2);
            // Verify project field is not empty (should contain project_nr)
            expect(linkObject.project).toBeTruthy();
          } catch (error) {
            fail('Invalid base64 encoding in link');
          }
        }
      }
    });

    it('should include required fields in club data', async () => {
      const response = await request(app.getHttpServer())
        .get('/v2/external/gympare')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      if (response.body.clubs.length > 0) {
        const firstClub = response.body.clubs[0];
        
        expect(firstClub).toHaveProperty('id');
        expect(firstClub).toHaveProperty('name');
        expect(firstClub).toHaveProperty('score');
        expect(firstClub).toHaveProperty('based_on');
        
        // Optional fields
        if (firstClub.reviews) {
          expect(Array.isArray(firstClub.reviews)).toBe(true);
        }
      }
    });
  });

  describe('Performance Tests', () => {
    it('should respond within acceptable time limit', async () => {
      const startTime = Date.now();
      
      await request(app.getHttpServer())
        .get('/v2/external/gympare')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(5000); // 5 seconds
    });
  });
});
