# Gympare Integration Tests

This directory contains integration tests for the Gympare external API module.

## Overview

Integration tests verify that the Gympare API works correctly with:
- Database connections
- External service integrations
- Authentication and authorization
- Data transformation and validation

## Test Files

- `gympare-api.integration.spec.ts` - Main integration test suite
- `gympare-auth.integration.spec.ts` - Authentication integration tests
- `gympare-database.integration.spec.ts` - Database integration tests

## Running Tests

```bash
# Run all gympare integration tests
npm run test:integration:gympare

# Run specific test file
npm run test test/integration/gympare/gympare-api.integration.spec.ts

# Run with verbose output
npm run test:integration:gympare -- --verbose
```

## Test Environment

These tests require:
- Running database instance
- Valid authentication tokens
- External service endpoints to be available
- Environment variables configured

## Configuration

Set the following environment variables:
- `GYMPARE_API_URL` - Base URL for the API
- `GYMPARE_TEST_TOKEN` - Valid test token
- `DATABASE_URL` - Database connection string
