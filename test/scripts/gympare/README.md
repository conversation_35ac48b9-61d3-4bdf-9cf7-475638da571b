# Gympare Test Scripts

This directory contains environment-specific test scripts for the Gympare external API module.

## Available Scripts

### Node.js Script
- **File**: `test-gympare-api.js`
- **Purpose**: Comprehensive API testing with detailed validation
- **Usage**: `node test-gympare-api.js`
- **Features**:
  - API endpoint testing
  - Response structure validation
  - Link format verification
  - Base64 decoding and validation
  - Error handling

### cURL Script (Linux/Mac)
- **File**: `test-gympare-curl.sh`
- **Purpose**: Command-line testing with curl
- **Usage**: `./test-gympare-curl.sh`
- **Features**:
  - Basic API call
  - Response structure validation (with jq)
  - Link decoding and validation
  - HTTP status and timing information

### PowerShell Script (Windows)
- **File**: `test-gympare-powershell.ps1`
- **Purpose**: Windows-specific testing with PowerShell
- **Usage**: `.\test-gympare-powershell.ps1`
- **Features**:
  - Basic API call
  - Response structure validation
  - Link format verification
  - Base64 decoding and validation
  - Error handling with detailed information

## Environment Variables

All scripts support the following environment variables:

```bash
# API Configuration
GYMPARE_API_URL=http://localhost:3000/v2/external/gympare
GYMPARE_TEST_TOKEN=your-valid-token-here

# Optional: Test timeout
GYMPARE_TEST_TIMEOUT=10000
```

## Quick Start

1. **Set up environment variables**:
   ```bash
   export GYMPARE_API_URL="http://localhost:3000/v2/external/gympare"
   export GYMPARE_TEST_TOKEN="your-valid-token-here"
   ```

2. **Choose your platform**:
   - **Node.js**: `node test-gympare-api.js`
   - **Linux/Mac**: `./test-gympare-curl.sh`
   - **Windows**: `.\test-gympare-powershell.ps1`

## Prerequisites

- Valid authentication token
- API server running
- Network connectivity
- Required dependencies (axios for Node.js, jq for curl script)

## Troubleshooting

- **Permission denied**: Make scripts executable with `chmod +x`
- **Module not found**: Install dependencies with `npm install axios`
- **Authentication failed**: Verify token is valid and has correct permissions
- **Connection refused**: Ensure API server is running and accessible
