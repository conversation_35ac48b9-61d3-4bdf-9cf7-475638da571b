# Gympare API Testing Guide

This guide provides instructions for testing the Gympare external API, including the newly implemented link building functionality.

## 🎯 Overview

The Gympare API provides club information with reviews and generates StoryWidget links. The link building functionality creates URLs in the format:
```
https://feedback4sports.com/verhalen/?t=<base64_object>
```

Where the base64 object contains:
```json
{
  "token": "<retrieved_token>",
  "server": "https://servoy4.welcomeccs.nl/FF",
  "project": "<project_nr>",
  "companyType": 2
}
```

## 🧪 Testing Methods

### 1. Unit Tests (Recommended)

Run the comprehensive unit tests that cover all functionality:

```bash
# Run all Gympare service tests
npm run test -- --testPathPatterns=gympare.service.spec.ts

# Run with verbose output
npm run test -- --testPathPatterns=gympare.service.spec.ts --verbose

# Run all tests in watch mode
npm run test:watch
```

**Test Coverage:**
- ✅ Service instantiation
- ✅ Empty clubs array handling
- ✅ Clubs with reviews data
- ✅ StoryWidget token retrieval
- ✅ Link building with valid token
- ✅ Link building with no token (empty string)
- ✅ Query builder parameter validation

### 2. Node.js Test Script

Use the Node.js test script for integration testing:

```bash
# Install axios if not already installed
npm install axios

# Set environment variables
export GYMPARE_API_URL="http://localhost:3000/v2/external/gympare"
export GYMPARE_TEST_TOKEN="your-valid-token-here"

# Run the test
node test/scripts/gympare/test-gympare-api.js
```

**Features:**
- ✅ API endpoint testing
- ✅ Response structure validation
- ✅ Link format verification
- ✅ Base64 decoding and validation
- ✅ Error handling

### 3. cURL Test Script (Linux/Mac)

Use the bash script for command-line testing:

```bash
# Make the script executable
chmod +x test/scripts/gympare/test-gympare-curl.sh

# Set environment variables
export GYMPARE_API_URL="http://localhost:3000/v2/external/gympare"
export GYMPARE_TEST_TOKEN="your-valid-token-here"

# Run the test
./test/scripts/gympare/test-gympare-curl.sh
```

**Features:**
- ✅ Basic API call
- ✅ Response structure validation (with jq)
- ✅ Link decoding and validation
- ✅ HTTP status and timing information

### 4. PowerShell Test Script (Windows)

Use the PowerShell script for Windows testing:

```powershell
# Set environment variables
$env:GYMPARE_API_URL = "http://localhost:3000/v2/external/gympare"
$env:GYMPARE_TEST_TOKEN = "your-valid-token-here"

# Run the test
.\test\scripts\gympare\test-gympare-powershell.ps1
```

**Features:**
- ✅ Basic API call
- ✅ Response structure validation
- ✅ Link format verification
- ✅ Base64 decoding and validation
- ✅ Error handling with detailed information

## 🔧 Configuration

### Environment Variables

Set these environment variables for testing:

```bash
# API Configuration
GYMPARE_API_URL=http://localhost:3000/v2/external/gympare
GYMPARE_TEST_TOKEN=your-valid-token-here

# Optional: Test timeout
GYMPARE_TEST_TIMEOUT=10000
```

### Authentication

The API requires Bearer token authentication. Make sure you have a valid token that has access to the Gympare API endpoint.

## 📋 Test Scenarios

### 1. Basic API Access
- [ ] Endpoint is accessible
- [ ] Authentication works correctly
- [ ] Response format is valid JSON

### 2. Data Structure Validation
- [ ] Response contains `clubs` array
- [ ] Each club has required fields (id, name, score, based_on)
- [ ] Optional fields are handled correctly (reviews, link)

### 3. Link Generation
- [ ] Links are generated in correct format
- [ ] Base64 encoding/decoding works
- [ ] Link object contains required fields including project_nr
- [ ] Project number is correctly included in the link object
- [ ] Handles missing StoryWidget tokens gracefully

### 4. Error Handling
- [ ] Invalid token returns appropriate error
- [ ] Network errors are handled gracefully
- [ ] Timeout scenarios work correctly

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify token is valid and not expired
   - Check token has correct permissions
   - Ensure Bearer prefix is included

2. **Connection Refused**
   - Verify API server is running
   - Check correct port and URL
   - Ensure firewall allows connection

3. **Invalid Response Format**
   - Check API version compatibility
   - Verify database has test data
   - Review server logs for errors

4. **Link Generation Issues**
   - Verify StoryWidget token exists in database
   - Check base64 encoding/decoding
   - Validate link object structure

### Debug Mode

Enable debug logging by setting:
```bash
export DEBUG=gympare:*
```

## 📊 Performance Testing

For performance testing, you can:

1. **Load Testing**: Use tools like Apache Bench or Artillery
2. **Response Time**: Monitor with the provided test scripts
3. **Concurrent Requests**: Test multiple simultaneous API calls

Example load test:
```bash
# Test with 10 concurrent requests
ab -n 100 -c 10 -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/v2/external/gympare
```

## 🔄 Continuous Integration

Include these tests in your CI/CD pipeline:

```yaml
# Example GitHub Actions step
- name: Test Gympare API
  run: |
    npm install
    export GYMPARE_TEST_TOKEN=${{ secrets.GYMPARE_TEST_TOKEN }}
    node test/scripts/gympare/test-gympare-api.js
```

## 📝 Reporting

Test results should be documented and tracked. Consider:

- Test execution logs
- Performance metrics
- Error reports
- Coverage reports

## 🤝 Contributing

When adding new tests:

1. Follow the existing naming conventions
2. Include proper documentation
3. Add to appropriate test category
4. Update this guide if needed
5. Ensure tests are maintainable and reliable
