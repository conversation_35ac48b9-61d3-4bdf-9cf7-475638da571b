# Gympare Manual Tests

This directory contains manual testing resources and documentation for the Gympare external API module.

## Overview

Manual tests provide step-by-step instructions for testing the Gympare API functionality manually, including test plans, checklists, and verification steps.

## Contents

- `test-plan.md` - Comprehensive test plan for manual testing
- `test-checklist.md` - Quick checklist for manual verification
- `test-scenarios.md` - Detailed test scenarios and expected results
- `api-documentation.md` - API documentation for manual testing

## Test Plan

### Prerequisites
- Valid authentication token
- API endpoint accessible
- Test data available in database

### Test Scenarios
1. **Basic API Access**
   - Verify endpoint is accessible
   - Check authentication requirements
   - Validate response format

2. **Data Retrieval**
   - Test clubs data retrieval
   - Verify data structure and content
   - Check pagination if applicable

3. **Link Generation**
   - Test StoryWidget link creation
   - Verify base64 encoding/decoding
   - Validate link format and content

4. **Error Handling**
   - Test invalid token scenarios
   - Verify error response format
   - Check timeout handling

## Manual Testing Steps

1. Set up test environment
2. Configure authentication token
3. Execute test scenarios
4. Document results and issues
5. Verify expected vs actual behavior

## Documentation

Refer to the individual markdown files for detailed testing instructions and scenarios.
