{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".integration.spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "moduleNameMapper": {"^@entities$": "<rootDir>/../src/modules/database/entities", "^@libs/(.*)$": "<rootDir>/../src/libs/$1", "^@helpers/(.*)$": "<rootDir>/../src/modules/helpers/$1", "^@/(.*)$": "<rootDir>/../src/$1"}, "setupFilesAfterEnv": ["<rootDir>/setup/integration.setup.ts"], "testTimeout": 30000}