# Test Directory

This directory contains all test files for the Focus API project, organized by type and module.

## Directory Structure

```
test/
├── integration/          # Integration tests that test multiple components together
│   └── gympare/         # Gympare-specific integration tests
├── e2e/                 # End-to-end tests that test complete user workflows
│   └── gympare/         # Gympare-specific E2E tests
├── manual/              # Manual test scripts and documentation
│   └── gympare/         # Gympare-specific manual testing resources
├── scripts/             # Test scripts for different environments
│   └── gympare/         # Gympare-specific test scripts
├── unit/                # Unit tests (located in src/**/*.spec.ts)
├── app.e2e-spec.ts      # Main E2E test file
└── jest-e2e.json        # E2E Jest configuration
```

## Test Types

### Unit Tests
- Located in `src/**/*.spec.ts` files alongside the source code
- Test individual functions, classes, and modules in isolation
- Use Jest as the testing framework

### Integration Tests
- Test the interaction between multiple components
- Located in `test/integration/`
- May require database connections or external services

### End-to-End Tests
- Test complete user workflows from start to finish
- Located in `test/e2e/`
- Simulate real user interactions

### Manual Tests
- Scripts and documentation for manual testing
- Located in `test/manual/`
- Include test plans, checklists, and manual verification steps

### Test Scripts
- Environment-specific test scripts (Node.js, cURL, PowerShell, etc.)
- Located in `test/scripts/`
- Useful for quick testing and debugging

## Running Tests

### Unit Tests
```bash
npm run test
npm run test:watch
npm run test:coverage
```

### Integration Tests
```bash
# Run specific integration tests
npm run test:integration

# Run gympare integration tests
npm run test:integration:gympare
```

### E2E Tests
```bash
# Run all E2E tests
npm run test:e2e

# Run gympare E2E tests
npm run test:e2e:gympare
```

## Test Configuration

- Jest configuration: `jest.config.js`
- E2E Jest configuration: `test/jest-e2e.json`
- Test environment setup: `test/setup/`
- Test utilities: `test/utils/`
- Test data: `test/fixtures/`

## Best Practices

1. **Naming Convention**: Use descriptive names that indicate what is being tested
2. **Organization**: Group related tests together in appropriate directories
3. **Documentation**: Include README files in each test directory explaining its purpose
4. **Maintenance**: Keep test scripts updated as the API evolves
5. **Environment**: Use environment variables for configuration (tokens, URLs, etc.)
