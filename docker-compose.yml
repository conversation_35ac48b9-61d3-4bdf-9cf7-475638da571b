version: '3.8'

services:
  postgres:
    image: postgres:13.1
    container_name: focus.postgres
    restart: always
    ports:
      - 5435:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: focus
      POSTGRES_DB: backend
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - ./ddl/init.sql:/docker-entrypoint-initdb.d/init.sql

  adminer:
    image: adminer:latest
    container_name: focus.adminer
    restart: always
    ports:
      - 3308:8080
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
