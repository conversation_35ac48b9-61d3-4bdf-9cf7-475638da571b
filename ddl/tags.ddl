CREATE SEQUENCE tag_id_seq OWNED BY tags.tag_id;
select setval('tag_id_seq', (select max(tag_id) from tags) + 1);
ALTER TABLE tags ALTER COLUMN tag_id SET DEFAULT nextval('tag_id_seq');


ALTER TABLE "tags"
ADD "tag_type" character varying(25) NULL;
UPDATE tags SET tag_type ='clf';

CREATE SEQUENCE rl_tags_rl_tag_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."report_layout_tags" (
    "rl_tag_id" integer DEFAULT nextval('rl_tags_rl_tag_id_seq') NOT NULL,
    "tag_id" integer,
    "rl_id" integer,
    "created_by" integer,
    "created_on" timestamp,
    CONSTRAINT "rl_tags_pkey" PRIMARY KEY ("rl_tag_id")
) WITH (oids = false);

