-- Create sec_company_api_rights table
CREATE TABLE IF NOT EXISTS sec_company_api_rights (
    sec_company_api_rights_id SERIAL PRIMARY KEY,
    sec_company_id INTEGER NOT NULL,
    sec_company_api_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER NOT NULL,
    created_on TIMESTAMPTZ DEFAULT NOW(),
    updated_by INTEGER NOT NULL,
    updated_on TIMESTAMPTZ DEFAULT NOW()
);

-- Add gympare column to projects table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' 
        AND column_name = 'gympare'
    ) THEN
        ALTER TABLE projects ADD COLUMN gympare BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_sec_company_api_rights_company_api 
ON sec_company_api_rights(sec_company_id, sec_company_api_id, is_active);

CREATE INDEX IF NOT EXISTS idx_projects_gympare 
ON projects(gympare);

-- Insert gympare API record (ID 15) into sec_company_api_rights for testing
-- This should be done for specific companies that need access
-- Note: API ID 15 corresponds to ApiId.GYMPARE enum value
-- INSERT INTO sec_company_api_rights (sec_company_id, sec_company_api_id, created_by, updated_by)
-- VALUES (1, 15, 1, 1);
