-- Adminer 4.8.1 PostgreSQL 13.4 dump

CREATE SEQUENCE allowed_filters_allowed_filter_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."allowed_filters" (
    "allowed_filter_id" integer DEFAULT nextval('allowed_filters_allowed_filter_id_seq') NOT NULL,
    "filter_name" character varying(100),
    "short_description" character varying(500),
    "filter_image" character varying(100),
    "company_type" integer,
    "filter_options" character varying(1000),
    "is_default" integer,
    "filter_key" character varying(50),
    "sort_order" integer,
    "filter_type" integer,
    "allowed_templates" json,
    "is_required" integer,
    CONSTRAINT "allowed_filters_pkey" PRIMARY KEY ("allowed_filter_id")
) WITH (oids = false);


CREATE TABLE "public"."answer_stopwords" (
    "answer_stopword_id" text NOT NULL,
    "stop_word" character varying(100),
    "creator" integer,
    "creation_date" timestamp,
    "sec_company_id" integer,
    CONSTRAINT "answer_stopwords_pkey" PRIMARY KEY ("answer_stopword_id")
) WITH (oids = false);


CREATE TABLE "public"."answertable" (
    "answertable_uuid" character varying(36) NOT NULL,
    "answertable_tableid" character varying(36),
    "created_by" integer,
    "created_on" timestamp,
    "modified_by" integer,
    "modified_on" timestamp,
    "large_01" character varying(100),
    "large_02" character varying(100),
    "large_03" character varying(100),
    "medium_01" character varying(50),
    "medium_02" character varying(50),
    "medium_03" character varying(50),
    "medium_04" character varying(50),
    "small_01" character varying(20),
    "small_02" character varying(20),
    "small_03" character varying(20),
    "sec_company_id" integer,
    "unique_id" character varying(50),
    CONSTRAINT "answertable_pkey" PRIMARY KEY ("answertable_uuid")
) WITH (oids = false);


CREATE TABLE "public"."answertable_config" (
    "answertable_config_uuid" character varying(36) NOT NULL,
    "answertable_name" character varying(50),
    "answertable_description" character varying(100),
    "answertable_config" text,
    "sec_company_id" integer,
    CONSTRAINT "answertable_config_pkey" PRIMARY KEY ("answertable_config_uuid")
) WITH (oids = false);


CREATE SEQUENCE brands_brand_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."brands" (
    "brand_id" integer DEFAULT nextval('brands_brand_id_seq') NOT NULL,
    "sec_company_id" integer,
    "brand_name" character varying(100),
    CONSTRAINT "brands_pkey" PRIMARY KEY ("brand_id")
) WITH (oids = false);


CREATE TABLE "public"."bsh_categories" (
    "bsh_category_id" integer NOT NULL,
    "bsh_category_name" character varying(50),
    "bsh_category_uuid" character varying(36),
    "parent_bsh_category_id" integer,
    CONSTRAINT "bsh_categories_pkey" PRIMARY KEY ("bsh_category_id")
) WITH (oids = false);


CREATE SEQUENCE bsh_mapping_map_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."bsh_mapping" (
    "map_id" integer DEFAULT nextval('bsh_mapping_map_id_seq') NOT NULL,
    "map_key" character varying(20),
    "map_description" character varying(100),
    "isactive" integer,
    "sec_company_id" integer,
    "map_field" character varying(100),
    CONSTRAINT "bsh_mapping_pkey" PRIMARY KEY ("map_id")
) WITH (oids = false);

CREATE INDEX "bsh_mapping_map_key_map_field_idx" ON "public"."bsh_mapping" USING btree ("map_field", "map_key");


CREATE TABLE "public"."classification_group_survey" (
    "classification_group_survey_id" character varying(36) NOT NULL,
    "classification_group_id" integer,
    "surv_survey_id" integer,
    "creation_date" timestamp,
    "creator" integer,
    CONSTRAINT "classification_group_survey_pkey" PRIMARY KEY ("classification_group_survey_id")
) WITH (oids = false);


CREATE TABLE "public"."classification_groups" (
    "classification_group_id" integer NOT NULL,
    "classification_group" character varying(50),
    "sec_company_id" integer,
    "creation_date" timestamp,
    "creator" integer,
    "classification_type" integer,
    "remover" integer,
    "removal_date" timestamp,
    CONSTRAINT "classification_groups_pkey" PRIMARY KEY ("classification_group_id")
) WITH (oids = false);


CREATE SEQUENCE clf_call_clf_call_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."clf_call" (
    "clf_call_id" integer DEFAULT nextval('clf_call_clf_call_id_seq') NOT NULL,
    "email_customer_id" integer,
    "assigned_by" integer,
    "assigned_to" integer,
    "assigned_date" timestamp,
    "closed_by" integer,
    "closed_by_date" timestamp,
    "declined_by" integer,
    "declined_by_date" timestamp,
    "status" integer,
    "sec_company_id" integer,
    "caused_by" character varying(100),
    "cause" character varying(100),
    "result" character varying(100),
    "credit" double precision,
    "c_answer_id" integer,
    "c_clf1_q_id" integer,
    "c_clf2_q_id" integer,
    "c_nps_q_id" integer,
    "c_notes" text,
    "lane" integer,
    "onhold_by" integer,
    "onhold_date" timestamp,
    "clf_summary" character varying(2000),
    "modification_date" timestamp,
    "c_callback_q_id" integer,
    CONSTRAINT "clf_call_pkey" PRIMARY KEY ("clf_call_id")
) WITH (oids = false);

CREATE INDEX "clf_call_assigned_to_idx" ON "public"."clf_call" USING btree ("assigned_to");

CREATE INDEX "clf_call_c_answer_id_idx" ON "public"."clf_call" USING btree ("c_answer_id" DESC);

CREATE INDEX "clf_call_email_customer_id_idx" ON "public"."clf_call" USING btree ("email_customer_id");


CREATE TABLE "public"."clf_call_note" (
    "clf_call_note_id" integer NOT NULL,
    "clf_call_id" integer,
    "sec_company_id" integer,
    "note_type" integer,
    "note" text,
    "creation_date" timestamp,
    "creator" integer,
    "assigned_to" integer,
    CONSTRAINT "clf_call_note_pkey" PRIMARY KEY ("clf_call_note_id")
) WITH (oids = false);


CREATE TABLE "public"."clf_categories" (
    "clf_category_id" character varying(36) NOT NULL,
    "clf_category_name" character varying(100),
    "display_name" character varying(100),
    "ordernumber" integer,
    CONSTRAINT "clf_categories_pkey" PRIMARY KEY ("clf_category_id")
) WITH (oids = false);


CREATE TABLE "public"."clf_category_lists" (
    "item_id" integer NOT NULL,
    "list" character varying(50),
    "description" character varying(100),
    "ordernumber" integer,
    "template_id" integer,
    "email" character varying(254),
    "sec_company_id" integer,
    CONSTRAINT "clf_category_lists_pkey" PRIMARY KEY ("item_id")
) WITH (oids = false);


CREATE SEQUENCE clf_favorites_clf_favorite_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."clf_favorites" (
    "clf_favorite_id" integer DEFAULT nextval('clf_favorites_clf_favorite_id_seq') NOT NULL,
    "call_id" integer NOT NULL,
    "sec_user_id" integer NOT NULL,
    CONSTRAINT "clf_favorites_pkey" PRIMARY KEY ("clf_favorite_id")
) WITH (oids = false);


CREATE TABLE "public"."clf_instructions" (
    "clf_instruction_id" integer NOT NULL,
    "clf_instruction" text,
    "sec_company_id" integer,
    CONSTRAINT "clf_instructions_pkey" PRIMARY KEY ("clf_instruction_id")
) WITH (oids = false);


CREATE TABLE "public"."clf_mailtemplates" (
    "clf_mailtemplate_id" integer NOT NULL,
    "clf_mailtype" integer,
    "subject" character varying(200),
    "mailtext" text,
    "clf_mailname" character varying(50),
    "sec_company_id" integer,
    "valuelist_name" character varying(50),
    "base_template" integer,
    CONSTRAINT "clf_mailtemplates_pkey" PRIMARY KEY ("clf_mailtemplate_id")
) WITH (oids = false);


CREATE SEQUENCE clf_tags_clf_tag_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."clf_tags" (
    "clf_tag_id" integer DEFAULT nextval('clf_tags_clf_tag_id_seq') NOT NULL,
    "clf_call_id" integer,
    "created_by" integer,
    "created_on" timestamp,
    "tag_id" integer,
    CONSTRAINT "clf_tags_pkey" PRIMARY KEY ("clf_tag_id")
) WITH (oids = false);


CREATE SEQUENCE connector_connector_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."connector" (
    "connector_id" integer DEFAULT nextval('connector_connector_id_seq') NOT NULL,
    "connector_object" text,
    "sec_company_id" integer,
    "creator" character varying(200),
    "modifier" character varying(200),
    "modification_date" timestamp,
    "creation_date" timestamp,
    "connector_type" integer,
    CONSTRAINT "connector_pkey" PRIMARY KEY ("connector_id")
) WITH (oids = false);


CREATE TABLE "public"."contacts" (
    "contact_id" character varying(36) NOT NULL,
    "project_id" character varying(36),
    "contact_type" integer,
    "contact_initials" character varying(15),
    "contact_name" character varying(100),
    "contact_salutation" character varying(20),
    "contact_email" character varying(300),
    "contact_company" character varying(200),
    "object_address" character varying(100),
    "object_postal_code" character varying(10),
    "object_house_number" integer,
    "object_nr_addition" character varying(10),
    "contact_phone1" character varying(20),
    "creation_date" timestamp,
    "modification_date" timestamp,
    "object_fulladdress" character varying(120),
    "creator" integer,
    "modifier" integer,
    "object_buildnr" character varying(20),
    "object_city" character varying(50),
    "contact_nr" integer,
    "sec_company_id" integer,
    "contact_language" character varying(2),
    CONSTRAINT "contacts_pkey" PRIMARY KEY ("contact_id")
) WITH (oids = false);


CREATE SEQUENCE csi_data_csi_data_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."csi_data" (
    "csi_data_id" integer DEFAULT nextval('csi_data_csi_data_id_seq') NOT NULL,
    "report_year" integer NOT NULL,
    "csi_1" numeric,
    "csi_2" numeric,
    "csi_3" numeric,
    "csi_4" numeric,
    "csi_5" numeric,
    "csi_6" numeric,
    "csi_7" numeric,
    "created_on" timestamp,
    "created_by" integer,
    "company_id" integer,
    "first_date" timestamp,
    "last_date" timestamp,
    "completed_surveys" double precision,
    "nps" double precision,
    CONSTRAINT "csi_data_pkey" PRIMARY KEY ("csi_data_id")
) WITH (oids = false);


CREATE SEQUENCE customer_blacklist_customer_blacklist_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."customer_blacklist" (
    "customer_blacklist_id" integer DEFAULT nextval('customer_blacklist_customer_blacklist_id_seq') NOT NULL,
    "date_blacklisted" timestamp,
    "customer_email" character varying(300),
    "sec_company_id" integer,
    "brand" character varying(100),
    "customer_email_md5" character varying(50),
    "phone" character varying(50),
    "phone_md5" character varying(50),
    CONSTRAINT "customer_blacklist_pkey" PRIMARY KEY ("customer_blacklist_id")
) WITH (oids = false);


CREATE TABLE "public"."customer_log" (
    "customer_log_id" character varying(36) NOT NULL,
    "creation_date" timestamp,
    "sec_company_id" integer,
    "creator" integer,
    "note" character varying(2000),
    "form_name" character varying(50),
    "field_label" character varying(50),
    "field_name" character varying(400),
    "old_value" character varying(200000),
    "new_value" character varying(200000),
    CONSTRAINT "customer_log_pkey" PRIMARY KEY ("customer_log_id")
) WITH (oids = false);


CREATE TABLE "public"."d_date" (
    "datekey" character varying(8) NOT NULL,
    "date" date,
    "weekdaynumber" character varying(5),
    "dayshort" character varying(5),
    "daylong" character varying(10),
    "weeknumber" character varying(5),
    "weekisonumber" character varying(5),
    "weekendflag" boolean,
    "weekday" character varying(7),
    "yearweekisonumber" character varying(10),
    "weekstartdate" date,
    "weekenddate" date,
    "month" character varying(5),
    "monthshort" character varying(5),
    "monthlong" character varying(10),
    "yearmonth" character varying(10),
    "monthstartdate" date,
    "monthenddate" date,
    "quarter" character varying(5),
    "quarterlong" character varying(10),
    "yearquarter" character varying(10),
    "quarterstartdate" date,
    "quarterenddate" date,
    "year" character varying(5),
    "yearstartdate" date,
    "yearenddate" date,
    "weekdays" character varying(5),
    "monthdays" character varying(5),
    "quarterdays" character varying(5),
    "yeardays" character varying(5),
    "weekworkdays" character varying(5),
    "monthworkdays" character varying(5),
    "quarterworkdays" character varying(5),
    "yearworkdays" character varying(5),
    "holidayflag" character varying(5),
    "datelastweek" date,
    "datelastmonth" date,
    "datelastquarter" date,
    "datelastyear" date,
    "datenextweek" date,
    "datenextmonth" date,
    "datenextquarter" date,
    "datenextyear" date,
    CONSTRAINT "d_date_pkey" PRIMARY KEY ("datekey")
) WITH (oids = false);


CREATE TABLE "public"."dashboard_data" (
    "id" character varying(50) NOT NULL,
    "dashboard" integer,
    "dt" timestamp,
    "data_obj" text,
    "data_year" character varying(4),
    "sec_company_id" integer,
    CONSTRAINT "dashboard_data_pkey" PRIMARY KEY ("id")
) WITH (oids = false);


CREATE TABLE "public"."dashboard_settings" (
    "dashboard_setting_id" character varying(36) NOT NULL,
    "dashboard_name" character varying(50),
    "dashboard_link" character varying(500),
    "sort_order" integer,
    "sec_company_id" integer,
    CONSTRAINT "dashboard_settings_pkey" PRIMARY KEY ("dashboard_setting_id")
) WITH (oids = false);


CREATE SEQUENCE email_customer_email_customer_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."email_customer" (
    "email_customer_id" integer DEFAULT nextval('email_customer_email_customer_id_seq') NOT NULL,
    "xml_salutation" character varying(40),
    "xml_initials" character varying(70),
    "xml_customername" character varying(200),
    "xml_email" character varying(300),
    "xml_brand" character varying(100),
    "xml_rasnumber" character varying(70),
    "xml_technician" character varying(20),
    "xml_internal_external" character varying(50),
    "creator" character varying(200),
    "modifier" character varying(200),
    "creation_date" timestamp,
    "modification_date" timestamp,
    "send_date" date,
    "import_date" timestamp,
    "survey_done" integer,
    "remainder_send_date" timestamp,
    "sec_company_id" integer,
    "send_count" integer,
    "xml_readydate" timestamp,
    "email_template_id" integer,
    "xml_techgrp" character varying(20),
    "xml_acceptance_by" character varying(20),
    "thankmail_send" integer,
    "xml_visittime_end" timestamp,
    "xml_wvb" character varying(50),
    "xml_planning" character varying(50),
    "xml_department" integer,
    "surv_survey_id" integer,
    "blacklisted" integer,
    "thankmail_senddate" timestamp,
    "xml_address" character varying(100),
    "xml_fulladdress" character varying(120),
    "xml_city" character varying(50),
    "xml_refnr" character varying(100),
    "xml_language" character varying(5),
    "xml_region" character varying(12),
    "xml_enumber" character varying(18),
    "xml_prodarea" character varying(1),
    "xml_proddivision" character varying(3),
    "xml_proddate" character varying(4),
    "xml_purchasedate" timestamp,
    "xml_operationtime" integer,
    "xml_nrvisits" integer,
    "xml_originservice" character varying(10),
    "xml_acceptancedate" timestamp,
    "xml_lastvisitdate" timestamp,
    "xml_waitingtime" integer,
    "xml_outagetime" integer,
    "xml_grossinvoice" numeric,
    "xml_payment" character varying(3),
    "flag_sentthreshold" integer,
    "xml_causecomplaint" character varying(50),
    "xml_solution" character varying(50),
    "xml_complain_agent" character varying(25),
    "xml_closuredate" timestamp,
    "xml_phone1" character varying(50),
    "xml_phone2" character varying(50),
    "xml_projectid" integer,
    "xml_projectnm" character varying(150),
    "xml_amount" double precision,
    "xml_presentitem" character varying(50),
    "xml_employee" character varying(50),
    "xml_wa" character varying(2),
    "xml_custcreditnr" character varying(12),
    "xml_logisticpartner" character varying(5),
    "xml_companyname" character varying(150),
    "xml_projectnr" character varying(10),
    "xml_projectdiv" character varying(10),
    "xml_fkz" character varying(5),
    "xml_la" character varying(5),
    "error_code" integer,
    "surv_key" character varying(36),
    "xml_callcode" character varying(5),
    "xml_functioncode" character varying(2),
    "xml_rc" character varying(5),
    "xml_fc" character varying(5),
    "xml_accountindic" character varying(2),
    "xml_fident" character varying(54),
    "xml_consecnr" double precision,
    "xml_timezone" character varying(6),
    "xml_currency" character varying(5),
    "xml_techgrouptext" character varying(50),
    "xml_areatext" character varying(50),
    "sms_id" character varying(50),
    "xml_cp" character varying(20),
    "xml_sv" character varying(20),
    "flag_ext_source" double precision,
    "ext_id" character varying(50),
    "email_template_i18n_id" character varying(36),
    "sms_content_type" integer,
    "sms_session_id" character varying(50),
    "sms_status" integer,
    "short_url" character varying(50),
    "xml_dealer" character varying(100),
    "xml_dealer_pcode" character varying(10),
    "survey_accessed" integer DEFAULT '0',
    "xml_hybrisid" character varying(20),
    "is_anonymous" integer,
    "sent_medium" integer,
    CONSTRAINT "email_customer_pkey" PRIMARY KEY ("email_customer_id")
) WITH (oids = false);

CREATE INDEX "email_customer_creation_date_idx" ON "public"."email_customer" USING btree ("creation_date");

CREATE INDEX "email_customer_import_date_idx" ON "public"."email_customer" USING btree ("import_date");

CREATE INDEX "email_customer_sec_company_id_idx" ON "public"."email_customer" USING btree ("sec_company_id");

CREATE INDEX "email_customer_send_date_idx" ON "public"."email_customer" USING btree ("send_date");

CREATE INDEX "email_customer_surv_key_idx" ON "public"."email_customer" USING btree ("surv_key");

CREATE INDEX "email_customer_surv_survey_id_idx" ON "public"."email_customer" USING btree ("surv_survey_id");

CREATE INDEX "email_customer_survey_done_idx" ON "public"."email_customer" USING btree ("survey_done");

CREATE INDEX "email_customer_xml_department_idx" ON "public"."email_customer" USING btree ("xml_department");

CREATE INDEX "email_customer_xml_email_idx" ON "public"."email_customer" USING btree ("xml_email");

CREATE INDEX "email_customer_xml_projectnr_idx" ON "public"."email_customer" USING btree ("xml_projectnr");

CREATE INDEX "email_customer_xml_rasnumber_idx" ON "public"."email_customer" USING btree ("xml_rasnumber");

CREATE INDEX "email_customer_xml_readydate_idx" ON "public"."email_customer" USING btree ("xml_readydate");

CREATE INDEX "email_customer_xml_techgrp_idx" ON "public"."email_customer" USING btree ("xml_techgrp");


CREATE SEQUENCE email_log_email_log_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."email_log" (
    "email_log_id" integer DEFAULT nextval('email_log_email_log_id_seq') NOT NULL,
    "send_date" timestamp,
    "firstmailing" integer,
    "remindermailing" integer,
    "thankmailing" integer,
    "firstsms" integer,
    "remindersms" integer,
    "thanksms" integer,
    "sec_company_id" integer,
    "queuesms" integer,
    CONSTRAINT "email_log_pkey" PRIMARY KEY ("email_log_id")
) WITH (oids = false);


CREATE SEQUENCE email_month_average_send_email_month_average_send_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."email_month_average_send" (
    "email_month_average_send_id" integer DEFAULT nextval('email_month_average_send_email_month_average_send_id_seq') NOT NULL,
    "check_date" timestamp,
    "sunday" integer,
    "monday" integer,
    "wednesday" integer,
    "thursday" integer,
    "friday" integer,
    "saturday" integer,
    "sec_company_id" integer,
    "year_nr" integer,
    "month_nr" integer,
    "tuesday" integer,
    CONSTRAINT "email_month_average_send_pkey" PRIMARY KEY ("email_month_average_send_id")
) WITH (oids = false);


CREATE SEQUENCE email_template_email_template_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."email_template" (
    "email_template_id" integer DEFAULT nextval('email_template_email_template_id_seq') NOT NULL,
    "logo" bytea,
    "emailtext" text,
    "companyname" character varying(200),
    "email" character varying(300),
    "triggername" character varying(50),
    "email_subject" character varying(200),
    "emailtext_remainder" text,
    "modifier" character varying(200),
    "creator" character varying(200),
    "modification_date" timestamp,
    "creation_date" timestamp,
    "sec_company_id" integer,
    "remainder_afterdays" integer,
    "close_survey_days" integer,
    "remainder_count" integer,
    "remainder_subject" character varying(200),
    "send_thank_mail" integer,
    "emailtext_thankmail" text,
    "subject_thankmail" character varying(200),
    "template_name" character varying(200),
    "base_template" integer,
    "template_info" character varying(500),
    "email_reply" character varying(300),
    "sender_sms" character varying(20),
    CONSTRAINT "email_template_pkey" PRIMARY KEY ("email_template_id")
) WITH (oids = false);

CREATE INDEX "email_template_companyname_idx" ON "public"."email_template" USING btree ("companyname");


CREATE TABLE "public"."email_template_i18n" (
    "email_template_i18n_id" character varying(36) NOT NULL,
    "email_template_id" integer,
    "language_key" character varying(5),
    "sec_company_id" integer,
    "email_template_type" integer,
    "remainder_afterdays" integer,
    "remainder_count" integer,
    "send_thank_mail" integer,
    "emailtext" text,
    "emailtext_remainder" text,
    "subject_remainder" character varying(200),
    "subject" character varying(200),
    "migrate_id" character varying(50),
    "is_active" integer,
    "base_template" integer,
    "smstext_thank" character varying(500),
    "send_thank_sms" integer,
    "smstext" character varying(500),
    "smstext_reminder" character varying(500),
    "emailtext_thankmail_negative" text,
    "emailtext_thankmail_passive" text,
    "emailtext_thankmail_positive" text,
    "subject_thankmail_negative" character varying(200),
    "subject_thankmail_passive" character varying(200),
    "subject_thankmail_positive" character varying(200),
    "image_link" character varying(200),
    CONSTRAINT "email_template_i18n_pkey" PRIMARY KEY ("email_template_i18n_id")
) WITH (oids = false);


CREATE TABLE "public"."employees" (
    "id" character varying(36) NOT NULL,
    "external_id" character varying(50),
    "emp_firstname" character varying(100),
    "emp_lastname" character varying(100),
    "emp_function" character varying(50),
    "emp_phone" character varying(50),
    "emp_email" character varying(254),
    "sec_company_id" integer,
    CONSTRAINT "employees_pkey" PRIMARY KEY ("id")
) WITH (oids = false);


CREATE TABLE "public"."export_log" (
    "export_log_id" character varying(36) NOT NULL,
    "export_date" timestamp,
    "exported_by" integer,
    "sec_company_id" integer,
    "export_type" integer,
    "exported_lines" integer,
    "export_filename" character varying(100),
    CONSTRAINT "export_log_pkey" PRIMARY KEY ("export_log_id")
) WITH (oids = false);


CREATE TABLE "public"."feedback_properties" (
    "feedback_property_id" character varying(50) NOT NULL,
    "property_name" character varying(200),
    "property_value" text,
    CONSTRAINT "feedback_properties_pkey" PRIMARY KEY ("feedback_property_id")
) WITH (oids = false);


CREATE TABLE "public"."hot_news" (
    "hot_news_id" character varying(36) NOT NULL,
    "modified_by" integer,
    "modified_on" timestamp,
    "creation_date" timestamp,
    "creator" integer,
    "hot_news_title" character varying(50),
    "description" text,
    "is_published" integer,
    "published_date" timestamp,
    "hot_news_link" character varying(2000),
    "company_type" integer,
    CONSTRAINT "hot_news_pkey" PRIMARY KEY ("hot_news_id")
) WITH (oids = false);


CREATE TABLE "public"."i18n" (
    "message_id" integer NOT NULL,
    "message_key" character varying(150) NOT NULL,
    "message_language" character varying(5),
    "message_value" text,
    CONSTRAINT "i18n_pkey" PRIMARY KEY ("message_id")
) WITH (oids = false);

CREATE INDEX "i18n_m_l" ON "public"."i18n" USING btree ("message_language");

CREATE INDEX "i18n_message_key_message_language_index" ON "public"."i18n" USING btree ("message_key", "message_language");


CREATE SEQUENCE import_log_import_log_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."import_log" (
    "import_log_id" integer DEFAULT nextval('import_log_import_log_id_seq') NOT NULL,
    "log_dt" timestamp,
    "sec_company_id" integer,
    "file_count" integer,
    "file_rows" integer,
    "file_dups" integer,
    "in_db" integer,
    "imported_rows" integer,
    "log_data" character varying(500),
    CONSTRAINT "import_log_pkey" PRIMARY KEY ("import_log_id")
) WITH (oids = false);


CREATE TABLE "public"."languages" (
    "language_id" integer NOT NULL,
    "lang_369_2" character varying(3),
    "lang_369_1" character varying(2),
    "lang_desc" character varying(100),
    "isselectable" integer,
    CONSTRAINT "languages_pkey" PRIMARY KEY ("language_id")
) WITH (oids = false);

CREATE INDEX "languages_lang_369_2_idx" ON "public"."languages" USING btree ("lang_369_2");


CREATE SEQUENCE seq_log_events_log_event_id INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."log_events" (
    "log_event_id" integer DEFAULT nextval('seq_log_events_log_event_id') NOT NULL,
    "event_time" timestamp,
    "logger_name" character varying(100),
    "log_level" integer,
    "log_message" character varying(3000),
    "solution_name" character varying(50),
    "sec_user_id" integer,
    "form_name" character varying(50),
    "sec_company_id" integer,
    "client_id" character varying(36),
    "request_id" character varying(36),
    "additional_info" jsonb,
    CONSTRAINT "log_events_pkey" PRIMARY KEY ("log_event_id")
) WITH (oids = false);


CREATE TABLE "public"."log_management" (
    "log_management_id" character varying(36) NOT NULL,
    "logger_name" character varying(100),
    "logger_active" integer,
    "application_part" character varying(50),
    "sec_user_id" integer,
    "sec_company_id" integer,
    "log_level" character varying(5),
    CONSTRAINT "log_management_pkey" PRIMARY KEY ("log_management_id")
) WITH (oids = false);


CREATE SEQUENCE seq_mclases_item_mclas_item_id INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."mclass_item" (
    "mclass_item_id" integer DEFAULT nextval('seq_mclases_item_mclas_item_id') NOT NULL,
    "sec_company_id" integer,
    "item_label" character varying(300),
    "item_label_locale" character varying(300),
    "parent_id" integer,
    "deleted_on" timestamp,
    "deleted_by" character varying(120),
    "changed_on" timestamp,
    "changed_by" character varying(120),
    "created_on" timestamp,
    "created_by" character varying(120),
    "order_number" integer,
    "external_id" character varying(50),
    "graph_color" character varying(10),
    CONSTRAINT "mclass_item_pkey" PRIMARY KEY ("mclass_item_id")
) WITH (oids = false);


CREATE SEQUENCE mclass_item_local_mclass_item_local_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."mclass_item_local" (
    "mclass_item_local_id" integer DEFAULT nextval('mclass_item_local_mclass_item_local_id_seq') NOT NULL,
    "mclass_item_id" integer,
    "sec_company_id" integer,
    "item_label_locale" character varying(300),
    CONSTRAINT "mclass_item_local_pkey" PRIMARY KEY ("mclass_item_local_id")
) WITH (oids = false);

CREATE INDEX "mclass_item_id_idx" ON "public"."mclass_item_local" USING btree ("mclass_item_id");


CREATE SEQUENCE seq_mclasesponse_mclasponse_id INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."mclass_proces_item" (
    "mclass_proces_item_id" integer DEFAULT nextval('seq_mclasesponse_mclasponse_id') NOT NULL,
    "item_id" integer,
    "process_id" integer,
    "parent_id" integer,
    "created_on" timestamp,
    "created_by" integer,
    "deleted_on" timestamp,
    "deleted_by" integer,
    "order_number" integer,
    "is_active" integer,
    "survey_id" numeric,
    CONSTRAINT "mclass_proces_item_pkey" PRIMARY KEY ("mclass_proces_item_id")
) WITH (oids = false);


CREATE SEQUENCE seq_mclasesponse_mclasponse_idd INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."mclass_response" (
    "mclass_response_id" integer DEFAULT nextval('seq_mclasesponse_mclasponse_idd') NOT NULL,
    "item_id" integer,
    "answer_id" integer,
    "is_liked" integer,
    "is_potential" integer,
    "istype" integer,
    "is_excluded" integer,
    "created_by" integer,
    "created_on" timestamp,
    "confidence" double precision,
    "record_status" integer,
    "modification_date" timestamp,
    CONSTRAINT "mclass_response_pkey" PRIMARY KEY ("mclass_response_id")
) WITH (oids = false);

CREATE INDEX "answer_id_idx" ON "public"."mclass_response" USING btree ("answer_id");

CREATE INDEX "item_id_idx" ON "public"."mclass_response" USING btree ("item_id");


CREATE TABLE "public"."mod_monitor" (
    "mod_monitor_id" character varying(36) NOT NULL,
    "modification_date" timestamp,
    "deletion_date" timestamp,
    "task_name" character varying(100),
    "cron_expression" character varying(50),
    CONSTRAINT "mod_monitor_pkey" PRIMARY KEY ("mod_monitor_id")
) WITH (oids = false);


CREATE TABLE "public"."mod_wccs_import_maps" (
    "map_id" character varying(50) NOT NULL,
    "template_id" character varying(50),
    "metafield_id" character varying(50),
    "map_destination_field" character varying(200),
    "map_destination_title" character varying(200),
    "map_source_field" character varying(200),
    "map_key" integer,
    "map_order" integer,
    "map_ismandatory" integer,
    CONSTRAINT "mod_wccs_import_maps_pkey" PRIMARY KEY ("map_id")
) WITH (oids = false);


CREATE TABLE "public"."mod_wccs_import_templates" (
    "template_id" character varying(50) NOT NULL,
    "metatable_id" character varying(50),
    "template_name" character varying(200),
    "template_firstrow" integer,
    "template_file_separator" character varying(5),
    "template_date_separator" character varying(5),
    "template_time_separator" character varying(5),
    "template_file_type" character varying(5),
    "template_last_imported" character varying(500),
    "sec_company_id" integer,
    "template_date_format" character varying(5),
    "mapped_fields" json,
    CONSTRAINT "mod_wccs_import_templates_pkey" PRIMARY KEY ("template_id")
) WITH (oids = false);


CREATE TABLE "public"."mod_wccs_meta_fields" (
    "metafield_id" character varying(50) NOT NULL,
    "metatable_id" character varying(50),
    "metafield_name" character varying(200),
    "metafield_isfield" integer,
    "metafield_title" character varying(50),
    "metafield_valuelist" character varying(50),
    "metafield_order" character varying(50),
    "metafield_ismandatory" integer,
    "metafield_isunique" integer,
    "metafield_limit" integer,
    "metafield_type" character varying(20),
    "metafield_is_system_field" integer,
    CONSTRAINT "mod_wccs_meta_fields_pkey" PRIMARY KEY ("metafield_id")
) WITH (oids = false);


CREATE TABLE "public"."mod_wccs_meta_tables" (
    "metatable_id" character varying(50) NOT NULL,
    "metatable_name" character varying(50),
    "metatable_title" character varying(50),
    "metatable_company_type" integer,
    CONSTRAINT "mod_wccs_meta_tables_pkey" PRIMARY KEY ("metatable_id")
) WITH (oids = false);


CREATE TABLE "nps_per_survey_creation_date" ("sec_company_id" integer, "surv_survey_id" integer, "promoters" bigint, "passives" bigint, "detractors" bigint, "records" bigint, "date" timestamp);


CREATE TABLE "nps_per_survey_readydate" ("sec_company_id" integer, "surv_survey_id" integer, "promoters" bigint, "passives" bigint, "detractors" bigint, "records" bigint, "date" timestamp);


CREATE TABLE "public"."pcategories" (
    "pcategory_id" character varying(36) NOT NULL,
    "category" character varying(100),
    "sec_company_id" integer,
    "category_nr" integer,
    CONSTRAINT "pcategories_pkey" PRIMARY KEY ("pcategory_id")
) WITH (oids = false);


CREATE TABLE "public"."project_groups" (
    "group_id" integer,
    "sec_company_id" integer,
    "project_group_id" character varying(50) NOT NULL,
    "project_id" character varying(50),
    CONSTRAINT "project_groups_pkey" PRIMARY KEY ("project_group_id")
) WITH (oids = false);


CREATE TABLE "public"."project_pcategories" (
    "project_pcategory_id" character varying(36) NOT NULL,
    "project_id" character varying(36),
    "pcategory_id" character varying(36),
    CONSTRAINT "project_pcategories_pkey" PRIMARY KEY ("project_pcategory_id")
) WITH (oids = false);


CREATE TABLE "public"."project_stages" (
    "project_stage_id" character varying(36) NOT NULL,
    "stage_id" character varying(36),
    "contact_id" character varying(36),
    "creation_date" timestamp,
    "email_customer_id" integer,
    "creator" character varying(200),
    CONSTRAINT "project_stages_pkey" PRIMARY KEY ("project_stage_id")
) WITH (oids = false);


CREATE TABLE "public"."projects" (
    "project_id" character varying(50) NOT NULL,
    "project_nr" character varying(20),
    "project_name" character varying(100),
    "sec_company_id" integer,
    "creation_date" timestamp,
    "creator" integer,
    "modification_date" timestamp,
    "modifier" integer,
    "project_city" character varying(50),
    "project_name_extern" character varying(100),
    "enable_thankmail" integer,
    "coupon_text" text,
    CONSTRAINT "projects_pkey" PRIMARY KEY ("project_id")
) WITH (oids = false);


CREATE TABLE "public"."publication_reports" (
    "sec_company_id" integer,
    "project_id" text,
    "project_nr" character varying(50),
    "avg_score" double precision,
    "publication_report_id" character varying(36) NOT NULL,
    "based_on" integer,
    "modification_date" timestamp,
    "based_on_neg" integer,
    "based_on_pos" integer,
    "based_on_pas" integer,
    "brand" character varying(50),
    "survey_category_id" integer,
    CONSTRAINT "publication_reports_pkey" PRIMARY KEY ("publication_report_id")
) WITH (oids = false);

CREATE INDEX "publication_reports_project_id_index" ON "public"."publication_reports" USING btree ("project_id");

CREATE INDEX "publication_reports_project_id_survey_category_id_index" ON "public"."publication_reports" USING btree ("project_id", "survey_category_id");

CREATE INDEX "publication_reports_sec_company_id_index" ON "public"."publication_reports" USING btree ("sec_company_id");

CREATE INDEX "publication_reports_survey_category_id_index" ON "public"."publication_reports" USING btree ("survey_category_id");


CREATE TABLE "public"."publications" (
    "publication_id" character varying(36) NOT NULL,
    "sec_company_id" integer,
    "project_id" text,
    "project_nr" character varying(50),
    "nps" integer,
    "survey_name" character varying(50),
    "surv_survey_id" integer,
    "response_date" timestamp,
    "strengths" character varying(2000),
    "improvements" character varying(2000),
    "other_answers" text,
    "score" double precision,
    "surv_surveyanswer_id" integer,
    "respondent_initials" character varying(50),
    "respondent_customername" character varying(50),
    "brand" character varying(50),
    CONSTRAINT "publications_pkey" PRIMARY KEY ("publication_id")
) WITH (oids = false);


CREATE TABLE "public"."q_notifications" (
    "q_notification_id" character varying(36) NOT NULL,
    "surv_surveyanswer_id" integer,
    "is_processed" integer,
    "company_id" integer,
    CONSTRAINT "q_notifications_pkey" PRIMARY KEY ("q_notification_id")
) WITH (oids = false);


CREATE TABLE "public"."questi_answer" (
    "questi_answer_id" integer NOT NULL,
    "creation_date" timestamp,
    "answer_date" timestamp,
    "answer_value" integer,
    "questi_survey_id" integer,
    "sec_user_id" integer,
    "sec_company_id" integer,
    "questi_survey_type" integer,
    CONSTRAINT "questi_answer_pkey" PRIMARY KEY ("questi_answer_id")
) WITH (oids = false);


CREATE TABLE "public"."questi_answer_items" (
    "questi_answer_item_id" integer NOT NULL,
    "questi_answer_id" integer,
    "questi_question_id" integer,
    "answer_text" text,
    CONSTRAINT "questi_answer_items_pkey" PRIMARY KEY ("questi_answer_item_id")
) WITH (oids = false);


CREATE TABLE "public"."questi_question_options" (
    "questi_option_id" integer NOT NULL,
    "questi_question_id" integer,
    "option_sort" integer,
    "option_key" character varying(255),
    "option_value" character varying(255),
    "option_action" character varying(255),
    CONSTRAINT "questi_question_options_pkey" PRIMARY KEY ("questi_option_id")
) WITH (oids = false);


CREATE TABLE "public"."questi_questions" (
    "questi_question_id" integer NOT NULL,
    "questi_survey_id" integer,
    "question" character varying(4000),
    "question_type" integer,
    "question_order" integer,
    "question_options" character varying(4000),
    CONSTRAINT "questi_questions_pkey" PRIMARY KEY ("questi_question_id")
) WITH (oids = false);


CREATE TABLE "public"."questi_survey" (
    "questi_survey_id" integer NOT NULL,
    "creation_date" timestamp,
    "modification_date" timestamp,
    "creator" character varying(200),
    "modifier" character varying(200),
    "survey_type" integer,
    "survey_question" text,
    "survey_title" character varying(200),
    "survey_question_isnps" integer,
    "survey_isactive" integer,
    "survey_answer_lo" character varying(200),
    "survey_answer_hi" character varying(200),
    "sec_company_id" integer,
    "feedback_text" character varying(512),
    "logo" bytea,
    "flag_deleted" integer,
    "deleted_by" character varying(50),
    "deletion_date" timestamp,
    "feedback_isactive" integer,
    "survey_start_button_text" character varying(150),
    CONSTRAINT "questi_survey_pkey" PRIMARY KEY ("questi_survey_id")
) WITH (oids = false);


CREATE TABLE "public"."redacted_answers" (
    "redacted_id" integer NOT NULL,
    "surv_surveyansweritem_id" integer,
    "return_value" text,
    "sec_company_id" integer,
    CONSTRAINT "redacted_answers_pkey" PRIMARY KEY ("redacted_id")
) WITH (oids = false);

CREATE INDEX "redacted_answers_surv_surveyansweritem_id_idx" ON "public"."redacted_answers" USING btree ("surv_surveyansweritem_id");


CREATE SEQUENCE report_layout_report_layout_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."report_layout" (
    "report_layout_id" integer DEFAULT nextval('report_layout_report_layout_id_seq') NOT NULL,
    "layout_name" character varying(100),
    "layout_data" text,
    "sec_company_id" integer,
    "created_by" integer,
    "created_on" timestamp,
    "modified_by" integer,
    "modified_on" timestamp,
    "tags" character varying(500),
    "layout_type" integer,
    "is_home" integer,
    CONSTRAINT "report_layout_pkey" PRIMARY KEY ("report_layout_id")
) WITH (oids = false);


CREATE SEQUENCE report_layout_shared_report_layout_shared_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."report_layout_shared" (
    "report_layout_shared_id" integer DEFAULT nextval('report_layout_shared_report_layout_shared_id_seq') NOT NULL,
    "report_layout_id" integer,
    "sec_user_id" integer,
    "sec_company_id" integer,
    "last_viewed" timestamp,
    CONSTRAINT "report_layout_shared_pkey" PRIMARY KEY ("report_layout_shared_id")
) WITH (oids = false);


CREATE SEQUENCE rl_tags_rl_tag_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."report_layout_tags" (
    "rl_tag_id" integer DEFAULT nextval('rl_tags_rl_tag_id_seq') NOT NULL,
    "tag_id" integer,
    "rl_id" integer,
    "created_by" integer,
    "created_on" timestamp,
    CONSTRAINT "rl_tags_pkey" PRIMARY KEY ("rl_tag_id")
) WITH (oids = false);


CREATE SEQUENCE report_templates_report_template_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."report_templates" (
    "report_template_id" integer DEFAULT nextval('report_templates_report_template_id_seq') NOT NULL,
    "report_name" character varying(100),
    "short_description" character varying(300),
    "report_image" character varying(50),
    "company_type" integer,
    "allowed_charttypes" character varying(200),
    "preferred_charttype" character varying(20),
    "restricted_filters" character varying(200),
    "for_parent_company" integer,
    CONSTRAINT "report_templates_pkey" PRIMARY KEY ("report_template_id")
) WITH (oids = false);


CREATE TABLE "public"."response_classifications" (
    "response_classification_id" integer NOT NULL,
    "surv_surveyanswer_id" integer,
    "classification_id" integer,
    "creator" integer,
    "creation_date" timestamp,
    "classification_type" integer,
    CONSTRAINT "response_classifications_pkey" PRIMARY KEY ("response_classification_id")
) WITH (oids = false);

CREATE INDEX "response_classifications_classification_id_idx" ON "public"."response_classifications" USING btree ("classification_id");

CREATE INDEX "response_classifications_surv_surveyanswer_id_idx" ON "public"."response_classifications" USING btree ("surv_surveyanswer_id");


CREATE SEQUENCE salutation_items_salutation_item_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."salutation_items" (
    "salutation_item_id" integer DEFAULT nextval('salutation_items_salutation_item_id_seq') NOT NULL,
    "salutation_id" integer,
    "salutation_item" character varying(50),
    "creator" integer,
    "creation_date" timestamp,
    "sec_company_id" integer,
    CONSTRAINT "salutation_items_pkey" PRIMARY KEY ("salutation_item_id")
) WITH (oids = false);


CREATE SEQUENCE salutations_salutation_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."salutations" (
    "salutation_id" integer DEFAULT nextval('salutations_salutation_id_seq') NOT NULL,
    "salutation_greet" character varying(50),
    "salutation_language" character varying(5),
    "creator" integer,
    "creation_date" timestamp,
    "modified_by" integer,
    "modified_on" timestamp,
    "sec_company_id" integer,
    CONSTRAINT "salutations_pkey" PRIMARY KEY ("salutation_id")
) WITH (oids = false);


CREATE TABLE "public"."searchlists" (
    "sl_name" character varying(50),
    "sl_id" integer NOT NULL,
    "sl_values" text,
    "sec_company_id" integer,
    CONSTRAINT "searchlists_pkey" PRIMARY KEY ("sl_id")
) WITH (oids = false);


CREATE TABLE "public"."sec_clf_filters" (
    "filter_id" integer NOT NULL,
    "sec_user_id" integer,
    "filter_obj" text,
    CONSTRAINT "sec_clf_filters_pkey" PRIMARY KEY ("filter_id")
) WITH (oids = false);


CREATE SEQUENCE sec_company_sec_company_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."sec_company" (
    "sec_company_id" integer DEFAULT nextval('sec_company_sec_company_id_seq') NOT NULL,
    "company_name" character varying(200),
    "modification_date" timestamp,
    "creation_date" timestamp,
    "modifier" character varying(200),
    "creator" character varying(200),
    "company_type" integer,
    "default_language_key" character varying(5),
    "jasper_settings" text,
    "country_code" character varying(5),
    "send_hour" integer,
    "time_zone" character varying(50),
    "api_key" character varying(50),
    "api_secret" character varying(50),
    "api_date" timestamp,
    "company_code" character varying(50),
    "gdpr_months" integer,
    "last_run_by" integer,
    "last_run_on" timestamp,
    "is_parent_company" integer,
    "sec_company_uid" bytea,
    "gdpr_oldest_dt" timestamp,
    CONSTRAINT "sec_company_pkey" PRIMARY KEY ("sec_company_id")
) WITH (oids = false);


CREATE TABLE "public"."sec_failed_logins" (
    "sec_failed_login_id" character varying(36) NOT NULL,
    "username" character varying(50),
    "expires_on" timestamp,
    "captcha_used" integer,
    "captcha_failed" integer,
    CONSTRAINT "sec_failed_logins_pkey" PRIMARY KEY ("sec_failed_login_id")
) WITH (oids = false);


CREATE SEQUENCE sec_group_group_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."sec_group" (
    "group_id" integer DEFAULT nextval('sec_group_group_id_seq') NOT NULL,
    "name" character varying(500),
    "description" character varying(4000),
    "sec_company_id" integer,
    CONSTRAINT "sec_group_pkey" PRIMARY KEY ("group_id")
) WITH (oids = false);


CREATE TABLE "public"."sec_key" (
    "key_id" integer NOT NULL,
    "name" character varying(50),
    "description" character varying(200),
    "sort_order" integer,
    "solution_type" integer,
    CONSTRAINT "sec_key_pkey" PRIMARY KEY ("key_id")
) WITH (oids = false);


CREATE TABLE "public"."sec_parent_company_link" (
    "sec_parent_company_link_id" character varying(36) NOT NULL,
    "modification_date" timestamp,
    "creation_date" timestamp,
    "modifier" character varying(200),
    "creator" character varying(200),
    "sec_company_id_parent" integer,
    "sec_company_id_child" integer,
    CONSTRAINT "sec_parent_company_link_pkey" PRIMARY KEY ("sec_parent_company_link_id")
) WITH (oids = false);


CREATE SEQUENCE sec_passwords_sec_password_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."sec_passwords" (
    "sec_password_id" integer DEFAULT nextval('sec_passwords_sec_password_id_seq') NOT NULL,
    "sec_user_id" integer,
    "is_active" integer,
    "valid_till" timestamp,
    "pass" character varying(100),
    CONSTRAINT "sec_passwords_pkey" PRIMARY KEY ("sec_password_id")
) WITH (oids = false);


CREATE SEQUENCE sec_programsettings_sec_programsetting_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."sec_programsettings" (
    "sec_programsetting_id" integer DEFAULT nextval('sec_programsettings_sec_programsetting_id_seq') NOT NULL,
    "ftp_path" character varying(200),
    "ftp_password" character varying(50),
    "ftp_username" character varying(50),
    "ftp_port" integer,
    "ftp_host" character varying(100),
    "sec_company_id" integer,
    "survey_link" text,
    "survey_linkdisplay" text,
    "ftp_lastimport_time" timestamp,
    "survey_previewaddress" text,
    "optout_link" text,
    "optout_linkdisplay" text,
    "ftp_usesftp" integer,
    "sent_threshold" character varying(500),
    "import_filter" character varying(500),
    "export_mailer" character varying(500),
    "company_properties" text,
    "file_encoding" character varying(50),
    "pwd_score" integer,
    "pwd_settings" text,
    "event_code" text,
    "disclaimer" text,
    "imprint_link" character varying(200),
    "export_prefix" character varying(3),
    "export_to_ftp" integer,
    "benchmark_fase1_surv_survey_id" integer,
    "benchmark_fase2_surv_survey_id" integer,
    "benchmark_fase3_surv_survey_id" integer,
    "benchmark_fase4_surv_survey_id" integer,
    "flag_benchmark" integer,
    "neutral_sentiment" integer,
    CONSTRAINT "sec_programsettings_pkey" PRIMARY KEY ("sec_programsetting_id")
) WITH (oids = false);

CREATE INDEX "sec_programsettings_sec_company_id_idx" ON "public"."sec_programsettings" USING btree ("sec_company_id");


CREATE TABLE "public"."sec_tables" (
    "sec_table_id" character varying(50) NOT NULL,
    "table_name" character varying(100),
    "company_type" integer,
    "display_name" character varying(100),
    CONSTRAINT "sec_tables_pkey" PRIMARY KEY ("sec_table_id")
) WITH (oids = false);


CREATE TABLE "public"."sec_user_company" (
    "sec_company_id" integer NOT NULL,
    "sec_user_id" integer NOT NULL,
    "sec_group" integer NOT NULL,
    "modifier" character varying(200),
    "creator" character varying(200),
    "modification_date" timestamp,
    "creation_date" timestamp,
    CONSTRAINT "sec_user_company_pkey" PRIMARY KEY ("sec_company_id", "sec_user_id")
) WITH (oids = false);


CREATE SEQUENCE sec_user_login_sec_user_login_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."sec_user_login" (
    "sec_user_login_id" integer DEFAULT nextval('sec_user_login_sec_user_login_id_seq') NOT NULL,
    "sec_user_id" integer,
    "sec_company_id" integer,
    "creation_date" timestamp,
    CONSTRAINT "sec_user_login_pkey" PRIMARY KEY ("sec_user_login_id")
) WITH (oids = false);


CREATE TABLE "public"."sec_user_nav_log" (
    "sec_user_nav_log_uuid" integer NOT NULL,
    "sec_user_id" integer NOT NULL,
    "nav_path" integer NOT NULL,
    "nav_date" timestamp NOT NULL,
    "client_id" character varying(36) NOT NULL,
    "info" character varying(500),
    CONSTRAINT "sec_user_nav_log_pkey" PRIMARY KEY ("sec_user_nav_log_uuid")
) WITH (oids = false);


CREATE SEQUENCE sec_user_right_user_right_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."sec_user_right" (
    "user_right_id" integer DEFAULT nextval('sec_user_right_user_right_id_seq') NOT NULL,
    "group_id" integer,
    "key_id" integer,
    CONSTRAINT "sec_user_right_pkey" PRIMARY KEY ("user_right_id")
) WITH (oids = false);


CREATE TABLE "public"."sec_user_table_prop" (
    "sec_user_table_prop_id" integer NOT NULL,
    "user_id" integer,
    "form_name" character varying(100),
    "element_name" character varying(50),
    "width" integer,
    "height" integer,
    "location_x" integer,
    "location_y" integer,
    CONSTRAINT "sec_user_table_prop_pkey" PRIMARY KEY ("sec_user_table_prop_id")
) WITH (oids = false);


CREATE SEQUENCE sec_user_ws_token_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1;

CREATE TABLE "public"."sec_user_ws_token" (
    "sec_user_ws_token_id" integer DEFAULT nextval('sec_user_ws_token_seq') NOT NULL,
    "sec_user_id" integer,
    "sec_company_id" integer,
    "token" character varying(50),
    "creation_date" timestamp,
    "expiry_date" timestamp,
    "token_privilege" integer,
    "hash" character varying(50),
    "ip_range" character varying(200),
    CONSTRAINT "sec_user_ws_token_pkey" PRIMARY KEY ("sec_user_ws_token_id")
) WITH (oids = false);


CREATE SEQUENCE sec_users_sec_user_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."sec_users" (
    "sec_user_id" integer DEFAULT nextval('sec_users_sec_user_id_seq') NOT NULL,
    "username" character varying(50),
    "user_password" character varying(50),
    "email" character varying(500),
    "modifier" character varying(200),
    "creator" character varying(200),
    "modification_date" timestamp,
    "cration_date" timestamp,
    "sec_group" integer,
    "activation_pwd" character varying(50),
    "activation_date" timestamp,
    "failed_count" integer,
    "failed_last_date" timestamp,
    "firstname" character varying(50),
    "islocked" integer,
    "last_api_contact" timestamp,
    "phone" character varying(50),
    "job_title" character varying(50),
    "date_of_birth" timestamp,
    "work_anniversary" timestamp,
    "avatar" bytea,
    "avatar_type" character varying(50),
    "salutation" character varying(5),
    "new_news_articles" integer,
    "has_opened_settings" integer,
    CONSTRAINT "sec_users_pkey" PRIMARY KEY ("sec_user_id")
) WITH (oids = false);


CREATE TABLE "public"."stages" (
    "stage_id" character varying(36) NOT NULL,
    "stage_name" character varying(100),
    "stage_sequence" integer,
    "sec_company_id" integer,
    "stage_active" integer,
    "contact_type" integer,
    "creation_date" timestamp,
    "modification_date" timestamp,
    "department_id" integer,
    "creator" integer,
    "modifier" integer,
    CONSTRAINT "stages_pkey" PRIMARY KEY ("stage_id")
) WITH (oids = false);


CREATE TABLE "public"."stop_list" (
    "stop_id" character varying(36) NOT NULL,
    "stop_word" character varying(100),
    "creator" integer,
    "creation_date" timestamp,
    "sec_company_id" integer,
    CONSTRAINT "stop_list_pkey" PRIMARY KEY ("stop_id")
) WITH (oids = false);


CREATE TABLE "public"."subcontractors" (
    "id" character varying(36) NOT NULL,
    "name" character varying(100),
    "external_id" character varying(50),
    "email" character varying(254),
    "sec_company_id" integer,
    CONSTRAINT "subcontractors_pkey" PRIMARY KEY ("id")
) WITH (oids = false);


CREATE SEQUENCE seq_surv_parse_id INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_answer_preparse" (
    "surv_answer_preparse_id" integer DEFAULT nextval('seq_surv_parse_id') NOT NULL,
    "creation_date" timestamp,
    "visit_date" timestamp,
    "surv_surveyanswer_id" integer,
    "nps" integer,
    "pos" text,
    "neg" text,
    CONSTRAINT "surv_answer_preparse_pkey" PRIMARY KEY ("surv_answer_preparse_id")
) WITH (oids = false);

CREATE INDEX "surv_answer_preparse_creation_date_index" ON "public"."surv_answer_preparse" USING btree ("creation_date");

CREATE INDEX "surv_answer_preparse_surv_surveyanswer_id_index" ON "public"."surv_answer_preparse" USING btree ("surv_surveyanswer_id");

CREATE INDEX "surv_answer_preparse_visit_date_index" ON "public"."surv_answer_preparse" USING btree ("visit_date");


CREATE TABLE "public"."surv_categories" (
    "category_id" integer NOT NULL,
    "category" character varying(100),
    "sec_company_id" integer,
    CONSTRAINT "surv_categories_pkey" PRIMARY KEY ("category_id")
) WITH (oids = false);


CREATE TABLE "public"."surv_categories_local" (
    "surv_category_local_id" integer NOT NULL,
    "category_id" integer,
    "sec_company_id" integer,
    "category" character varying(100),
    CONSTRAINT "surv_categories_local_pkey" PRIMARY KEY ("surv_category_local_id")
) WITH (oids = false);


CREATE SEQUENCE surv_coupon_surv_coupon_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_coupon" (
    "surv_coupon_id" integer DEFAULT nextval('surv_coupon_surv_coupon_id_seq') NOT NULL,
    "coupon_code" character varying(200),
    "start_date" timestamp,
    "end_date" timestamp,
    "email_customer_id" integer,
    "email_date" timestamp,
    "triggername" character varying(50),
    "sec_company_id" integer,
    CONSTRAINT "surv_coupon_pkey" PRIMARY KEY ("surv_coupon_id")
) WITH (oids = false);

CREATE INDEX "surv_coupon_email_customer_id_index" ON "public"."surv_coupon" USING btree ("email_customer_id");

CREATE INDEX "surv_coupon_sec_company_id_index" ON "public"."surv_coupon" USING btree ("sec_company_id");

CREATE INDEX "surv_coupon_triggername_index" ON "public"."surv_coupon" USING btree ("triggername");


CREATE TABLE "public"."surv_departments" (
    "department_name" character varying(50),
    "department_id" integer NOT NULL,
    "sec_company_id" integer,
    "department_key" character varying(50),
    "department_key2" character varying(50),
    CONSTRAINT "surv_departments_pkey" PRIMARY KEY ("department_id")
) WITH (oids = false);


CREATE TABLE "public"."surv_i18n" (
    "surv_i18n_id" integer NOT NULL,
    "surv_survey_id" integer,
    "language_key" character varying(5),
    "isactive" integer,
    "language_description" character varying(50),
    CONSTRAINT "surv_i18n_pkey" PRIMARY KEY ("surv_i18n_id")
) WITH (oids = false);


CREATE TABLE "public"."surv_imported_contacts" (
    "surv_imported_contact_id" character varying(36) NOT NULL,
    "department_id" integer,
    "brand" character varying(100),
    "email" character varying(300),
    "salutation" character varying(50),
    "initials" character varying(35),
    "customername" character varying(100),
    "contact_date" timestamp,
    "creator" character varying(200),
    "creation_date" timestamp,
    "modifier" character varying(200),
    "modification_date" timestamp,
    "sec_company_id" integer,
    "contact_language" character varying(5),
    "rasnumber" character varying(40),
    "enumber" character varying(18),
    "fdnumber" integer,
    "phone1" character varying(20),
    "flag_threshold" integer,
    "phone2" character varying(20),
    "region" character varying(12),
    "prodarea" character varying(50),
    "technician" character varying(20),
    "purchase_date" timestamp,
    "call_code" character varying(5),
    "consecnr" double precision,
    "error_code" integer,
    "employee" character varying(20),
    "companyname" character varying(150),
    "acceptance_by" character varying(20),
    CONSTRAINT "surv_imported_contacts_pkey" PRIMARY KEY ("surv_imported_contact_id")
) WITH (oids = false);


CREATE TABLE "public"."surv_reports" (
    "report_id" integer NOT NULL,
    "report_name" character varying(50),
    "report_form" character varying(50),
    CONSTRAINT "surv_reports_pkey" PRIMARY KEY ("report_id")
) WITH (oids = false);


CREATE SEQUENCE surv_survey_surv_survey_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_survey" (
    "surv_survey_id" integer DEFAULT nextval('surv_survey_surv_survey_id_seq') NOT NULL,
    "creator" character varying(200),
    "modifier" character varying(200),
    "modification_date" timestamp,
    "creation_date" timestamp,
    "name" character varying(200),
    "email_template_id" integer,
    "sec_company_id" integer,
    "description" character varying(1000),
    "max_question_page" integer,
    "description_end" character varying(1000),
    "desc_surv_already_done" character varying(1000),
    "department" integer,
    "dashboard" integer,
    "survey_language" character varying(5),
    "on_dashboard2" integer,
    "is_clf" integer,
    "nps_question" integer,
    "clf_question1" integer,
    "clf_question2" integer,
    "callback_question" integer,
    "internal_name" character varying(200),
    "category" integer,
    "test_email" character varying(200),
    "test_mode" integer,
    "test_phone_nr" character varying(20),
    "surv_weighting_factor" double precision,
    "surv_target" integer,
    "touchpoint_name" character varying(100),
    "surv_uuid" bytea,
    "is_anonymous" integer,
    "bsh_category_id" integer,
    CONSTRAINT "surv_survey_pkey" PRIMARY KEY ("surv_survey_id")
) WITH (oids = false);

CREATE INDEX "surv_survey_name_idx" ON "public"."surv_survey" USING btree ("name");

CREATE INDEX "surv_survey_sec_company_id_idx" ON "public"."surv_survey" USING btree ("sec_company_id");

CREATE INDEX "surv_survey_surv_survey_id_idx" ON "public"."surv_survey" USING btree ("surv_survey_id");


CREATE SEQUENCE surv_survey_categories_surv_survey_category_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_survey_categories" (
    "surv_survey_category_id" integer DEFAULT nextval('surv_survey_categories_surv_survey_category_id_seq') NOT NULL,
    "surv_survey_id" integer,
    "category_id" integer,
    CONSTRAINT "surv_survey_categories_pkey" PRIMARY KEY ("surv_survey_category_id")
) WITH (oids = false);


CREATE SEQUENCE surv_survey_groups_surv_survey_group_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_survey_groups" (
    "surv_survey_group_id" integer DEFAULT nextval('surv_survey_groups_surv_survey_group_id_seq') NOT NULL,
    "surv_survey_id" integer,
    "group_id" integer,
    "sec_company_id" integer,
    CONSTRAINT "surv_survey_groups_pkey" PRIMARY KEY ("surv_survey_group_id")
) WITH (oids = false);


CREATE SEQUENCE surv_surveyanswer_surv_surveyanswer_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_surveyanswer" (
    "surv_surveyanswer_id" integer DEFAULT nextval('surv_surveyanswer_surv_surveyanswer_id_seq') NOT NULL,
    "creator" character varying(200),
    "modifier" character varying(200),
    "creation_date" timestamp,
    "modification_date" timestamp,
    "surv_survey_id" integer,
    "email_customer_id" integer,
    "sec_company_id" integer,
    "browser_version" character varying(50),
    "browser" character varying(50),
    "device" character varying(50),
    "os_version" character varying(50),
    "os" character varying(50),
    "creation_date_system" timestamp,
    "nps_value" integer,
    "comments_neg_length" integer,
    "comments_length" integer,
    "comments_pos_length" integer,
    "has_text" integer,
    "lock_date" timestamp,
    "lock_user" integer,
    "publication_id" character varying(36),
    "answers" jsonb,
    CONSTRAINT "surv_surveyanswer_pkey" PRIMARY KEY ("surv_surveyanswer_id")
) WITH (oids = false);

CREATE INDEX "surv_surveyanswer_creation_date_idx" ON "public"."surv_surveyanswer" USING btree ("creation_date");

CREATE INDEX "surv_surveyanswer_email_customer_id_idx" ON "public"."surv_surveyanswer" USING btree ("email_customer_id");

CREATE INDEX "surv_surveyanswer_nps_value" ON "public"."surv_surveyanswer" USING btree ("nps_value");

CREATE INDEX "surv_surveyanswer_sec_company_id_idx" ON "public"."surv_surveyanswer" USING btree ("sec_company_id");

CREATE INDEX "surv_surveyanswer_surv_survey_id_idx" ON "public"."surv_surveyanswer" USING btree ("surv_survey_id");


CREATE SEQUENCE surv_surveyansweritem_surv_surveyansweritem_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_surveyansweritem" (
    "surv_surveyansweritem_id" integer DEFAULT nextval('surv_surveyansweritem_surv_surveyansweritem_id_seq') NOT NULL,
    "creator" character varying(200),
    "modifier" character varying(200),
    "creation_date" timestamp,
    "modification_date" timestamp,
    "surv_surveyanswer_id" integer,
    "return_value" text,
    "surv_surveyquestion_id" integer,
    "surv_surveysubquestion_id" integer,
    CONSTRAINT "surv_surveyansweritem_pkey" PRIMARY KEY ("surv_surveyansweritem_id")
) WITH (oids = false);

CREATE INDEX "surv_surveyansweritem_creation_date_idx" ON "public"."surv_surveyansweritem" USING btree ("creation_date");

CREATE INDEX "surv_surveyansweritem_surv_surveyanswer_id_idx" ON "public"."surv_surveyansweritem" USING btree ("surv_surveyanswer_id");

CREATE INDEX "surv_surveyansweritem_surv_surveyquestion_id_idx" ON "public"."surv_surveyansweritem" USING btree ("surv_surveyquestion_id");


CREATE TABLE "public"."surv_surveymedium" (
    "surv_surveymedium_id" character varying(36) NOT NULL,
    "surv_survey_id" integer,
    "medium_id" integer,
    "medium_order" integer,
    "creator" integer,
    "creation_date" timestamp,
    "modifier" integer,
    "modification_date" timestamp,
    CONSTRAINT "surv_surveymedium_pkey" PRIMARY KEY ("surv_surveymedium_id")
) WITH (oids = false);


CREATE SEQUENCE surv_surveypart_surv_surveypart_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_surveypart" (
    "surv_surveypart_id" integer DEFAULT nextval('surv_surveypart_surv_surveypart_id_seq') NOT NULL,
    "title" character varying(250),
    "question" text,
    "creator" character varying(200),
    "modifier" character varying(200),
    "creation_date" timestamp,
    "modification_date" timestamp,
    "question_end" text,
    "surv_survey_id" integer,
    "scale_begin" character varying(50),
    "scale_end" character varying(50),
    "ordernumber" integer,
    "page_break" integer,
    "isactive" integer,
    "use_smileys" integer,
    "use_nps_colors" integer,
    "csi" integer,
    CONSTRAINT "surv_surveypart_pkey" PRIMARY KEY ("surv_surveypart_id")
) WITH (oids = false);


CREATE SEQUENCE surv_surveyquestion_surv_surveyquestion_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_surveyquestion" (
    "surv_surveyquestion_id" integer DEFAULT nextval('surv_surveyquestion_surv_surveyquestion_id_seq') NOT NULL,
    "questiontype" integer,
    "surv_surveypart_id" integer,
    "creator" character varying(200),
    "modifier" character varying(200),
    "modification_date" timestamp,
    "creation_date" timestamp,
    "question_order" integer,
    "question" text,
    "question_title" character varying(200),
    "question_required" integer,
    "show_other" integer,
    "question_isnps" integer,
    "surv_survquest_temp_id" integer,
    "answertable_id" character varying(36),
    CONSTRAINT "surv_surveyquestion_pkey" PRIMARY KEY ("surv_surveyquestion_id")
) WITH (oids = false);


CREATE SEQUENCE surv_surveyquestion_template_surv_surveyquestion_template_i_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_surveyquestion_template" (
    "surv_surveyquestion_template_id" integer DEFAULT nextval('surv_surveyquestion_template_surv_surveyquestion_template_i_seq') NOT NULL,
    "title" character varying(250),
    "creator" character varying(200),
    "modifier" character varying(200),
    "creation_date" timestamp,
    "modification_date" timestamp,
    "question_end" text,
    "scale_begin" character varying(50),
    "scale_end" character varying(50),
    "ordernumber" integer,
    "page_break" integer,
    "use_smileys" integer,
    "use_nps_colors" integer,
    "questiontype" integer,
    "question" text,
    "question_required" integer,
    "show_other" integer,
    "question_isnps" integer,
    "sec_company_id" integer,
    "callback_question" integer,
    "csi" integer,
    "clf_type" integer,
    "answertable_id" character varying(36),
    CONSTRAINT "surv_surveyquestion_template_pkey" PRIMARY KEY ("surv_surveyquestion_template_id")
) WITH (oids = false);


CREATE SEQUENCE surv_surveyquestionrow_surv_surveyquestionrow_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_surveyquestionrow" (
    "surv_surveyquestionrow_id" integer DEFAULT nextval('surv_surveyquestionrow_surv_surveyquestionrow_id_seq') NOT NULL,
    "surv_surveyquestion_id" integer,
    "ordernumber" integer,
    "question_label" text,
    "question_returnvalue" text,
    "creator" character varying(200),
    "modifier" character varying(200),
    "creation_date" timestamp,
    "modification_date" timestamp,
    "next_surveypart_id" integer,
    "next_question_id" integer,
    "question_shortlabel" character varying(100),
    "isactive" integer,
    "surv_survquest_temp_id" integer,
    CONSTRAINT "surv_surveyquestionrow_pkey" PRIMARY KEY ("surv_surveyquestionrow_id")
) WITH (oids = false);

CREATE INDEX "surv_surveyquestionrow_surv_surveyquestion_id_idx" ON "public"."surv_surveyquestionrow" USING btree ("surv_surveyquestion_id");


CREATE SEQUENCE surv_surveyquestionrow_templa_surv_surveyquestionrow_templa_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_surveyquestionrow_template" (
    "surv_surveyquestionrow_template_id" integer DEFAULT nextval('surv_surveyquestionrow_templa_surv_surveyquestionrow_templa_seq') NOT NULL,
    "surv_surveyquestion_template_id" integer,
    "ordernumber" integer,
    "question_label" text,
    "question_returnvalue" text,
    "creator" character varying(200),
    "modifier" character varying(200),
    "creation_date" timestamp,
    "modification_date" timestamp,
    "next_surv_surveyquestion_template_id" integer,
    "question_shortlabel" character varying(30),
    "isactive" integer,
    CONSTRAINT "surv_surveyquestionrow_template_pkey" PRIMARY KEY ("surv_surveyquestionrow_template_id")
) WITH (oids = false);


CREATE TABLE "public"."surv_surveysubquestion" (
    "surv_surveysubquestion_id" integer NOT NULL,
    "surv_surveyquestion_id" integer,
    "ordernumber" integer,
    "question_label" text,
    "creator" character varying(200),
    "modifier" character varying(200),
    "creation_date" timestamp,
    "modification_date" timestamp,
    CONSTRAINT "surv_surveysubquestion_pkey" PRIMARY KEY ("surv_surveysubquestion_id")
) WITH (oids = false);


CREATE SEQUENCE surv_surveytemplate_surv_surveytemplate_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."surv_surveytemplate" (
    "surv_surveytemplate_id" integer DEFAULT nextval('surv_surveytemplate_surv_surveytemplate_id_seq') NOT NULL,
    "surv_survey_id" integer,
    "email_template_id" integer,
    CONSTRAINT "surv_surveytemplate_pkey" PRIMARY KEY ("surv_surveytemplate_id")
) WITH (oids = false);


CREATE SEQUENCE tag_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."tags" (
    "tag_id" integer DEFAULT nextval('tag_id_seq') NOT NULL,
    "tag_name" character varying(100),
    "created_by" integer,
    "created_on" timestamp,
    "sec_company_id" integer,
    "tag_type" character varying(25),
    CONSTRAINT "tags_pkey" PRIMARY KEY ("tag_id")
) WITH (oids = false);


CREATE SEQUENCE techgrp_techgrp_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."techgrp" (
    "techgrp_id" integer DEFAULT nextval('techgrp_techgrp_id_seq') NOT NULL,
    "techgrp_group" character varying(50),
    "techgrp_techn" character varying(50),
    "techgrp_name" character varying(100),
    "sec_company_id" integer,
    "techgrp_email" character varying(254),
    "techgrp_login" character varying(50),
    "techgrp_function" character varying(50),
    "techgrp_dep" character varying(20),
    "techgrp_function2" character varying(50),
    "techgrp_firstname" character varying(100),
    "techgrp_type" character varying(50),
    CONSTRAINT "techgrp_pkey" PRIMARY KEY ("techgrp_id")
) WITH (oids = false);


CREATE SEQUENCE template_base_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."template_base" (
    "template_base_id" integer DEFAULT nextval('template_base_id_seq') NOT NULL,
    "template_text" text,
    "sec_company_id" integer,
    "template_name" character varying(100),
    "builder" jsonb,
    CONSTRAINT "template_base_pkey" PRIMARY KEY ("template_base_id")
) WITH (oids = false);


CREATE TABLE "public"."tmp_import" (
    "xml_salutation" character varying(50),
    "xml_initials" character varying(35),
    "xml_customername" character varying(100),
    "xml_email" character varying(300),
    "xml_brand" character varying(100),
    "xml_rasnumber" character varying(20),
    "xml_technician" character varying(20),
    "xml_internal_external" character varying(50),
    "xml_readydate" timestamp,
    "xml_techgrp" character varying(20),
    "xml_acceptance_by" character varying(20),
    "xml_visittime_end" timestamp,
    "xml_wvb" character varying(50),
    "xml_planning" character varying(50),
    "xml_department" integer,
    "xml_language" character varying(5),
    "xml_region" character varying(12),
    "xml_enumber" character varying(18),
    "xml_prodarea" character varying(1),
    "xml_proddivision" character varying(3),
    "xml_proddate" character varying(4),
    "xml_purchasedate" timestamp,
    "xml_operationtime" integer,
    "xml_nrvisits" integer,
    "xml_originservice" character varying(10),
    "xml_acceptancedate" timestamp,
    "xml_lastvisitdate" timestamp,
    "xml_waitingtime" integer,
    "xml_outagetime" integer,
    "xml_grossinvoice" numeric,
    "xml_payment" character varying(3),
    "xml_causecomplaint" character varying(50),
    "xml_solution" character varying(50),
    "xml_complain_agent" character varying(25),
    "xml_closuredate" timestamp,
    "xml_phone1" character varying(20),
    "xml_phone2" character varying(20),
    "xml_amount" double precision,
    "xml_presentitem" character varying(50),
    "xml_employee" character varying(50),
    "xml_wa" character varying(2),
    "xml_custcreditnr" character varying(12),
    "xml_logisticpartner" character varying(5),
    "xml_address" character varying(100),
    "xml_fulladdress" character varying(120),
    "xml_city" character varying(50),
    "xml_refnr" character varying(100),
    "xml_projectid" integer,
    "xml_projectnm" character varying(150),
    "xml_companyname" character varying(150),
    "flag_threshold" integer,
    "flag_blacklist" integer,
    "sec_company_id" integer,
    "tmp_import_id" integer NOT NULL,
    "xml_projectnr" character varying(10),
    "xml_projectdiv" character varying(10),
    "xml_la" character varying(5),
    "xml_fkz" character varying(5),
    "error_code" integer,
    "surv_key" character varying(36),
    "xml_callcode" character varying(5),
    "xml_functioncode" character varying(2),
    "xml_rc" character varying(5),
    "xml_fc" character varying(5),
    "xml_accountindic" character varying(2),
    "xml_fident" character varying(54),
    "xml_consecnr" double precision,
    "xml_timezone" character varying(6),
    "xml_currency" character varying(5),
    "xml_techgrouptext" character varying(50),
    "xml_areatext" character varying(50),
    "xml_cp" character varying(20),
    "xml_sv" character varying(20),
    "xml_dealer" character varying(100),
    "xml_dealer_pcode" character varying(10),
    "xml_hybrisid" character varying(20),
    CONSTRAINT "tmp_import_tmp_import_id_pk" PRIMARY KEY ("tmp_import_id")
) WITH (oids = false);


CREATE TABLE "public"."topic_list" (
    "topic_list_id" character varying(36) NOT NULL,
    "topiclist_name" character varying(100),
    "creator" integer,
    "creation_date" timestamp,
    "modified_by" integer,
    "modified_on" timestamp,
    "sec_company_id" integer,
    "is_favorite" integer,
    CONSTRAINT "topic_list_pkey" PRIMARY KEY ("topic_list_id")
) WITH (oids = false);


CREATE TABLE "public"."topic_list_items" (
    "topic_list_item_id" character varying(36) NOT NULL,
    "topic_list_id" character varying(36),
    "topic_item" character varying(100),
    "sec_company_id" integer,
    CONSTRAINT "topic_list_items_pkey" PRIMARY KEY ("topic_list_item_id")
) WITH (oids = false);


CREATE SEQUENCE user_reports_user_report_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

CREATE TABLE "public"."user_reports" (
    "user_report_id" integer DEFAULT nextval('user_reports_user_report_id_seq') NOT NULL,
    "report_name" character varying(100),
    "short_description" character varying(300),
    "sec_user_id" integer,
    "report_template" integer,
    "filters" text,
    "created_by" integer,
    "created_on" timestamp,
    "modified_by" integer,
    "modified_on" timestamp,
    "report_properties" text,
    CONSTRAINT "user_reports_pkey" PRIMARY KEY ("user_report_id")
) WITH (oids = false);

CREATE INDEX "user_reports_sec_user_id_index" ON "public"."user_reports" USING btree ("sec_user_id");


CREATE TABLE "public"."user_settings" (
    "user_setting_id" integer NOT NULL,
    "user_setting_type" integer,
    "name" character varying(100),
    "setting_object" text,
    "sec_user_id" integer,
    "created_on" timestamp,
    "company_id" integer,
    CONSTRAINT "user_settings_pkey" PRIMARY KEY ("user_setting_id")
) WITH (oids = false);


DROP TABLE IF EXISTS "nps_per_survey_creation_date";
CREATE VIEW "nps_per_survey_creation_date" AS SELECT s.sec_company_id,
    s.surv_survey_id,
    sum(
        CASE
            WHEN (a.nps_value >= 9) THEN 1
            ELSE 0
        END) AS promoters,
    sum(
        CASE
            WHEN ((a.nps_value >= 7) AND (a.nps_value <= 8)) THEN 1
            ELSE 0
        END) AS passives,
    sum(
        CASE
            WHEN (a.nps_value <= 6) THEN 1
            ELSE 0
        END) AS detractors,
    count(a.nps_value) AS records,
    date_trunc('day'::text, a.creation_date) AS date
   FROM ((surv_surveyanswer a
     LEFT JOIN surv_survey s ON ((a.surv_survey_id = s.surv_survey_id)))
     LEFT JOIN email_customer ec ON ((a.email_customer_id = ec.email_customer_id)))
  WHERE (a.nps_value IS NOT NULL)
  GROUP BY s.sec_company_id, s.surv_survey_id, (date_trunc('day'::text, a.creation_date));

DROP TABLE IF EXISTS "nps_per_survey_readydate";
CREATE VIEW "nps_per_survey_readydate" AS SELECT s.sec_company_id,
    s.surv_survey_id,
    sum(
        CASE
            WHEN (a.nps_value >= 9) THEN 1
            ELSE 0
        END) AS promoters,
    sum(
        CASE
            WHEN ((a.nps_value >= 7) AND (a.nps_value <= 8)) THEN 1
            ELSE 0
        END) AS passives,
    sum(
        CASE
            WHEN (a.nps_value <= 6) THEN 1
            ELSE 0
        END) AS detractors,
    count(a.nps_value) AS records,
    date_trunc('day'::text, ec.xml_readydate) AS date
   FROM ((surv_surveyanswer a
     LEFT JOIN surv_survey s ON ((a.surv_survey_id = s.surv_survey_id)))
     LEFT JOIN email_customer ec ON ((a.email_customer_id = ec.email_customer_id)))
  WHERE (a.nps_value IS NOT NULL)
  GROUP BY s.sec_company_id, s.surv_survey_id, (date_trunc('day'::text, ec.xml_readydate));

-- 2022-10-19 14:17:10.030291+02
