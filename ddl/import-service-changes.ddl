CREATE SEQUENCE i18n_message_id_seq OWNED BY i18n.message_id;
SELECT setval('i18n_message_id_seq', coalesce(max(message_id), 0) + 1, false) FROM i18n;
ALTER TABLE i18n ALTER COLUMN message_id SET DEFAULT nextval('i18n_message_id_seq'); 

INSERT INTO "i18n" ("message_key", "message_language", "message_value")
VALUES ('ccs.lbl.lastname', NULL, 'Last Name');



UPDATE mod_wccs_meta_fields SET metafield_title ='ccs.lbl.unique' WHERE metafield_name ='techgrp_techn';
UPDATE mod_wccs_meta_fields SET metafield_title ='ccs.lbl.unique' WHERE metafield_name ='map_key';
UPDATE i18n SET message_value ='First Name' WHERE message_key ='ccs.lbl.firstname' and message_language IS NULL;

UPDATE mod_wccs_meta_fields SET metafield_title ='ccs.lbl.lastname' WHERE metafield_name ='techgrp_name';


DELETE FROM mod_wccs_meta_fields WHERE metafield_name ='techgrp_login';
DELETE FROM mod_wccs_meta_fields WHERE metafield_name ='techgrp_function';
DELETE FROM mod_wccs_meta_fields WHERE metafield_name ='techgrp_function2';