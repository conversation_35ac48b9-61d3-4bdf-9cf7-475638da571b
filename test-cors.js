#!/usr/bin/env node

/**
 * CORS Test Script for External API
 * 
 * This script tests the CORS configuration for the external token endpoint
 * Run with: node test-cors.js <API_URL> <API_KEY> <API_SECRET>
 * 
 * Example: node test-cors.js https://your-api.com/v2/external/token your-api-key your-secret
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Get command line arguments
const [,, apiUrl, apiKey, apiSecret] = process.argv;

if (!apiUrl || !apiKey || !apiSecret) {
  console.error('Usage: node test-cors.js <API_URL> <API_KEY> <API_SECRET>');
  console.error('Example: node test-cors.js https://your-api.com/v2/external/token your-api-key your-secret');
  process.exit(1);
}

const url = new URL(apiUrl);
const isHttps = url.protocol === 'https:';
const httpModule = isHttps ? https : http;

const postData = JSON.stringify({
  id: apiKey,
  secret: apiSecret
});

// Test 1: OPTIONS request (preflight)
console.log('🔍 Testing CORS preflight (OPTIONS request)...');

const optionsReq = httpModule.request({
  hostname: url.hostname,
  port: url.port || (isHttps ? 443 : 80),
  path: url.pathname,
  method: 'OPTIONS',
  headers: {
    'Origin': 'https://example.com',
    'Access-Control-Request-Method': 'POST',
    'Access-Control-Request-Headers': 'Content-Type,Authorization'
  }
}, (res) => {
  console.log(`✅ OPTIONS Status: ${res.statusCode}`);
  console.log('📋 CORS Headers:');
  
  const corsHeaders = [
    'access-control-allow-origin',
    'access-control-allow-methods', 
    'access-control-allow-headers',
    'access-control-allow-credentials',
    'access-control-max-age'
  ];
  
  corsHeaders.forEach(header => {
    if (res.headers[header]) {
      console.log(`   ${header}: ${res.headers[header]}`);
    }
  });
  
  if (res.statusCode === 204 || res.statusCode === 200) {
    console.log('✅ CORS preflight successful');
    testActualRequest();
  } else {
    console.log('❌ CORS preflight failed');
    process.exit(1);
  }
});

optionsReq.on('error', (err) => {
  console.error('❌ OPTIONS request failed:', err.message);
  process.exit(1);
});

optionsReq.end();

// Test 2: Actual POST request
function testActualRequest() {
  console.log('\n🔍 Testing actual POST request...');
  
  const postReq = httpModule.request({
    hostname: url.hostname,
    port: url.port || (isHttps ? 443 : 80),
    path: url.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'Origin': 'https://example.com'
    }
  }, (res) => {
    console.log(`✅ POST Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode === 400 && data.includes('Not allowed by CORS')) {
        console.log('❌ CORS error still present');
        console.log('📄 Response:', data);
      } else if (res.statusCode === 200) {
        console.log('✅ Token request successful');
        try {
          const response = JSON.parse(data);
          console.log('🎉 Token received:', response.access_token ? 'Yes' : 'No');
        } catch (e) {
          console.log('📄 Response:', data);
        }
      } else if (res.statusCode === 401) {
        console.log('✅ CORS working (got authentication error instead of CORS error)');
        console.log('📄 Response:', data);
      } else {
        console.log(`ℹ️  Got status ${res.statusCode}`);
        console.log('📄 Response:', data);
      }
    });
  });
  
  postReq.on('error', (err) => {
    console.error('❌ POST request failed:', err.message);
    process.exit(1);
  });
  
  postReq.write(postData);
  postReq.end();
}

// Test 3: Request without Origin header (like Postman/curl)
setTimeout(() => {
  console.log('\n🔍 Testing request without Origin header (like Postman)...');
  
  const noOriginReq = httpModule.request({
    hostname: url.hostname,
    port: url.port || (isHttps ? 443 : 80),
    path: url.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
      // No Origin header
    }
  }, (res) => {
    console.log(`✅ No-Origin POST Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode === 400 && data.includes('Not allowed by CORS')) {
        console.log('❌ CORS error for no-origin request');
      } else {
        console.log('✅ No-origin request working (CORS not blocking)');
      }
    });
  });
  
  noOriginReq.on('error', (err) => {
    console.error('❌ No-origin request failed:', err.message);
  });
  
  noOriginReq.write(postData);
  noOriginReq.end();
}, 1000);
