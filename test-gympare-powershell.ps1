# Gympare API Test Script for PowerShell
# Replace YOUR_TOKEN_HERE with an actual valid token

$TOKEN = "YOUR_TOKEN_HERE"
$BASE_URL = "http://localhost:3000/v2/external/gympare"

Write-Host "🧪 Testing Gympare API with PowerShell..." -ForegroundColor Green
Write-Host "📡 Endpoint: $BASE_URL" -ForegroundColor Cyan
Write-Host "🔑 Token: $TOKEN" -ForegroundColor Yellow
Write-Host ""

try {
    # Test 1: Basic API call
    Write-Host "📋 Test 1: Basic API call" -ForegroundColor Magenta
    
    $headers = @{
        "Authorization" = "Bearer $TOKEN"
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri $BASE_URL -Method Get -Headers $headers -TimeoutSec 10
    
    Write-Host "✅ API call successful!" -ForegroundColor Green
    Write-Host "📊 Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 10
    
    # Test 2: Validate response structure
    Write-Host ""
    Write-Host "📋 Test 2: Response structure validation" -ForegroundColor Magenta
    
    if ($response -and $response.clubs) {
        Write-Host "✅ Response contains clubs array" -ForegroundColor Green
        Write-Host "🏢 Number of clubs: $($response.clubs.Count)" -ForegroundColor Cyan
        
        if ($response.clubs.Count -gt 0) {
            $firstClub = $response.clubs[0]
            Write-Host ""
            Write-Host "🏢 First club structure:" -ForegroundColor Yellow
            Write-Host "- ID: $($firstClub.id)"
            Write-Host "- Name: $($firstClub.name)"
            Write-Host "- Score: $($firstClub.score)"
            Write-Host "- Based on: $($firstClub.based_on)"
            Write-Host "- Link: $($firstClub.link)"
            
            if ($firstClub.reviews) {
                Write-Host "- Reviews: $($firstClub.reviews.Count) reviews"
            }
            
            # Test link format
            if ($firstClub.link) {
                Write-Host ""
                Write-Host "🔗 Testing link format..." -ForegroundColor Magenta
                
                if ($firstClub.link.StartsWith("https://feedback4sports.com/verhalen/?t=")) {
                    Write-Host "✅ Link format is correct" -ForegroundColor Green
                    
                    # Decode base64 to verify structure
                    try {
                        $base64Part = $firstClub.link.Split("?t=")[1]
                        $decodedBytes = [System.Convert]::FromBase64String($base64Part)
                        $decodedString = [System.Text.Encoding]::UTF8.GetString($decodedBytes)
                        $linkObject = $decodedString | ConvertFrom-Json
                        
                        Write-Host "✅ Base64 decoded successfully" -ForegroundColor Green
                        Write-Host "📋 Link object structure:" -ForegroundColor Cyan
                        Write-Host "- token: $(if ($linkObject.token) { '✅ Present' } else { '❌ Missing' })"
                        Write-Host "- server: $(if ($linkObject.server -eq 'https://servoy4.welcomeccs.nl/FF') { '✅ Correct' } else { '❌ Incorrect' })"
                        Write-Host "- project: $(if ($linkObject.project -eq '') { '✅ Empty' } else { '❌ Not empty' })"
                        Write-Host "- companyType: $(if ($linkObject.companyType -eq 2) { '✅ Correct' } else { '❌ Incorrect' })"
                        
                    } catch {
                        Write-Host "❌ Failed to decode base64: $($_.Exception.Message)" -ForegroundColor Red
                    }
                } else {
                    Write-Host "❌ Link format is incorrect" -ForegroundColor Red
                }
            } else {
                Write-Host "⚠️  No link provided (this might be expected if no StoryWidget token exists)" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "❌ Response does not contain clubs array" -ForegroundColor Red
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ API Test Failed:" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $statusText = $_.Exception.Response.StatusDescription
        Write-Host "Status: $statusCode" -ForegroundColor Red
        Write-Host "Status Text: $statusText" -ForegroundColor Red
        
        # Try to get response body
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Data: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response body" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🏁 Test completed" -ForegroundColor Green
