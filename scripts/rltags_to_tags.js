const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
dotenv.config({ path: path.join(__dirname, '../.env') });

const { Client } = require('pg');
const client = new Client({
  host: process.env.POSTGRES_HOST,
  port: process.env.POSTGRES_POST,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  database: process.env.POSTGRES_DATABASE,
});
const start = async () => {
  await client.connect();

  const list = await client.query(
    "select tags,sec_company_id,report_layout_id from report_layout where tags !='' and tags IS NOT NULL",
  );
  let c = {};
  let rows = list.rows.map((row) => ({
    ...row,
    tags: row.tags.split(',').filter((i) => i.length > 0),
  }));

  for (let item of rows) {
    const { sec_company_id, tags, report_layout_id } = item;
    await client.query(
      `DELETE FROM report_layout_tags WHERE rl_id ='${report_layout_id}'`,
    );
    if (!c[sec_company_id]) c[sec_company_id] = new Set();
    for (let tag of tags) {
      c[sec_company_id].add(tag);
    }
  }
  let tagList = [];
  for (let cid in c) {
    let sql = [...c[cid]].map(
      (tag) =>
        `INSERT INTO tags ("tag_name", "created_by", "created_on", "sec_company_id", "tag_type") VALUES ('${tag}', '0', now(), '${cid}', 'report_layout') RETURNING tag_id, tag_name `,
    );
    sql = sql.join(';');

    let result = await client.query(sql);
    if (!Array.isArray(result)) result = [result];

    tagList.push(
      ...result.map((item) => ({
        id: item.rows[0].tag_id,
        name: item.rows[0].tag_name,
      })),
    );
  }

  for (let tag of tagList) {
    let items = rows.filter((i) => i.tags.includes(tag.name));
    console.log(tag, items);
    let sql = items.map(
      (item) =>
        ` INSERT INTO report_layout_tags ("tag_id", "rl_id", "created_by", "created_on") VALUES ('${tag.id}', '${item.report_layout_id}', '0', now())`,
    );
    sql = sql.join(';');
    let result = await client.query(sql);
    console.log(result);
  }
};

start();
