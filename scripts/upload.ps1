<#
.Description
Upload script to upload the latest build (use build.ps1 to generate build) to the specified server environment. 
NB ssh is required to be setup on the system.

.PARAMETER server
Use one of the following server names to upload the current build:
API_BSH_TEST, API_BSH_DEV, API_BSH_ACCEPT, API_BOUW_TEST

.EXAMPLE
PS> .\upload -server API_BSH_DEV
#>
param([parameter(mandatory)] [validateset("API_BSH_TEST","API_BSH_DEV","API_BSH_ACCEPT","API_BOUW_TEST")] [string] $server )

switch($server){
	'API_BOUW_TEST' {	$map = '\node\api_bouw_test\'
						$instance = 'Node-BOUW-TEST'}
	'API_BSH_TEST' {	$map = '\node\api_bsh_test\' 
						$instance = 'Node-BSH-TEST'}
	'API_BSH_DEV' {		$map = '\node\api_bsh_dev\' 
						$instance = 'Node-BSH-DEV'}
	'API_BSH_ACCEPT' {	$map = '\node\api_bsh_accept\' 
						$instance = 'Node-BSH-ACCEPT'}
}

npm run build
scp package.json <EMAIL>:$map
scp package-lock.json <EMAIL>:$map
ssh -t <EMAIL> "pm2 stop $instance"
ssh -t <EMAIL> "cd $map && npm i && npm run clear"
scp -r dist\* <EMAIL>:$map\dist\
scp -r scripts\* <EMAIL>:$map\scripts\
ssh -t <EMAIL> "pm2 restart $instance"