const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');

const host = '<EMAIL>';

function executeCommand(command) {
  console.log(`running: ${command}`);

  try {
    const stdout = execSync(command, { encoding: 'utf-8' }).toString();
    console.log(stdout);
  } catch (error) {
    console.log(error);
    process.exit(1);
  }
}

// Input parameter
const server = process.argv[2];

if (process.argv[2] === 'unzip') {
  // Unzip the dist.zip file
  console.log('Unzipping dist.zip...');

  const zipPath = path.join(__dirname, '../dist.zip');

  // Load the zip file
  const zip = new AdmZip(zipPath);

  // Extract the contents to the current directory
  zip.extractAllTo(path.join(__dirname, '../'), true);

  // Remove the zip file after extraction
  console.log('Removing dist.zip...');
  fs.unlinkSync(zipPath);

  console.log('Unzip complete.');

  return;
}

// Server configuration
let map, instance;

switch (server) {
  case 'API_BOUW_TEST':
    map = '/node/api_bouw_test';
    instance = 'Node-BOUW-TEST';
    break;
  case 'API_BSH_TEST':
    map = '/node/api_bsh_test';
    instance = 'Node-BSH-TEST';
    break;
  case 'API_BSH_DEV':
    map = '/node/api_bsh_dev';
    instance = 'Node-BSH-DEV';
    break;
  case 'API_BSH_ACCEPT':
    map = '/node/api_bsh_accept';
    instance = 'Node-BSH-ACCEPT';
    break;
  default:
    console.error('Invalid server specified');
    process.exit(1);
}

// Execute commands
executeCommand('npm run build');

// Zip the dist folder
console.log('Zipping the dist folder...');
execSync('zip -r dist.zip dist');

// Upload the zip file to the demo server
console.log('Uploading dist.zip to the demo server...');
execSync(`scp dist.zip ${host}:${map}/dist.zip`);

// Remove the local zip file
console.log('Removing local dist.zip...');
fs.unlinkSync('dist.zip');

// Copy package.json and package-lock.json
executeCommand(`scp package.json ${host}:${map}`);
executeCommand(`scp package-lock.json ${host}:${map}`);

// Stop PM2 instance
executeCommand(`ssh -t ${host} "pm2 stop ${instance}"`);

// Install dependencies and run clear script
executeCommand(
  `ssh -t ${host} "cd ${map} && npm i && npm run clear && npm run clear-public"`,
);

// Copy dist and scripts directories
// executeCommand(`scp -r dist/* ${host}:${map}/dist/`);
executeCommand(`scp -r public ${host}:${map}/`);
executeCommand(`scp -r scripts/* ${host}:${map}/scripts/`);

// Call unzip file
executeCommand(
  `ssh -t ${host} "cd ${map} && node ./scripts/upload_server.js unzip"`,
);

// Restart PM2 instance
executeCommand(`ssh -t ${host} "pm2 restart ${instance}"`);
